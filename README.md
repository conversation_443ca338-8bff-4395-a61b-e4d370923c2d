腾讯云控制台 CAPACITY-MONITORING 前端 SDK 项目
======================================

![当前版本](https://img.shields.io/badge/当前版本-alpha-green.svg)

查看[发布日志](./CHANGELOG.md)。

---

本 SDK 为腾讯云控制台业务业务提供 CAPACITY-MONITORING 能力。

> TODO：提供具体的能力说明/应用场景

---

## 使用方法

### 本地开发
clone 项目：
```
<NAME_EMAIL>:cloud-arch-plugin/capacity-frontend.git --recursive
```

安装依赖：
```
pnpm i
```

启动本地开发服务器：
```
npm run dev
```

然后需要使用 Whistle 代理本地开发，代理规则如下：

```
/\/qcloud\/tea\/app\/([\w-]+)(\.(zh|en|jp|ko|dev))?(\.\w{10})?\.(js|css)(\.map)?/ http://127.0.0.1:8322/$1.$5$6
/\/qcloud\/tea\/app\/(.+\/)?__webpack_hmr$/ http://127.0.0.1:8322/__webpack_hmr
/\/qcloud\/tea\/app\/(.+\.)?(\w+)\.(hot-update\.(js|json))$/ http://127.0.0.1:8322/$1$2.$3
```


### 本地构建app
```
npm run deploy
```

### 本地构建模板
```
npm run build:template
```


### 目录说明

- `packages/app` 容量插件 【对应目录下启动命令：npm run dev】
- `packages/sigma-editor` gitmodule 子模块，用于容量报告里面的sigma编辑器 【对应目录下启动命令：npm run dev】
- `packages/sigma-editor-for-server` 用于容量报告的服务端渲染的sigma编辑器 【对应目录下启动命令：npm run dev】
- `packages/vite-template` 用于容量报告的服务端渲染的模板 【对应目录下构建模板命令：npm run build】
- `packages/ssr` 用于测试容量报告的服务端打印 【对应目录下启动命令：npm run dev】

### 三端说明
- 服务端：`packages/vite-template/build` 用于服务端的模板
- 云顾问租户端&运营端：`packages/app/src/render.tsx` init
- 数字资产租户端：`packages/app` init -> reportComponent  【https://iwiki.woa.com/p/4012357465】

### Git Submodule使用指南

#### 1. 更新子模块
##### 更新子模块
```
git submodule update --remote
```

### Sigma-editor-for-server手动同步Sigma-editor
##### rsync方式
```
rsync -av --exclude='.git'  --exclude='package-lock.json'   --exclude='package.json'  --exclude='.husky' --exclude='node_modules' --exclude='*.log' packages/sigma-editor/ packages/sigma-editor-for-server/
```

##### 特别注意【同步规则
- 同步后，localStorage、sessionStorage相关代码要移除(服务端不支持)
- 同步后，sigma-editor-for-server 相关字样要保留(同步后的目录和包名是sigma-editor-for-server)

##### 流水线
- 开源治理+预发布/正式环境 模板COS桶
```
https://zhiyan.woa.com/qci/15787/pipeline/132272/envId/155837/env/prod/envType/prod#/pipeline/detail/11028513/build/current
```

- 应用部署
```
https://zhiyan.woa.com/qci/15787/pipeline/132272/envId/155837/env/prod/envType/prod#/pipeline/detail/11335869/build/current
```

##### 预发布环境hosts加上这一条，因为有一个大容量请求
```
************* console-hc.cloud.tencent.com
```

##### 本地测试打印效果
```
1.启动ssr项目
2.build 最新vite-template
3.postman之类的软件访问http://localhost:4000/api/export/v1-1
```