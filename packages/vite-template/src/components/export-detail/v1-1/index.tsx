/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState, useEffect, useRef } from 'react';
import Sigma from '@tencent/sigma-editor-for-server/dist/index.es';
import A4 from '@tencent/tea-sdk-capacity-monitoring/src/components/archive-report/components/detail/components/a4/vite-template-v1-1';


interface ExportProps {
  data?: any;
  activeKey?: string;
  sigmaImage?: string;
}

const Export: React.FC<ExportProps> = (props) => {
  const data = props?.data || window?.dataDev || (typeof window?.data === 'string' ? JSON.parse(window?.data) : window?.data);
  const activeKey = props?.activeKey || window?.activeKeyDev || window?.activeKey;
  return (
    <div>
      <A4 Sigma={Sigma as any} visible={true} print={true} data={data} activeKey={activeKey} sigmaImage={props?.sigmaImage} />
    </div>
  );
};

export default Export;
