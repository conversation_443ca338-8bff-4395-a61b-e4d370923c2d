/* eslint-disable  */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import type { SigmaType, ShapeVnode } from '@tencent/sigma-editor';

export enum ShapeTypeEnum {
  SIGMA_RECTANGLE_SHAPE = 'SIGMA_RECTANGLE_SHAPE',
  SIGMA_BLOCK_SHAPE = 'SIGMA_BLOCK_SHAPE',
  SIGMA_TEXTLABEL_SHAPE = 'SIGMA_TEXTLABEL_SHAPE',
  SIGMA_ICON_SHAPE = 'SIGMA_ICON_SHAPE',
  SIGMA_IMAGE_SHAPE = 'SIGMA_IMAGE_SHAPE',
  SIGMA_AREA_SHAPE = 'SIGMA_AREA_SHAPE',
  SIGMA_AUTOSCALING_SHAPE = 'SIGMA_AUTOSCALING_SHAPE',
  SIGMA_AVAILABILITYZONE_SHAPE = 'SIGMA_AVAILABILITYZONE_SHAPE',
  SIGMA_PRODUCT_SHAPE = 'SIGMA_PRODUCT_SHAPE',
  SIGMA_LINE_SHAPE = 'SIGMA_LINE_SHAPE',
  SIGMA_CIRCLE_SHAPE = 'SIGMA_CIRCLE_SHAPE',
  SIGMA_SECURITY_GROUP_SHAPE = 'SIGMA_SECURITY_GROUP_SHAPE',
  SIGMA_SUBNET_SHAPE = 'SIGMA_SUBNET_SHAPE',
  SIGMA_VPC_SHAPE = 'SIGMA_VPC_SHAPE',
  SIGMA_GROUP_SHAPE = 'SIGMA_GROUP_SHAPE',
  SIGMA_SECURITY_SHAPE = 'SIGMA_SECURITY_SHAPE',
  SIGMA_BASE_GROUP_SHAPE = 'SIGMA_BASE_GROUP_SHAPE',
  SIGMA_REMARK_NOTE_SHAPE = 'SIGMA_REMARK_NOTE_SHAPE',
  SIGMA_CCN_SHAPE = 'SIGMA_CCN_SHAPE',
  SIGMA_TKE_SHAPE = 'SIGMA_TKE_SHAPE',
}

// 根据key获取shape
export const getShapeByKey = (
  sigma: SigmaType & Partial<{core: any}>,
  key: string,
): ShapeVnode | null => {
  if (!sigma) {
    return null;
  }
  const { shapes } = sigma.core?.data ?? {};
  return shapes?.[key];
};

// 根据keys获取shape列表
export const getShapesByKeys = (
  sigma: SigmaType & Partial<{core: any}>,
  keys: string[],
): ShapeVnode[] | null => {
  if (!sigma) {
    return null;
  }
  if (!(keys instanceof Array)) {
    return [];
  }
  const { shapes } = sigma.core?.data ?? {};
  return keys.map((key) => shapes[key]);
};

// 获取当前画布上的所有图元信息
export const getCurrShapes = (sigma: SigmaType & Partial<{core: any}>) => {
  if (!sigma) {
    return {};
  }
  const { shapes } = sigma.core?.data ?? {};
  return shapes;
};

export const clearConnectLineToolBox = () => {
  const t = document.getElementById('sigma-c-box');
  if (t && t.innerHTML !== '') t.innerHTML = '';
};

// eslint-disable-next-line
export const getShapeStyleLable = (shape: ShapeVnode) => shape?.styles?.label?.default || '';


// 给productShape添加类名
export const addProductClass = (sigma: SigmaType, productKey: string, className: string, classLevel = 'svg') => {
  const currShape = getShapeByKey(sigma, productKey);
  if (currShape?.type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
    // @ts-ignore
    const { doc } = sigma.getGlobalValue();
    if (!doc) return;
    const selector = classLevel === 'svg'
      ? `[key="${productKey}"] .position`
      : `[key="${productKey}"]`;
    const shapeBodys = doc.find(selector);
    shapeBodys?.forEach((shapeBody: any) => {
      shapeBody?.addClass(className);
    });
  }
};

// 给productShape移除类名
export const removeProductClass = (
  sigma: SigmaType,
  productKey: string,
  className: string,
  classLevel = 'svg',
) => {
  const currShape = getShapeByKey(sigma, productKey);
  if (currShape?.type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
    const { doc } = sigma.getGlobalValue();
    if (!doc) return;
    const selector = classLevel === 'svg'
      ? `[key="${productKey}"] .position`
      : `[key="${productKey}"]`;
    const shapeBodys = doc.find(selector);
    shapeBodys?.forEach((shapeBody: any) => {
      shapeBody?.removeClass(className);
    });
  }
};

// 返回文本节点是否是线条的标签
export function isLineLabel(vnode: ShapeVnode, sigma: SigmaType & Partial<{core: any}>) {
  if (!vnode?.attach || vnode?.type !== 'SIGMA_TEXTLABEL_SHAPE') return false;
  const { shapes } = sigma.core.data;
  return shapes[vnode.attach]?.type === 'SIGMA_LINE_SHAPE';
}

export const addSvgToDetail = (detail: string) => {
  const originDetail = JSON.parse(detail);
  const { svgMap } = originDetail;
  if (!svgMap) return detail;
  Object.values(originDetail).forEach((shape: any) => {
    if (shape.type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
      if (svgMap[shape.name]) {
        shape.data = svgMap[shape.name];
      }
    }
  });
  delete originDetail.svgMap;
  return JSON.stringify(originDetail);
};

// svg与detail需要在收尾去掉单引号
export const getValidString = (str: string) => str?.replace(/^'|'$/g, '') ?? '';

export default { getShapeByKey, clearConnectLineToolBox, getValidString };
