import React, { useEffect, useState, useRef } from 'react';
import SigmaComponent from "@tencent/tea-sdk-capacity-monitoring/src/components/archive-report/components/sigma/vite-template-v1-1/index";
import Sigma from '@tencent/sigma-editor-for-server/dist/index.es';
import ExportOverview from "../export-overview/v1-1";
import ExportDetail from "../export-detail/v1-1";
import ExportTable from "../export-table/v1-1";
import s from './index.module.scss';

export default function Main(): React.ReactElement {
  const data = window?.dataDev || (typeof window?.data === 'string' ? JSON.parse(window?.data) : window?.data);
  const [list, setList] = useState([]);
  const [sigmaDom, setSigmaDom] = useState<HTMLDivElement>(null);
  const [overviewSvg, setOverviewSvg] = useState('');
  const [hasExecutedGetData, setHasExecutedGetData] = useState(false);
  const [isListRendered, setIsListRendered] = useState(false);
  const listRef = useRef<HTMLDivElement>(null);
  const getSvgStr = (str) => {
    if (!str) {
      return;
    }
    let newStr = str;
    if (newStr.indexOf('\'') === 0) {
      newStr = newStr.slice(1);
    }
    if (newStr.lastIndexOf('\'') === newStr.length - 1) {
      newStr = newStr.slice(0, newStr.length - 1);
    }

    return newStr;
  };
  // 将 SVG 转换为 Base64 编码
  const svgToBase64 = (svg) => {
    // 使用 encodeURIComponent 对 SVG 字符串进行编码
    const encodedSvg = encodeURIComponent(svg)
      .replace(/%([0-9A-F]{2})/g, (match, p1) => String.fromCharCode((`0x${p1}`) as unknown as number));
      // 使用 btoa 对编码后的字符串进行 Base64 编码
    return btoa(encodedSvg);
  };

  // 高级图片质量优化函数
  const getHighQualitySvgImage = async (svgElement, options: {
    scaleFactor?: number;
    quality?: number;
    enableSmoothing?: boolean;
    smoothingQuality?: 'low' | 'medium' | 'high';
    format?: 'png' | 'jpeg' | 'webp';
  } = {}) => {
    const {
      scaleFactor = 3,           // 缩放因子，默认3倍
      quality = 1.0,             // PNG质量，0-1
      enableSmoothing = true,    // 是否启用平滑
      smoothingQuality = 'high', // 平滑质量: 'low', 'medium', 'high'
      format = 'png'             // 输出格式: 'png', 'jpeg', 'webp'
    } = options;

    return new Promise((resolve) => {
      // 创建SVG序列化字符串
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(svgElement);

      // 创建图片对象
      const img = new Image();

      // 将SVG转换为Base64数据URL
      const svgBase64 = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString)));

      // 创建Canvas进行渲染
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // 设置Canvas尺寸，使用缩放因子提升分辨率
      const originalWidth = svgElement.clientWidth;
      const originalHeight = svgElement.clientHeight;
      canvas.width = originalWidth * scaleFactor;
      canvas.height = originalHeight * scaleFactor;

      // 启用图像平滑和抗锯齿
      if (enableSmoothing) {
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = smoothingQuality;
      }

      // 设置白色背景（对于透明SVG）
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 图像加载完成后绘制到Canvas
      img.onload = () => {
        // 使用缩放因子绘制，提升图片质量
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // 根据格式生成不同质量的数据URL
        let dataUrl;
        if (format === 'jpeg') {
          dataUrl = canvas.toDataURL('image/jpeg', quality);
        } else if (format === 'webp') {
          dataUrl = canvas.toDataURL('image/webp', quality);
        } else {
          dataUrl = canvas.toDataURL('image/png', quality);
        }

        resolve(dataUrl);
      };

      // 处理图片加载错误
      img.onerror = () => {
        console.error('SVG图片加载失败');
        resolve('');
      };

      // 加载SVG图像
      img.src = svgBase64;
    });
  };

  const captureCenteredSvgElement = async (targetElement, width, height, scaleFactor = 2) => {
    return new Promise((resolve) => {
      // 1. 获取目标元素的位置信息
      const bbox = targetElement?.getBBox?.();

      // 2. 计算居中坐标
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;

      // 3. 创建临时SVG副本
      const tempSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      tempSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      tempSvg.innerHTML = `
        <style>
          svg {
            overflow: auto !important;
          }
        </style>
        ${getSvgStr(document.getElementById('sigma-container').children[0].innerHTML)}
      `;
      tempSvg.style.overflow = 'auto';

      // 4. 设置裁剪视窗，使用缩放后的尺寸
      const scaledWidth = width * scaleFactor;
      const scaledHeight = height * scaleFactor;
      tempSvg.setAttribute(
        'viewBox',
        `${centerX - width / 2} ${centerY - height / 2} ${width} ${height}`,
      );

      // 设置SVG的物理尺寸
      tempSvg.setAttribute('width', scaledWidth.toString());
      tempSvg.setAttribute('height', scaledHeight.toString());

      // 5. 生成图片数据
      const svgData = new XMLSerializer().serializeToString(tempSvg);
      const base64Svg = svgToBase64(svgData); // 使用修复后的编码函数

      const result = `data:image/svg+xml;base64,${base64Svg}`;
      resolve(result);
    });
  };
  useEffect(() => {
    // hack:放到  生成pdf那里 去缩放， sigma缩放 - 居中 有问题
    // document.getElementById('v1-1').style.cssText = `transform: scale(${window.innerWidth / 590}); margin: 0 auto; padding: 0; width: 580px; overflow: auto; transform-origin: center top;`;
  }, []);

  // 监听列表渲染完成
  useEffect(() => {
    if (list.length > 0 && !isListRendered) {
      // 使用 requestAnimationFrame 确保 DOM 更新完成
      requestAnimationFrame(() => {
        // 再次使用 requestAnimationFrame 确保所有子组件都渲染完成
        requestAnimationFrame(() => {
          setIsListRendered(true);
        });
      });
    }
  }, [list, isListRendered]);

  // 当列表渲染完成后设置 finish 标签
  useEffect(() => {
    if (isListRendered) {
      const finishTag = document.createElement('div');
      finishTag.setAttribute('id', 'finish');
      document.body.appendChild(finishTag);
    }
  }, [isListRendered]);

  useEffect(() => {
    /**
     * 异步获取并处理Sigma节点图片
     *
     * 1. 生成高质量SVG图片并设置到state
     * 2. 遍历节点数据，对每个存在的节点：
     *    - 重置所有图形选中状态
     *    - 设置当前节点为选中状态
     *    - 捕获节点中心区域截图并更新数据
     * 3. 最终将处理后的节点数据设置到state
     *
     * @remarks
     * 该函数会通过`hasExecutedGetData`标志位确保只执行一次
     * 依赖sigmaDom实例和原始数据data的存在
     */
    const getData = async () => {
      if (sigmaDom && !hasExecutedGetData) {
        // 使用高质量图片生成函数
        const newUrl = await getHighQualitySvgImage(
          document.getElementById('sigma-container').children[0],
          {
            scaleFactor: 3,
            quality: 1.0,
            enableSmoothing: true,
            smoothingQuality: 'high',
            format: 'png',
          }
        );
        setOverviewSvg(newUrl as string);
        setHasExecutedGetData(true);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const shapes = (sigmaDom as any)?.core?.data?.shapes;

        // 等待所有异步操作完成
        await Promise.all(
          data?.NodeLoadDetailItems?.map(async (item) => {
            const exist = document.getElementById(item?.NodeUuid);
            try {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              (sigmaDom as any)?.setShapeChecked?.(Object.values(shapes), false);
            } catch (error) {
              console.error(error);
            }
            if (exist) {
              const shape = shapes?.[item?.NodeUuid];
              // eslint-disable-next-line @typescript-eslint/no-unused-expressions, @typescript-eslint/no-explicit-any
              shape && (sigmaDom as any)?.setShapeChecked?.([shape], true);
              const targetNode = document.getElementById(item?.NodeUuid);
              const screenshotData = await captureCenteredSvgElement(
                targetNode,
                580,
                440,
                2
              );
              item.image = screenshotData;
            }
          }) || []
        );

        setList(data?.NodeLoadDetailItems);
      }
    };
    getData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sigmaDom, hasExecutedGetData]);
  return (
    <div id="v1-1" className={s.v1_1}>
      <div style={{ height: 0, width: 0, overflow: 'hidden' }}>
        <div className={s.sigma} style={{ height: 260, width: 502 }}>
          <SigmaComponent
            digitalAssets={false}
            Sigma={Sigma}
            data={data}
            id="sigma-detail"
            width={502}
            height={260}
            interact={false}
            onInit={(sigmaRef) => setSigmaDom(sigmaRef)}
          />
        </div>
      </div>

      <ExportOverview sigmaImage={overviewSvg} />
      <div className={s.break}></div>
      <div ref={listRef}>
        {
          list?.map((item, parentIndex) => {
            const instanceLoadInfos = item?.InstanceLoadInfos || [];
            const pages = instanceLoadInfos.length <= 4 ? 1 : Math.ceil((instanceLoadInfos.length - 4) / 15) + 1;
            return (
              <React.Fragment key={item?.NodeUuid}>
                <ExportDetail sigmaImage={item.image} data={data} activeKey={item?.NodeUuid} />
                {pages > 1 && <div className={s.break}></div>}
                {
                  Array.from({ length: pages }, (_, index) => {
                    return index ? (
                      <React.Fragment key={`${parentIndex}-${index}`}>
                        <ExportTable data={data} activeKey={item?.NodeUuid} currentPage={index + 1} />
                        {index !== pages - 1 && <div className={s.break}></div>}
                      </React.Fragment>
                    ) : null
                  })
                }
              </React.Fragment>
            )
          })
        }
      </div>
    </div>
  );
}