/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState, useEffect, useRef } from 'react';
import Sigma from '@tencent/sigma-editor-for-server/dist/index.es';
import A4 from '@tencent/tea-sdk-capacity-monitoring/src/components/archive-report/components/detail/components/table';


interface ExportProps {
  data?: any;
}

const Export: React.FC<ExportProps> = () => {
  const data = window?.dataDev || (typeof window?.data === 'string' ? JSON.parse(window?.data) : window?.data);
  const currentPage = window?.currentPageDev || (typeof window.currentPage === 'string' ? parseInt(window.currentPage) : window.currentPage);
  const activeKey = window?.activeKeyDev || window?.activeKey;
  return (
    <div>
      <A4 Sigma={Sigma as any} visible={true} print={true} data={data} currentPage={currentPage} activeKey={activeKey} />
    </div>
  );
};

export default Export;
