import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [
          ['lodash', { skipBabel6: true }]
        ]
      }
    }),
  ].filter(Boolean),
  define: {
    'process.env': {},
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@tencent/tea-app/lib/i18n': path.resolve(__dirname, 'src/mock/i18n.ts'),
      '@tea/app/i18n': path.resolve(__dirname, 'src/mock/i18n.ts'),
    },
  },
  build: {
    minify: 'terser',
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
        assetFileNames: (assetInfo) => {
          if (assetInfo.name && assetInfo.name.endsWith('.css')) {
            return 'style.css';
          }
          return '[name].[hash][extname]';
        }
      }
    },
    cssCodeSplit: false,
  },
  server: {
    port: 9000,
  },
});