/**
 * @version: 1.1.0
 * @description: 构建v1-1版本
 * @description feat: 由单页单模板升级为单页长模版&性能优化等
 */

import { build } from 'vite';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { visualizer } from 'rollup-plugin-visualizer';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const mainComponentName = 'v1-1';
const exportName = 'report';
const componentMap = {
  [mainComponentName]: "Main",
}

async function buildComponent(name) {
  await build({
    configFile: path.resolve(__dirname, 'vite.config.ts'),
    build: {
      lib: {
        entry: path.resolve(__dirname, `src/components/${name}/index.tsx`),
        formats: ['umd'],
        fileName: () => `index.js`,
        name: componentMap[name]
      },
      outDir: `dist/${name}`,
      rollupOptions: {
        plugins: process.env.ANALYZE === 'true' ? [
          visualizer({
            filename: `stats-${name}.html`,
            open: false,
            gzipSize: true,
            brotliSize: true,
          })
        ] : []
      }
    }
  });
}

async function generateHtml() {
  let componentScript = '';
  let componentStyle = '';

  const componentDir = `dist/${mainComponentName}`;
  componentScript = await fs.readFile(
    path.join(__dirname, `${componentDir}/index.js`),
    'utf-8'
  );
  // 检查组件样式文件是否存在
  try {
    await fs.access(path.join(__dirname, `${componentDir}/style.css`));
    componentStyle = await fs.readFile(
      path.join(__dirname, `${componentDir}/style.css`),
      'utf-8'
    );
  } catch (err) {
    console.warn(`Warning: ${componentDir}/style.css 文件不存在，使用空字符串`);
  }

  try {

    // 准备数据
    const data = {};

    // 生成HTML
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <style>@page {margin: 0mm 0mm 0mm 0mm;size: 148.1mm 211mm}</style>
          <style>
            html,body {
              font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
                "Helvetica Neue", "Microsoft YaHei", "微软雅黑", -apple-system,
                BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Source Han Sans SC",
                "Source Han Sans CN", "思源黑体 CN", sans-serif;
              margin: 0;
              padding: 0;
            }
            blockquote, dd, dl, figure, h1, h2, h3, h4, h5, h6, ol, p, pre, ul {
              margin: 0;
            }
            td, th {
              padding: 0;
            }
          </style>
          <style>${componentStyle}</style>
          <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
          <script>
            window.data={};
            window.activeKey='';
            window.currentPage=1;
          </script>
        </head>
        <body>
          <div id="root"></div>
          <script>
            window.__INITIAL_DATA__ = ${JSON.stringify(data)};
          </script>
          <script>
            ${componentScript}
            const root = ReactDOM.createRoot(document.getElementById('root'));
            // 使用对应的全局变量名访问组件
            const Component = ${componentMap[mainComponentName]};
            root.render(React.createElement(Component));
          </script>
        </body>
      </html>
    `;

    // 确保build目录存在
    await fs.mkdir(path.join(__dirname, 'build'), { recursive: true });

    // 写入HTML文件
    const htmlPath = path.join(__dirname, `build/${exportName}.html`);
    await fs.writeFile(htmlPath, html);

    console.log(`Generated: ${htmlPath}`);
  } catch (error) {
    console.error(`Error generating ${exportName}.html:`, error);
  }
}

async function buildAll() {
  // 先构建所有组件
  await buildComponent(mainComponentName);

  // 然后生成 HTML 文件
  await generateHtml();
}

buildAll().catch((err) => {
  console.error(err);
  process.exit(1);
});
