{"name": "vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node build-v1-1.js", "analyze": "cross-env ANALYZE=true node build-v1-1.js", "lint": "eslint ."}, "dependencies": {"@tencent/sigma-editor": "workspace:*", "@tencent/sigma-editor-for-server": "workspace:*", "@tencent/tea-sdk-capacity-monitoring": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@babel/core": "^7.17.5", "@eslint/js": "^9.15.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.5.0", "babel-plugin-lodash": "^3.3.4", "cross-env": "^7.0.3", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "rollup-plugin-visualizer": "^5.13.1", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.3"}}