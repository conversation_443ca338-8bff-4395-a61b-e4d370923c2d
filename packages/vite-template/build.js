/**
 * @deprecated 已废弃版本⚠️
 * @version: 1.0.0
 * @description: 构建v1-0版本 初始版本
 * @description feat: 服务端单页单模板渲染
 */

import { build } from 'vite';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { visualizer } from 'rollup-plugin-visualizer';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const components = ['export-overview', 'export-detail', 'export-table'];

const componentMap = {
  "export-overview": "ExportOverview",
  "export-detail": "ExportDetail",
  "export-table": "ExportTable"
}

async function buildComponent(name) {
  await build({
    configFile: path.resolve(__dirname, 'vite.config.ts'),
    build: {
      lib: {
        entry: path.resolve(__dirname, `src/components/${name}/index.tsx`),
        formats: ['umd'],
        fileName: () => `index.js`,
        name: componentMap[name]
      },
      outDir: `dist/${name}`,
      rollupOptions: {
        plugins: process.env.ANALYZE === 'true' ? [
          visualizer({
            filename: `stats-${name}.html`,
            open: false,
            gzipSize: true,
            brotliSize: true,
          })
        ] : []
      }
    }
  });
}

async function generateHtml(componentName) {
  try {
    let componentStyle = '';
    const componentDir = `dist/${componentName}`;

    // 检查组件样式文件是否存在
    try {
      await fs.access(path.join(__dirname, `${componentDir}/style.css`));
      componentStyle = await fs.readFile(
        path.join(__dirname, `${componentDir}/style.css`),
        'utf-8'
      );
    } catch (err) {
      console.warn(`Warning: ${componentDir}/style.css 文件不存在，使用空字符串`);
    }

    // 读取组件脚本文件
    const componentScript = await fs.readFile(
      path.join(__dirname, `${componentDir}/index.js`),
      'utf-8'
    );

    // 准备数据
    const data = {};

    // 生成HTML
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <style>
            html,body {
              font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
                "Helvetica Neue", "Microsoft YaHei", "微软雅黑", -apple-system,
                BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Source Han Sans SC",
                "Source Han Sans CN", "思源黑体 CN", sans-serif;
            }
            blockquote, dd, dl, figure, h1, h2, h3, h4, h5, h6, ol, p, pre, ul {
              margin: 0;
            }
            td, th {
              padding: 0;
            }
          </style>
          <style>${componentStyle}</style>
          <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
          <script>
            window.data={};
            window.activeKey='';
            window.currentPage=1;
          </script>
        </head>
        <body>
          <div id="root"></div>
          <script>
            window.__INITIAL_DATA__ = ${JSON.stringify(data)};
          </script>
          <script>
            ${componentScript}
            const root = ReactDOM.createRoot(document.getElementById('root'));
            // 使用对应的全局变量名访问组件
            const Component = ${componentMap[componentName]};
            root.render(React.createElement(Component));
          </script>
        </body>
      </html>
    `;

    // 确保build目录存在
    await fs.mkdir(path.join(__dirname, 'build'), { recursive: true });

    // 写入HTML文件
    const htmlPath = path.join(__dirname, `build/${componentName}.html`);
    await fs.writeFile(htmlPath, html);

    console.log(`Generated: ${htmlPath}`);
  } catch (error) {
    console.error(`Error generating ${componentName}.html:`, error);
  }
}

async function buildAll() {
  // 先构建所有组件
  for (const component of components) {
    await buildComponent(component);
  }

  // 然后生成 HTML 文件
  for (const component of components) {
    await generateHtml(component);
  }
}

buildAll().catch((err) => {
  console.error(err);
  process.exit(1);
});
