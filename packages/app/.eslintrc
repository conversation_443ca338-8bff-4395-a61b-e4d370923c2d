{"env": {"browser": true, "es6": true}, "settings": {"react": {"version": "detect"}}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "extends": ["@tencent/eslint-config-tencent", "plugin:react/recommended", "plugin:@tencent/tea-i18n/recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["@tencent/tea-i18n", "@typescript-eslint"], "rules": {"@typescript-eslint/no-empty-function": "error", "@typescript-eslint/no-explicit-any": "off", "max-lines-per-function": "off"}}