.thresholds-setting-wrap {
  .tea-icon {
    background-size: auto;
  }
  .loading-wrap {
    text-align: center;
    margin-top: 200px;
  }
  .tea-accordion__header {
    display: flex;
    .tea-accordion__header-title {
      flex-shrink: 1;
      flex-grow: 1;
      .t-wrap {
        display: flex;
        .t-text {
          flex-shrink: 1;
          flex-grow: 1;
          color: #12161B;
          font-weight: 600;
        }
        .reset-btn {
          flex-shrink: 0;
          flex-grow: 0;
          text-decoration: none;
          .tea-icon {
            background-size: auto;
          }
        }
      }
    }
  }
  .thresholds-setting-collapse-wrap {
    margin-top: 32px;
    .tea-accordion__header {
      .tea-icon-arrowup {
        transform-origin: center;
        transform: rotate(180deg);
      }
    }
    .thresholds-setting-collapse {
      margin-top: 24px;
      &:nth-child(1) {
        margin-top: 0;
      }
    }
    .thresholds-setting-collapse-none {
      display: none;
    }
  }
  .thresholds-setting-select {
    margin-top: 16px;
  }
  .threshold-slider {
    margin-top: 10px;
    &:nth-child(1) {
      margin-top: 0;
    }
  }
}
.thresholds-setting-sdk-footer-btn-wrap {
  text-align: center;
  button:nth-child(1) {
    margin-right: 10px;
  }
  .is-loading .tea-icon-loading {
    background-size: auto;
  }
  .tea-btn--link {
    text-decoration: none;
  }
}