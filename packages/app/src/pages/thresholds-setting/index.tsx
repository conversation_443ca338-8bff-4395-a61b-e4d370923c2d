/* eslint-disable no-param-reassign */
import React, { useEffect, useMemo, useState } from 'react';
import { t } from '@tea/app/i18n';
import {
  Text,
  // SelectMultiple,
  Collapse,
  Button,
  message,
  StatusTip,
} from '@tencent/tea-component';
import {
  describeArchProductThresholdInfo,
  modifyArchProductThreshold as modifyProductThreshold,
} from '@src/api/thresholdsSetting';
import { cloneDeep } from 'lodash-es';
import ThresholdSetting from '@src/components/threshold-setting';
import HandleArea from '../handle-area';
import './index.less';
import {
  isRefreshArea /* , nodeConfigInfo */,
  setIsOpenSetting,
  setIsRefreshArea,
} from '@src/utils/caching';

export interface Props {
  arInfo: any;
  isRefresh?: boolean;
}

const ThresholdsSetting = ({ arInfo, isRefresh }: Props) => {
  const currentMapId = arInfo.archInfo.archId;
  // const productOptions = nodeConfigInfo.ProductInfos?.map((item)=>{
  //   return {
  //     text: item.Name,
  //     value: item.Product
  //   };
  // });
  const [archProductThresholdInfos, setArchProductThresholdInfos] = useState([]);
  const [activeIds, setActiveIds] = useState([]);
  const [saveLoading, setSaveLoading] = useState(false);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const getSingleDescribeArchProductThresholdInfo = async (info) => {
    // eslint-disable-next-line no-param-reassign
    info.resetLoading = true;
    setArchProductThresholdInfos(cloneDeep(archProductThresholdInfos));
    try {
      const res = await describeArchProductThresholdInfo({
        MapId: currentMapId,
        IsDefault: 1,
        Products: [info.Product],
      });
      if (res.Error) {
        info.resetLoading = false;
        setArchProductThresholdInfos(cloneDeep(archProductThresholdInfos));
        return;
      }
      archProductThresholdInfos.forEach((el) => {
        if (el.Product === info.Product) {
          el.ArchThresholdInfos = cloneDeep(res.ArchProductThresholdInfos[0].ArchThresholdInfos);
          el.resetLoading = false;
        }
      });
      setArchProductThresholdInfos(cloneDeep(archProductThresholdInfos));
    } catch (err) {
      info.resetLoading = false;
      setArchProductThresholdInfos(cloneDeep(archProductThresholdInfos));
    }
  };
  const getDescribeArchProductThresholdInfo = async () => {
    try {
      const res = await describeArchProductThresholdInfo({
        MapId: currentMapId,
      });
      if (res.Error) {
        setSettingsLoading(false);
        return;
      }
      setArchProductThresholdInfos(res.ArchProductThresholdInfos);
      setActiveIds([res.ArchProductThresholdInfos?.[0]?.Product]);
      setSettingsLoading(false);
    } catch (err) {
      setSettingsLoading(false);
    }
  };
  useEffect(() => {
    if (isRefresh) {
      getDescribeArchProductThresholdInfo();
    }
  }, [isRefresh]);
  useEffect(
    () => () => {
      setIsOpenSetting(false);
    },
    []
  );
  const modifyArchProductThreshold = async () => {
    setSaveLoading(true);
    const archProductThresholdInfosRe: any = (
      archProductThresholdInfos || []
    ).map(item => ({
      ArchThresholdInfos: cloneDeep(item.ArchThresholdInfos),
      Product: item.Product,
      ProductNames: item.ProductNames,
    }));
    let flag = true;
    archProductThresholdInfosRe.forEach((item) => {
      item.ArchThresholdInfos.forEach((item2) => {
        item2.ThresholdConfigInfos.forEach((item3) => {
          if (!item3.PeriodValue) {
            flag = false;
          }
        });
      });
    });
    if (flag) {
      try {
        const res = await modifyProductThreshold({
          MapId: currentMapId,
          ArchProductThresholdInfos: archProductThresholdInfosRe,
        });
        if (!res?.Error) {
          message.success({
            content: t('操作成功'),
          });
          setIsRefreshArea(!isRefreshArea);
          arInfo.setSlotComponent(<HandleArea arInfo={arInfo} isRefreshArea={isRefreshArea} />);
        }
        setSaveLoading(false);
      } catch (err) {
        setSaveLoading(false);
        console.log(err);
      }
    } else {
      setSaveLoading(false);
      message.error({
        content: t('请选择周期'),
      });
    };
  };
  const footerNode = (
    <div className={'thresholds-setting-sdk-footer-btn-wrap'}>
      <Button
        type={'primary'}
        loading={saveLoading}
        onClick={() => {
          modifyArchProductThreshold();
        }}
      >
        {t('保存')}
      </Button>
      <Button
        onClick={() => {
          arInfo.closeDrawer();
        }}
      >
        {t('取消')}
      </Button>
    </div>
  );
  useMemo(() => {
    arInfo.setDrawerProps({
      style: { width: '500px' },
      footer: footerNode,
    });
  }, [archProductThresholdInfos, saveLoading]);
  return (
    <div className={'thresholds-setting-wrap'}>
      {settingsLoading ? (
        <div className="loading-wrap">
          <StatusTip status={'loading'} />
        </div>
      ) : (
        <>
          <Text className={'notice-text'} theme={'label'}>
            {t('提示：该设定将作用于各节点的默认设置，如果节点单独修改后，此处设置将对该节点无效')}
          </Text>
          {/* <SelectMultiple*/}
          {/*  className={'thresholds-setting-select'}*/}
          {/*  options={productOptions}*/}
          {/*  appearance="button"*/}
          {/*  size="full"*/}
          {/*  listWidth={510}*/}
          {/*  searchable*/}
          {/*  clearable*/}
          {/*  onChange={*/}
          {/*    (value)=>{*/}
          {/*      if (value.length > 0) {*/}
          {/*        const list = [];*/}
          {/*        archProductThresholdInfos.forEach((item)=>{*/}
          {/*          if (!value.includes(item.Product)) {*/}
          {/*            item.isNotShow = true;*/}
          {/*          } else {*/}
          {/*            list.push(item.Product);*/}
          {/*            item.isNotShow = false;*/}
          {/*          }*/}
          {/*        });*/}
          {/*        setActiveIds([list[0], list[1]]);*/}
          {/*      } else {*/}
          {/*        setActiveIds([archProductThresholdInfos?.[0]?.Product]);*/}
          {/*        archProductThresholdInfos.forEach((item)=>{*/}
          {/*          item.isNotShow = false;*/}
          {/*        });*/}
          {/*      }*/}
          {/*      setArchProductThresholdInfos(cloneDeep(archProductThresholdInfos));*/}
          {/*    }*/}
          {/*  }*/}
          {/* />*/}
          <div className={'thresholds-setting-collapse-wrap'}>
            {archProductThresholdInfos?.map((item, i) => (
              <Collapse
                key={`coll-${i}`}
                className={`thresholds-setting-collapse ${
                  item.isNotShow ? 'thresholds-setting-collapse-none' : ''
                }`}
                activeIds={activeIds}
                onActive={(ids) => {
                  setActiveIds(ids);
                }}
              >
                <Collapse.Panel
                  id={item.Product}
                  title={
                    <div className="t-wrap">
                      <div className="t-text">{item.ProductName}</div>
                      {item.resetLoading ? (
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <StatusTip status={'loading'} />
                        </div>
                      ) : (
                        <Button
                          type={'link'}
                          className={'reset-btn'}
                          onClick={(e) => {
                            getSingleDescribeArchProductThresholdInfo(item);
                            e.stopPropagation();
                          }}
                        >
                          {t('恢复为平台推荐值')}
                        </Button>
                      )}
                    </div>
                  }
                >
                  <ThresholdSetting
                    singleProductThresholdInfo={item}
                    changeSingleProductThresholdInfo={(info) => {
                      item = cloneDeep(info);
                      setArchProductThresholdInfos(cloneDeep(archProductThresholdInfos));
                    }}
                  />
                </Collapse.Panel>
              </Collapse>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
export default ThresholdsSetting;
