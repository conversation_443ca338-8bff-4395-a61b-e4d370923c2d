import React from 'react';
import ReactDOM from 'react-dom';
import { reportEvent, EVENT } from '@src/utils/report';
import ArchiveReport from '@src/components/archive-report';
import SettingModal from '@src/components/global-setting-modal-v2';
/**
 * 渲染容量报告弹框
 * @param {object} arInfo - 业务架构信息
 * @param {object} [props] - 可选参数
 * @param {function} [props.onViewClose] - 關閉弹框回调函数
 */
const renderReport = (arInfo: any, props?: {
  onViewClose?: (unArchived?: boolean) => void;
  exportReport?: (success: () => void, error: () => void) => void;
}) => {
  let container = document.getElementById('archiveReport');
  if (!container) {
    container = document.createElement('div');
    container.style.zIndex = '10000';
    container.style.position = 'fixed';
    container.setAttribute('id', 'archiveReport');
    document.body.appendChild(container);
  }
  // eslint-disable-next-line react/no-deprecated
  ReactDOM.render(
    React.createElement(ArchiveReport, {
      onClose: (unArchived?: boolean) => {
        // eslint-disable-next-line react/no-deprecated
        ReactDOM.unmountComponentAtNode(container);
        document.body.removeChild(container);
        props?.onViewClose?.(unArchived);
        reportEvent({
          key: EVENT.DISCARD_REPORT,
          extraInfo: null,
        });
      },
      visible: true,
      arInfo,
      exportReport: props?.exportReport,
    }),
    container
  );
};

const renderSettingModal = (arInfo: any) => {
  let container = document.getElementById('settingModalContainer');
  if (!container) {
    container = document.createElement('div');
    container.setAttribute('id', 'settingModalContainer');
    document.body.appendChild(container);
  }
  // eslint-disable-next-line react/no-deprecated
  ReactDOM.render(
    React.createElement(SettingModal, {
      onClose: () => {
        arInfo.initOver();
        // eslint-disable-next-line react/no-deprecated
        ReactDOM.unmountComponentAtNode(container);
        document.body.removeChild(container);
      },
      visible: true,
      arInfo,
    }),
    container
  );
};

/**
 * 分割字符串中第一个连字符('-')，返回分割后的两部分
 * @param str 待分割的字符串，默认为空字符串
 * @returns 包含两个元素的元组，第一个是连字符前的部分，第二个是连字符后的部分（若无连字符则返回空字符串）
 */
function splitFirstHyphen(str: string = ''): [string, string] {
  const index = str?.indexOf?.('-');
  if (index === -1) return [str, ''];
  return [str.slice(0, index), str.slice(index + 1)];
}

export {
  renderReport,
  renderSettingModal,
  splitFirstHyphen,
};
