.thresholds-handle-list {
  width: 110px;
  & > li {
    &{
      display: flex!important;
      align-items: center;
      color: #C1C6C8!important;
    }
    &:hover {
      color: #fff;
      background-color: #006EFF!important;
    }
  }
  @keyframes Routes {
    0% {
      transform: rotate(0);
    }
    100% {
      transform: rotate(1turn);
    }
  }
  svg {
    margin-right: 5px;
    path {
      fill: #C1C6C8;
    }
  }
  .loading-icon {
    animation: Routes 1s linear infinite;
  }
  .thresholds-loading-icon {
    animation: Routes 1s linear infinite;
  }
}
.thresholds-sdk-sub-only-icon-wrap {
  display: flex;
  align-items: center;
  svg {
    flex: none;
  }
}
.thresholds-sdk-sub-icon-wrap {
  display: flex;
  align-items: center;
  font-size: 16px;
  float: left;
  margin-left: -50%;
  &.thresholds-sdk-sub-icon-wrap-bg {
    background-color: rgba(244, 223, 225, 0.80);
    padding: 2px 4px;
    border-radius: 40px;
  }
  svg {
    flex: none;
  }
  .num {
    padding: 0px 3px;
    box-sizing: border-box;
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 14px;
    color: #fff;
    font-size: 16px;
    margin-right: 3px;
  }
  .high-num {
    background-color: #E54545;
  }
  .mid-num {
    background-color: #FF7200;
    margin-right: 0;
  }
  img {
    width: 28px;
    height: 28px;
  }
}