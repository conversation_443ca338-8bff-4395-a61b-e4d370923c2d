import React, { useEffect, useMemo, useRef, useCallback, useState } from 'react';
import ReactDOM from 'react-dom';
import { useHookstate } from '@hookstate/core';
import {
  describeArchThresholdConfigStatus,
  describeNodeLoadInfo,
  updateNodeResourceCapacityMetric as updateMetric,
} from '@src/api/handleArea';
import HoverCard from '@src/components/capacity-agent-chat/components/hover-card';
import {
  describeCapacityReportTask,
  createCapacityReportTask,
  exportCapacityReportTask,
  resetCapacityReportTask,
} from '@src/api/export';
import report from '@src/statics/svg/report.svg';
import predication from '@src/statics/svg/predication.svg';
import defaultIndicator from '@src/statics/svg/defaultIndicator.svg';
import loading from '@src/statics/svg/loading.svg';
import { AILogoWithStar } from '@tencent/cloud-chat-ui';
import { renderReport, renderSettingModal, splitFirstHyphen } from './tools';
import Prediction from '@src/components/prediction-v2';
import { app } from '@tea/app';
import CapacityAgentChat from '@src/components/capacity-agent-chat';
import globalState from '@src/stores/global.state';
import { t } from '@tea/app/i18n';
import { ReportTaskStatus, ReportType } from '@src/components/archive-report/constants/index';
import { ArchiveTypeEnum, EnvEnum, UrlParams, LocalStorageKey } from '@src/constants';
import { reportEvent, EVENT } from '@src/utils/report';
import LoadingTimer, { Status } from './components/loading-timer';
import {
  Bubble,
  notification,
  Button,
} from '@tencent/tea-component';
import CapacityMonitoring from '@src/pages/capacity-monitoring';
import {
  isOpenSetting,
  nodeDataInfo,
  nodeOriginInfo,
  setIsOpenSetting,
  setNodeConfigInfo,
  setNodeDataInfo,
  setNodeOriginInfo,
  nodeLoadRiskInfos,
  setNodeLoadRiskInfos,
} from '@src/utils/caching';
import './index.less';
import s from './index.module.scss';
import { RiskCircle } from '@src/components/risk-circle';
import { ArchiveStatus } from '@src/components/archive-report/constants';
import TimeLineBar from '@src/components/capacity-agent-chat/components/time-line-bar/app';

export interface Props {
  arInfo: AppPluginAPI.PluginAPI;
  isRefreshArea?: boolean;
  isRefreshUpdate?: boolean;
  transformNodeByConfigInfo?: () => void;
}

let timer;
let timerUpdate;
let timerUpdateShort;
let initContainKey: any = null;
let isUpdate = false;
let isRefresh = false;
// eslint-disable-next-line @typescript-eslint/no-unused-vars
let isRefreshSetting = false;
let isForceRefresh = false;
let isLeave = true;
let isInit = true;
let isLoading = true;

const HandleArea = ({ arInfo, isRefreshArea, isRefreshUpdate, transformNodeByConfigInfo }: Props) => {
  // let products;
  const currentMapId = arInfo.archInfo.archId;
  const exportParams = useRef({
    MapId: currentMapId,
    TaskId: '',
  });
  const instance = useRef(null);
  const { userName } = arInfo;
  const global = useHookstate(globalState);
  const exportState = useHookstate(globalState.exportState).get();
  const { closed, status, mapId, downloadData } = exportState;
  const data = useHookstate(globalState.exportState.data).get();
  const [nodeLoadRiskInfosState, setNodeLoadRiskInfosState] = useState([]);

  const resetBubble = () => {
    operations.current[reportMenuIndex].disabled = false;
    delete operations.current[reportMenuIndex].hoverTextExtraProps;
    arInfo.setOperations(operations.current);
    clearInterval(exportState.timer);
    globalState.exportState.set(state => ({
      ...state,
      mapId: currentMapId,
      closed: true,
      data: {},
      status: Status.UNINITIALIZED,
      timer: null,
      seconds: 0,
    }));
  };

  const exportReport = async (success?: () => void, error?: () => void) => {
    let res;
    reportEvent({
      key: EVENT.ARCHIVE_REPORT,
      extraInfo: null,
    });
    globalState.exportState.set(state => ({
      ...state,
      data: {
        ...state.data,
        ArchiveStatus: (state.data as any)?.ArchiveStatus,
      },
    }));

    if (!exportParams.current?.TaskId || (window as any).CURRENT_MAP_ID !== currentMapId) {
      return;
    }

    try {
      res = await exportCapacityReportTask({
        MapId: currentMapId,
        TaskId: exportParams.current?.TaskId,
        Username: userName,
        ReportSource: t('容量插件'),
        ReportName: (data as any)?.Title,
        SdkName: 'capacity-monitoring-sdk',
        ReportVersion: 'v1', // todo: ReportVersion 临时写死
        ArchiveType: arInfo.env === EnvEnum.ISA ? ArchiveTypeEnum.UNARCHIVE : ArchiveTypeEnum.ARCHIVE,
      });
      globalState.exportState.set(state => ({
        ...state,
        data: {
          ...state.data,
          ArchiveStatus: res?.ArchiveStatus ?? ArchiveStatus.待归档,
          DigitalAssetUrl: res?.DigitalAssetUrl,
          ReportFile: res?.ReportFile,
          ExcelFile: res?.ExcelFile,
        },
      }));
      switch (res?.ArchiveStatus) {
        case ArchiveStatus.归档中: {
          setTimeout(() => {
            exportReport(success, error);
          }, 3000);
          break;
        }
        case ArchiveStatus.归档完成: {
          success?.();
          break;
        }
        case ArchiveStatus.已导出: {
          success?.();
          break;
        }
        case ArchiveStatus.归档失败: {
          error?.();
          break;
        }
      }
    } catch (error) {
      console.log(error);
    }

    if (!res || res?.Error || Object.keys(res).length === 0) {
      globalState.exportState.set(state => ({
        ...state,
        data: {
          ...state.data,
          ArchiveStatus: ArchiveStatus.归档失败,
          DigitalAssetUrl: '',
        },
      }));
      error?.();
      app.tips.error(t('归档失败'));
    }
  };

  /**
   * 展示 导出 - bubble
   * @function setExportBubble
   * @param {Void} - no params
   * @return {Void} - no return
   * @example
   * setExportBubble()
   */
  const setExportBubble = () => {
    if (operations.current[reportMenuIndex].disabled) {
      return;
    }
    operations.current[reportMenuIndex].hoverTextExtraProps = {
      visible: true,
      overlayStyle: {
        zIndex: 9,
      },
      overlayClassName: s.overlay,
      key: `${+new Date()}`,
      content: <LoadingTimer onStop={() => {
        operations.current[reportMenuIndex].disabled = false;
        delete operations.current[reportMenuIndex].hoverTextExtraProps;
        arInfo.setOperations(operations.current);
      }} onClose={() => {
        operations.current[reportMenuIndex].disabled = false;
        delete operations.current[reportMenuIndex].hoverTextExtraProps;
        arInfo.setOperations(operations.current);
        if (exportParams.current?.TaskId) {
          resetCapacityReportTask({
            MapId: currentMapId,
            TaskId: exportParams.current?.TaskId,
          });
        }
      }} onView={() => {
        delete operations.current[reportMenuIndex].hoverTextExtraProps;
        arInfo.setOperations(operations.current);
        renderReport(arInfo, {
          onViewClose: (unArchived?: boolean) => {
            // 如果未归档或者归档了，则重置
            if (unArchived && exportParams.current?.TaskId) {
              resetCapacityReportTask({
                MapId: currentMapId,
                TaskId: exportParams.current?.TaskId,
              });
            }
            resetBubble();
          },
          exportReport,
        });
      }} />,
    };
    operations.current[reportMenuIndex].disabled = true;
    arInfo.setOperations(operations.current);
  };

  const exportReportError = useCallback(
    () => {
      clearInterval(exportState.timer);
      globalState.exportState.set(state => ({
        ...state,
        status: Status.ERROR,
        timer: null,
        seconds: 0,
        data: {
          ...state.data,
          ArchiveStatus: ArchiveStatus.归档失败,
          DigitalAssetUrl: '',
        },
      }));
    },
    [exportState.timer],
  );
  /**
   * @function getReport
   * @description 1. 查询报告 2. 如果没有生成报告任务就创建报告
   * @param {Void} - no params
   * @return {Void} - no return
   * @example
   * getReport()
   */
  const getReport = useCallback(async () => {
    let res;
    instance.current?.destroy?.();
    globalState.exportState.set(state => ({
      ...state,
      downloadData: {
        type: '',
        downloadHash: '',
      },
    }));
    // 非同一架构图 或者已经关闭导出bubble
    if (!currentMapId || (window as any).CURRENT_MAP_ID !== currentMapId || exportState.closed) {
      return;
    }
    try {
      res = await describeCapacityReportTask({
        MapId: currentMapId,
        Username: userName,
      });
    } catch (err) {
      console.log(err);
    }
    if (!res || res?.Error || Object.keys(res).length === 0) {
      exportReportError();
      return;
    }
    if (!res.TaskId) {
      let res;
      exportParams.current.TaskId = '';
      try {
        res = await createCapacityReportTask({
          MapId: currentMapId,
        });
      } catch (error) {
        console.log(error);
      }
      if (!res || res?.Error || Object.keys(res).length === 0) {
        exportReportError();
        return;
      }
      if (res?.TaskId) {
        exportParams.current.TaskId = res.TaskId;
        getReport();
      } else {
        exportReportError();
      }
    } else {
      exportParams.current.TaskId = res.TaskId;
      switch (res?.Status) {
        case ReportTaskStatus.进行中: {
          globalState.exportState.set(state => ({
            ...state,
            status: Status.LOADING,
          }));
          setTimeout(() => {
            getReport();
          }, 2000);
          break;
        }
        case ReportTaskStatus.已完成: {
          globalState.exportState.set(state => ({
            ...state,
            status: Status.SUCCESS,
            data: res,
          }));
          break;
        }
        case ReportTaskStatus.失败: {
          exportReportError();
          break;
        }
        default: {
          break;
        }
      }
    }
  }, [exportState.closed]);

  /**
   * @function generateReport
   * @description 1.展示bubble 2.生成报告
   * @param {Void} - no params
   * @return {Void} - no return
   * @example
   * generateReport()
   */
  const generateReport = async () => {
    globalState.exportState.set(state => ({
      ...state,
      status: Status.UNINITIALIZED,
      mapId: currentMapId,
      closed: false,
    }));
    setExportBubble();
    getReport();
  };

  const operations = useRef<AppPluginAPI.Operations[]>([
    {
      key: 'broadcast-agent',
      iconNode: <AILogoWithStar />,
      desc: t('容量回放'),
      hoverText: t('容量回放'),
      disabled: false,
      onClick: () => {
        operations.current[agentMenuIndex].disabled = true;
        arInfo.closeDrawer();
        if (globalState.get().agentDrawerVisible) {
          return;
        }
        globalState.set(state => ({
          ...state,
          agentDrawerVisible: true,
          agentDatePickerVisible: true,
          agentRenderHoverCard: false,
          agentShowAnalysisResult: false,
          agentShowResourceCapacity: false,
          agentShowNotificationBanner: false,
          agentTimeLineBarVisible: false,
          agentTimeLineBarPlaying: false,
          agentCurrentTime: 0,
          expandedHoverCardId: '',
        }));
        arInfo.clearBar();
        renderSubscript(nodeLoadRiskInfos);
      },
    },
    {
      iconNode: (
        <img src={report} alt="report" />
      ),
      desc: t('生成报告'),
      key: 'generate-report',
      onClick: () => {
        reportEvent({
          key: EVENT.GENERATE_REPORT,
          extraInfo: null,
        });
        generateReport();
      },
      disabled: false,
    },
    {
      iconNode: (
        <img src={predication} alt="predication" />
      ),
      desc: t('容量预测'),
      onClick: () => {
        reportEvent({
          key: EVENT.PREDICT_CAPACITY,
          extraInfo: null,
        });
        setIsOpenSetting(false);
        const container = document.getElementById('settingModalContainer');
        if (container) {
          // eslint-disable-next-line react/no-deprecated
          ReactDOM.unmountComponentAtNode(container);
          document.body.removeChild(container);
        }
        arInfo.clearBar();
        arInfo.openDrawer();
        globalState.set(state => ({
          ...state,
          predictionOpened: true,
        }));
        arInfo.setDrawerProps({
          style: { width: '500px' },
          extra: {
            outerClickClosable: false,
            onClose: () => {
              arInfo.closeDrawer();
              if (globalState.get().predictionOpened) {
                globalState.set(state => ({
                  ...state,
                  predictionOpened: false,
                }));
              }
              renderSubscript(nodeLoadRiskInfos);
            },
          },
          className: 'capacityPredictionDrawer',
          children: <Prediction key={`${+new Date()}`} arInfo={arInfo} nodeInfo={nodeOriginInfo} />,
          title: t('容量预测'),
          footer: null,
        });
      },
      disabled: nodeDataInfo?.ArchNodeList?.length === 0,
    },
    {
      iconNode: (
        <img src={defaultIndicator} alt="defaultIndicator" />
      ),
      desc: t('默认指标'),
      onClick: () => {
        reportEvent({
          key: EVENT.DEFAULT_CONFIG,
          extraInfo: null,
        });
        arInfo.closeDrawer();
        if (globalState.get().predictionOpened) {
          globalState.set(state => ({
            ...state,
            predictionOpened: false,
          }));
          renderSubscript(nodeLoadRiskInfos || []);
        }
        renderSettingModal(arInfo);
        setIsOpenSetting(true);
      },
      disabled: nodeDataInfo?.ArchNodeList?.length === 0,
    },
  ]);
  const reportMenuIndex = useMemo(() => operations.current.findIndex(item => item.key === 'generate-report'), [operations.current]);
  const agentMenuIndex = useMemo(() => operations.current.findIndex(item => item.key === 'broadcast-agent'), [operations.current]);
  // const [isFirstSetting, setIsFirstSetting] = useState(false);
  const getLayHeight = (data) => {
    let initW = 40;
    const hLen = data.HighLoadCount?.toString?.()?.length ?? 0;
    const mLen = data.MediumLoadCount?.toString?.()?.length ?? 0;
    const lLen = data.LowLoadCount?.toString?.()?.length ?? 0;
    initW = 36 + 10 + 8 + (hLen + mLen + lLen) * 10 + 8;
    return initW;
  };
  if (arInfo?.extraParams === 'defaultSetting') {
    renderSettingModal(arInfo);
  }
  const delHoverCard = (nodeLoadRiskInfos) => {
    ((nodeLoadRiskInfos ?? []) as any)?.forEach((item) => {
      arInfo.removeBar([item.NodeId]);
    });
  };

  const renderHoverCard = (nodeLoadRiskInfos) => {
    ((nodeLoadRiskInfos ?? []) as any)?.forEach((item) => {
      arInfo.removeBar([item.NodeId]);
      if (!item.LastSuccessTaskId) {
        arInfo.createBar([item.NodeId], {
          width: 40,
          children: (
                <HoverCard
                  nodeId={item.NodeId}
                  arInfo={arInfo}
                />
          ),
        });
      } else {
        // const unsupported = item.SupportLevel === 2;
        if (!item.TaskId || (!item.HighLoadCount && !item.MediumLoadCount && !item.LowLoadCount && !item.LowUsedCount && !item.UnSupportCount)) {
          return;
        }
        arInfo.createBar([item.NodeId], {
          width: getLayHeight(item),
          children: (
                <HoverCard
                  nodeId={item.NodeId}
                  arInfo={arInfo}
                />
          ),
        });
      }
    });
  };
  /**
   * renderSubscript
   * @description  渲染bar
   * @param {Array} list
   * @param {boolean} [remove] 是否删除bar
   */
  const renderSubscript = (list) => {
    if (globalState.get().predictionOpened || globalState.get().agentDrawerVisible) {
      return false;
    }
    list?.forEach((item) => {
      if (!item.LastSuccessTaskId) {
        arInfo.createBar([item.NodeId], {
          width: 40,
          children: (
            <Bubble content={t('加载中')}>
              <div
                className={
                  'thresholds-handle-list thresholds-sdk-sub-icon-wrap'
                }
                style={{
                  width: 'auto',
                }}
              >
                <img src={loading} alt="loading" className={'thresholds-loading-icon'} />
              </div>
            </Bubble>
          ),
        });
      } else {
        // const unsupported = item.SupportLevel === 2;
        if (!item.TaskId || (!item.HighLoadCount && !item.MediumLoadCount && !item.LowLoadCount && !item.LowUsedCount && !item.UnSupportCount)) {
          return;
        }
        arInfo.createBar([item.NodeId], {
          width: getLayHeight(item),
          children: (
            <div className={'thresholds-sdk-sub-icon-wrap'}>
              <RiskCircle
                highLoadCount={item?.HighLoadCount ?? 0}
                mediumLoadCount={item?.MediumLoadCount ?? 0}
                lowLoadCount={item?.LowLoadCount ?? 0}
                lowUsedCount={item?.LowUsedCount ?? 0}
                unSupportCount={item?.UnSupportCount ?? 0}
              />
            </div>
          ),
        });
      }
    });
  };

  // 更新节点容量指标 step2
  const updateNodeResourceCapacityMetric = async (isForce?: boolean) => {
    const params: any = {
      MapId: currentMapId,
      SessionId: arInfo?.getPluginSessionId?.() ?? '',
    };
    if (isForce === true && nodeOriginInfo?.node?.key) {
      params.NodeUuidList = [nodeOriginInfo?.node?.key];
    }
    const showTips = isForce !== undefined;
    try {
      const res = await updateMetric(params, showTips);
      if (!res?.Error) {
        if (isLeave) {
          clearInterval(timerUpdateShort);
          timerUpdateShort = null;
          clearInterval(timerUpdate);
          timerUpdate = null;
          return;
        }
        // 如果没有异步id
        if (!res.CapacityAsyncId) {
          if (!timerUpdateShort) {
            timerUpdateShort = setInterval(() => {
              updateNodeResourceCapacityMetric();
            }, 1 * 1000);
          }
        } else {
          // 如果有异步id 查询一次更新后的 支持的容量产品列表
          if (isInit) {
            // getDescribeArchNodeConfigInfo(currentMapId);
            isInit = false;
          }
          clearInterval(timerUpdateShort);
          timerUpdateShort = null;
          isRefreshSetting = true;
          if (isOpenSetting) {
            setIsOpenSetting(false);
          }
          if (!timerUpdate) {
            timerUpdate = setInterval(() => {
              isUpdate = true;
              updateNodeResourceCapacityMetric(false);
            }, 5 * 60 * 1000);
          }
          // 查询节点风险数
          getDescribeNodeLoadInfo();
        }
      }
    } catch (err) {
      if (isLoading) {
        arInfo.initOver();
        isLoading = false;
      }
    }
  };

  // 处理 &plugin=capacity-monitoring-sdk&pluginParam=reportView-report-3d150823-bf87-407c-932a-d6239f9302c1 类url
  const autoOpenFromUrl = async () => {
    const pluginParam = arInfo?.extraParams ?? '';
    if (pluginParam) {
      const [prefix, suffix] = splitFirstHyphen(pluginParam);
      if (prefix === UrlParams.REPORT_VIEW) {
        const taskId = suffix;
        if (taskId) {
          exportParams.current.TaskId = taskId;
          const res = await describeCapacityReportTask({
            MapId: currentMapId,
            Username: userName,
            ReportId: taskId,
          });
          globalState.exportState.set(state => ({
            ...state,
            status: Status.SUCCESS,
            data: res,
          }));
          renderReport(arInfo, {
            onViewClose: (unArchived?: boolean) => {
              // 如果未归档或者归档了，则重置
              if (unArchived) {
                resetCapacityReportTask({
                  MapId: currentMapId,
                  TaskId: taskId,
                });
              }
              resetBubble();
            },
            exportReport,
          });
        }
      }
    }
  };

  // 来自卓越架构 打开抽屉
  const openDrawerFromAnother = () => {
    const nodeId = arInfo?.extraParams ?? '';
    const specials = [UrlParams.REPORT_VIEW] as string[];
    const [prefix] = splitFirstHyphen(nodeId);
    const flag = !specials.includes(prefix);
    if (nodeId && flag) {
      const nodes = arInfo?.getArchNodes?.();
      const node = nodes?.[nodeId];
      if (node) {
        setNodeOriginInfo({
          node,
        });
        arInfo.openDrawer();
        arInfo.setDrawerProps({
          title: t('{{nodeName}}', { nodeName: node?.customize?.label || '' }),
          className: 'capacityMonitoringDrawer',
          extra: {
            outerClickClosable: false,
          },
          children: (
            <CapacityMonitoring
              arInfo={arInfo}
              nodeInfo={{ node }}
              key={node?.key}
            />
          ),
        });
      } else if (nodeId !== 'defaultSetting') {
        notification.error({
          description: t('图元不存在'),
        });
      }
    }
  };
  // 获取节点风险数 step4
  const getDescribeNodeLoadInfo = async () => {
    try {
      const res = await describeNodeLoadInfo({
        MapId: currentMapId,
        SessionId: arInfo.getPluginSessionId(),
      });
      arInfo.initOver();
      if (!res?.Error) {
        setNodeLoadRiskInfos(res.NodeLoadRiskInfos);
        setNodeLoadRiskInfosState(res.NodeLoadRiskInfos);
        // 渲染架构图节点风险数
        renderSubscript(res.NodeLoadRiskInfos);
        // 第三方打开抽屉
        openDrawerFromAnother();
        if (isLoading) {
          arInfo.showTools();
          isLoading = false;
        }
        let currentNodeKeyState: any;
        let currentNodeKey;
        let contain: any = null;
        if (Object.keys(nodeOriginInfo).length > 0) {
          contain = false;
        }
        res.NodeLoadRiskInfos?.forEach((item) => {
          if (item?.NodeId === nodeOriginInfo?.node?.key) {
            currentNodeKeyState = item.IsFinish;
            currentNodeKey = item.NodeId;
            contain = true;
          }
        });
        if (contain === false) {
          initContainKey = false;
        }
        if (
          (isUpdate || initContainKey === false || isForceRefresh === true)
          && currentNodeKey
          && currentNodeKeyState === true
        ) {
          isRefresh = !isRefresh;
          isUpdate = false;
          initContainKey = null;
          currentNodeKeyState = null;
          isForceRefresh = false;
        }
        // 如果已完成查询或则离开页面，则停止定时器
        if (res.IsFinish || isLeave) {
          globalState.set(state => ({ ...state, isRefreshing: false }));
          clearInterval(timer);
          timer = null;
        } else {
          // 如果还没完成风险数查询，则每隔5秒查询一次
          if (!timer) {
            timer = setInterval(() => {
              getDescribeNodeLoadInfo();
            }, 5000);
          }
        }
      }
    } catch (err) {
      if (isLoading) {
        arInfo.initOver();
        isLoading = false;
      }
      console.log(err);
    }
  };

  // 获取架构图配置状态 step1
  const getDescribeArchThresholdConfigStatus = async (
    currentMapId,
    userName
  ) => {
    isLeave = false;
    try {
      const res = await describeArchThresholdConfigStatus({
        MapId: currentMapId,
        UserName: userName,
      });
      if (!res?.Error) {
        // 设置支持产品
        setNodeConfigInfo(res);
        // 根据配置信息转换节点透明度
        transformNodeByConfigInfo();

        if (res.Status === 0) {
          // setIsFirstSetting(true);
        }
        if (!isLeave) {
          updateNodeResourceCapacityMetric();
        }
      }
    } catch (err) {
      if (isLoading) {
        arInfo.initOver();
        isLoading = false;
      }
      console.log(err);
    }
  };

  const onCloseCallback = useCallback(() => {
    operations.current[agentMenuIndex].disabled = false;
    arInfo.setOperations(operations.current);
  }, [operations]);

  useEffect(() => {
    if (nodeDataInfo?.ArchNodeList?.length !== 0) {
      getDescribeArchThresholdConfigStatus(currentMapId, userName);
    }
    return () => {
      clearInterval(timer);
      timer = null;
      isLeave = true;
      clearInterval(timerUpdateShort);
      timerUpdateShort = null;
      clearInterval(timerUpdate);
      timerUpdate = null;
      isRefreshSetting = false;
      setIsOpenSetting(false);
      isUpdate = false;
      initContainKey = null;
      isForceRefresh = false;
      setNodeOriginInfo({});
      setNodeDataInfo({});
      isInit = true;
      isLoading = true;
    };
  }, []);

  useMemo(() => {
    if (isRefreshArea !== undefined) {
      getDescribeNodeLoadInfo();
    }
  }, [isRefreshArea]);

  useEffect(() => {
    if (global.get().getDescribeNodeLoadInfoKey) {
      getDescribeNodeLoadInfo();
    }
  }, [global.get().getDescribeNodeLoadInfoKey]);

  // 监听agentRenderHoverCard状态变化
  useEffect(() => {
    const shouldRenderHoverCard = global.agentRenderHoverCard.get();
    if (shouldRenderHoverCard) {
      renderHoverCard(nodeLoadRiskInfosState);
    } else {
      delHoverCard(nodeLoadRiskInfosState);
      renderSubscript(nodeLoadRiskInfos);
    }
  }, [global.agentRenderHoverCard.get(), nodeLoadRiskInfosState]);

  useMemo(() => {
    if (isRefreshUpdate !== undefined) {
      isForceRefresh = true;
      updateNodeResourceCapacityMetric(true);
    }
  }, [isRefreshUpdate]);

  useEffect(() => {
    if (status !== Status.UNINITIALIZED && !closed) {
      setExportBubble();
      return;
    }
    arInfo.setOperations(operations.current);
  }, [closed, status]);

  useEffect(() => {
    if (!((window as any).CURRENT_MAP_ID)) {
      (window as any).CURRENT_MAP_ID = '';
    }
    (window as any).CURRENT_MAP_ID = currentMapId;
    // 非同一架构图 - 重置bubble
    if (mapId && (mapId !== currentMapId)) {
      localStorage.setItem('mapId', currentMapId);
      operations.current[reportMenuIndex].disabled = false;
      resetBubble();
    } else {
      globalState.exportState.set(state => ({
        ...state,
        mapId: currentMapId,
      }));
    }
  }, [currentMapId]);

  useEffect(() => {
    const { type } = downloadData;
    const reportFile = (data as any)?.ReportFile;
    const excelFile = (data as any)?.ExcelFile;
    if (reportFile && excelFile && type) {
      instance.current?.destroy?.();
      setTimeout(() => {
        instance.current = notification.success({
          title: `${type === ReportType.PDF ? 'PDF' : 'Excel'}${t('导出已完成')}`,
          duration: 0,
          description: '',
          className: s.notification,
          extra: '',
          onClose: () => {
            instance.current.destroy();
          },
          popupContainer: document.body,
          footer: <Button type="text" onClick={() => {
            window.open(type === ReportType.PDF ? reportFile : excelFile, '_blank');
            instance.current.destroy();
            globalState.exportState.set(state => ({
              ...state,
              downloadData: {
                type: '',
                downloadHash: '',
              },
            }));
          }}>{t('下载')}</Button>,
        });
      }, 500);
    }
  }, [downloadData.type, downloadData.downloadHash, (data as any)?.ReportFile, (data as any)?.ExcelFile]);

  useEffect(() => {
    let ins;
    const capacityTip = localStorage.getItem(LocalStorageKey.CAPACITY_SUBSCRIBE_TIP);
    // 租户端提示
    if (!capacityTip && arInfo?.env !== EnvEnum.ISA) {
      ins = notification.success({
        title: '',
        description:
          t('容量报告现已支持订阅，助您及时关注资源容量'),
        footer: <div><Button onClick={() => {
          ins?.destroy?.();
          localStorage.setItem(LocalStorageKey.CAPACITY_SUBSCRIBE_TIP, 'true');
        }} type="link" style={{ color: 'rgba(0,0,0,0.7)' }}>{t('不再提示')}</Button><Button onClick={() => {
          arInfo?.openSubscription?.();
          ins?.destroy?.();
        }} style={{ marginLeft: 15 }} type="link">{t('立即订阅')}</Button></div>,
        duration: 0,
      });
    }
    autoOpenFromUrl();
    return () => {
      arInfo?.closeSubscription?.();
      ins?.destroy?.();
    };
  }, []);
  useEffect(() => () => {
    instance.current?.destroy?.();
    globalState.set(state => ({
      ...state,
      agentDrawerVisible: false,
      agentDatePickerVisible: false,
      agentRenderHoverCard: false,
      agentShowAnalysisResult: false,
      agentShowResourceCapacity: false,
      agentShowNotificationBanner: false,
      agentTimeLineBarVisible: false,
      agentTimeLineBarPlaying: false,
      agentCurrentTime: 0,
      expandedHoverCardId: '',
    }));
  }, []);

  return (
    <>
      {
        <CapacityAgentChat visible={globalState.get().agentDrawerVisible} onCloseCallback={onCloseCallback}/>
      }
      {global.agentTimeLineBarVisible.get() && <TimeLineBar/>}
    </>
  );
};
export default HandleArea;
