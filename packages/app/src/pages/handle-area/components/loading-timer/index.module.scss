.container {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  border-radius: 8px;
  width: fit-content;
  font-family: 'PingFang SC';
  font-size: 12px;
  img {
    &:hover {
      cursor: pointer;
    }
  }
}

.loader {
  width: 20px;
  height: 20px;
  color: #3b82f6;
  animation: spin 1s linear infinite;
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.text {
  color: #4b5563;
}

.timer {
  color: #6b7280;
}
