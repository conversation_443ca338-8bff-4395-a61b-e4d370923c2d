import React, { useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@tencent/tea-component';
import { useHookstate } from '@hookstate/core';
import globalState from '@src/stores/global.state';
import { reportEvent, EVENT } from '@src/utils/report';
import stop from '@src/statics/svg/report-stop.svg';
import success from '@src/statics/svg/report-success.svg';
import close from '@src/statics/svg/report-close.svg';
import fail from '@src/statics/svg/report-fail.svg';
import { t } from '@tea/app/i18n';
import styles from './index.module.scss';

interface IProps {
  onStop?: () => void;
  onClose?: () => void;
  onView?: () => void;
}

export enum Status {
  UNINITIALIZED = '',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

const LoadingTimer: React.FC<IProps> = ({ onStop, onClose, onView }) => {
  const state = useHookstate(globalState.exportState).get();
  const { status, timer, seconds } = state;

  const formatTime = (totalSeconds: number) => {
    const minutes = Math.floor(totalSeconds / 60);
    const remainingSeconds = totalSeconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  };

  useEffect(() => {
    // 未初始化时，设置为loading
    if (!state.status) {
      globalState.exportState.set(state => ({
        ...state,
        status: Status.LOADING,
      }));
    }
    if (state.status === Status.LOADING) {
      if (!timer) {
        const newTimer = setInterval(() => {
          globalState.exportState.set(state => ({
            ...state,
            seconds: state.seconds + 1,
          }));
        }, 1000);
        globalState.exportState.set(state => ({
          ...state,
          timer: newTimer,
        }));
      }
    } else {
      clearInterval(timer);
      globalState.exportState.set(state => ({
        ...state,
        timer: null,
        seconds: 0,
      }));
    }
  }, [state.status, seconds, timer]);

  return (
    <div className={styles.container}>
      {
        status === Status.LOADING && (
          <>
            <Loader2 className={styles.loader} />
            <span className={styles.text}>{t('生成中')}</span>
            <span className={styles.timer}>{formatTime(seconds)}</span>
            <img src={stop} alt="stop" onClick={() => {
              onStop?.();
              clearInterval(timer);
              globalState.exportState.set(state => ({
                ...state,
                status: Status.UNINITIALIZED,
                timer: null,
                seconds: 0,
                closed: true,
              }));
              clearInterval(timer);
            }} />
          </>
        )
      }
      {
        status === Status.SUCCESS && (
          <>
            <img src={success} alt="success" />
            <span className={styles.text}>{t('生成成功')}</span>
            <Button type="link" onClick={() => {
              reportEvent({
                key: EVENT.CHECK_REPORT,
                extraInfo: null,
              });
              onView?.();
            }}>
              {t('查看')}
            </Button>
            <img src={close} alt="close" onClick={() => {
              clearInterval(timer);
              globalState.exportState.set(state => ({
                ...state,
                timer: null,
                closed: true,
                seconds: 0,
              }));
              onClose?.();
            }} />
          </>
        )
      }
      {
        status === Status.ERROR && (
          <>
            <img src={fail} alt="fail" />
            <span className={styles.text}>{t('生成失败')}</span>
            <img src={close} alt="close" onClick={() => onClose?.()} />
          </>
        )
      }
    </div>
  );
};

export default LoadingTimer;
