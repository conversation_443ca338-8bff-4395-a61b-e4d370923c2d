.capacityMonitoringDrawer{
  .app-cloud-arch-drawer__body{
    padding-bottom: 0;
  }
  .app-cloud-arch-drawer__body-inner{
    height: 100%;
  }
  .app-cloud-arch-drawer__footer,.tea-drawer__footer{
    text-align: center;
  }
}

.capacityMonitoringWrap {
  height: 0;
  display: flex;
  flex-direction: column;

  .tea-icon{
    background-size: auto;
  }
  .subTitleWrap {
    display: flex;

    .subTitle {
      display: flex;
      margin-right: 32px;
      color: rgba(0, 0, 0, 0.9);
      align-items: center;
      .label {
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }

  .beeNestBox {
    margin-top:10px;
    .tea-h5 {
      margin-bottom: 8px;
    }
    .beeNestDimension {
      display: flex;
      align-items: center;
      margin-bottom: 16px;


      .dimensionLabel {
        color: rgba(0, 0, 0, 0.4);
        margin-right: 16px;
      }

      .dimensionSelect {
        width: 180px;
      }
    }

    .beeNestContent {
      width: 100%;
      //height: calc(100% - 11px);
      height: calc(100% - 48px);
    }

  }
  .line {
    margin: 25px 0 14px -20px;
    border-top: 1px solid #E7EAEF;
    width: calc(100% + 40px);
  }
  .infoWrap{
    flex: 1;
    // overflow: auto;
    .tea-h5 {
      margin-bottom: 8px;
    }
    .tea-accordion__header {
      .tea-icon-arrowup {
        transform-origin: center;
        transform: rotate(180deg);
      }
    }
    .indexTitle{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .dimensionSelect{
        width: 100px;
      }
    }
    .tea-accordion-group{
      height: 100%;
      display: flex;
      flex-direction: column;
      .indexPanel{
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .chartContent{
          padding-right: 6px;
        }
        .tea-accordion__body{
          flex: 1;
          overflow: auto;
        }
      }
    }
    .tea-accordion__header{
      font-weight: 600;
      .tea-accordion__header-title{
        width: calc(100% - 16px);
      }
    }
    .tea-accordion__body{
      margin: 10px 0 10px 16px;
    }
  }

  .LoadingWrap{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.baseInfoCol{
  padding-bottom: 0 !important;
  .tea-grid__box{
    display: flex;
    width: 100%;
    &>span{
      flex: 1;
      overflow: hidden;
    }
  }
  .baseInfoKey{
    flex-shrink: 0;
    width: 38%;
    color: rgba(0, 0, 0, 0.4);
    margin-right: 8px;
  }
  .tea-icon-copy{
    margin-left: 6px;
  }
}

.indexModal{
  & > div {
    border-radius: 8px !important;
    overflow: hidden !important;
  }
  .tea-icon{
    background-size: auto !important;
  }
}
.update-threshold-state-wrap {
  display: flex;
  align-items: center;
  font-size: 12px;
  .update-threshold-state {
    color: #888;
    font-weight: normal;
    margin-left: 15px;
  }
}