/* eslint-disable no-nested-ternary */
import React from 'react';
import { Row, Col, Text } from '@tencent/tea-component';
import { Skeleton } from 'tdesign-react';
import { DorisConfig } from '@src/components/archive-report/constants';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface BaseInfoProps {
  loading: boolean;
  data: any[];
  reginMap?: any;
}

/**
 * 基础信息展示组件
 * @param {object} props 组件属性
 * @param {boolean} props.loading 加载状态
 * @param {Array} props.data 基础信息数据
 * @returns {React.ReactElement} 基础信息展示组件
 */
const BaseInfo: React.FC<BaseInfoProps> = ({ loading, data, reginMap }) => {
  if (loading || data === null) {
    return (
      <div
        style={{
          overflow: 'hidden',
          paddingTop: 10,
        }}
      >
        <Skeleton theme={'text'} animation="flashed"></Skeleton>
        <Skeleton
          style={{ marginTop: 7 }}
          theme={'text'}
          animation="flashed"
        ></Skeleton>
      </div>
    );
  }

  if (!data?.length) {
    return <div className={s['no-data']}>{t('暂无数据')}</div>;
  }

  return (
    <Row style={{ padding: '10px 0', minHeight: 80 }}>
      {data?.map((i, index) => (
        <Col key={`col-${index}`} span={12} className="baseInfoCol">
        <div className="baseInfoKey">{t('{{key}}', { key: i.Key })}</div>
        {i.Value ? (
          <Text
            overflow
            onClick={() => {
              i.IsSkip && i.Url && window.open(i.Url);
            }}
            style={{ cursor: 'pointer' }}
            theme={i.IsSkip ? 'primary' : 'text'}
            copyable={i.IsCopy}
            tooltip={
              <>
                {i.Value.split('\n').length > 1
                  ? i.Value.split('\n').map((val, i) => (
                      <div key={`div-${i}`}>
                        {t('{{Value}}', { Value: val })}
                      </div>
                  ))
                  : t('{{Value}}', { Value: DorisConfig.BASE_AREA_NAME === i.Key ? reginMap.get(i.Value) ?? i.Value : i.Value })}
              </>
            }
          >
            {t('{{Value}}', { Value: DorisConfig.BASE_AREA_NAME === i.Key ? reginMap.get(i.Value) ?? i.Value : i.Value })}
          </Text>
        ) : (
          t('暂无')
        )}
      </Col>
      ))}
    </Row>
  );
};

export default BaseInfo;
