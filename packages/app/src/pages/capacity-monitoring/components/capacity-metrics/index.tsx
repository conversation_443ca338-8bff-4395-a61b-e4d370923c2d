import React from 'react';
import { Segment, StatusTip } from '@tencent/tea-component';
import { Skeleton } from 'tdesign-react';
import { t } from '@tea/app/i18n';
import Chart from '@src/components/charts';
import { SegViewType } from '@src/constants';
import success from '@src/statics/svg/success.svg';
import nodata from '@src/statics/svg/nodata.svg';

const { LoadingTip } = StatusTip;

interface CapacityMetricsProps {
  loading: boolean;
  data: any[];
  notSupport: boolean;
  segmentState: {
    options: Array<{
      text: string;
      value: string;
    }>;
    value: string;
  };
  onSegmentChange: (value: string) => void;
  selectBeeNest: any;
  multiRegionState: any;
  isMultiRegionTab: boolean;
  isCcn: boolean;
}

/**
 * 容量指标组件，用于展示容量监控相关的图表数据
 * @param {Object} props 组件属性
 * @param {boolean} props.loading 加载状态
 * @param {Array} props.data 图表数据
 * @param {boolean} props.notSupport 是否不支持当前实例类型
 * @param {Object} props.segmentState 分段控制状态
 * @param {Function} props.onSegmentChange 分段改变回调
 * @param {Object} props.selectBeeNest 选中的蜂巢节点信息
 * @returns {React.ReactElement} 渲染容量指标图表或相应的状态提示
 */
const CapacityMetrics: React.FC<CapacityMetricsProps> = ({
  loading,
  data,
  notSupport,
  segmentState,
  onSegmentChange,
  selectBeeNest,
  multiRegionState,
  isMultiRegionTab,
  isCcn,
}) => {
  const riskIndexData = data?.filter(v => v?.status >= 1) || [];
  if (loading || data === null) {
    return (
      <div className="LoadingWrap" style={{ minHeight: 60 }}>
        <div
          style={{
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            marginTop: 10,
          }}
        >
          <Skeleton theme={'tab'} animation="flashed"></Skeleton>
        </div>
      </div>
    );
  }

  if (!data?.length || notSupport) {
    return (
      <div
        className="LoadingWrap"
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: 400,
        }}
      >
        <img src={nodata} />
        <p style={{ marginTop: 15, fontSize: 12 }}>{t('暂无数据')}</p>
        {notSupport ? (
          <p
            style={{
              marginTop: 15,
              fontSize: 12,
              color: 'rgba(0, 0, 0, 0.4)',
            }}
          >
            { isCcn ? t('暂未绑定网络实例') : t('不支持此实例类型')}
          </p>
        ) : null}
      </div>
    );
  }

  return (
    <div className="chartContent">
      {!notSupport && (
        <Segment
          style={{ marginTop: 10 }}
          options={segmentState.options}
          value={segmentState.value}
          onChange={onSegmentChange}
        />
      )}
      {!notSupport
        && segmentState.value === SegViewType.RISK
        && !riskIndexData?.length
        && data?.every(item => item.finish)
        && data?.length && (
          <p style={{ display: 'flex', alignItems: 'center', marginTop: 10, color: 'rgba(0, 0, 0, 0.4)', fontSize: 12 }}>
            <img src={success} />
            &nbsp;&nbsp;{t('暂无数据')}
          </p>
      )}
      {data?.every(item => item.finish) ? (
        (segmentState.value === SegViewType.ALL
          ? data || []
          : riskIndexData || []
        ).map((i, index) => (
          <Chart
            key={`lc-${index}`}
            title={i.title}
            multiRegionState={multiRegionState}
            isMultiRegionTab={isMultiRegionTab}
            lineData={i.lineData}
            defaultExpandAll={index === 0}
            data={i.data}
            status={i?.status}
            statusLabel={i?.statusLabel}
            indicator={{
              min: i.min,
              max: i.max,
              avg: i.avg,
            }}
            algorithmValue={i.algorithmValue}
            params={{
              MapId: selectBeeNest.MapId,
              NodeUuid: selectBeeNest.NodeUuid,
              InsId: selectBeeNest.InsId,
              InsRegion: selectBeeNest.Region,
            }}
          />
        ))
      ) : (
        <div className="LoadingWrap" style={{ marginTop: 20 }}>
          <LoadingTip />
        </div>
      )}
    </div>
  );
};

export default CapacityMetrics;
