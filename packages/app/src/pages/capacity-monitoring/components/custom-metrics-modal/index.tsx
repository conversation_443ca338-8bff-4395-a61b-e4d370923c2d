/* eslint-disable no-nested-ternary */
import React from 'react';
import { Modal, Button } from '@tencent/tea-component';
import { Radio as TRadio, Skeleton } from 'tdesign-react';
import { t } from '@tea/app/i18n';
import { CcnConfig } from '@src/components/archive-report/constants';
import { orderBy } from 'lodash-es';
import SwitchCard from '@src/components/switch-card';
import reset from '@src/statics/svg/restore.svg';
import s from './index.module.scss';

interface CustomMetricsModalProps {
  visible: boolean;
  loading: boolean;
  changed: boolean;
  modifyMode: number;
  archProductThresholdInfos: any[];
  tabsBar: any[];
  activeTab: string;
  tab: any[];
  algorithmInfos: any[];
  periodTypeInfos: any[];
  metricPeriodTypeInfos: any[];
  product: string;
  onClose: () => void;
  onReset: () => void;
  onSave: () => void;
  onCancel: () => void;
  onTabChange: (value: string) => void;
  onSwitchCardChange: (data: any, index: number) => void;
  popupContainer: () => HTMLElement;
}

/**
 * 自定义指标设置弹窗组件
 * @param {boolean} visible - 控制弹窗显示/隐藏
 * @param {boolean} loading - 加载状态
 * @param {boolean} changed - 指标是否已修改
 * @param {Array} archProductThresholdInfos - 产品阈值信息
 * @param {Array} tabsBar - 实例类型选项列表
 * @param {string} activeTab - 当前激活的实例类型tab
 * @param {Array} tab - 指标设置列表
 * @param {Array} algorithmInfos - 算法信息列表
 * @param {Array} periodTypeInfos - 周期类型信息列表
 * @param {Function} onClose - 关闭弹窗回调
 * @param {Function} onReset - 重置为默认指标回调
 * @param {Function} onSave - 保存修改回调
 * @param {Function} onCancel - 取消修改回调
 * @param {Function} onTabChange - 切换实例类型回调
 * @param {Function} onSwitchCardChange - 指标设置修改回调
 */
const CustomMetricsModal: React.FC<CustomMetricsModalProps> = ({
  visible,
  loading,
  changed,
  archProductThresholdInfos,
  tabsBar,
  activeTab,
  tab,
  algorithmInfos,
  periodTypeInfos,
  metricPeriodTypeInfos,
  product,
  onClose,
  onReset,
  onSave,
  onCancel,
  onTabChange,
  onSwitchCardChange,
  popupContainer,
}) => {
  const defaultInsType = orderBy(archProductThresholdInfos?.[0]?.ArchThresholdInfos || [], ['InsTypeName', 'InsType'], ['desc', 'desc'])?.[0]?.InsType || '0';
  return (
  <Modal
    visible={visible}
    className={`indexModal ${s.modal}`}
    popupContainer={popupContainer}
    caption={
      <div className={'update-threshold-state-wrap'}>
        {t('定制指标')}
        <div className="update-threshold-state">
          {archProductThresholdInfos?.[0]?.ThresholdStatus
            ? archProductThresholdInfos?.[0]?.ThresholdStatus === 1
              && !changed
              && archProductThresholdInfos?.[0]?.IsDefault
              ? t('当前状态：默认指标')
              : t('当前状态：定制指标')
            : t('当前状态：-')}
        </div>
        {archProductThresholdInfos
        && !loading
        && (changed || !archProductThresholdInfos?.[0]?.IsDefault) ? (
          <div className={s.reset} onClick={onReset}>
            <img src={reset} />
            <span>{t('恢复默认指标')}</span>
          </div>
          ) : null}
      </div>
    }
    onClose={onClose}
  >
    <Modal.Body>
      {!loading ? (
        <>
          {tabsBar?.length > 1 ? (
            <p
              style={{
                color: '#666',
                marginLeft: 8,
                marginBottom: -7,
                fontSize: 13,
              }}
            >
              {product?.toLowerCase?.() === CcnConfig.CCNS ? CcnConfig.DETAIL_TITLE : t('实例类型')}
            </p>
          ) : null}
          {tabsBar?.length > 1 ? (
            <div style={{ width: 488, overflow: 'hidden', paddingRight: 28 }}>
              <TRadio.Group
                variant="primary-filled"
                className={s.radios}
                defaultValue={defaultInsType}
                value={
                  !activeTab
                    ? defaultInsType
                    : activeTab || '0'
                }
                onChange={onTabChange}
                style={{ marginTop: 15, marginLeft: 6 }}
              >
                {tabsBar.map((v, i) => (
                  <TRadio.Button
                    key={`TRadio-single-${i}`}
                    value={v?.InsType || `${i}`}
                  >
                    {v?.InsTypeName || `${i}`}
                  </TRadio.Button>
                ))}
              </TRadio.Group>
            </div>
          ) : null}
          <div style={{ maxHeight: 600, overflowY: 'auto' }}>
            {tab?.length > 1 ? (
              <p
                style={{
                  color: '#666',
                  fontSize: 13,
                  marginLeft: 10,
                  marginTop: 10,
                }}
              >
                {t('指标设置')}
              </p>
            ) : null}
            {(tab || []).map((v, i) => (
              <SwitchCard
                key={`SwitchCard-single-${i}`}
                product={product}
                data={v}
                algorithmInfos={algorithmInfos}
                periodTypeInfos={{
                  commonPeriodTypeInfos: periodTypeInfos,
                  metricPeriodTypeInfos,
                }}
                onChange={data => onSwitchCardChange(data, i)}
              />
            ))}
          </div>
        </>
      ) : (
        <div
          style={{
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            marginTop: 10,
          }}
        >
          <Skeleton theme={'text'} animation="flashed"></Skeleton>
          <Skeleton
            style={{ marginTop: 20 }}
            theme={'paragraph'}
            animation="flashed"
          ></Skeleton>
        </div>
      )}
    </Modal.Body>
    {archProductThresholdInfos?.[0] && changed && (
      <Modal.Footer>
        <Button type="primary" disabled={loading} onClick={onSave}>
          {t('保存')}
        </Button>
        <Button type="weak" disabled={loading} onClick={onCancel}>
          {t('取消')}
        </Button>
        </Modal.Footer>
      )}
    </Modal>
  );
};

export default CustomMetricsModal;
