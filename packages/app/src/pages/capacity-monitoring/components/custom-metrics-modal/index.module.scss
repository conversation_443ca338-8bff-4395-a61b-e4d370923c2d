.modal {
  :global {
    .update-threshold-state-wrap {
      display: flex;
      align-items: center;
      .update-threshold-state {
        margin-left: 20px;
        color: #666;
        font-size: 13px;
      }
    }
  }
}

.reset {
  display: flex;
  align-items: center;
  margin-left: 20px;
  cursor: pointer;
  img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
  span {
    color: #006eff;
    font-size: 13px;
  }
}

.radios {
  border-radius: 5px !important;
  padding: 3px !important;
  background-color: #EDF1F7 !important;
  flex-wrap: nowrap;
  overflow-x: auto;
  :global {
    .t-is-checked {
      span {
        color: #fff !important;
      }
    }
    .t-radio-button {
      span {
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        text-align: left;
        color: #6A7B92;
      }
    }
    .t-radio-group__bg-block {
      background-color: #006EFF !important;
      border-radius: 5px !important;
    }
  }
}