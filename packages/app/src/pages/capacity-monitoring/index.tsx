/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
} from 'react';
import { t } from '@tea/app/i18n';
import { orderBy, uniq } from 'lodash-es';
import { useHookstate } from '@hookstate/core';
import {
  Button,
  message,
  StatusTip,
  Status,
  H5,
  H6,
  Select,
} from '@tencent/tea-component';
import { reportEvent, EVENT } from '@src/utils/report';
import { BeeNest } from '@src/components/bee-nest';
import { RefreshIcon } from '@src/components/refresh-icon';
import {
  describeCapacityMetricInfo,
  describeNodeResourceLoadInfo,
  describeInsResourceRealTimeInfo,
  describeInsCapacityMetricData,
  describeRegions,
  describeRegionsAndZones,
} from '@src/api/nodeDrawer';
import {
  describeArchProductThresholdInfo,
  modifyArchProductThreshold as modifyArchThreshold,
  describeArchProductAlgorithmType,
  describeArchProductPeriodType,
} from '@src/api/thresholdsSetting';
import moment from 'moment';
import fetchData from '@src/utils/fetch';
import changeIcon from '@src/statics/svg/arrow-left-right-1.svg';
import {
  isRefreshArea,
  setIsRefreshArea,
  nodeOriginInfo,
} from '@src/utils/caching';
import HandleArea from '@src/pages/handle-area';
import globalState from '@src/stores/global.state';
import {
  SegViewType,
  EipAccountTypeEnum,
  EnvEnum,
  RiskLevel,
} from '@src/constants';
import { StatusColorMap } from '@src/constants/color';
import { CcnConfig, DorisConfig } from '@src/components/archive-report/constants';
import BaseInfo from './components/base-info';
import CapacityMetrics from './components/capacity-metrics';
import CustomMetricsModal from './components/custom-metrics-modal';
import s from './index.module.scss';
import './index.less';
const { LoadingTip } = StatusTip;

/**
 * 容量监控组件
 * @description 展示架构图节点的资源容量分布、基础信息和容量指标数据
 * @param {Object} arInfo - 架构图相关信息
 * @param {Object} nodeInfo - 节点相关信息
 * @param {boolean} [isRefresh] - 是否需要刷新
 * @returns {JSX.Element} 容量监控组件
 */
const CapacityMonitoring = ({
  arInfo,
  nodeInfo,
}: {
  arInfo: any;
  nodeInfo: any;
  isRefresh?: boolean;
}) => {
  // 架构图id
  const MapId = arInfo?.archInfo?.archId || '';
  // 节点uuid
  const NodeUuid = nodeInfo?.node?.key || '';
  // 最近更新时间
  const [latestUpdateTime, setLatestUpdateTime] = useState('');
  // 资源总数
  const [totalCount, setTotalCount] = useState(0);
  // 统计维度
  const [dimension] = useState([]);
  // 资源蜂巢数据
  const [beeNestData, setBeeNestData] = useState([]);
  // 当前选中蜂巢
  const [selectBeeNest, setSelectBeeNest] = useState<any>(null);
  // 当前点击蜂巢的基本信息
  const [baseInfo, setBaseInfo] = useState<null | Array<any>>(null);

  const [changed, setChanged] = useState(false);
  const [modifyMode, setModifyMode] = useState(0); // 0 普通修改， 1 恢复默认

  const [state, setState] = useState(null);

  const [multiRegionState, setMultiRegionState] = useState({
    isMultiRegion: undefined, // 是否为地域间： 用于展示地域tab（单地域/多地域）
    regionList: [], // 地域列表
    regionRiskItems: [], // 多地域标记红点数据
    multiRegionList: {
      dstRegions: [], // 目标地域
      srcRegions: [], // 源地域
    }, // 多地域列表
    regionCodeList: [], // 地域code列表
    regionNameList: [], // 地域name列表
    singleSRegion: '', // 单地域-源地域
    multiSRegion: '', // 多地域-源地域
    multiDRegion: '', // 多地域-目标地域
    hash: '',
  });

  const [activeTab, setActiveTab] = useState('');

  const oriState = useRef(null);
  const refDom = useRef(null);

  const [segmentState, setSegmentState] = useState({
    options: [
      {
        text: `${t('需要关注')}·${0}`,
        value: SegViewType.RISK,
      },
      {
        text: t('全部'),
        value: SegViewType.ALL,
      },
    ],
    value: SegViewType.RISK,
  });

  // 当前点击蜂巢的容量指标维度
  const [monitorMetricInfos, setMonitorMetricInfos] = useState([]);
  // 容量指标数据
  const [indexData, setIndexData] = useState(null);
  // 指标弹框显示
  const [indexVisible, setIndexVisible] = useState(false);
  // 产品阈值
  const [archProductThresholdInfos, setArchProductThresholdInfos] = useState([]);

  // 加载loading
  const [beeNestLoading, setBeeNestLoading] = useState(false);
  const [baseInfoLoading, setBaseInfoLoading] = useState(false);
  const [indexDataLoading, setIndexDataLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const currentInsEipAccountType = useHookstate(globalState.currentInsEipAccountType).get();
  const globalRegionMap = useHookstate(globalState.globalRegionMap).get();

  const ccnInfo = useMemo(
    () => baseInfo?.find?.(v => v?.Key === CcnConfig.DETAIL_TITLE),
    [baseInfo]
  );

  const { result: result2 } = fetchData(
    'DescribeArchProductAlgorithmType',
    null,
    describeArchProductAlgorithmType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const { result: result3 } = fetchData(
    'DescribeArchProductPeriodType',
    null,
    describeArchProductPeriodType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const algorithmInfos = result2?.AlgorithmInfos || [];
  const periodTypeInfos = result3?.PeriodTypeInfos || [];
  const metricPeriodTypeInfos = result3?.MetricPeriodTypeInfos || [];

  // 查询架构图节点资源容量分布
  const getDescribeNodeResourceLoadInfo = async () => {
    setBeeNestData([]);
    setBeeNestLoading(true);
    try {
      const res = await describeNodeResourceLoadInfo({
        MapId,
        NodeUuid,
        Metrics: dimension,
      });
      if (!res?.Error) {
        setTotalCount(res.ResourceCount || 0);
        setLatestUpdateTime(res.LatestUpdateTime);
        setBeeNestData(orderBy(res.ResourceLoadRiskInfos || [], ['RiskLevel'], ['desc']));
        // 如果没有资源分布，去掉loading
        if ((res.ResourceLoadRiskInfos || []).length === 0) {
          setBaseInfo([]);
        }
      }
      setBeeNestLoading(false);
      setBaseInfoLoading(false);
      setIndexDataLoading(false);
    } catch (err) {
      setBeeNestLoading(false);
      setBaseInfoLoading(false);
      setIndexDataLoading(false);
    }
  };

  const getStatusColor = (status: number) => StatusColorMap[status as RiskLevel];

  // 调用云顾问 查询cnn的地域列表
  const getCnnRegionList = async () => {
    try {
      const res = await describeRegions({
        ProductId: 'VPC',
      });
      if (!res?.Error) {
        setMultiRegionState(last => ({
          ...last,
          regionNameList: res?.RegionSet ?? [],
        }));
      }
    } catch (err) {
      console.log(err);
    }
  };

  // 租户端调用云顾问 global 查询地域列表
  const getGlobalRegionList = useCallback(async () => {
    if (EnvEnum.CONSOLE === arInfo?.env && !globalRegionMap.size) {
      try {
        const res = await describeRegionsAndZones();
        if (!res?.Error) {
          const map = new Map();
          (res?.ResourceRegionSet ?? []).forEach((item) => {
            map.set(item.Region, item.RegionName);
            if (item?.ResourceZoneSet?.length) {
              item.ResourceZoneSet.forEach((zone) => {
                map.set(zone.Zone, zone.ZoneName);
              });
            }
          });
          globalState.set(state => ({ ...state, globalRegionMap: map }));
        }
      } catch (err) {
        console.log(err);
      }
    }
  }, [globalRegionMap]);

  // 查询实例基本信息
  const getDescribeInsResourceRealTimeInfo = async () => {
    setBaseInfoLoading(true);
    try {
      const res = await describeInsResourceRealTimeInfo({
        MapId,
        NodeUuid,
        InsId: selectBeeNest.InsId,
        InsRegion: selectBeeNest.Region,
      });
      if (!res?.Error) {
        const eipAccountType = (res?.EipAccountType as EipAccountTypeEnum)
          ?? EipAccountTypeEnum.标准账户;

        if (eipAccountType === EipAccountTypeEnum.标准账户) {
          getInsMetricInfo();
        }
        setBaseInfoLoading(false);
        // 保存当前实例基本信息，只保留 ShowType为 2 3
        setBaseInfo((res?.InsResourceInfoList || []).filter(i => i.ShowType === 3 || i.ShowType === 2));
        const isMultiRegion = (res?.RegionList || []).length > 1;
        const initValue = (res?.RegionList || [])?.[0] || '';
        const initSecondValue = (res?.RegionList || [])?.[1] || '';
        setMultiRegionState(last => ({
          ...last,
          isMultiRegion,
          regionCodeList: res?.RegionList,
          singleSRegion: initValue,
          multiSRegion: initValue,
          multiDRegion: initSecondValue,
          regionRiskItems: res?.RegionRiskItems ?? [],
        }));
        globalState.set(state => ({
          ...state,
          currentInsEipAccountType: eipAccountType,
          insEipAccountTypeMap: {
            ...state.insEipAccountTypeMap,
            [selectBeeNest.InsId]: eipAccountType,
          },
        }));
      }
      setBaseInfoLoading(false);
    } catch (err) {
      setBaseInfoLoading(false);
      console.log(err);
    }
  };

  // 查询实例容量指标
  const getInsMetricInfo = async () => {
    setIndexDataLoading(true);
    try {
      const res = await describeCapacityMetricInfo({
        MapId,
        NodeUuid,
        InsId: selectBeeNest.InsId,
      });
      if (!res?.Error) {
        const infos = (res?.MonitorMetricInfos || []).filter(i => i.Metric !== 'resource');
        setMonitorMetricInfos(infos);
      }
      setIndexDataLoading(false);
    } catch (err) {
      setIndexDataLoading(false);
      console.log(err);
    }
  };

  // 查询实例容量指标数据
  const getDescribeInsCapacityMetricData = useCallback(
    async (MetricInfo) => {
      const { PeriodValue: periodValue } = MetricInfo?.PeriodTypeInfo ?? {};
      const isMultiRegionTab = ccnInfo?.Value === CcnConfig.MULTIPLE;
      setIndexDataLoading(true);
      // 区域指标匹配
      try {
        const res = await describeInsCapacityMetricData({
          MapId,
          NodeUuid,
          InsId: selectBeeNest.InsId,
          InsRegion: selectBeeNest.Region,
          Metric: MetricInfo.Metric,
          Namespace: MetricInfo.Namespace,
          SourceRegion: isMultiRegionTab
            ? multiRegionState?.multiSRegion
            : multiRegionState.singleSRegion,
          DesRegion: isMultiRegionTab ? multiRegionState.multiDRegion : '',
          StartTime: moment()
            .subtract(periodValue ? periodValue - 1 : 6, 'days')
            .startOf('day')
            .format('YYYY-MM-DDTHH:mm:ssZ'),
          EndTime: moment().endOf('day')
            .format('YYYY-MM-DDTHH:mm:ssZ'),
          CalculateMode: 1,
        });
        if (!res?.Error) {
          setIndexData((val) => {
            const newData = (val || []).map((j) => {
              if (j.Metric === MetricInfo.Metric) {
                let status = 0; // 0.正常 绿色 1.低使用率 淡绿色    2.中度负载 中风险 橙色   3. 高度负载 高风险 红色
                let statusLabel = t('正常负载');
                const {
                  HighThreshold: highThreshold,
                  MediumThreshold: mediumThreshold,
                  // LowUsed: lowUsed,
                  MediumUsed: mediumUsed,
                } = j.data;
                const algorithmValue = res?.AlgorithmValue ?? 0; // 算法值
                if (algorithmValue >= mediumThreshold) {
                  status = 2;
                  statusLabel = t('高负载');
                }
                if (algorithmValue >= highThreshold) {
                  status = 3;
                  statusLabel = t('严重高负载');
                }
                if (algorithmValue < mediumUsed) {
                  status = 1;
                  statusLabel = t('未充分使用');
                }
                if (algorithmValue === -1) {
                  status = -1;
                  statusLabel = t('无数据');
                }
                j.lineData = (res.Timestamps || []).map((i, index) => ({
                  time: moment.unix(i).format('YYYY/MM/DD HH:mm:ss'),
                  value: res.Values?.[index] || 0,
                }));
                j.max = res?.Max ?? 0;
                j.min = res?.Min ?? 0;
                j.avg = res?.Avg ?? 0;
                j.algorithmValue = algorithmValue;
                j.finish = true;
                j.status = status;
                j.statusLabel = statusLabel;
              }
              return j;
            });
            return orderBy(
              newData,
              ['status', 'algorithmValue', 'title'],
              ['desc', 'desc', 'asc']
            );
          });
        }
        setIndexDataLoading(false);
      } catch (err) {
        setIndexDataLoading(false);
        console.log(err);
      }
    },
    [
      selectBeeNest,
      multiRegionState.multiSRegion,
      multiRegionState.multiDRegion,
      multiRegionState.singleSRegion,
      ccnInfo,
    ]
  );

  // 查询节点阈值/恢复默认阈值
  const getDescribeArchProductThresholdInfo = useCallback(
    async (IsDefault = 0) => {
      setBtnLoading(true);
      try {
        const res: any = await describeArchProductThresholdInfo({
          MapId,
          NodeUuidSet: [NodeUuid],
          IsDefault,
          Products: IsDefault ? [archProductThresholdInfos?.[0]?.Product] : [],
        });
        if (!res?.Error) {
          if (IsDefault) {
            reportEvent({
              key: EVENT.RESET_TO_DEFAULT_CONFIG,
              extraInfo: JSON.stringify(res.ArchProductThresholdInfos || []),
            });
            setModifyMode(1);
            res.ArchProductThresholdInfos[0]
              && (res.ArchProductThresholdInfos[0].ThresholdStatus = 1);
            res.ArchProductThresholdInfos[0]
              && (res.ArchProductThresholdInfos[0].IsDefault = true);
          } else {
            setModifyMode(0);
          }
          setArchProductThresholdInfos(res.ArchProductThresholdInfos || []);
          try {
            const newDataStr = JSON.stringify(res?.ArchProductThresholdInfos || []);
            if (oriState.current !== null) {
              const lastDataStr = JSON.stringify(oriState.current);
              if (newDataStr === lastDataStr) {
                setChanged(false);
              } else {
                setChanged(true);
              }
            } else {
              setChanged(false);
            }
            if (IsDefault === 0) {
              // 非默认配置
              oriState.current = JSON.parse(JSON.stringify(res?.ArchProductThresholdInfos || []));
            }
            const newData = JSON.parse(newDataStr);
            setState(newData);
          } catch (error) {
            console.log(error);
          }
          setBtnLoading(false);
        }
      } catch (err) {
        setBtnLoading(false);
      }
    },
    [state, NodeUuid]
  );

  // 修改节点阈值
  const modifyArchProductThreshold = useCallback(async () => {
    setBtnLoading(true);
    let flag = true;
    state.forEach((item) => {
      item.ArchThresholdInfos.forEach((item2) => {
        item2.ThresholdConfigInfos.forEach((item3) => {
          if (!item3.PeriodValue) {
            flag = false;
          }
        });
      });
    });
    if (flag) {
      try {
        const res = await modifyArchThreshold({
          MapId,
          ArchProductThresholdInfos: state,
          NodeUuidSet: [NodeUuid],
          ModifyMode: modifyMode,
        });
        if (res.Error) {
          setBtnLoading(false);
          return;
        }
        oriState.current = null;
        message.success({
          content: t('操作成功'),
        });
        // 刷新蜂巢
        getDescribeNodeResourceLoadInfo();
        setIndexVisible(false);
        setBtnLoading(false);
        setIsRefreshArea(!isRefreshArea);
        arInfo.setSlotComponent(<HandleArea arInfo={arInfo} isRefreshArea={isRefreshArea} />);
      } catch (err) {
        setBtnLoading(false);
        console.log(err);
      }
    } else {
      setBtnLoading(false);
      message.error({
        content: t('请选择周期'),
      });
    }
  }, [state, NodeUuid, modifyMode]);

  const tabsBar = useMemo(
    () => orderBy(
      state?.[0]?.ArchThresholdInfos?.filter(v => v?.InsType && v?.InsTypeName) || [],
      ['InsTypeName', 'InsType'],
      ['desc', 'desc']
    ) || [],
    [state]
  );

  const capacityMetricsData = useMemo(() => indexData ?? [], [indexData]);

  const tab = useMemo(() => {
    const tabs = orderBy(
      state?.[0]?.ArchThresholdInfos || [],
      ['InsTypeName', 'InsType'],
      ['desc', 'desc']
    ) || [];
    return !activeTab
      ? tabs?.[0]?.ThresholdConfigInfos || []
      : tabs?.find(item => item?.InsType === activeTab)
        ?.ThresholdConfigInfos
          || tabs?.[0]?.ThresholdConfigInfos
          || [];
  }, [state, activeTab]);

  const riskIndexData = useMemo(
    () => (capacityMetricsData || []).filter(v => v?.status >= 1),
    [capacityMetricsData]
  );

  const notSupport = useMemo(
    () => selectBeeNest?.RiskLevel === -1,
    [selectBeeNest]
  );

  const isCcn = useMemo(() => nodeOriginInfo?.node?.name?.toLowerCase() === CcnConfig.CCNS, [nodeOriginInfo?.node?.name]);
  const isDoris = useMemo(() => nodeOriginInfo?.node?.name?.toLowerCase() === DorisConfig.DORIS, [nodeOriginInfo?.node?.name]);

  // 统计维度变化，查询资源分布
  useEffect(() => {
    if (dimension) {
      getDescribeNodeResourceLoadInfo();
    }
  }, [dimension]);

  useEffect(() => {
    // 清空基本信息和容量指标折线数据
    setBaseInfo(null);
    setIndexData(null);
    // 选中蜂巢变化，查询 实例基本信息 和 容量指标数据
    if (selectBeeNest?.InsId) {
      getDescribeInsResourceRealTimeInfo();
    }
  }, [selectBeeNest]);

  useEffect(() => {
    let data = null;
    if (monitorMetricInfos.length) {
      data = [];
      // 固定容量指标顺序
      // eslint-disable-next-line array-callback-return
      monitorMetricInfos.map((i) => {
        data.push({
          finish: false,
          Metric: i.Metric,
          title: i.MetricName,
          lineData: [],
          max: 0,
          min: 0,
          avg: 0,
          algorithmValue: 0,
          data: i,
        });
      });
    }
    setIndexData(data);
  }, [
    monitorMetricInfos,
  ]);

  useEffect(() => {
    // 先请求describeCapacityMetricInfo  再请求getDescribeInsCapacityMetricData
    if (monitorMetricInfos.length) {
      // eslint-disable-next-line array-callback-return
      monitorMetricInfos.map((i) => {
        getDescribeInsCapacityMetricData(i);
      });
    }
  }, [
    monitorMetricInfos,
    multiRegionState.hash,
  ]);

  useEffect(() => {
    // 查询统计维度
    arInfo.setDrawerProps({
      style: { width: '500px' },
      footer: (
        <div
          style={{ width: '100%', display: 'flex', justifyContent: 'center' }}
        >
          <Button
            type="primary"
            onClick={() => {
              reportEvent({
                key: EVENT.SPECIFY_NODE_CONFIG,
                extraInfo: null,
              });
              // 每次打开时清空阈值
              setArchProductThresholdInfos([]);
              setIndexVisible(true);
              getDescribeArchProductThresholdInfo();
            }}
          >
            {t('定制指标')}
          </Button>
        </div>
      ),
    });
  }, [nodeInfo]);

  useEffect(() => {
    setSegmentState(last => ({
      ...last,
      options: [
        {
          text: `${t('需要关注')}·${riskIndexData.length}`,
          value: SegViewType.RISK,
        },
        {
          text: t('全部'),
          value: SegViewType.ALL,
        },
      ],
      // 无风险时进入全部
      value: riskIndexData.length ? SegViewType.RISK : SegViewType.ALL,
    }));
  }, [riskIndexData]);

  useEffect(() => {
    if (isCcn || isDoris) {
      getCnnRegionList();
      getGlobalRegionList();
    }
  }, [isCcn, isDoris]);

  useEffect(() => {
    const { regionCodeList, regionNameList, regionRiskItems } = multiRegionState;
    if (regionCodeList?.length && regionNameList?.length) {
      const regionNameMap = {};
      const newRegionList = [];
      const newMultiRegionList = {
        srcRegions: [],
        dstRegions: [],
      };
      regionNameList.forEach((item) => {
        regionNameMap[item.Region] = item.RegionName;
      });
      (regionRiskItems || []).forEach((item) => {
        const createRegionOption = (region: string) => ({
          value: `${region}`,
          text: (
            <p style={{ display: 'flex', alignItems: 'center' }}>
              {regionNameMap[region]}
              {item.RiskLevel > 0 && (
                <span
                  style={{
                    marginLeft: 8,
                    marginTop: 3,
                    display: 'inline-block',
                    width: 7,
                    height: 7,
                    borderRadius: '50%',
                    backgroundColor: getStatusColor(item.RiskLevel),
                  }}
                />
              )}
            </p>
          ),
        });

        newMultiRegionList.dstRegions.push(createRegionOption(item.DstRegion));
        newMultiRegionList.srcRegions.push(createRegionOption(item.SrcRegion));
      });
      uniq(regionCodeList).forEach((item) => {
        if (regionNameMap[item]) {
          newRegionList.push({
            value: item,
            text: regionNameMap[item],
          });
        }
      });
      setMultiRegionState(last => ({
        ...last,
        regionList: newRegionList,
        multiRegionList: newMultiRegionList,
      }));
    }
  }, [
    multiRegionState.regionCodeList,
    multiRegionState.regionNameList,
    multiRegionState.isMultiRegion,
    multiRegionState.regionRiskItems,
  ]);

  return (
    <div className="capacityMonitoringWrap">
      <div className="subTitleWrap">
        <div className="subTitle">
          <div className="label">{t('数据更新时间：')}</div>
          <div>
            {t('{{updateTime}}', {
              updateTime: latestUpdateTime,
            })}
          </div>
          <RefreshIcon
            onClick={() => {
              globalState.set(state => ({ ...state, isRefreshing: true }));
              getDescribeNodeResourceLoadInfo();
              setIsRefreshArea(!isRefreshArea);
              arInfo.setSlotComponent(<HandleArea arInfo={arInfo} isRefreshUpdate={isRefreshArea} />);
            }}
          />
        </div>
        <div className="subTitle">
          <div className="label">{t('资源总数：')}</div>
          <div>{t('{{count}}', { count: totalCount })}</div>
        </div>
      </div>

      <div className="beeNestBox">
        <H5>{t('资源容量分布')}</H5>
        <div className="beeNestContent">
          {beeNestLoading ? (
            <div className="LoadingWrap" style={{ minHeight: 60 }}>
              <LoadingTip />
            </div>
          ) : beeNestData.length ? (
            <BeeNest
              ccnInfo={{
                isCcn,
              }}
              MapId={MapId}
              NodeUuid={NodeUuid}
              data={beeNestData}
              product={nodeOriginInfo?.node?.name?.toLowerCase()}
              selectChange={(item) => {
                setSelectBeeNest(item);
              }}
            />
          ) : (
            <div
              className="LoadingWrap"
              style={{
                color: 'rgba(0, 0, 0, 0.4)',
                fontSize: 12,
                paddingTop: 10,
              }}
            >
              {t('暂无数据')}
            </div>
          )}
        </div>
      </div>
      <div className="line"></div>
      <div className="infoWrap" style={{ paddingBottom: 30 }}>
        <H5>{t('选中资源详情')}</H5>
        <H6>{t('基础信息')}</H6>
        <BaseInfo loading={baseInfoLoading} data={baseInfo} reginMap={globalRegionMap} />
        <H6 className={s['capacity-indicators']}>
          {t('容量指标')}
        </H6>
        {currentInsEipAccountType === EipAccountTypeEnum.标准账户 ? (
          <>
            {
              isCcn && multiRegionState.regionCodeList.length > 0 && ccnInfo?.Value === CcnConfig.SINGLE ? (
                <>
                  <Select
                    appearance="button"
                    value={multiRegionState.singleSRegion}
                    options={multiRegionState.regionList}
                    style={{ width: 150 }}
                    placeholder={t('请选择地域')}
                    onChange={(v) => {
                      setMultiRegionState(last => ({
                        ...last,
                        singleSRegion: v,
                        hash: `${new Date().getTime()}`,
                      }));
                    }}
                  />
                </>
              ) : null
            }
            {
              isCcn && multiRegionState.regionCodeList.length > 0 && ccnInfo?.Value === CcnConfig.MULTIPLE ? (
                <div className={s['flex-row']}>
                  <div className={s['flex-column']}>
                    <span>{t('源地域')}</span>
                    <Select
                      appearance="button"
                      options={multiRegionState?.multiRegionList?.srcRegions?.map(item => ({
                        ...item,
                        disabled: item.value === multiRegionState.multiDRegion,
                      }))}
                      value={multiRegionState.multiSRegion}
                      style={{ width: 150, marginRight: 10 }}
                      placeholder={t('请选择地域间')}
                      onChange={(v) => {
                        setMultiRegionState(last => ({
                          ...last,
                          multiSRegion: v,
                          hash: `${new Date().getTime()}`,
                        }));
                      }}
                    />
                  </div>
                  <img src={changeIcon} className={multiRegionState.regionCodeList.length === 1 ? s['change-icon'] : s['change-icon-hover']} onClick={() => {
                    if (multiRegionState.regionCodeList.length > 1) {
                      // 交换地域
                      setMultiRegionState(last => ({
                        ...last,
                        multiSRegion: last.multiDRegion,
                        multiDRegion: last.multiSRegion,
                        hash: `${new Date().getTime()}`,
                      }));
                    }
                  }} />
                  <div className={s['flex-column']} style={{ marginLeft: 10 }}>
                    <span>{t('目标地域')}</span>
                    <Select
                      appearance="button"
                      options={multiRegionState?.multiRegionList?.dstRegions?.map(item => ({
                        ...item,
                        disabled: item.value === multiRegionState.multiSRegion,
                      }))}
                      // disabled={multiRegionState.regionCodeList.length === 1}
                      value={multiRegionState.multiDRegion}
                      style={{ width: 150 }}
                      placeholder={t('请选择地域间')}
                      onChange={(v) => {
                        setMultiRegionState(last => ({
                          ...last,
                          multiDRegion: v,
                          hash: `${new Date().getTime()}`,
                        }));
                      }}
                    />
                  </div>
                </div>
              ) : null
            }
            <CapacityMetrics
              loading={indexDataLoading}
              data={(isCcn && multiRegionState.regionCodeList.length === 1) ? [] : capacityMetricsData}
              notSupport={notSupport}
              segmentState={segmentState}
              multiRegionState={multiRegionState}
              isMultiRegionTab={ccnInfo?.Value === CcnConfig.MULTIPLE}
              onSegmentChange={(value) => {
                setSegmentState(last => ({
                  ...last,
                  value: value as SegViewType,
                }));
              }}
              selectBeeNest={{
                ...selectBeeNest,
                MapId,
                NodeUuid,
              }}
              isCcn={isCcn}
            />
          </>
        ) : (
          <div>
            <Status
              className={s.status}
              icon="blank"
              size="xs"
              title={t('暂无数据')}
            />
            <div className={s.box}>
              <p>
                {t('云顾问-容量监测针对EIP带宽数据展示仅支持"标准账户类型"，')}
              </p>
              <p>
                {t('如您为')}
                <Button type="link" onClick={() => window.open('https://cloud.tencent.com/document/product/1199/49090#.E8.B4.A6.E6.88.B7.E7.B1.BB.E5.9E.8B.E7.9A.84.E5.8C.BA.E5.88.AB')}>&quot;{t('传统账户类型')}&quot;</Button>，
                {t('请单击查看')}
                <Button type="link" onClick={() => window.open('https://cloud.tencent.com/document/product/213/5178')}>{t('CVM公网带宽数据')}</Button>
              </p>
            </div>
          </div>
        )}
      </div>
      <div ref={refDom}></div>
      <CustomMetricsModal
        visible={indexVisible}
        popupContainer={() => refDom.current}
        loading={btnLoading}
        changed={changed}
        modifyMode={modifyMode}
        archProductThresholdInfos={archProductThresholdInfos}
        tabsBar={tabsBar}
        activeTab={activeTab}
        tab={tab}
        product={state?.[0]?.Product}
        algorithmInfos={algorithmInfos}
        periodTypeInfos={periodTypeInfos}
        metricPeriodTypeInfos={metricPeriodTypeInfos}
        onClose={() => {
          oriState.current = null;
          setChanged(false);
          setIndexVisible(false);
        }}
        onReset={() => {
          getDescribeArchProductThresholdInfo(1);
        }}
        onSave={() => {
          reportEvent({
            key: EVENT.ADJUST_SPECIFIC_CONFIG,
            extraInfo: null,
          });
          modifyArchProductThreshold();
        }}
        onCancel={() => {
          setIndexVisible(false);
          setChanged(false);
        }}
        onTabChange={(v) => {
          setActiveTab(v as string);
        }}
        onSwitchCardChange={(data, i) => {
          const productConfig = state?.[0];
          // eslint-disable-next-line max-len, operator-linebreak
          const productTabs = // eslint-disable-next-line max-len
            (
              productConfig?.ArchThresholdInfos?.find(item => item?.InsType === activeTab)
              || orderBy(
                productConfig?.ArchThresholdInfos || [],
                ['InsTypeName', 'InsType'],
                ['desc', 'desc']
              )?.[0]
            )?.ThresholdConfigInfos;
          productTabs[i] = data;
          setState([...state]);
          const str1 = JSON.stringify(state);
          const str2 = JSON.stringify(oriState.current);
          if (str1 !== str2) {
            setChanged(true);
            if (modifyMode === 1) {
              setModifyMode(0);
            }
          } else {
            setChanged(false);
          }
        }}
      />
    </div>
  );
};
export default CapacityMonitoring;
