.change-icon {
  width: 17px;
  transform: translateY(10px);
}
.change-icon-hover {
  width: 17px;
  transform: translateY(10px);
  &:hover {
    cursor: pointer;
  }
}
.flex-row {
  display: flex;
  align-items: center;
}
.flex-column {
  display: flex;
  flex-direction: column;
  span {
    display: block;
    margin-bottom: 3px;
    color: rgba(0, 0, 0, 0.6);
  }
}
.capacity-indicators {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
  display: flex;
  align-items: center;
  &-tag {
    margin-top: 0 !important;
    margin-left: 10px !important;
    span {
      font-size: 10px !important;
    }
  }
}
.multiple-region {
  margin-top: 10px;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.detail {
  padding: 15px;
  .card {
    background-color: #fff;
    border: 1px solid #EDF1F7;
    box-shadow: none;
    border-radius: 8px;
    padding-bottom: 20px;
    :global {

    }
  }
}
.col-flex {
  &>div {
    display: flex;
    align-items: center;
  }
}

.reset {
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #006EFF;
  margin-left: 15px;
  &:hover {
    cursor: pointer;
  }
  img {
    margin-right: 6px;
  }
  &:hover {
    cursor: pointer;
  }
}
.radios {
  border-radius: 5px !important;
  padding: 3px !important;
  background-color: #EDF1F7 !important;
  flex-wrap: nowrap;
  overflow-x: auto;
  :global {
    .t-is-checked {
      span {
        color: #fff !important;
      }
    }
    .t-radio-button {
      span {
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        text-align: left;
        color: #6A7B92;
      }
    }
    .t-radio-group__bg-block {
      background-color: #006EFF !important;
      border-radius: 5px !important;
    }
  }
}
.status {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column !important;
}
.box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 400px;
  p {
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    padding-left: 60px;
    font-weight: 500;
  }
}