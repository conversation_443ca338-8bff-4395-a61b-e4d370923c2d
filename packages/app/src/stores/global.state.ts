
import { hookstate } from '@hookstate/core';
import { EipAccountTypeEnum } from '@src/constants';

const globalState = hookstate({
  env: '', // 当前环境
  sessionId: '', // 当前会话id
  archId: '', // 当前架构图id
  regionNameList: [], // 地域列表
  regionNameMap: {}, // 地域名称与id的映射关系
  insEipAccountTypeMap: {}, // EipAccountType
  currentInsEipAccountType: EipAccountTypeEnum.标准账户, // 当前选择的EipAccountType
  predictionOpened: false, // 是否打开预测抽屉
  chooseFigureMetropolitanMode: false, // 是否处于选择图元模式
  currentChooseFigure: {
    NodeUuid: '',
    ItemName: '', // 产品名称
    Label: '', // 图元名称
  }, // 当前选择的图元
  isRefreshing: false, // 点击手动刷新时，是否正在刷新
  getDescribeNodeLoadInfoKey: undefined, // 用于刷新架构图NodeLoadIn
  predictionHightNodeKey: '', // 高亮节点key
  clearAllPositions: false, // 清除所有定位状态的标志
  exportState: {
    mapId: '', // 当前架构图id
    timer: null, // 导出进度定时器
    status: '', // 导出状态
    seconds: 0, // 导出进度
    closed: false, // 生成状态 bubble是否关闭
    data: {}, // 导出相关数据
    downloadData: {
      type: '', // 导出类型
      downloadHash: '', // 导出hash
    }, // 运营端导出报告相关数据
  }, // 导出相关数据
  globalRegionMap: new Map(),
  agentInputValue: '', // agent输入框值
  agentDrawerVisible: false, // agent抽屉是否可见
});
export default globalState;
