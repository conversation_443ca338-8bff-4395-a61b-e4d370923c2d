// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
export const simpleMarkdownToHTML = (input: string): string => {
  /* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
  const reg = new RegExp(
    '(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
    'g'
  );
  /* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */
  let result = input.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`);

  // 无序列表markdown语法转换
  if (result.includes('- ')) {
    result = result
      .split('- ')
      .filter(r => r.length)
      .map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
      .join('');
  }
  return result;
};
