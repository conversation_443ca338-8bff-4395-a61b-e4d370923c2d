const prefix = 'dynamic-inserted-css-';


/**
 * 插入或更新具有指定 id 和 CSS 文本的 CSS 样式元素。
 *
 * @param id - 要创建或更新的样式元素的唯一标识符。
 * @param cssText - 要在样式元素内部应用的 CSS 规则。
 * @returns 创建或更新后的 HTMLStyleElement。
 */
export function insertCSS(id: string, cssText: string) {
  let style: HTMLStyleElement;
  style = document.getElementById(prefix + id) as HTMLStyleElement;
  if (!style) {
    style = document.createElement('style');
    style.id = prefix + id;

    // IE8/9 can not use document.head
    document.getElementsByTagName('head')[0].appendChild(style);
  }
  if (style.textContent !== cssText) {
    style.textContent = cssText;
  }
  return style;
}
