import { EnvEnum } from '@src/constants';
import { t } from '@tea/app/i18n';
import { app } from '@tea/app';
import globalState from '@src/stores/global.state';
import { getCurrTimeToStr } from '@src/utils/common';

export enum ROTATE_REPORT_TYPE {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal'
}

export enum EVENT {
  GENERATE_REPORT = 'GenerateReport', // 生成报告
  CHECK_REPORT = 'CheckReport', // 查看生成的报告
  DISCARD_REPORT = 'DiscardReport', // 关闭生成的报告
  ARCHIVE_REPORT = 'ArchiveReport', // 归档报告
  ROTATE_REPORT = 'RotateReport', // 轮转报告 旋转后的形态：vertical/horizontal
  PREDICT_CAPACITY = 'PredictCapacity', // 预测容量
  SPECIFY_PREDICT_CONFIG = 'SpecifyPredictConfig', // 预测添加精确设置 配置的参数 Json {产品、周期、计算方法、倍数}
  START_PREDICT = 'StartPredict', // 立即预测
  LOCATE_PREDICTED_NODE = 'LocatePredictedNode', // 定位
  CHECK_PREDICTED_INSTANCE = 'CheckPredictedInstance', // 查看实例
  CLICK_PREDICTED_CARD = 'ClickPredictedCard', // 点击图卡
  DEFAULT_CONFIG = 'DefaultConfig', // 默认指标
  ADJUST_DEFAULT_CONFIG = 'AdjustDefaultConfig', // 调整默认指标 变更的对象以及调整后的配置参数JSON {产品、机型、指标、开关、周期、算法、阈值组}
  RESET_DEFAULT_CONFIG = 'ResetDefaultConfig', // 重置默认指标
  CHECK_NODE_DETAIL = 'CheckNodeDetail', // 查看节点容量详情 产品类型
  CLICK_NEST_DIAGRAM = 'ClickNestDiagram', // 点击蜂窝图
  CLICK_DETAIL_CARD = 'ClickDetailCard', // 点击指标图卡
  SWITCH_TO_TREND = 'SwitchToTrend', // 趋势图卡
  SWITCH_TO_DISTRIBUTE = 'SwitchToDistribute', // 分布图卡
  TREND_DRILL_DOWN = 'TrendDrillDown', // 下钻趋势图
  SPECIFY_NODE_CONFIG = 'SpecifyNodeConfig', // 定制指标
  ADJUST_SPECIFIC_CONFIG = 'AdjustSpecificConfig', // 调整定制指标
  RESET_TO_DEFAULT_CONFIG = 'ResetToDefaultConfig', // 重置为默认指标
}

/**
 * 事件名称类型
 */
export type EventName =
  | EVENT.GENERATE_REPORT // 生成报告
  | EVENT.CHECK_REPORT // 查看生成的报告
  | EVENT.DISCARD_REPORT // 关闭生成的报告
  | EVENT.ARCHIVE_REPORT // 归档报告
  | EVENT.ROTATE_REPORT // 轮转报告 旋转后的形态：vertical/horizontal
  | EVENT.PREDICT_CAPACITY // 预测容量
  | EVENT.SPECIFY_PREDICT_CONFIG // 预测添加精确设置 配置的参数 Json {产品、周期、计算方法、倍数}
  | EVENT.START_PREDICT // 立即预测
  | EVENT.LOCATE_PREDICTED_NODE // 定位
  | EVENT.CHECK_PREDICTED_INSTANCE // 查看实例
  | EVENT.CLICK_PREDICTED_CARD // 点击图卡
  | EVENT.DEFAULT_CONFIG // 默认指标
  | EVENT.ADJUST_DEFAULT_CONFIG // 调整默认指标 变更的对象以及调整后的配置参数JSON {产品、机型、指标、开关、周期、算法、阈值组}
  | EVENT.RESET_DEFAULT_CONFIG // 重置默认指标
  | EVENT.CHECK_NODE_DETAIL // 查看节点容量详情 产品类型
  | EVENT.CLICK_NEST_DIAGRAM // 点击蜂窝图
  | EVENT.CLICK_DETAIL_CARD // 点击指标图卡
  | EVENT.SWITCH_TO_TREND // 趋势图卡
  | EVENT.SWITCH_TO_DISTRIBUTE // 分布图卡
  | EVENT.TREND_DRILL_DOWN // 下钻趋势图
  | EVENT.SPECIFY_NODE_CONFIG // 定制指标
  | EVENT.ADJUST_SPECIFIC_CONFIG // 调整定制指标
  | EVENT.RESET_TO_DEFAULT_CONFIG // 重置为默认指标

/**
 * 上报参数类型
 */
export interface ReportFields {
  subUin?: string; // 子账号
  subUinName?: string; // 子账号名称
  key: EventName; // 事件类型
  extraInfo: string | null; // 事件参数
}


/**
 * 埋点上报
 * @param platform 平台
 * @param fields 上报参数
 * @description
 */
export const reportEvent = async (fields: ReportFields) => {
  const platform = globalState.env.get();
  const sessionId = globalState.sessionId.get();
  const archId = globalState.archId.get();
  if (!event || !platform) {
    return;
  }
  try {
    if (platform === EnvEnum.CONSOLE) {
      const user = await app.user.current();
      const { appId, loginUin, displayName } = user;
      const data = {
        actionTime: getCurrTimeToStr(),
        subUin: loginUin?.toString(),
        subUinName: displayName,
        appId: appId?.toString(),
        sessionId,
        archId,
        ...fields,
      };
      // console.log('埋点上报:', data);
      app.insight.stat({
        ns: 'capacityPluginSdk',
        event: 'capacityPluginYeHeData',
        stringFields: data,
        integerFields: {
          time: Date.now(),
        },
      });
    } else if (platform === EnvEnum.ISA) {
      // // 上报到isa;
      // const data = {
      //   actionTime: getCurrTimeToStr(),
      //   appId: getAppid().toString(),
      //   ...fields,
      // };
      // console.log('isa:', data);
    }
    // 上报完成后删除from参数，防止复制链接出去上报数据不准
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete('from');
    window.history.replaceState(null, '', currentUrl.toString());
  } catch (e) {
    console.error(t('参数不合法：'), e);
  }
};

