export function toUTCtime(timestamp = Date.now(), format = 'yyyy-MM-dd HH:mm:ss') {
  if (isNaN(timestamp)) {
    return '';
  }

  if (format.length < 4 || 'yyyy-MM-dd HH:mm:ss'.indexOf(format) !== 0) {
    return '';
  }

  // let end = Math.round(new Date() / 1000)
  const date = new Date(Number(timestamp * 1000));

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return format
    .replace('yyyy', year.toString())
    .replace('MM', month > 9 ? month.toString() : `0${month}`)
    .replace('dd', day > 9 ? day.toString() : `0${day}`)
    .replace('HH', hour > 9 ? hour.toString() : `0${hour}`)
    .replace('mm', minute > 9 ? minute.toString() : `0${minute}`)
    .replace('ss', second > 9 ? second.toString() : `0${second}`);
}
