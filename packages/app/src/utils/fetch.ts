import { isPlainObject } from 'lodash-es';
import useSWR from 'swr';

/**
 * @description 判断data是否是一个不完整的对象
 * @param data object
 * @returns boolean
 */
const unCompleteInspection = data => (
  // 判断data是否是一个纯对象
  isPlainObject(data)
    // 判断data中的key的数量和value中不为空的元素的数量是否相等
    && Object.keys(data).length !== Object.values(data).filter(v => v !== undefined && v !== null && v !== '').length
);

/**
 * 通过SWR获取数据
 *
 * @param {string} name - 表示要获取的数据名称。
 * @param {object} params - 表示获取请求的参数，默认为空对象。
 * @param {function} fetch - 表示用于获取数据的函数。
 * @param {boolean} cancel - 示取消获取请求的标志，默认为 false
 * @return {object} 返回一个对象，包含获取到的数据、错误信息、加载状态以及重新加载数据的函数。
 */
const fetchData = (name, params = {}, fetch, cancel = false, config = {}) => {
  const remove = unCompleteInspection(params) || cancel;

  const { data, error, mutate, isLoading, isValidating } = useSWR(
    remove ? null : [name, params], // `/api/${name}&params=${JSON.stringify(params || {})}`
    () => (params ? fetch(params) : fetch()),
    { revalidateOnFocus: false, ...config }
  );
  return {
    result: data,
    error,
    isLoading: isLoading || isValidating,
    reload: mutate,
  };
  // mutate(); // 重新验证当前缓存键的数据
  // mutate(newData); // 更新缓存数据，并重新验证
  // mutate(newData, false); // 仅更新缓存数据，不重新验证
};
export default fetchData;
