export let nodeDataInfo: any = {};

export const setNodeDataInfo = (info) => {
  nodeDataInfo = info;
};

export let nodeConfigInfo: any = {};

export const setNodeConfigInfo = (info) => {
  nodeConfigInfo = info;
};

export let nodeOriginInfo: any = {};

export const setNodeOriginInfo = (info) => {
  nodeOriginInfo = info;
};

export let taskSummaryInfo: any = {};

export const setTaskSummaryInfo = (info) => {
  taskSummaryInfo = info;
};

export let subDataOrigin = [];

export const setSubDataOrigin = (info) => {
  subDataOrigin = info;
};

export let productDiagramList = [];

export const setProductDiagramList = (info) => {
  productDiagramList = info;
};

export let archInfo: any = {};
export const setArchInfo = (info) => {
  archInfo = info;
};

export let isOpenSetting: any = false;
export const setIsOpenSetting = (info) => {
  isOpenSetting = info;
};

export let isRefreshArea: boolean = false;
export const setIsRefreshArea = (info) => {
  isRefreshArea = info;
};

export let nodeLoadRiskInfos: any[] = [];
export const setNodeLoadRiskInfos = (info) => {
  nodeLoadRiskInfos = info;
};
