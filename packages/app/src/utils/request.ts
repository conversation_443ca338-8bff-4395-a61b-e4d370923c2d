/* eslint-disable no-param-reassign */
import axios, { AxiosResponse } from 'axios';   // 引入 axios
import { message, notification } from '@tencent/tea-component';
import { getUrlParamFromLocation, copyTextToClipboard, rejectError } from '@src/utils/common';
import { getStorage, setStorage } from '@src/utils/storage';
import { t } from '@tea/app/i18n';

interface ApiError {
  Message: string;
  Code: string | number;
}

interface ServerResponse<T = any> {
  Response: ApiResponse<T>;
}

export type ApiResponse<T = any> = T & {
  Error?: ApiError;
}

// eslint-disable-next-line
// @ts-ignore
const { source, signature, timestamp /* env */ } = getUrlParamFromLocation(['source', 'signature', 'timestamp', 'env'], location);

// eslint-disable-next-line import/no-mutable-exports
let baseURL = '';
let appId = '';
const { domain } = document;
baseURL = `${window.location.origin}/1`;

if (domain.startsWith('isa-intl')) {
  setStorage('site', 'sinapore');
} else {
  setStorage('site', 'china');
}

console.log(t('环境'), process.env.NODE_ENV);
console.log(t('站点'), getStorage('site') || 'china');

const header = {
  'X-Request-Signature': signature,
  'X-Request-Timestamp': timestamp,
  'X-Request-Source': source,
};
// create an axios instance   创建axios实例
const service = axios.create({
  baseURL, // api 的 base_url
  timeout: 20 * 1000, // request timeout  设置请求超时时间
  withCredentials: true, // 是否允许带cookie这些
  headers: source === '' ? header : {},
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    appId = location.search?.split('appid')?.[1]?.split('&')?.[0]?.split('=')?.[1] || appId;
    config.data.Language = domain.startsWith('isa-intl') ? 'en-US' : 'zh-CN';
    config.data.AppId = Number(appId);
    config.data.OnlyData = true;
    config.data.ShowError = true;
    // 在发送请求之前做什么
    if (config.method === 'post') {
      // TODO: 分请求方式做参数处理

    } else {

    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么，自己定义
    message.error({ content: t('接口请求拦截出错！') });
    return Promise.reject(error);
  }
);

// response 拦截器配置 copy from 巡检插件
service.interceptors.response.use(
  (response: AxiosResponse<ServerResponse>) => {
    if (response.data?.Response?.Error) {
      const { Code: title, Message: description } = response?.data?.Response?.Error ?? {};
      const { data } = response?.config ?? {};
      try {
        const parseData = JSON.parse(data);
        // 资源绑定接口错误特殊处理（统一处理业务错误提示）
        if (title !== 'ResourceNotFound') {
          notification.error({
            title: parseData?.Action,
            extra: title,
            description,
            footer: t('复制requestId'),
            onFooterClick: () => {
              if (response?.data?.Response?.RequestId) {
                copyTextToClipboard(response?.data?.Response?.RequestId);
              }
            },
            duration: 5000,
          });
        }
      } catch (e) {
        console.error(e.message);
      }
    }
    // 返回业务数据【Axios 拦截器中的返回值会被自动包装成 Promise】
    return response?.data?.Response;
  },

  (error) => {
    const showError = error?.config?.headers?.showError ?? true;
    if (!showError) {
      return false;
    }
    if (error?.response) {
      switch (error?.response?.status) {
        case 401: {
          return rejectError(t('未登录或登录态已过期，点击“确定”马上刷新页面重新登录，点击“取消”稍后手动刷新'));
        }
        case 403: {
          const err = t('暂无权限');
          return rejectError(err);
        }
        default: {
          return rejectError(t('请求失败'));
        }
      }
    } else if (error?.request || error?.message) {
      // 返回非业务错误数据：客户端错误，服务端错误，网络错误等（并统一处理错误信息提示）
      return rejectError(`request = ${error?.request}, message = ${error?.message}`,);
    }
    // 返回其他错误数据
    return rejectError(error?.config);
  }
);
export { baseURL };
export default service;
