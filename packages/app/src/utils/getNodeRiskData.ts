import { nodeDataInfo } from '@src/utils/caching';

/**
 * 给定节点数据来源，返回节点风险数据。
 * @param {Object} nodeDataOrigin - 节点数据来源。
 * @returns {Array} 节点风险数据。
 */
export const getNodeRiskData = (nodeDataOrigin) => {
  const list = [];
  nodeDataInfo.ArchNodeList.forEach((el) => {
    nodeDataOrigin.NodeRiskCountList?.forEach((item) => {
      if (el.DiagramId === item.NodeUuid) {
        list.push({
          ...item,
          key: el.DiagramId,
        });
      }
    });
  });
  if (nodeDataOrigin.NodeRiskCountList === null || nodeDataOrigin.NodeRiskCountList.length === 0) {
    list[0] = 'no';
  }
  return list;
};
