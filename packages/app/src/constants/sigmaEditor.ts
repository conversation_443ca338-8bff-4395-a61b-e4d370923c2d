export enum ShapeTypeEnum {
  SIGMA_RECTANGLE_SHAPE = 'SIGMA_RECTANGLE_SHAPE',
  SIGMA_BLOCK_SHAPE = 'SIGMA_BLOCK_SHAPE',
  SIGMA_TEXTLABEL_SHAPE = 'SIGMA_TEXTLABEL_SHAPE',
  SIGMA_ICON_SHAPE = 'SIGMA_ICON_SHAPE',
  SIGMA_IMAGE_SHAPE = 'SIGMA_IMAGE_SHAPE',
  SIGMA_AREA_SHAPE = 'SIGMA_AREA_SHAPE',
  SIGMA_AUTOSCALING_SHAPE = 'SIGMA_AUTOSCALING_SHAPE',
  SIGMA_AVAILABILITYZONE_SHAPE = 'SIGMA_AVAILABILITYZONE_SHAPE',
  SIGMA_PRODUCT_SHAPE = 'SIGMA_PRODUCT_SHAPE',
  SIGMA_LINE_SHAPE = 'SIGMA_LINE_SHAPE',
  SIGMA_CIRCLE_SHAPE = 'SIGMA_CIRCLE_SHAPE',
  SIGMA_SECURITY_GROUP_SHAPE = 'SIGMA_SECURITY_GROUP_SHAPE',
  SIGMA_SUBNET_SHAPE = 'SIGMA_SUBNET_SHAPE',
  SIGMA_VPC_SHAPE = 'SIGMA_VPC_SHAPE',
  SIGMA_GROUP_SHAPE = 'SIGMA_GROUP_SHAPE',
  SIGMA_SECURITY_SHAPE = 'SIGMA_SECURITY_SHAPE',
  SIGMA_BASE_GROUP_SHAPE = 'SIGMA_BASE_GROUP_SHAPE',
  SIGMA_REMARK_NOTE_SHAPE = 'SIGMA_REMARK_NOTE_SHAPE',
  SIGMA_CCN_SHAPE = 'SIGMA_CCN_SHAPE',
  SIGMA_TKE_SHAPE = 'SIGMA_TKE_SHAPE',
}
