.grey {
  opacity: 0.4;
}
.predictionContainer {
  :global {
    .tea-capacity-icon-arrowup, .tea-icon-arrowup {
      transform: rotate(180deg);
    }
  }
}

.loading {
  min-height: 200px;
}
.deleteIcon {
  position: absolute;
  right: 4px;
  &:hover {
    cursor: pointer;
  }
}
.predication-icon-wrap {
  position: relative;
  margin-left: -13px;
  & > div {
    display: flex;
    align-items: center;
    height: 26px;
    background: rgba(244, 223, 225, 0.8);
    border-radius: 20px;
    padding: 3px;
    min-width: 60px;
    span {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: rgba(229, 69, 69, 1);
      padding-left: 10px;
      padding-right: 10px;
    }
  }
}
.cancelDescribe {
  color: rgba(0, 0, 0, 0.60);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  display: inline-block;
  margin-left: 10px;
}
.dialog {
  position: absolute;
  position: fixed;
  width: 500px;
  height: 100%;
  background: rgba(0, 0, 0, 0.20);
  height: 100%;
  right: 0;
  top: 0;
  z-index: 3;
}
.block {
  margin-top: 15px;
  .table {
    background-color: #fff;
    :global {
      th {
        position: relative;
        padding: 10px;
        border-bottom: 1px solid #e7eaef;
      }
      td {
        padding-left: 10px;
        padding-right: 10px;
      }
    }
  }
  .hover {
    cursor: pointer;
  }
  .photoName {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
  .link {
    color: #006EFF;
    &:hover {
      cursor: pointer;
    }
  }
  .segment {
    :global {
      button {
        background-color: transparent !important;
      }
    }
  }
  .risk {
    .riskItem {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      span {
        &:nth-child(1) {
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          color: #000;
        }
        &:nth-child(2) {
          font-family: PingFang SC;
          font-size: 10px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          color: #888;
        }
      }
    }
  }
  .header {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
  }
  .extra {
    display: flex;
    justify-content: space-between;
    span {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      span {
        color: #888;
        margin-right: 25px;
      }
    }
  }
  .row {
    display: flex;
    align-items: center;
    :global {
      input {
        &::placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &::-webkit-input-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &:-moz-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &::-moz-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &:-ms-input-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
      }
      .tea-input {
        width: 100%;
        text-align: left;
      }
    }
    &:not(:first-child) {
      margin-top: 12px;
    }
    & > p {
      min-width: 90px;
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #888;
      &:nth-child(2) {
        color: #000000
      }
    }
  }
}
.tCollapseTitle {
  display: flex;
  align-items: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  span:nth-child(1) {
    color: #888;
  }
  .tStopPosition {
    display: flex;
    align-items: center;
    background-color: #fff;
    margin-left: 8px;
    justify-content: center;
    padding-right: 7px;
    padding-left: 7px;
    color: #006EFF;
    border: 1px solid #006EFF;
    box-sizing: border-box;
    height: 23px;
    overflow: hidden;
    transform: scale(0.9);
  }
  .tPosition {
    height: 23px;
    overflow: hidden;
    display: flex;
    align-items: center;
    background-color: #006EFF;
    margin-left: 8px;
    justify-content: center;
    padding-right: 7px;
    color: #fff;
    transform: scale(0.9);
    img {
      margin-right: 5px;
      margin-left: 5px !important;
    }
  }
}
.tabs {
  :global {
    .tea-tabs__tabpanel {
      background-color: #F3F4F7;
    }
  }
}
.tCollapse {
  border-radius: 6px;
  :global {
    .t-collapse-panel__body {
      background-color: #fff;
    }
    .t-collapse-panel__content {
      padding: 0 !important;
    }
    .t-collapse-panel__header {
      padding: 9px;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
  }
}
.collapse {
  :global {
    .tea-accordion {
      margin-top: 8px;
    }
    .tea-accordion__header-title {
      color: #000;
      font-weight: 500;
      margin-left: 6px;
    }
  }
}
.panel {
  box-sizing: border-box;
  width: 404px;
  margin-top: 12px;
  border-radius: 5px;
  width: 100%;
  background: #f3f4f7;
  padding: 12px;
  margin-left: 5px;
  margin-right: 5px;
  .risk {
    margin-top: 12px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
    dl {
      dt {
        margin-top: 12px;
        img {
          margin-right: 12px;
        }
      }
      dd {
        margin-top: 5px;
      }
      .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
        p {
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          color: #888;
        }
        .expand {
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          color: #006EFF;
          margin-left: 5px;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
    & > p {
      margin-top: 12px;
      display: flex;
      align-items: center;
      img {
        margin-right: 6px;
      }
    }
  }
}
.footer {
  position: absolute;
  background-color: #fff;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 45px;
  border-top: 1px solid #e7eaef;
  margin-left: -20px;
  margin-right: -20px;
  z-index: 2;
}
.progress {
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    & > div {
      span {
        position: relative;
        top: -5px;
        left: 5px;
      }
    }
    & > p {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      line-height: 16.8px;
      text-align: right;
      color: #888;
    }
  }
}
.skeleton {
  margin-top: 12px;
  :global {
    .t-skeleton__col {
      background-color: #d9dfe7;
    }
  }
}

.alert {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

.back {
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 600;
  text-align: left;
  color: #000;
  img {
    margin-right: 15px;
    &:hover {
      cursor: pointer;
    }
  }
}