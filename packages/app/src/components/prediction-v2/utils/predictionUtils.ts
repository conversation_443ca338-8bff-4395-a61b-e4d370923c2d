import { ShapeTypeEnum } from '../../../constants/sigmaEditor';

/**
 * 获取产品列表
 * @param {any} arInfo - 架构信息
 * @returns {string[]} 产品列表
 */
export const getProducts = (arInfo: any): string[] => {
  const nodes = arInfo?.getArchNodes?.() ?? {};
  return Object.keys(nodes).filter((v: string) => nodes[v].type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE);
};

/**
 * 定位处理函数
 * @param {any[]} data - 数据
 * @param {any} archInfo - 架构信息
 * @param {string[]} products - 产品列表
 * @param {any} s - 样式对象
 */
export const handlePosition = (data: any[], archInfo: any, products: string[], s: any) => {
  const res = data || [];
  archInfo.addNodeClass(products, s.grey);
  res.forEach((e: any) => {
    e?.PredictionNodeIdInfos?.forEach((v: any) => {
      if (v?.position) {
        // 完全透明度
        archInfo.removeNodeClass(v?.NodeUuid, s.grey);
      } else {
        // 降低透明度
        archInfo.addNodeClass([v?.NodeUuid], s.grey);
      }
    });
  });
  const noPosNode = res.every((v: any) => v?.PredictionNodeIdInfos?.every((v: any) => !v?.position));
  if (noPosNode) {
    archInfo.removeNodeClass(products, s.grey);
  }
};
