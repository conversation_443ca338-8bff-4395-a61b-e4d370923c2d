.block {
  margin-top: 15px;
  .header {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
  }
  .row {
    display: flex;
    align-items: center;
    & > p {
      min-width: 90px;
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #888;
      &:nth-child(2) {
        color: #000000
      }
    }
  }
}

.tCollapseTitle {
  display: flex;
  align-items: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  span:nth-child(1) {
    color: #888;
  }
  .tStopPosition {
    display: flex;
    align-items: center;
    background-color: #fff;
    margin-left: 8px;
    justify-content: center;
    padding-right: 7px;
    padding-left: 7px;
    color: #006EFF;
    border: 1px solid #006EFF;
    box-sizing: border-box;
    height: 23px;
    overflow: hidden;
    transform: scale(0.9);
  }
  .tPosition {
    height: 23px;
    overflow: hidden;
    display: flex;
    align-items: center;
    background-color: #006EFF;
    margin-left: 8px;
    justify-content: center;
    padding-right: 7px;
    color: #fff;
    transform: scale(0.9);
    img {
      margin-right: 5px;
      margin-left: 5px !important;
    }
  }
}

.tabs {
  :global {
    .tea-tabs__tabpanel {
      background-color: #F3F4F7;
    }
  }
}

.tCollapse {
  border-radius: 6px;
  :global {
    .t-collapse-panel__body {
      background-color: #fff;
    }
    .t-collapse-panel__content {
      padding: 0 !important;
    }
    .t-collapse-panel__header {
      padding: 9px;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
  }
}

.collapse {
  :global {
    .tea-accordion {
      margin-top: 8px;
    }
    .tea-accordion__header-title {
      color: #000;
      font-weight: 500;
      margin-left: 6px;
    }
  }
}
