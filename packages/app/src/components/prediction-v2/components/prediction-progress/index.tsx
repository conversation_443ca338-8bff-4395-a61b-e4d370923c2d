import React from 'react';
import { Progress } from '@tencent/tea-component';
import { Skeleton } from 'tdesign-react';
import { t } from '@tea/app/i18n';
import analyze from '@src/statics/svg/analyze.svg';
import done from '@src/statics/svg/done.svg';
import s from './index.module.scss';

interface IProps {
  predictionState: string;
  predictionResult: Record<string, any>;
}

/**
 * 预测进度组件
 * @param {string} predictionState - 预测状态 ('analyzing' | 'done')
 * @param {Record<string, any>} predictionResult - 预测结果数据
 * @returns React.ReactElement
 */
export default function PredictionProgress(props: IProps): React.ReactElement {
  const { predictionState, predictionResult } = props;

  if (predictionState === 'analyzing') {
    return (
      <>
        <div className={s.progress}>
          <div className={s.flex}>
            <div>
              <img src={analyze} width={48} />
              <span>{t('分析中...')}</span>
            </div>
            <p>
              {predictionResult?.DoneCount ?? '-'}/
              {predictionResult?.TotalCount ?? '-'}
            </p>
          </div>
          <Progress
            percent={
              ((predictionResult?.DoneCount ?? 0)
                / (predictionResult?.TotalCount ?? 100))
              * 100
            }
            strokeColor="#1888FA"
            strokeWidth={6}
          />
        </div>
        <div className={s.block}>
          <p className={s.header}>{t('查看结果')}</p>
          <div style={{ marginTop: 10 }}>
            <Skeleton theme={'paragraph'} animation="flashed"></Skeleton>
            <Skeleton style={{ marginTop: 30 }} theme={'paragraph'} animation="flashed"></Skeleton>
          </div>
        </div>
      </>
    );
  }

  if (predictionState === 'done') {
    return (
      <div className={s.progress}>
        <div className={s.flex}>
          <div>
            <img src={done} width={32} />
            <span>
              {t('分析完成，发现 ')} {predictionResult?.RiskCount}{' '}
              {t(' 个风险实例')}
            </span>
          </div>
          <p>
            {predictionResult?.DoneCount ?? '-'}/
            {predictionResult?.TotalCount ?? '-'}
          </p>
        </div>
        <Progress percent={100} strokeColor="#1888FA" strokeWidth={6} />
      </div>
    );
  }

  return null;
}
