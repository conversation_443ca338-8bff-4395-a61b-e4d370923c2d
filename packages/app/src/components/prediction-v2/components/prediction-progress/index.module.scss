.progress {
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    & > div {
      span {
        position: relative;
        top: -5px;
        left: 5px;
      }
    }
    & > p {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      line-height: 16.8px;
      text-align: right;
      color: #888;
    }
  }
}

.block {
  margin-top: 15px;
  .header {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
  }
}

.skeleton {
  margin-top: 12px;
  :global {
    .t-skeleton__col {
      background-color: #d9dfe7;
    }
  }
}
