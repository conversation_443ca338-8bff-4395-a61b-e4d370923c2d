.container {
  position: relative;
  width: 90px;
  height: 12px;
}

/* 中间的矩形 */
.middle-rect {
  position: absolute;
  left: 4.5px;
  top: 0px;
  width: 81px;
  height: 3px;
  background-color: #006EFF;
  border: 1px solid #006EFF;
}

/* 左侧的形状 */
.left-shape {
  position: absolute;
  left: 1px;
  top: 0px;
  width: 4px;
  height: 12px;
  background-color: #006EFF;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 58.33%);
}

/* 右侧的形状 */
.right-shape {
  position: absolute;
  right: 0;
  top: 0;
  width: 4px;
  height: 12px;
  background-color: #006EFF;
  clip-path: polygon(0 0, 100% 0, 100% 58.33%, 0 100%);
}