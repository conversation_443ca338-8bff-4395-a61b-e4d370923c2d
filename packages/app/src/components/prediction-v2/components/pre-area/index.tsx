/* eslint-disable  */
import React, { useState, useEffect } from 'react';
import s from './index.module.scss';

interface IProps {
  prop1?: any;
  prop2?: (data?: any) => void;
}

/**
 * preArea 组件
 * @param {any} prop1 - xx属性
 * @param {function} prop2 - xx方法
 * @returns React.ReactElement
 */
export default function PreArea(props: IProps): React.ReactElement {
  const { prop1, prop2 = () => {} } = props;
  const [data, setData] = useState();

  useEffect(() => {
    //
  }, []);

  return (
    <div className={s.container}>
        <div className={s['dashed-line']}></div>
        <div className={s['left-shape']}></div>
        <div className={s['right-shape']}></div>
    </div>
  );
}
