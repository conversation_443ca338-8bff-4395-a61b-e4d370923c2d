.container {
  position: relative;
  width: 132px;
  height: 12px;
}

/* 中间的虚线 */
.dashed-line {
  position: absolute;
  left: 4px;
  right: 4px;
  top: 8px; /* 10px - 2px (half of stroke-width) */
  height: 4px;
  background: linear-gradient(to right, #6E829D 50%, transparent 50%);
  background-size: 16px 100%; /* 8px + 8px for dash pattern */
  background-repeat: repeat-x;
}

/* 左侧的形状 */
.left-shape {
  position: absolute;
  left: 0;
  width: 4px;
  height: 12px;
  background-color: #6E829D;
  clip-path: polygon(0 41.67%, 100% 0, 100% 100%, 0 100%);
}

/* 右侧的形状 */
.right-shape {
  position: absolute;
  right: 0;
  width: 4px;
  height: 12px;
  background-color: #6E829D;
  clip-path: polygon(0 0, 100% 41.67%, 100% 100%, 0 100%);
}