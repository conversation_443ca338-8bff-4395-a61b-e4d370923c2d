import React from 'react';
import { Select, InputNumber, Bubble } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import info from '@src/statics/svg/info.svg';
import s from './index.module.scss';

interface IProps {
  globalSetting: Record<string, any>;
  setGlobalSetting: (setting: Record<string, any>) => void;
  periodTypeInfosOpts: Array<{ value: any; text: string }>;
  algorithmInfosOpts: Array<{ value: any; text: string }>;
  algorithmTypeMap: Record<string, string>;
}

/**
 * 全局设置组件
 * @param {IProps} props - 组件属性
 * @returns React.ReactElement
 */
export default function GlobalSettings(props: IProps): React.ReactElement {
  const {
    globalSetting,
    setGlobalSetting,
    periodTypeInfosOpts,
    algorithmInfosOpts,
    algorithmTypeMap,
  } = props;

  return (
    <div className={s.block}>
      <p className={s.header}>{t('全局设置')}</p>
      <div className={s.row}>
        <p>{t('参考周期')}</p>
        <Select
          matchButtonWidth
          appearance="button"
          style={{ width: 110 }}
          options={periodTypeInfosOpts}
          value={globalSetting?.PeriodValue}
          onChange={(value) => {
            setGlobalSetting({ ...globalSetting, PeriodValue: value });
          }}
          placeholder={t('请选择')}
        />
      </div>

      <div className={s.row}>
        <p>{t('计算方法')}</p>
        <Select
          appearance="button"
          matchButtonWidth
          value={globalSetting.AlgorithmType}
          style={{ width: 110 }}
          options={algorithmInfosOpts}
          onChange={(value) => {
            setGlobalSetting({
              ...globalSetting,
              AlgorithmType: value,
            });
          }}
          placeholder={t('请选择')}
        />
        {algorithmTypeMap[globalSetting.AlgorithmType] ? (
          <Bubble
            placement="top"
            trigger="hover"
            dark
            content={algorithmTypeMap[globalSetting.AlgorithmType]}
          >
            <img
              style={{ marginLeft: 15 }}
              src={info}
              alt=""
              className={s.info}
            />
          </Bubble>
        ) : null}
      </div>
      <div className={s.row}>
        <p>{t('放量倍数')}</p>
        <InputNumber
          hideButton
          style={{ width: 110 }}
          value={globalSetting?.ResourceTimes}
          onChange={(value) => {
            if (!isNaN(value as number) && value !== undefined) {
              setGlobalSetting({
                ...globalSetting,
                ResourceTimes: value,
              });
            }
          }}
          min={1}
          max={100}
        />
      </div>
    </div>
  );
}
