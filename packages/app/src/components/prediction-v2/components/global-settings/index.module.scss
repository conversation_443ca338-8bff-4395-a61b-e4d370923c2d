.block {
  margin-top: 15px;
  .header {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
  }
  .row {
    display: flex;
    align-items: center;
    :global {
      input {
        &::placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &::-webkit-input-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &:-moz-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &::-moz-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &:-ms-input-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
      }
      .tea-input {
        width: 100%;
        text-align: left;
      }
    }
    &:not(:first-child) {
      margin-top: 12px;
    }
    & > p {
      min-width: 90px;
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #888;
      &:nth-child(2) {
        color: #000000
      }
    }
  }
}

.info {
  cursor: pointer;
}
