.bar1 {
  // hack fix slider左边圆角问题
  :global {
    .rc-slider-track-1 {
      border-top-left-radius: 6px !important;  /* 左上角圆角 */
      border-bottom-left-radius: 6px !important;  /* 左下角圆角 */
      border-top-right-radius: 0 !important;  /* 右上角无圆角 */
      border-bottom-right-radius: 0 !important;  /* 右下角无圆角 */
    }
  }
}
.bar2 {
  :global {
    .rc-slider-track-2 {
      border-top-left-radius: 6px !important;  /* 左上角圆角 */
      border-bottom-left-radius: 6px !important;  /* 左下角圆角 */
      border-top-right-radius: 0 !important;  /* 右上角无圆角 */
      border-bottom-right-radius: 0 !important;  /* 右下角无圆角 */
    }
  }
}
.bar3 {
  :global {
    .rc-slider-track-3 {
      border-top-left-radius: 6px !important;  /* 左上角圆角 */
      border-bottom-left-radius: 6px !important;  /* 左下角圆角 */
      border-top-right-radius: 0 !important;  /* 右上角无圆角 */
      border-bottom-right-radius: 0 !important;  /* 右下角无圆角 */
    }
  }
}
.bar4 {
  :global {
    .rc-slider-track-4 {
      border-top-left-radius: 6px !important;  /* 左上角圆角 */
      border-bottom-left-radius: 6px !important;  /* 左下角圆角 */
    }
  }
}
.container {
  padding: 12px;
  background: #F3F4F7;
  margin-top: 12px;
}
.pre {
  span {
    width: 1px;
    height: 6px;
    position: absolute;
    display: inline-block;
  }
  span:nth-child(1) {
    background-color: #6E829D;
    left: -0px;
    top: -6px;
  }
  span:nth-child(2) {
    background-color: #6E829D;
    right: -0px;
    top: -6px;
  }
}
.none {
  span {
    display: none !important;
  }
}
.base {
  span {
    width: 1px;
    height: 6px;
    position: absolute;
    display: inline-block;
  }
  span:nth-child(1) {
    background-color: #006EFF;
    left: -0px;
    top: 4px;
  }
  span:nth-child(2) {
    background-color: #006EFF;
    right: -0px;
    top: 4px;
  }
}
.indicator {
  font-family: "PingFang SC";
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  margin-top: 15px;
  p {
    display: flex;
    align-items: center;
  }
  .bar {
    width: 12px;
    height: 4px;
    flex-shrink: 0;
    display: inline-block;
    margin-right: 5px;
  }
  .baseBar {
    background-color: #006EFF;
  }
  .preBar {
    background-color: #6E829D;
  }

}
.tCollapse {
  border-radius: 0px;
  :global {
    .t-collapse-panel {
      box-shadow: 0px 2px 4px 0px rgba(54, 58, 80, 0.32);
    }
    .t-collapse-panel__body {
      background-color: #fff;
    }
    .t-collapse-panel__content {
      padding: 0 !important;
    }
    .t-collapse-panel__header {
      padding: 9px;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
  }
}
.desc {
  color: #000;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}

.collapse {
  margin-top: 10px;
  span {
    color: #0058DF;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    &:hover {
      cursor: pointer;
    }
  }
}