import React, { useCallback } from 'react';
import s from './index.module.scss';
import { Icon, message, Table } from '@tencent/tea-component';
import Slider from '@src/components/slider-v2';
import { t } from '@tea/app/i18n';
import { orderBy } from 'lodash-es';
import { reportEvent, EVENT } from '@src/utils/report';
import { CcnConfig } from '@src/components/archive-report/constants';
import { StatusColorMap as color } from '@src/constants/color';
import baseStartBlue from '@src/statics/svg/base-start-blue.svg';
import baseEndBlue from '@src/statics/svg/base-end-blue.svg';
import preStartGrey from '@src/statics/svg/pre-start-grey.svg';
import preEndGrey from '@src/statics/svg/pre-end-grey.svg';

import { SegViewType, periodValueType as period } from '@src/constants';

const { pageable } = Table.addons;
interface IProps {
  periodValueType: typeof period;
  metric: Record<string, string | number>;
  algorithmTypeMap: Record<string, string>;
  statusColor: typeof color;
  expand: Record<string, boolean>;
  setExpand: (data) => void;
  data: Record<string, any>;
  segmentState: SegViewType;
}
/**
 * 复制文本
 * @param {string} str 待复制的文本
 * @returns {string | Promise<void>}
 */
const copyText = async (str) => {
  const copyFn = () => {
    const elm = document.createElement('textarea');
    elm.value = str;
    elm.setAttribute('readonly', '');
    elm.style.position = 'absolute';
    elm.style.left = '-9999px';
    document.body.appendChild(elm);
    const selected =      document.getSelection().rangeCount > 0
      ? document.getSelection().getRangeAt(0)
      : false;
    elm.select();
    document.execCommand('copy');
    document.body.removeChild(elm);
    if (selected) {
      document.getSelection().removeAllRanges();
      document.getSelection().addRange(selected);
    }
  };
  if (navigator.clipboard) {
    const text = str;
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      copyFn();
    }
  } else {
    copyFn();
  }
};
/**
 * detailCard组件
 * @param {any} prop1 - xx属性
 * @param {function} prop2 - xx方法
 * @returns React.ReactElement
 */
export default function DetailCard(props: IProps): React.ReactElement {
  const {
    periodValueType,
    metric,
    algorithmTypeMap,
    statusColor,
    expand,
    setExpand,
    data: v,
    segmentState,
  } = props;
  const {
    BaseLowValue: baseLowValue,
    BaseHighValue: baseHighValue,
    RiskLowValue: riskLowValue,
    RiskHighValue: riskHighValue,
  } = v || {};
  const validRatio = baseLowValue >= 0 && baseHighValue >= 0;
  const computedClassName = (baseHighValue, baseLowValue) => {
    const start = parseFloat(baseHighValue);
    const end = parseFloat(baseLowValue);
    if (start - end === 0) {
      return s.none;
    }
    if (start - end < 1) {
      return s.none1;
    }
    return '';
  };

  const ordered = orderBy(
    v?.RiskInsLists?.map(e => ({
      ...e,
      RiskValue: parseFloat(e?.RiskValue ?? 0),
    })) || [],
    ['RiskLeval', 'RiskValue', 'RiskInsId'],
    ['desc', 'desc', 'asc']
  );

  const computedProp = useCallback(
    (key: string) => `${key}-${segmentState}`,
    [segmentState]
  );

  /**
   * 渲染CCN（云联网）信息的回调函数
   *
   * @param record - 包含CCN信息的JSON字符串
   * @returns 渲染后的CCN区域信息组件，格式为"源区域-目标区域"，解析失败时返回'-'
   * @remarks 处理JSON解析错误情况，确保UI稳定性
   */
  const renderCcn = useCallback((record) => {
    if (!record) {
      return '-';
    }
    try {
      const data = JSON.parse(record);
      return (
        <div className={s.ccnWrapper}>
          <p>{data?.SRegion ?? ''}</p>
          {data?.DRegion ? (
            <>
              <p>-</p>
              <p>{data.DRegion}</p>
            </>
          ) : null}
        </div>
      );
    } catch (error) {
      return '-';
    }
  }, []);
  return (
    <div style={{ padding: '5px 10px 10px 10px' }}>
      {validRatio && (
        <>
          <p className={s.desc}>
            {t('基于')}
            <strong>{periodValueType[v?.PeriodValue]}</strong>
            {t('数据统计')}，{v?.RiskItemName}
            {t('的')}
            <strong>{algorithmTypeMap[metric?.algorithmType]}</strong>
            {t('基准值为')}
            <strong>
              {baseLowValue}%-{baseHighValue}%，
            </strong>
            {riskLowValue >= 0 && riskHighValue >= 0 ? (
              <>
                {t('放量后的预测值为')}
                <strong>
                  {riskLowValue}%-{riskHighValue}%，
                </strong>
              </>
            ) : null}
            {v?.RiskInsLists.every(v => v.RiskLeval < 1) ? (
              <span>
                {t('所有实例处于')}
                <strong style={{ color: 'rgba(10, 191, 91, 1)' }}>
                  {t('正常负载')}
                </strong>
                {t('状态')}。
              </span>
            ) : (
              t('存在')
            )}
            {!!v?.RiskInsLists?.filter(v => v.RiskLeval === 3).length && (
              <>
                <strong style={{ color: statusColor[3] }}>
                  {[...new Set(v?.RiskInsLists?.filter(v => v.RiskLeval === 3).map(item => item.RiskInsId))].length}
                </strong>
                {t('个实例处于')}
                <strong style={{ color: statusColor[3] }}>
                  {t('严重高负载')}
                </strong>
                {t('状态')}，
              </>
            )}
            {!!v?.RiskInsLists?.filter(v => v.RiskLeval === 2).length && (
              <>
                <strong style={{ color: statusColor[2] }}>
                  {[...new Set(v?.RiskInsLists?.filter(v => v.RiskLeval === 2).map(item => item.RiskInsId))].length}
                </strong>
                {t('个实例处于')}
                <strong style={{ color: statusColor[2] }}>{t('高负载')}</strong>
                {t('状态')}，
              </>
            )}
            {!!v?.RiskInsLists?.filter(v => v.RiskLeval === 1).length && (
              <>
                <strong style={{ color: statusColor[0] }}>
                  {[...new Set(v?.RiskInsLists?.filter(v => v.RiskLeval === 1).map(item => item.RiskInsId))].length}
                </strong>
                {t('个实例处于')}
                <strong style={{ color: statusColor[0] }}>
                  {t('未充分使用')}
                </strong>
                {t('状态')}，
              </>
            )}
          </p>
          <div style={{ position: 'relative', paddingTop: 15 }}>
            <div
              className={`${s.base} ${computedClassName(v?.BaseHighValue, v?.BaseLowValue)}`}
              style={{
                width: `calc(${
                  // eslint-disable-next-line max-len
                  parseFloat(v?.BaseHighValue) >= 100
                    ? 100 - parseFloat(v?.BaseLowValue)
                    : parseFloat(v?.BaseHighValue) - parseFloat(v?.BaseLowValue)
                }% + 8px)`,
                height: 4,
                backgroundColor: '#006EFF',
                position: 'absolute',
                top: 3,
                left: `calc(${parseFloat(v?.BaseLowValue >= 100 ? 100 : v?.BaseLowValue)}% - 4px)`,
              }}
            >
              <span>
                <img src={baseStartBlue} />
              </span>
              <span>
                <img src={baseEndBlue} />
              </span>
            </div>
            <div
              className={`${s.pre} ${computedClassName(v?.RiskHighValue, v?.RiskLowValue)}`}
              style={{
                width: `calc(${
                  // eslint-disable-next-line max-len
                  v?.RiskHighValue >= 100
                    ? 100 - parseFloat(v?.RiskLowValue)
                    : parseFloat(v?.RiskHighValue) - parseFloat(v?.RiskLowValue)
                }% + 8px)`,
                height: 4,
                // backgroundImage:
                //   'linear-gradient(to right, #6E829D 50%, transparent 50%)',
                background: `linear-gradient(to right, rgb(110, 130, 157) 0%, rgb(110, 130, 157) 5%, transparent 5%),
                    linear-gradient(to right, transparent calc(100% - 5%), rgb(110, 130, 157) calc(100% - 5%)),
                    linear-gradient(to right, rgb(110, 130, 157) 50%, transparent 50%`,
                backgroundSize: '10px 4px',
                position: 'absolute',
                top: 37,
                left: `calc(${parseFloat(v?.RiskLowValue >= 100 ? 100 : v?.RiskLowValue)}% - 4px)`,
              }}
            >
              <span>
                <img src={preStartGrey} />
              </span>
              <span>
                <img src={preEndGrey} />
              </span>
            </div>
            <div
              style={{
                marginTop: 1,
              }}
              className={
                s[
                  `bar${
                    [
                      0,
                      v?.MediumUsed || 0,
                      v?.MediumThreshold || 0,
                      v?.HighThreshold || 0,
                    ].filter(v => v === 0).length
                  }`
                ]
              }
            >
              <Slider
                value={[
                  0,
                  v?.MediumUsed || 0,
                  v?.MediumThreshold || 0,
                  v?.HighThreshold || 0,
                ]}
                disabled
                handleStyle={{
                  opacity: 0,
                }}
                showMarks={true}
                marksConfig={{
                  0: {
                    style: {
                      width: '35px',
                      color: '#fff',
                      marginLeft: '9px',
                      display: 'none',
                    },
                    label: '0%',
                  },
                  100: {
                    style: {
                      width: '35px',
                      color: '#fff',
                      marginLeft: '-16px',
                      display: 'none',
                    },
                    label: '100%',
                  },
                }}
              />
            </div>
            <div className={s.indicator}>
              <p>
                <span className={`${s.bar} ${s.baseBar}`}></span>
                {t('基准值')}
              </p>
              <p
                style={{
                  marginLeft: 15,
                }}
              >
                <span className={`${s.bar} ${s.preBar}`}></span>
                {t('预测值')}
              </p>
            </div>
          </div>
        </>
      )}
      {!validRatio && (
        <div style={{ padding: '5px 10px 10px 10px' }}>{t('暂无数据')}</div>
      )}
      <div className={s.collapse}>
        <span
          onClick={() => {
            reportEvent({
              key: EVENT.CHECK_PREDICTED_INSTANCE,
              extraInfo: null,
            });
            setExpand(last => ({
              ...last,
              [computedProp(v?.RiskItemName)]:
                !last?.[computedProp(v?.RiskItemName)],
            }));
          }}
        >
          {!expand[computedProp(v?.RiskItemName)]
            ? t('查看所有实例（{{attr0}}）', {
              attr0: [...new Set(v?.RiskInsLists?.map(item => item.RiskInsId))].length || 0,
            })
            : t('收起')}
        </span>
      </div>
      <div>
        {expand[computedProp(v?.RiskItemName)] && (
          <Table
            compact
            verticalTop
            style={{ marginTop: 10 }}
            records={ordered}
            recordKey={(record, index) => `${record.RiskInsId}-${index}`}
            className={s.table}
            columns={[
              {
                key: 'instance',
                header: t('实例名/ID'),
                width: 200,
                render: (record: any) => (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <div style={{ width: 25 }}>
                      <div
                        style={{
                          width: 8,
                          height: 8,
                          backgroundColor:
                            statusColor[
                              record?.RiskValue < 0 ? '-1' : record?.RiskLeval
                            ],
                          borderRadius: '50%',
                          marginRight: 5,
                          marginLeft: 5,
                        }}
                      />
                    </div>
                    <div>
                      <p>
                        {record?.Url ? (
                          <a
                            href={record?.Url}
                            target="_blank"
                            className={s.link}
                            rel="noreferrer"
                          >
                            {record?.RiskInsName}
                          </a>
                        ) : (
                          <span className={s.link}>{record?.RiskInsName}</span>
                        )}
                      </p>
                      <p
                        style={{
                          backgroundColor: '#F3F4F7',
                          padding: 3,
                        }}
                      >
                        {record?.RiskInsId}{' '}
                        {record?.RiskInsId ? (
                          <a>
                            <Icon
                              type="copy"
                              onClick={() => {
                                // 复制文本
                                copyText(record?.RiskInsId);
                                message.success({
                                  content: t('复制成功'),
                                });
                              }}
                            />
                          </a>
                        ) : null}
                      </p>
                    </div>
                  </div>
                ),
              },
              {
                key: 'ip',
                header: v?.RiskInsLists?.[0]?.Key || t('IP 地址'),
                render: (record: any) => (v?.RiskInsLists?.[0]?.Key !== CcnConfig.COLUMN ? (
                    <>
                      <p>{record?.Value || t('暂无')}</p>
                    </>
                ) : (
                  renderCcn(record?.Value ?? '')
                )),
              },
              {
                key: 'value',
                header: t('预测值'),
                width: 70,
                render: (record: any) => (
                  <>
                    <p style={{ marginLeft: 10 }}>
                      {record?.RiskValue < 0
                        ? t('无数据')
                        : `${record?.RiskValue}%`}
                    </p>
                  </>
                ),
              },
            ]}
            addons={[
              ...((v?.RiskInsLists || []).length > 5
                ? [
                  pageable({
                    recordCount: (v?.RiskInsLists || []).length,
                    pageSizeVisible: false,
                    stateTextVisible: false,
                    pageSize: 5,
                  }),
                ]
                : []),
            ]}
          />
        )}
      </div>
    </div>
  );
}
