import React, { useState, useEffect, useMemo } from 'react';
import { t } from '@tea/app/i18n';
import { Segment } from '@tencent/tea-component';
import { Collapse as TCollapse } from 'tdesign-react';
import { orderBy } from 'lodash-es';
import success from '@src/statics/svg/success.svg';
import fetchData from '@src/utils/fetch';
import { StatusColorMap as statusColor } from '@src/constants/color';
import { describeArchProductAlgorithmType } from '@src/api/thresholdsSetting';
import { SegViewType, periodValueType } from '@src/constants';
import Detail from './components/detail';
import s from './index.module.scss';
const { Panel } = TCollapse;

interface IProps {
  data?: any;
  metric?: any;
  style?: React.CSSProperties;
}


/**
 * DetailCard组件
 * @param {any} data - 数据
 * @param {any} metric - 指标
 * @returns React.ReactElement
 */
export default function DetailCard(props: IProps): React.ReactElement {
  const { data, metric, style = {} } = props;
  // 表格折叠
  const [expand, setExpand] = useState({});
  // 折叠组件折叠
  const [collapse, setCollapse] = useState({});

  (data?.RiskItems || []).forEach((v) => {
    // eslint-disable-next-line no-param-reassign
    v.MaxRiskValue = Math.max(...(v?.RiskInsLists?.map(item => item?.RiskLeval) || []));
    // eslint-disable-next-line no-param-reassign
    v.RiskInsLists = orderBy(v?.RiskInsLists || [], ['RiskLeval', 'RiskValue', 'RiskInsId'], ['desc', 'desc', 'asc']);
  });

  data.RiskItems = orderBy(data?.RiskItems || [], ['MaxRiskValue'], ['desc']);
  const riskPanels = data?.RiskItems?.filter(v => v?.RiskInsLists?.some(item => item.RiskLeval >= 2));

  const [segmentState, setSegmentState] = useState({
    options: [
      {
        text: `${t('需要关注')}·${riskPanels.length}`,
        value: SegViewType.RISK,
      },
      {
        text: t('全部'),
        value: SegViewType.ALL,
      },
    ],
    value: SegViewType.RISK,
  });

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  useEffect(() => {}, []);

  const { result: algorithmTypeRes } = fetchData(
    'DescribeArchProductAlgorithmType',
    null,
    describeArchProductAlgorithmType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const algorithmInfos = algorithmTypeRes?.AlgorithmInfos || [];

  const algorithmTypeMap = useMemo(() => {
    const obj = {};
    (algorithmInfos || []).forEach((v) => {
      obj[v?.AlgorithmType] = v?.AlgorithmName;
    });
    return obj;
  }, [algorithmInfos]);


  /**
   * calculateRiskLevel
   * @param {Object} data - prediction info
   * @returns {number} highest risk level
   * @description
   *  The function takes prediction info as an argument and returns the highest risk level.
   *  If all risk levels are lower than 1, it returns 0.
   */
  const calculateRiskLevel = (data) => {
    const riskLevels = data?.RiskInsLists.map(item => item.RiskLeval);

    if (riskLevels.every(level => level < 1)) {
      return 0;
    }

    const highestRiskLevel = Math.max(...riskLevels);
    return highestRiskLevel;
  };

  useEffect(() => {
    const first = data?.RiskItems?.[0];
    if (first) {
      setCollapse({
        [`${first?.RiskItemName}-${segmentState.value}`]: [1],
      });
      setExpand({});
    }
  }, [segmentState.value]);

  return (
    <div className={s.container} style={{ ...style }}>
      <div className="chartContent">
        <Segment
          // style={{ marginTop: 10 }}
          options={segmentState.options}
          value={segmentState.value}
          onChange={(value) => {
            setSegmentState(last => ({
              ...last,
              value: value as SegViewType,
            }));
          }}
        ></Segment>
        <div
          style={{
            marginTop: 10,
          }}
        >
          {segmentState.value === SegViewType.RISK && !riskPanels.length && (
            <p
              style={{
                display: 'flex',
                alignItems: 'center',
                marginTop: 15,
              }}
            >
              <img src={success} width={16} style={{ marginRight: 10 }} />
              <span style={{ fontSize: 12 }}>{t('暂未发现风险')}</span>
            </p>
          )}
          {segmentState.value === SegViewType.ALL
            && !data?.RiskItems?.length && (
              <p
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginTop: 15,
                }}
              >
                <img src={success} width={16} style={{ marginRight: 10 }} />
                <span style={{ fontSize: 12 }}>{t('暂未发现风险')}</span>
              </p>
          )}
          {(segmentState.value === SegViewType.RISK
            ? riskPanels
            : data?.RiskItems ?? []
          ).map((v, i) => (
            <TCollapse
              borderless
              expandIcon
              style={i > 0 ? { marginTop: 15 } : {}}
              key={`${v.RiskItemName}-${i}`}
              value={collapse[`${v.RiskItemName}-${segmentState.value}`]}
              expandIconPlacement={'right'}
              expandOnRowClick
              onChange={(val) => {
                setCollapse(last => ({
                  ...last,
                  [`${v.RiskItemName}-${segmentState.value}`]: val,
                }));
              }}
              className={s.tCollapse}
            >
              <Panel
                header={
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <p className={s.tCollapseTitle}>
                      <span>{v?.RiskItemName}</span>
                      {!collapse?.[`${v.RiskItemName}-${segmentState.value}`]?.length && <span
                        style={{
                          backgroundColor: statusColor[(v?.BaseLowValue < 0 || v?.BaseHighValue < 0) ? '-1' : calculateRiskLevel(v)],
                          width: 8,
                          height: 8,
                          marginLeft: 7,
                          borderRadius: '50%',
                          display: 'inline-block',
                        }}
                      ></span>}
                    </p>
                    {!collapse?.[`${v.RiskItemName}-${segmentState.value}`]?.length && (v?.BaseLowValue < 0 || v?.BaseHighValue < 0 ? null : <span>{v?.BaseLowValue}%-{v?.BaseHighValue}%</span>)}
                  </div>
                }
              >
                <Detail
                  periodValueType={periodValueType}
                  data={v}
                  metric={metric}
                  statusColor={statusColor}
                  segmentState={segmentState.value }
                  expand={expand}
                  setExpand={setExpand}
                  algorithmTypeMap={algorithmTypeMap}
                />
              </Panel>
            </TCollapse>
          ))}
        </div>
      </div>
    </div>
  );
}
