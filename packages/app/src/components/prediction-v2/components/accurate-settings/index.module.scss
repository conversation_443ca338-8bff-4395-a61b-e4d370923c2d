.grey {
  opacity: 0.4;
}

.deleteIcon {
  position: absolute;
  right: 4px;
  &:hover {
    cursor: pointer;
  }
}

.cancelDescribe {
  color: rgba(0, 0, 0, 0.60);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  display: inline-block;
  margin-left: 10px;
}

.block {
  margin-top: 15px;
  .header {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
  }
  .photoName {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
  .row {
    display: flex;
    align-items: center;
    :global {
      input {
        &::placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &::-webkit-input-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &:-moz-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &::-moz-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
        &:-ms-input-placeholder {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
        }
      }
      .tea-input {
        width: 100%;
        text-align: left;
      }
    }
    &:not(:first-child) {
      margin-top: 12px;
    }
    & > p {
      min-width: 90px;
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      color: #888;
      &:nth-child(2) {
        color: #000000
      }
    }
  }
}

.collapse {
  :global {
    .tea-accordion {
      margin-top: 8px;
    }
    .tea-accordion__header-title {
      color: #000;
      font-weight: 500;
      margin-left: 6px;
    }
  }
}

.panel {
  box-sizing: border-box;
  width: 404px;
  margin-top: 12px;
  border-radius: 5px;
  width: 100%;
  background: #f3f4f7;
  padding: 12px;
  margin-left: 5px;
  margin-right: 5px;
}

.info {
  cursor: pointer;
}
