import React, { useState } from 'react';
import pg from '@src/statics/svg/position-white.svg';
import s from './index.module.scss';

interface IProps {
  style?: React.CSSProperties;
}

/**
 * 定位图标组件
 * @param {React.CSSProperties} style - 样式
 * @returns React.ReactElement
 */
export default function PositionIcon(props: IProps): React.ReactElement {
  const { style = {} } = props;
  const [positionIcon] = useState(pg);

  return (
    <img
      className={s.hover}
      style={{ ...style }}
      onMouseOver={() => {
        // setPositionIcon(pb);
        // archInfo.addNodeClass(products, s.grey);
        // archInfo.removeNodeClass(product, s.grey);
      }}
      onMouseLeave={() => {
        // setPositionIcon(pg);
        // archInfo.removeAllNodeClass();
      }}
      src={positionIcon}
    />
  );
}
