import React from 'react';
import { Button } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface IProps {
  predictionState: string;
  predictionLoading: boolean;
  describeArchProductThresholdInfoLoading: boolean;
  inputting: boolean;
  disablePredict: boolean;
  onPredictClick: () => void;
}

/**
 * 底部操作栏组件
 * @param {string} predictionState - 预测状态
 * @param {boolean} predictionLoading - 预测加载状态
 * @param {boolean} describeArchProductThresholdInfoLoading - 阈值信息加载状态
 * @param {boolean} inputting - 输入状态
 * @param {boolean} disablePredict - 是否禁用预测
 * @param {function} onPredictClick - 预测点击回调
 * @returns React.ReactElement
 */
export default function FooterActions(props: IProps): React.ReactElement {
  const {
    predictionState,
    predictionLoading,
    describeArchProductThresholdInfoLoading,
    inputting,
    disablePredict,
    onPredictClick,
  } = props;

  return (
    <>
      <div style={{ height: 45 }}></div>
      <div className={s.footer} style={predictionState !== 'init' ? { display: 'none' } : {}}>
        <Button
          type="primary"
          loading={predictionLoading}
          disabled={
            predictionState !== 'init'
            || describeArchProductThresholdInfoLoading
            || inputting
            || disablePredict
          }
          onClick={onPredictClick}
        >
          {predictionState !== 'init' ? t('生成报告') : t('立即预测')}
        </Button>
      </div>
    </>
  );
}
