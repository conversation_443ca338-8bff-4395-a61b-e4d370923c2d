/* eslint-disable max-len */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useCallback } from 'react';
import { Alert } from '@tencent/tea-component';
import { Loading } from 'tdesign-react';
import { periodValueType } from '@src/constants';
import { reportEvent, EVENT } from '@src/utils/report';
import fetchData from '@src/utils/fetch';
import {
  describeArchProductPeriodType,
  modifyArchProductPredictionInfoV2New,
  startCapacityPredictionTaskV2New,
} from '@src/api/thresholdsSetting';
import back from '@src/statics/svg/back.svg';
import { t } from '@tea/app/i18n';
import { archInfo } from '@src/utils/caching';

// 导入子组件
import GlobalSettings from './components/global-settings';
import AccurateSettings from './components/accurate-settings';
import PredictionProgress from './components/prediction-progress';
import PredictionResults from './components/prediction-results';
import FooterActions from './components/footer-actions';
import PositionIcon from './components/position-icon';

// 导入hooks和工具函数
import { usePredictionData } from './hooks/usePredictionData';
import { getProducts, handlePosition } from './utils/predictionUtils';

import s from './index.module.scss';
interface IProps {
  arInfo?: any;
  nodeInfo?: any;
}

/**
 * 预测抽屉组件
 * @param {IProps} props - 组件属性
 * @returns React.ReactElement
 */
export default function Prediction(props: IProps): React.ReactElement {
  const { arInfo /* , nodeInfo*/ } = props;

  const [taskId, setTaskId] = useState('');
  const [inputting] = useState(false);

  // 架构图id
  const MapId = arInfo?.archInfo?.archId || '';

  // 使用自定义Hook管理预测数据
  const {
    globalSetting,
    setGlobalSetting,
    predictionResult,
    setPredictionResult,
    accurateSettings,
    setAccurateSettings,
    defaultActiveIds,
    doneDefaultActiveIds,

    dialogVisible,
    setDialogVisible,
    prediction,
    setPrediction,
    initPrediction,
    describeArchProductThresholdInfoLoading,

    algorithmInfosOpts,
    algorithmTypeMap,
    onDelete,
    disablePredict,
  } = usePredictionData(MapId, taskId, arInfo);

  // 获取时间
  const { result: result3 } = fetchData(
    'DescribeArchProductPeriodType',
    null,
    describeArchProductPeriodType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const products = getProducts(arInfo);

  const periodTypeInfos = result3?.PeriodTypeInfos || [];
  const periodTypeInfosOpts = (periodTypeInfos || []).map((v: any) => ({
    value: v.PeriodValue,
    text: periodValueType[v.PeriodValue],
  }));


  // 组件卸载时的清理
  useEffect(() => () => {
    setPrediction({ ...initPrediction });
    archInfo.removeNodeClass(products, s.grey);
  }, []);

  // 定位处理函数
  const onPosition = useCallback(
    (data?: any) => {
      handlePosition(data || predictionResult?.PredictionResultInfos || [], archInfo, products, s);
    },
    [predictionResult, products]
  );

  return (
    <div className={s.predictionContainer}>
      {dialogVisible && <div className={s.dialog}></div>}
      <Loading
        size="small"
        indicator
        loading={describeArchProductThresholdInfoLoading}
        showOverlay
        className={s.loading}
      >
        {prediction.state === 'init' && (
          <>
            <div>
              <Alert className={s.alert}>
                {t('基于近期监测数据进行容量预测，并给出容量规划建议。')}
              </Alert>
            </div>
            <GlobalSettings
              globalSetting={globalSetting}
              setGlobalSetting={setGlobalSetting}
              periodTypeInfosOpts={periodTypeInfosOpts}
              algorithmInfosOpts={algorithmInfosOpts}
              algorithmTypeMap={algorithmTypeMap}
            />
          </>
        )}
        {prediction.state === 'init' && (
          <AccurateSettings
            accurateSettings={accurateSettings}
            setAccurateSettings={setAccurateSettings}
            defaultActiveIds={defaultActiveIds}
            periodTypeInfosOpts={periodTypeInfosOpts}
            algorithmInfosOpts={algorithmInfosOpts}
            algorithmTypeMap={algorithmTypeMap}
            dialogVisible={dialogVisible}
            setDialogVisible={setDialogVisible}
            onDelete={onDelete}
            arInfo={arInfo}
            products={products}
            archInfo={archInfo}
          />
        )}
      </Loading>

      <PredictionProgress
        predictionState={prediction.state}
        predictionResult={predictionResult}
      />
      {prediction.state === 'done' && (
        <PredictionResults
          predictionResult={predictionResult}
          setPredictionResult={setPredictionResult}
          doneDefaultActiveIds={doneDefaultActiveIds}
          onPosition={onPosition}
          archInfo={archInfo}
          PositionIcon={PositionIcon}
        />
      )}
      <FooterActions
        predictionState={prediction.state}
        predictionLoading={prediction.loading}
        describeArchProductThresholdInfoLoading={describeArchProductThresholdInfoLoading}
        inputting={inputting}
        disablePredict={disablePredict}
        onPredictClick={async () => {
          if (prediction.state === 'init') {
            reportEvent({
              key: EVENT.START_PREDICT,
              extraInfo: null,
            });
            if (accurateSettings.length) {
              reportEvent({
                key: EVENT.SPECIFY_PREDICT_CONFIG,
                extraInfo: JSON.stringify(accurateSettings),
              });
            }
            setPrediction(last => ({ ...last, loading: true }));
            modifyArchProductPredictionInfoV2New({
              MapId,
              GlobalSetting: globalSetting,
              AccurateSettings: accurateSettings,
            })
              .then(() => {
                setPrediction(last => ({ ...last, state: 'analyzing' }));
                setPredictionResult({});
                startCapacityPredictionTaskV2New({
                  MapId,
                })
                  .then((res) => {
                    const { TaskId: taskId } = res;
                    setTaskId(taskId || '');
                  })
                  .finally(() => {
                    //
                  });
                arInfo.setDrawerProps({
                  style: { width: '500px' },
                  title: (
                    <div className={s.back}>
                      <img
                        src={back}
                        onClick={() => {
                          setTaskId('');
                          setPrediction(last => ({
                            ...last,
                            loading: false,
                            state: 'init',
                          }));
                          arInfo.setDrawerProps({
                            style: { width: '500px' },
                            title: t('容量预测'),
                          });
                        }}
                      />
                      {t('预测结果')}
                    </div>
                  ),
                });
              })
              .finally(() => {
                setPrediction(last => ({ ...last, loading: false }));
              });
          }
        }}
      />
    </div>
  );
}
