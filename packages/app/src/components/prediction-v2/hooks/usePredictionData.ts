import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useHookstate } from '@hookstate/core';
import { get, cloneDeep, orderBy } from 'lodash-es';
import { message, notification } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import globalState from '@src/stores/global.state';
import warning from '@src/statics/svg/warning.svg';
import success from '@src/statics/svg/success.svg';
import s from '../index.module.scss';
import fetchData from '@src/utils/fetch';
import {
  describeArchProductPredictionInfo,
  describeArchProductPredictionResultInfoV2New,
  describeArchProductAlgorithmType,
} from '@src/api/thresholdsSetting';

/**
 * 预测数据管理Hook
 * @param {string} MapId - 架构图ID
 * @param {string} taskId - 任务ID
 * @param {any} arInfo - 架构信息
 * @returns 预测相关的数据和方法
 */
export function usePredictionData(MapId: string, taskId: string, arInfo: any) {
  const currentChooseFigure = useHookstate(globalState.currentChooseFigure);
  const predictionHightNodeKey = useHookstate(globalState.predictionHightNodeKey).get();

  const [globalSetting, setGlobalSetting] = useState<Record<string, any>>({});
  const [oriGlobalSetting, setOriGlobalSetting] = useState<Record<string, any>>({});
  const [predictionResult, setPredictionResult] = useState<Record<string, any>>({});
  const [accurateSettings, setAccurateSettings] = useState([]);
  const [defaultActiveIds, setDefaultActiveIds] = useState([]);
  const [doneDefaultActiveIds, setDoneDefaultActiveIds] = useState([]);
  const [dialogVisible, setDialogVisible] = useState(false);

  const initPrediction = {
    loading: false,
    state: 'init', // done analyzing
  };
  const [prediction, setPrediction] = useState({ ...initPrediction });

  // 获取全局设置
  const {
    result: result1,
    isLoading: describeArchProductThresholdInfoLoading,
  } = fetchData(
    'DescribeArchProductThresholdInfo',
    {
      MapId,
    },
    describeArchProductPredictionInfo
  );

  // 获取预测结果-轮询
  const {
    result: predictionResultRes,
    reload: describeArchProductPredictionResultInfoReload,
    error: result4Error,
  } = fetchData(
    'DescribeArchProductPredictionResultInfoV2New',
    {
      MapId,
      TaskId: taskId,
      SessionId: arInfo.getPluginSessionId(),
    },
    describeArchProductPredictionResultInfoV2New,
    !taskId
  );

  // 获取算法类型
  const { result: algorithmTypeRes } = fetchData(
    'DescribeArchProductAlgorithmType',
    null,
    describeArchProductAlgorithmType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const algorithmInfos = algorithmTypeRes?.AlgorithmInfos || [];
  const algorithmInfosOpts = (algorithmInfos || []).map((v: any) => ({
    value: v.AlgorithmType,
    text: v.AlgorithmName,
  }));

  const algorithmTypeMap = useMemo(() => {
    const obj: any = {};
    (algorithmInfos || []).forEach((v: any) => {
      obj[v?.AlgorithmType] = v?.AlgorithmName;
    });
    return obj;
  }, [algorithmInfos]);

  // 创建架构图标记
  const createArchBar = useCallback((nodeData: any[], styles: any) => {
    nodeData.forEach((v) => {
      v.PredictionNodeIdInfos.forEach((e: any) => {
        arInfo.createBar([e.NodeUuid], {
          width: 100,
          children: React.createElement('div', {
            className: styles['predication-icon-wrap'],
          }, e.TotalRiskCount
            ? React.createElement(
              'div', null,
              React.createElement('img', {
                style: { width: 28, height: 28 },
                src: warning,
                alt: '',
              }),
              React.createElement('span', null, e.TotalRiskCount)
            )
            : React.createElement('img', {
              style: { width: 28, height: 28 },
              src: success,
              alt: '',
            })),
        });
      });
    });
  }, [arInfo]);

  // 处理全局设置数据
  useEffect(() => {
    const globalSettingData = result1?.GlobalSetting || {};
    const accurateSettingsData = result1?.AccurateSettings || [];
    const defaultActiveIdsData = accurateSettingsData.map((_: any, num: number) => `${num}`);
    setGlobalSetting({ ...globalSettingData });
    setOriGlobalSetting(cloneDeep(globalSettingData));
    setAccurateSettings([...accurateSettingsData]);
    setDefaultActiveIds([...defaultActiveIdsData]);
  }, [result1]);

  // 处理预测结果数据
  useEffect(() => {
    if (predictionResultRes && !predictionResultRes?.Error) {
      predictionResultRes?.PredictionResultInfos?.forEach((e) => {
        const totalRiskCountArr = (e?.PredictionNodeIdInfos?.map(v => v?.TotalRiskCount) || []).filter(v => !!v);
        e.RiskNum = totalRiskCountArr?.length ? Math.max.apply(null, totalRiskCountArr) : 0;
        e?.PredictionNodeIdInfos?.forEach((v) => {
          v?.InsTypeLists?.forEach((int) => {
            int.RiskItems = orderBy(int?.RiskItems || [], ['BaseHighValue', 'BaseLowValue'], ['desc', 'asc']);
          });
          if (!v?.TotalRiskCount) {
            v.TotalRiskCount = 0;
          }
          v.risk = v.TotalRiskCount;
          v.expanded = false;
        });
        e.PredictionNodeIdInfos = orderBy(e?.PredictionNodeIdInfos || [], ['TotalRiskCount', 'ItemName'], ['desc', 'asc']);
      });
      predictionResultRes.PredictionResultInfos = orderBy(predictionResultRes?.PredictionResultInfos || [], ['RiskNum', 'ProductName'], ['desc', 'asc']);
      if (predictionResultRes.PredictionResultInfos?.[0]?.PredictionNodeIdInfos?.[0]) {
        // 默认展开第一个
        predictionResultRes.PredictionResultInfos[0].PredictionNodeIdInfos[0].expanded = true;
      }

      setPredictionResult(predictionResultRes ?? {});

      if (!result4Error && Object.keys(predictionResultRes)?.length > 0) {
        if (!predictionResultRes?.Success) {
          describeArchProductPredictionResultInfoReload();
        }
      }
      if (predictionResultRes?.Success) {
        setPrediction(last => ({ ...last, loading: false, state: 'done' }));
        setDoneDefaultActiveIds([
          ...(predictionResultRes?.PredictionResultInfos ?? []).map((_: any, num: number) => `${num}`),
        ]);
        // 创建架构图标记
        createArchBar(predictionResultRes.PredictionResultInfos, s);
      } else {
        setPrediction(last => ({
          ...last,
          loading: true,
          state: 'analyzing',
        }));
      }
      if (Object.keys(predictionResultRes).length === 0) {
        setPrediction(last => ({
          ...last,
          loading: false,
          state: 'init',
        }));
      }
    } else {
      setPredictionResult({});
    }
  }, [predictionResultRes]);

  // 删除精确设置项
  const onDelete = useCallback(
    (product: any, prediction: any) => {
      const curAccurateSettings = accurateSettings;
      const curProductIndex = curAccurateSettings.findIndex(item => item?.ProductName === product?.ProductName);
      if (curProductIndex > -1) {
        const curProduct = curAccurateSettings[curProductIndex];
        curProduct.PredictionNodeInfos = curProduct.PredictionNodeInfos.filter(item => item?.NodeUuid !== prediction?.NodeUuid);
        if (!curProduct.PredictionNodeInfos.length) {
          curAccurateSettings.splice(curProductIndex, 1);
        }
        setAccurateSettings([...curAccurateSettings]);
      }
    },
    [accurateSettings]
  );

  // 处理当前选中图元
  useEffect(() => {
    const nodeUuid = get(currentChooseFigure, 'NodeUuid')?.get?.();
    const name = get(currentChooseFigure, 'ItemName')?.get?.();
    const label = get(currentChooseFigure, 'Label')?.get?.();
    if (nodeUuid) {
      const newAccurateSettings = accurateSettings;
      const target = newAccurateSettings.find(v => v?.ProductName?.toLowerCase() === name?.toLowerCase())
        || newAccurateSettings.find(v => v?.PredictionNodeInfos?.some(v => v?.Product?.toLowerCase() === name?.toLowerCase()));
      if (!target) {
        // 如果没有 则这个加入整个产品
        newAccurateSettings.push({
          ProductName: name,
          PredictionNodeInfos: [
            {
              NodeUuid: nodeUuid,
              ItemName: label, // 图元名称
              ResourceTimes: oriGlobalSetting.ResourceTimes, // 放量倍数
              PeriodValue: oriGlobalSetting.PeriodValue, // 参考周期
              AlgorithmType: oriGlobalSetting.AlgorithmType, // 计算方法
            },
          ],
        });
        message.success({
          duration: 1000,
          content: t('添加成功'),
        });
        setDefaultActiveIds(newAccurateSettings.map((v, i) => `${i}`));
        setAccurateSettings([...newAccurateSettings]);
        setDialogVisible(false);
        globalState.set(state => ({
          ...state,
          chooseFigureMetropolitanMode: false,
          currentChooseFigure: null, // 清空当前选中的图元
        }));
      } else {
        // 如果存在产品配置， 则对应配置新增配置
        const predictionNodeInfos = target.PredictionNodeInfos;
        const predictionNodeInfo = predictionNodeInfos.find(v => v.NodeUuid === nodeUuid);
        if (!predictionNodeInfo) {
          predictionNodeInfos.push({
            NodeUuid: nodeUuid,
            ItemName: label, // 图元名称
            ResourceTimes: oriGlobalSetting.ResourceTimes, // 放量倍数
            PeriodValue: oriGlobalSetting.PeriodValue, // 参考周期
            AlgorithmType: oriGlobalSetting.AlgorithmType, // 计算方法
          });
          message.success({
            duration: 1000,
            content: t('添加成功'),
          });
          setDefaultActiveIds(newAccurateSettings.map((_: any, i: number) => `${i}`));
          setAccurateSettings([...newAccurateSettings]);
          setDialogVisible(false);
          globalState.set(state => ({
            ...state,
            chooseFigureMetropolitanMode: false,
            currentChooseFigure: null, // 清空当前选中的图元
          }));
        } else {
          message.warning({
            duration: 1000,
            content: t('已添加过该图元'),
          });
        }
      }
    }
  }, [currentChooseFigure]);

  // 处理预测结果高亮
  useEffect(() => {
    if (predictionHightNodeKey) {
      predictionResult?.PredictionResultInfos?.forEach((element: any) => {
        element?.PredictionNodeIdInfos?.forEach((v: any) => {
          if (v?.NodeUuid === predictionHightNodeKey) {
            v.expanded = true;
          }
        });
      });
      setPredictionResult({ ...predictionResult });
    }
  }, [predictionHightNodeKey]);

  // 处理预测结果错误
  useEffect(() => {
    if (result4Error) {
      console.log(t('预测错误：'), result4Error);
      try {
        notification.error({
          title: t('预测失败'),
          description: `${JSON.stringify(
            result4Error?.data?.data?.Response,
            null,
            2
          )}`,
        });
      } catch (error) {
        console.log(error);
      }
      setPrediction(last => ({ ...last, state: 'init', loading: false }));
    }
  }, [result4Error]);

  // 禁用预测的条件
  const disablePredict = useMemo(() => (
    globalSetting?.ResourceTimes < 1
    || accurateSettings.some((v: any) => v?.PredictionNodeInfos.some((v: any) => v?.ResourceTimes < 1))
  ), [globalSetting, accurateSettings]);

  return {
    globalSetting,
    setGlobalSetting,
    oriGlobalSetting,
    predictionResult,
    setPredictionResult,
    accurateSettings,
    setAccurateSettings,
    defaultActiveIds,
    doneDefaultActiveIds,
    setDoneDefaultActiveIds,
    dialogVisible,
    setDialogVisible,
    prediction,
    setPrediction,
    initPrediction,
    describeArchProductThresholdInfoLoading,
    describeArchProductPredictionResultInfoReload,
    algorithmInfosOpts,
    algorithmTypeMap,
    onDelete,
    disablePredict,
  };
}
