/**
 * @description AgentChat抽屉
 */
/* eslint-disable */
import React, { useState, useRef } from 'react';
import {
  Bubble, Button, message as tip,
} from '@tencent/tea-component';
import { app } from '@tea/app';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { ClearIcon } from '@tencent/tea-icons-react';
import { archInfo as apis } from '@src/utils/caching';
import globalState from '@src/stores/global.state';
import { v4 as uuidv4 } from 'uuid';
import { isEmpty, find, get } from 'lodash';
import { getPopupContainer } from '@src/utils/common';
import {
  useChatStore,
  Chat,
  ChatOnboarding,
  Stack,
  ChatSuggestionList,
  ChatSender,
} from '@tencent/cloud-chat-ui';
import { RiskLevelEnum, RISK_FORECAST_KEY } from '@src/constants';
import MessageItem from './components/message-item';
import ShapeRiskBar from './components/shape-risk-bar';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

export const mockData = {
    "Title": "预测结果",
    "RiskCount": 3,
    "MaxHistoryQps": 10000,
    "ExpectQps": 32434,
    "MaxLoadQps": 1000,
    "Description": "流量突发场景评估完成！发现1个风险项，当前架构图整体评估如下：预计入口 OPS:10000，架构瓶颈OPS:6460",
    "NodeItems": [
        {
            "Product": "waf",
            "NodeUuid": "accc5fb3-f202-4a15-91d1-94ece37c48f2",
            "NodeName": "Waf",
            "RiskLevel": 1,
            "MetricValue": 40,
            "InstanceItems": [
                {
                    "InstanceId": "g.ehaa.top",
                    "MetricName": "实例QPS利用率",
                    "MetricValue": 40
                }
            ]
        },
        {
            "Product": "clb",
            "NodeUuid": "e9942ba5-7a47-4727-bb78-a34f7e5a6234",
            "NodeName": "test0307-1",
            "RiskLevel": 1,
            "MetricValue": 0.14,
            "InstanceItems": [
                {
                    "InstanceId": "lb-ioeneldw",
                    "MetricName": "公网QPS利用率",
                    "MetricValue": 0.14
                }
            ]
        },
        {
            "Product": "cvm",
            "NodeUuid": "8ece2ceb-b37b-414a-97df-e7b55e9050a3",
            "NodeName": "ins-8g7bfrpq等4个",
            "RiskLevel": 1,
            "MetricValue": 6.29,
            "InstanceItems": [
                {
                    "InstanceId": "ins-lipcsd1i",
                    "MetricName": "CPU利用率",
                    "MetricValue": 7.42
                },
                {
                    "InstanceId": "ins-6wvuv49w",
                    "MetricName": "CPU利用率",
                    "MetricValue": 5.16
                }
            ]
        },
        {
            "Product": "cvm",
            "NodeUuid": "1467868f-b919-4074-9d4b-e0cb5606aefe",
            "NodeName": "ins-mipva1xg",
            "RiskLevel": 2,
            "MetricValue": 60.33,
            "InstanceItems": [
                {
                    "InstanceId": "ins-mipva1xg",
                    "MetricName": "CPU利用率",
                    "MetricValue": 60.33
                }
            ]
        }
    ],
    "NodeLine": [
        "36c6eb31-055a-4163-b5f2-910c1c76c19f",
        "8b22ee29-70ef-480f-b15b-423ecc82434b",
        "67b50c05-94d3-48d0-b40d-b495f85c3120"
    ]
}

export enum MessageTypeEnum {
  user = 'user',
  assistant = 'assistant',
}
export enum ContentType {
  text = 'text',
  image = 'image',
  iframeUrl = 'iframeUrl',
  chart = 'chart',
  default = 'default'
}

export interface MessageType {
  id?: string;
  type: MessageTypeEnum,
  content: any,
  contentType: ContentType;
  questions?: string[];
  deepThink?: string;
  requestId?: string;
  steps?: any;
  costTime?: number;
}

interface IProps {
  visible: boolean;
}

const CapacityAgentChat = ({ visible }: IProps) => {
  const appId = location.search?.split('appid')?.[1]?.split('&')?.[0]?.split('=')?.[1] || '';
  const [messages, setMessages] = useState<MessageType[]>([]);
  const [suggestionList, setSuggestionList] = useState([]);
  const { processing, setProcessing } = useChatStore();
  const cancelRef = useRef(null);
  const [sessionId, setSessionId] = useState<string>(uuidv4());
  const scrollerRef = useRef({});

  const chatIdRef = useRef('');
  const textareaRef = useRef<any>(null);

  // 处理sse异常
  const handleError = (str?: string, ctrl?: any) => {
    setProcessing(false);
    setMessages((prev) => {
      const lastContent = prev[prev.length - 1]?.content;
      return [...(prev.slice(0, prev.length - 1)), {
        type: MessageTypeEnum.assistant,
        content: `${lastContent}
${(str || t('查询失败,请稍后再试'))}
`,
        contentType: ContentType.text,
        id: chatIdRef.current,
      }];
    });
    ctrl?.abort?.();
  };

  // 发送消息前对messages预处理
  const handleInitSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加
    error?: string; // 报错信息
  }) => {
    const { addLast = false } = options || {};
    setSuggestionList([]);
    // 正常消息
    if (!addLast) {
      setMessages(prev => [...prev, {
        type: MessageTypeEnum.user,
        content: v,
        contentType: ContentType.text,
        id: uuidv4(),
      }, {
        id: uuidv4(),
        type: MessageTypeEnum.assistant,
        content: '',
        contentType: ContentType.text,
        deepThink: '',
      }]);
    } else {
      setMessages(prev =>
        // 整理最后一条消息
        [...(prev.slice(0, prev.length - 1)), {
          type: MessageTypeEnum.assistant,
          content: prev[prev.length - 1]?.content,
          contentType: ContentType.text,
          id: prev[prev.length - 1].id,
        }]);
    }
  };

  const handleNodeListShine = (riskInfo) => {
    const {NodeItems, NodeLine} = riskInfo;
    apis?.removeAllNodeClass?.();
    apis?.clearBar();
    NodeItems?.forEach((item) => {
      // eslint-disable-next-line no-nested-ternary
      const clsName = item?.RiskLevel === RiskLevelEnum.HIGH
        ? 'high-risk-node'
        : item?.RiskLevel === RiskLevelEnum.MEDIUM
          ? 'medium-risk-node'
          : 'low-risk-node';
      apis?.addNodeClass([item?.NodeUuid], clsName);
      apis?.createBar([item?.NodeUuid], {
        children: React.createElement(ShapeRiskBar, {
          riskInfoItem: item,
        }),
        x: 20,
      });
    });
    NodeLine?.forEach((item) => {
      apis?.addNodeClass([item], 'line-risk-node');
    });
  };

  const handleEventStep = (data, id, ctrl) => {
    try {
      const {
        StepKey: stepKey,
        StepData: result,
      } = data;
      handleNodeListShine(mockData);
      // if (stepKey === RISK_FORECAST_KEY) {
      //   try {
      //     const riskInfo = JSON.parse(result);
      //     console.log(riskInfo);
      //     handleNodeListShine(riskInfo);
      //   } catch (e) {
      //     console.log(e);
      //   }
      // }
      setMessages((prev) => {
        const stepItem = find(prev[prev.length - 1]?.steps, item => item.stepKey === stepKey) || {};
        if (isEmpty(stepItem)) {
          const stepList = prev[prev.length - 1]?.steps || [];
          stepList.push({
            stepKey,
            stepContent: result,
          });
          return [...(prev.slice(0, prev.length - 1)), {
            id,
            type: MessageTypeEnum.assistant,
            contentType: ContentType.text,
            steps: stepList,
            deepThink: prev[prev.length - 1]?.deepThink,
            content: prev[prev.length - 1]?.content,
            costTime: prev[prev.length - 1]?.costTime,
          }];
        }
        const newSteps = prev[prev.length - 1]?.steps?.map((item) => {
          if (item.stepKey === stepKey) {
            return {
              stepKey,
              stepContent: result,
            };
          }
          return item;
        });
        return [...(prev.slice(0, prev.length - 1)), {
          id,
          type: MessageTypeEnum.assistant,
          contentType: ContentType.text,
          steps: newSteps,
          deepThink: prev[prev.length - 1]?.deepThink,
          content: prev[prev.length - 1]?.content,
          costTime: prev[prev.length - 1]?.costTime,
        }];
      });
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  const handleEventThought = (data, id) => {
    setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
      id,
      type: MessageTypeEnum.assistant,
      contentType: ContentType.text,
      steps: prev[prev.length - 1]?.steps,
      deepThink: (prev[prev.length - 1]?.deepThink ?? '') + (data ?? ''),
      costTime: prev[prev.length - 1]?.costTime ?? 0,
      content: prev[prev.length - 1]?.content,
    }]);
  };

  const handleEventEnd = (data, ctrl) => {
    setProcessing(false);
    ctrl.abort();
  };

  // 处理接收到的消息
  const handleReciveMessage = (params: {
    rsp: {data: string; event?: string};
    ctrl: any;
  }) => {
    const { rsp, ctrl } = params;
    setProcessing(true);
    if (!rsp?.data) {
      console.log(t('收到心跳:'), rsp);
      return;
    }

    try {
      const { Data: rsData, Event: rsEvent, Id: id, Response } = JSON.parse(rsp?.data);
      if (get(Response, 'Error.Code') === 'AuthFailure.UnauthorizedOperation') {
        handleError(get(Response, 'Error.Message'), ctrl);
        return;
      }
      switch (rsEvent) {
        case 'Step':
          handleEventStep(rsData, id, ctrl);
          break;
        case 'Thought':
          handleEventThought(rsData, id);
          break;
        case 'Message':
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            id,
            type: MessageTypeEnum.assistant,
            contentType: ContentType.text,
            steps: prev[prev.length - 1]?.steps,
            deepThink: prev[prev.length - 1]?.deepThink,
            content: (prev[prev.length - 1]?.content ?? '') + (rsData ?? ''),
            costTime: prev[prev.length - 1]?.costTime,
          }]);
          break;
        case 'End':
          handleEventEnd(rsData, ctrl);
          break;
        default:
          console.warn('Unknown event type:');
      }
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  // 处理发送消核心逻辑
  const handleSendMessage = (
    v: string,
    options?: {
      addLast?: boolean; // 是否追加
      error?: string; // 报错信息
    }
  ) => {
    if (processing || !v) return;
    setProcessing(true);
    const { addLast = false } = options || {};
    globalState.set(state => ({
      ...state,
      agentInputValue: '',
    }));
    handleInitSendMessage(v, options);
    // 控制取消请求
    const ctrl = new AbortController();
    cancelRef.current = ctrl;
    const isConsole = apis?.env === 'CONSOLE';
    let url;
    let method;
    let headers;
    let body;
    const question = v;
    if (isConsole) {
      const info = app.capi.generateSseRequest({
        serviceType: 'saai',
        cmd: 'GuardAgentCompletions',
        data: {
          Version: '2025-03-17',
          ArchId: apis?.archInfo?.archId,
          Message: question,
          SessionId: sessionId,
        },
        regionId: 1,
      });
      const {
        url: u, method: m, headers: h, body: b,
      } = info;
      url = u;
      method = m;
      headers = h;
      body = b;
    } else {
      // 运营端SSE请求
      const params = {
        AppId: +appId,
        ArchId: apis?.archInfo?.archId,
        SessionId: sessionId,
        Message: question,
        Uin: apis?.uin,
        SubAccountUin: apis?.uin,
      };

      url = 'http://30.165.223.17:8080/v1/chat';
      method = 'post';
      headers = {
        'Content-Type': 'application/json',
      };
      body = JSON.stringify(params);
    }

    fetchEventSource(url, {
      method,
      headers,
      body,
      credentials: 'include',
      openWhenHidden: true,
      signal: ctrl.signal,
      // 必须设置，否则出现异常无法终止
      onopen(res): any {
        if (res.status !== 200) {
          handleError(t('网络异常'), ctrl);
        } else if (!addLast) {
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.assistant,
            content: '',
            contentType: ContentType.text,
            id: prev[prev.length - 1].id,
          }]);
        }
      },
      onmessage(rsp) {
        handleReciveMessage({
          rsp,
          ctrl,
        });
      },
      onerror(e) {
        handleError(e.message, ctrl);
        console.error('sse error', e, ctrl);
      },
    });
  };

  const stopMessage = () => {
    if (!processing) return;
    cancelRef.current?.abort();
    setProcessing(false);
  };

  const handleSessionClear = () => {
    apis?.removeAllNodeClass?.();
    setSessionId(uuidv4());
    setMessages([]);
  };

  return <div>
    <Chat
      title={
        messages.length > 0
          ? <div className={s['chat-title-drawer']}>
              <span>{t('容量检测 AI 助手')}</span>
              <div className={s['chat-action-tag']}>
                Beta
              </div>
          </div>
          : ''
      }
      visible={visible}
      portalContainer={getPopupContainer()}
      isInSessions={messages.length > 0}
      fixedTop={50}
      className={s['chat-drawer-container']}
      bounds={{ top: 100, left: 100, right: 10, bottom: 30 }}
      logo={null}
      onCloseButtonClick={() => {
        stopMessage();
        globalState.set(state => ({
          ...state,
          agentDrawerVisible: false,
        }));
        apis?.removeAllNodeClass?.();
      }}
      inputArea={
        <>
          <ChatSender
            getTextareaRef={(node) => {
              textareaRef.current = node;
            }}
            value={globalState.get().agentInputValue}
            loading={processing}
            topExtra={
              <>
              </>
            }
            onChange={(v) => {
              globalState.set(state => ({
                ...state,
                agentInputValue: v,
              }));
            }}
            onSend={(v) => {
              if (v.trim().length <= 0) {
                return tip.warning({
                  content: t('请勿使用空白信息进行询问'),
                });
              }
              if (v.trim().length > 1024) {
                return tip.warning({
                  content: t('消息长度不能超过1024个字符'),
                });
              }

              handleSendMessage(v);
            }}
            onCancelProcess={stopMessage}
          />
        </>
      }
      actions={[
        ...(messages.length === 0 ? [
          <div key="beta-tag" className={s['chat-action-tag']}>
            Beta
          </div>,
        ] : []),
        <Bubble
          arrowPointAtCenter
          placement='bottom'
          className={s['chat-ui__bubble']}
          openDelay={100}
          content={t('清空会话')}
          key={uuidv4()}
        >
          <Button type='text' onClick={handleSessionClear}>
            <ClearIcon />
          </Button>
        </Bubble>,
      ]}
      scrollerRef={scrollerRef}
    >
      {
        messages.length > 0
          ? <Stack>
            {
              messages.map((item, index) => (
                <MessageItem
                  key={index}
                  item={item}
                  processing={processing}
                  messages={messages}
                  onSendMessage={(message) => {
                    handleSendMessage(message);
                  }}
                  onMessageChange={value => setMessages(value)}
                />
              ))
            }
            {!processing && messages.length > 0 && (
              <ChatSuggestionList
                list={suggestionList}
              />
            )}
          </Stack>
          : <ChatOnboarding
            subTitle={t('我是容量Agent')}
          />
      }
    </Chat>
  </div>;
};
export default CapacityAgentChat;
