/* eslint-disable */
import React from 'react';
import { Popover } from '@tencent/tea-component';
import { RiskLevelEnum } from '@src/constants';
import PopoverPanel from '../popover-panel';
import s from './index.module.scss';

interface IShapeNumberBarProps {
  riskInfoItem: any
}
/**
 * 图元角标组件
 * @returns
 */
export default function ShapeRiskBar(props: IShapeNumberBarProps): React.ReactElement {
  const {
    riskInfoItem,
  } = props;

  if (!riskInfoItem?.RiskLevel) {
    return <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOCAxNGMwLTcuNzMyLTYuMjY4LTE0LTE0LTE0UzAgNi4yNjggMCAxNHM2LjI2OCAxNCAxNCAxNCAxNC02LjI2OCAxNC0xNHptLTYuNDA3LTMuMTI2TDE5LjEyIDguNGwtNi44MDYgNi44MDZMOC42IDExLjQ5NGwtMi40NzUgMi40NzUgNi4xODggNi4xODggOS4yOC05LjI4M3oiIGZpbGw9IiMwQUJGNUIiLz48L3N2Zz4=" />;
  }
  return (
    <div className={s['shape-risk-bar']}>
      {(() => {
        switch (riskInfoItem?.RiskLevel) {
          case RiskLevelEnum.LOW:
            return <Popover
                placement="top"
                overlay={<PopoverPanel riskInfoItem={riskInfoItem}/>}
              >
                <div className={s['low-risk-node-tag']}>
                  风险资源
                </div>
              </Popover>;
          case RiskLevelEnum.MEDIUM:
            return <Popover
                placement="top"
                overlay={<PopoverPanel riskInfoItem={riskInfoItem}/>}
              >
                <div className={s['medium-risk-node-tag']}>
                  潜在瓶颈
                </div>
              </Popover>;
          case RiskLevelEnum.HIGH:
            return <Popover
                placement="top"
                overlay={<PopoverPanel riskInfoItem={riskInfoItem}/>}
              >
                <div className={s['high-risk-node-tag']}>
                  瓶颈资源
                </div>
              </Popover>;
          default: return <></>;
        }
      })()}
    </div>
  );
}
