/* eslint-disable */
import React from 'react';
import { Loading } from 'tdesign-react';
import GreenIconSvg from '@src/statics/svg/green-icon.svg';
import { Popover } from '@tencent/tea-component';
import { RiskLevelEnum } from '@src/constants';
import PopoverPanel from '../popover-panel';
import s from './index.module.scss';

interface IShapeNumberBarProps {
  riskInfoItem: any
}
/**
 * 图元角标组件
 * @returns
 */
export default function ShapeRiskBar(props: IShapeNumberBarProps): React.ReactElement {
  const {
    riskInfoItem,
  } = props;

  if (!riskInfoItem?.RiskLevel) {
    return <GreenIconSvg />;
  }
  return (
    <div className={s['shape-risk-bar']}>
      {(() => {
        switch (riskInfoItem?.RiskLevel) {
          case RiskLevelEnum.LOW:
            return <Popover
                placement="top"
                overlay={<PopoverPanel riskInfoItem={riskInfoItem}/>}
              >
                <div className={s['low-risk-node-tag']}>
                  风险资源
                </div>
              </Popover>;
          case RiskLevelEnum.MEDIUM:
            return <Popover
                placement="top"
                overlay={<PopoverPanel riskInfoItem={riskInfoItem}/>}
              >
                <div className={s['medium-risk-node-tag']}>
                  潜在瓶颈
                </div>
              </Popover>;
          case RiskLevelEnum.HIGH:
            return <Popover
                placement="top"
                overlay={<PopoverPanel riskInfoItem={riskInfoItem}/>}
              >
                <div className={s['high-risk-node-tag']}>
                  瓶颈资源
                </div>
              </Popover>;
          default: return <></>;
        }
      })()}
    </div>
  );
}
