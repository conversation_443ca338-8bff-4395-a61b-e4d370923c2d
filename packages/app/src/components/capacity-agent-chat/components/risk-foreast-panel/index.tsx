/* eslint-disable */
import {
  Select, Form, message, Input,
} from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import './index.module.scss';
import { t } from '@tea/app/i18n';

interface IProps {
  forecastDetail: any;
  id: string;
  stepKey: string;
}
const RiskForeastPanel = ({ forecastDetail, stepKey, id }: IProps) => {
  const { MockTemplateContent = '', BroadcastCycle } = forecastDetail;

  return <div className='charts-card'>
    123
  </div>;
};

export default RiskForeastPanel;
