/* eslint-disable */
import {
  Select, Form, message, Input,
} from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import s from './index.module.scss';
import { t } from '@tea/app/i18n';

interface IProps {
  forecastDetail: any;
  id: string;
  stepKey: string;
}
const RiskForeastPanel = ({ forecastDetail, stepKey, id }: IProps) => {
  const [forecastInfo, setForecastInfo] = useState<any>({});
  useEffect(() => {
    try {
      const forecast = JSON.parse(forecastDetail?.stepContent);
      setForecastInfo(forecast);
    }
    catch (e) {
      message.error({content: '解析数据失败'});
    }
  }, [forecastDetail])

  return <div className={s['charts-card']}>
    <h3>{forecastInfo?.Title}</h3>
    <div className={s['forecast-desc']}>{`流量突发场景评估完成！发现${forecastInfo?.RiskCount}个风险项！`}</div>
    <p>当前架构图整体评估如下</p>
    <div className={s['forecast-rto-item']}>
      <div className={s['forecast-rto-item-label']}>历史峰值QPS</div>
      <div className={s['forecast-rto-item-val']}>{forecastInfo?.MaxHistoryQps}</div>
    </div>
    <div className={s['forecast-rto-item']}>
      <div className={s['forecast-rto-item-label']}>预计入口 QPS</div>
      <div className={s['forecast-rto-item-val']}>{forecastInfo?.ExpectQps}</div>
    </div>
    <div className={s['forecast-rto-item']}>
      <div className={s['forecast-rto-item-label']}>架构瓶颈 QPS</div>
      <div className={s['forecast-rto-item-val']}>{forecastInfo?.MaxLoadQps}</div>
    </div>
  </div>;
};

export default RiskForeastPanel;
