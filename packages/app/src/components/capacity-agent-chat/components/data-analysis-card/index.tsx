/* eslint-disable */
import React, {useEffect, useState} from 'react';
import {
  ChatMarkdown,
  AITrigger,
} from '@tencent/cloud-chat-ui';
import dataAnalysisSvg from '@src/statics/svg/mathematics.svg';
import baseAnalysisSvg from '@src/statics/svg/chart-bubble.svg';
import greenSuccessSvg from '@src/statics/svg/green-success.svg';
import adviceSvg from '@src/statics/svg/questionnaire-double.svg';
import s from './index.module.scss';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  contentResult: string;
  processing: boolean;
  insightNodeStatus: any;
}

const DataAnalysisCard: React.FC<DataAnalysisCardProps> = ({
  contentResult = '',
  processing,
  insightNodeStatus,
}) => {
  const {stepContent} = insightNodeStatus
  const [hasAnalysisPattern, setHasAnalysisPattern] = useState(false);
  const [hasAdvicePattern, setHasAdvicePattern] = useState(false);
  const [analysisContent, setAnalysisContent] = useState('');
  const [adviceContent, setAdviceContent] = useState('');
  const [mainContent, setMainContent] = useState('');
  console.log(insightNodeStatus);
  
  useEffect(() => {
    // 处理主要内容
    let contentWithoutMarkers = contentResult;

    if (processing) {
      // processing为true时，不清除$[根因诊断]和$[优化建议]标签
      // 只清除$[数据分析]等其他标签
      contentWithoutMarkers = contentResult.replace(/\$\[(?!根因诊断|优化建议).*?\]/g, '');
    } else {
      // processing为false时，处理完整的contentResult进行匹配
      const analysisPattern = /\$\[根因诊断\]/g;
      const advicePattern = /\$\[优化建议\]/g;

      const hasAnalysis = analysisPattern.test(contentResult || '');
      const hasAdvice = advicePattern.test(contentResult || '');

      // 设置板块状态
      setHasAnalysisPattern(hasAnalysis);
      setHasAdvicePattern(hasAdvice);

      // 提取根因诊断的最后一个内容
      if (hasAnalysis) {
        // 找到所有$[根因诊断]的匹配
        const allAnalysisMatches = [...contentResult.matchAll(/\$\[根因诊断\]([\s\S]*?)(?=\$\[|$)/g)];
        if (allAnalysisMatches.length > 0) {
          // 取最后一个匹配的内容
          const lastAnalysisMatch = allAnalysisMatches[allAnalysisMatches.length - 1];
          if (lastAnalysisMatch && lastAnalysisMatch[1]) {
            setAnalysisContent(lastAnalysisMatch[1].trim());
          }
        }
      }

      // 提取优化建议的最后一个内容
      if (hasAdvice) {
        // 找到所有$[优化建议]的匹配
        const allAdviceMatches = [...contentResult.matchAll(/\$\[优化建议\]([\s\S]*?)(?=\$\[|$)/g)];
        if (allAdviceMatches.length > 0) {
          // 取最后一个匹配的内容
          const lastAdviceMatch = allAdviceMatches[allAdviceMatches.length - 1];
          if (lastAdviceMatch && lastAdviceMatch[1]) {
            setAdviceContent(lastAdviceMatch[1].trim());
          }
        }
      }

      // 截取到第一个特殊标签之前的内容
      const firstSpecialIndex = Math.min(
        contentResult.indexOf('$[根因诊断]') !== -1 ? contentResult.indexOf('$[根因诊断]') : Infinity,
        contentResult.indexOf('$[优化建议]') !== -1 ? contentResult.indexOf('$[优化建议]') : Infinity
      );

      if (firstSpecialIndex !== Infinity) {
        contentWithoutMarkers = contentResult.substring(0, firstSpecialIndex);
      }

      // 清除所有$[]标记
      contentWithoutMarkers = contentWithoutMarkers.replace(/\$\[.*?\]/g, '');
    }

    setMainContent(contentWithoutMarkers.trim());
  }, [contentResult, processing]);

  return (
    <div>
      <div className={s['analysis-data-card']}>
        <div className={s['analysis-data-card-title']}>
          <img src={dataAnalysisSvg} />
          <span>数据分析</span>
        </div>
        <div className={s['analysis-data-card-content-wrapper']}>
          {stepContent?.IsHealthy && <img src={greenSuccessSvg} />}
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={mainContent}
            markdownProps={{}}
          />
        </div>
        
        {processing && <DotLoading />}
      </div>
      {
        !processing && hasAnalysisPattern && analysisContent
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={baseAnalysisSvg} />
            <span>根因诊断</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={analysisContent}
            markdownProps={{}}
          />
        </div>
      }
      {
        !processing && hasAdvicePattern && adviceContent
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={adviceSvg} />
            <span>优化建议</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={adviceContent}
            markdownProps={{}}
          />
        </div>
      }
    </div>
    
  );
};

export default DataAnalysisCard;
