/* eslint-disable */
import React, {useEffect, useState} from 'react';
import { Tooltip } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import rehypeRaw from 'rehype-raw';
import {
  ChatMarkdown,
  AITrigger,
} from '@tencent/cloud-chat-ui';
import dataAnalysisSvg from '@src/statics/svg/mathematics.svg';
import baseAnalysisSvg from '@src/statics/svg/chart-bubble.svg';
import greenSuccessSvg from '@src/statics/svg/green-success.svg';
import adviceSvg from '@src/statics/svg/questionnaire-double.svg';
import s from './index.module.scss';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  contentResult: string;
  processing: boolean;
  insightNodeStatus: any;
}

const DataAnalysisCard: React.FC<DataAnalysisCardProps> = ({
  contentResult = '',
  processing,
  insightNodeStatus,
}) => {
  const {stepContent} = insightNodeStatus
  const [hasAnalysisPattern, setHasAnalysisPattern] = useState(false);
  const [hasAdvicePattern, setHasAdvicePattern] = useState(false);
  const [analysisContent, setAnalysisContent] = useState('');
  const [adviceContent, setAdviceContent] = useState('');
  const [mainContent, setMainContent] = useState('');
  console.log(insightNodeStatus);
  
  useEffect(() => {
    const analysisPattern = /\$\[根因诊断\]/g;
    const advicePattern = /\$\[优化建议\]/g;
    
    const hasAnalysis = analysisPattern.test(contentResult || '');
    const hasAdvice = advicePattern.test(contentResult || '');
    
    setHasAnalysisPattern(hasAnalysis);
    setHasAdvicePattern(hasAdvice);

    // 无论是否有根因诊断，都去除所有$[]标记及其内容
    let contentWithoutMarkers = contentResult;
    
    // 当有根因诊断时，先截取根因诊断之前的内容，再去除标记
    if (hasAnalysis) {
      const analysisIndex = contentResult.indexOf('$[根因诊断]');
      contentWithoutMarkers = contentResult.substring(0, analysisIndex);
    }
    
    // 去除所有$[]标记及其内容（包括$[数据分析]等）
    contentWithoutMarkers = contentWithoutMarkers.replace(/\$\[.*?\]/g, '');
    
    setMainContent(contentWithoutMarkers.trim());

    // 提取根因诊断内容
    if (hasAnalysis) {
      const analysisMatch = contentResult.match(/\$\[根因诊断\]([\s\S]*?)(?:\$\[优化建议\]|$)/);
      if (analysisMatch && analysisMatch[1]) {
        setAnalysisContent(analysisMatch[1].trim());
      }
    }

    // 提取优化建议内容
    if (hasAdvice) {
      const adviceMatch = contentResult.match(/\$\[优化建议\]([\s\S]*$)/);
      if (adviceMatch && adviceMatch[1]) {
        setAdviceContent(adviceMatch[1].trim());
      }
    }
  }, [contentResult]);

  return (
    <div>
      <div className={s['analysis-data-card']}>
        <div className={s['analysis-data-card-title']}>
          <img src={dataAnalysisSvg} />
          <span>数据分析</span>
        </div>
        <div className={s['analysis-data-card-content-wrapper']}>
          {stepContent?.IsHealthy && <img src={greenSuccessSvg} />}
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={mainContent}
            markdownProps={{}}
          />
        </div>
        
        {processing && !hasAnalysisPattern && <DotLoading />}
      </div>
      {
        hasAnalysisPattern && analysisContent
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={baseAnalysisSvg} />
            <span>根因诊断</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={analysisContent}
            markdownProps={{}}
          />
        </div>
      }
      {
        hasAdvicePattern && adviceContent
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={adviceSvg} />
            <span>优化建议</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={adviceContent}
            markdownProps={{}}
          />
        </div>
      }
    </div>
    
  );
};

export default DataAnalysisCard;
