/* eslint-disable */
import React, {useEffect, useState} from 'react';
import {
  ChatMarkdown,
  AITrigger,
} from '@tencent/cloud-chat-ui';
import dataAnalysisSvg from '@src/statics/svg/mathematics.svg';
import baseAnalysisSvg from '@src/statics/svg/chart-bubble.svg';
import greenSuccessSvg from '@src/statics/svg/green-success.svg';
import adviceSvg from '@src/statics/svg/questionnaire-double.svg';
import s from './index.module.scss';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  contentResult: string;
  processing: boolean;
  insightNodeStatus: any;
}

const DataAnalysisCard: React.FC<DataAnalysisCardProps> = ({
  contentResult = '',
  processing,
  insightNodeStatus,
}) => {
  const {stepContent} = insightNodeStatus
  const [hasAnalysisPattern, setHasAnalysisPattern] = useState(false);
  const [hasAdvicePattern, setHasAdvicePattern] = useState(false);
  const [analysisContent, setAnalysisContent] = useState('');
  const [adviceContent, setAdviceContent] = useState('');
  const [mainContent, setMainContent] = useState('');
  console.log(insightNodeStatus);
  
  useEffect(() => {
    // 处理主要内容
    let contentWithoutMarkers = contentResult;

    if (processing) {
      // processing为true时，不清除$[根因诊断]和$[优化建议]标签
      // 只清除$[数据分析]等其他标签
      contentWithoutMarkers = contentResult.replace(/\$\[(?!根因诊断|优化建议).*?\]/g, '');
    } else {
      // processing为false时，处理完整的contentResult进行匹配
      const analysisPattern = /\$\[根因诊断\]/g;
      const advicePattern = /\$\[优化建议\]/g;

      const hasAnalysis = analysisPattern.test(contentResult || '');
      const hasAdvice = advicePattern.test(contentResult || '');

      // 设置板块状态
      setHasAnalysisPattern(hasAnalysis);
      setHasAdvicePattern(hasAdvice);

      // 处理内容，只清除最后一个匹配的标签和内容
      contentWithoutMarkers = contentResult;

      // 提取根因诊断的最后一个内容
      if (hasAnalysis) {
        // 找到所有$[根因诊断]的匹配位置
        const analysisMatches = [...contentResult.matchAll(/\$\[根因诊断\]/g)];
        if (analysisMatches.length > 0) {
          // 获取最后一个$[根因诊断]的位置
          const lastAnalysisMatch = analysisMatches[analysisMatches.length - 1];
          const lastAnalysisIndex = lastAnalysisMatch.index;

          // 提取最后一个$[根因诊断]的内容
          const contentFromLastAnalysis = contentResult.substring(lastAnalysisIndex);
          const analysisContentMatch = contentFromLastAnalysis.match(/\$\[根因诊断\]([\s\S]*?)(?=\$\[|$)/);
          if (analysisContentMatch && analysisContentMatch[1]) {
            setAnalysisContent(analysisContentMatch[1].trim());

            // 从主内容中移除最后一个$[根因诊断]及其内容
            const endIndex = lastAnalysisIndex + analysisContentMatch[0].length;
            contentWithoutMarkers = contentResult.substring(0, lastAnalysisIndex) + contentResult.substring(endIndex);
          }
        }
      }

      // 提取优化建议的最后一个内容
      if (hasAdvice) {
        // 找到所有$[优化建议]的匹配位置
        const adviceMatches = [...contentWithoutMarkers.matchAll(/\$\[优化建议\]/g)];
        if (adviceMatches.length > 0) {
          // 获取最后一个$[优化建议]的位置
          const lastAdviceMatch = adviceMatches[adviceMatches.length - 1];
          const lastAdviceIndex = lastAdviceMatch.index;

          // 提取最后一个$[优化建议]的内容
          const contentFromLastAdvice = contentWithoutMarkers.substring(lastAdviceIndex);
          const adviceContentMatch = contentFromLastAdvice.match(/\$\[优化建议\]([\s\S]*?)(?=\$\[|$)/);
          if (adviceContentMatch && adviceContentMatch[1]) {
            setAdviceContent(adviceContentMatch[1].trim());

            // 从主内容中移除最后一个$[优化建议]及其内容
            const endIndex = lastAdviceIndex + adviceContentMatch[0].length;
            contentWithoutMarkers = contentWithoutMarkers.substring(0, lastAdviceIndex) + contentWithoutMarkers.substring(endIndex);
          }
        }
      }

      // 清除其他$[]标记（除了保留的$[根因诊断]和$[优化建议]）
      contentWithoutMarkers = contentWithoutMarkers.replace(/\$\[(?!根因诊断|优化建议).*?\]/g, '');
    }

    setMainContent(contentWithoutMarkers.trim());
  }, [contentResult, processing]);

  return (
    <div>
      <div className={s['analysis-data-card']}>
        <div className={s['analysis-data-card-title']}>
          <img src={dataAnalysisSvg} />
          <span>数据分析</span>
        </div>
        <div className={s['analysis-data-card-content-wrapper']}>
          {stepContent?.IsHealthy && <img src={greenSuccessSvg} />}
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={mainContent}
            markdownProps={{}}
          />
        </div>
        
        {processing && <DotLoading />}
      </div>
      {
        !processing && hasAnalysisPattern && analysisContent
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={baseAnalysisSvg} />
            <span>根因诊断</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={analysisContent}
            markdownProps={{}}
          />
        </div>
      }
      {
        !processing && hasAdvicePattern && adviceContent
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={adviceSvg} />
            <span>优化建议</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={adviceContent}
            markdownProps={{}}
          />
        </div>
      }
    </div>
    
  );
};

export default DataAnalysisCard;
