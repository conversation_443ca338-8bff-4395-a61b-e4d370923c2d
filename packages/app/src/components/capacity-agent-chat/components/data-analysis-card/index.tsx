/* eslint-disable */
import React, {useEffect, useState} from 'react';
import {
  ChatMarkdown,
  AITrigger,
} from '@tencent/cloud-chat-ui';
import dataAnalysisSvg from '@src/statics/svg/mathematics.svg';
import baseAnalysisSvg from '@src/statics/svg/chart-bubble.svg';
import greenSuccessSvg from '@src/statics/svg/green-success.svg';
import {ChevronDownIcon, ChevronUpIcon} from 'tdesign-icons-react';
import adviceSvg from '@src/statics/svg/questionnaire-double.svg';
import s from './index.module.scss';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  contentResult: string;
  processing: boolean;
  insightNodeStatus: any;
}

const DataAnalysisCard: React.FC<DataAnalysisCardProps> = ({
  contentResult = '',
  processing,
  insightNodeStatus,
}) => {
  const {stepContent} = insightNodeStatus
  const [hasAnalysisPattern, setHasAnalysisPattern] = useState(false);
  const [hasAdvicePattern, setHasAdvicePattern] = useState(false);
  const [analysisContent, setAnalysisContent] = useState('');
  const [adviceContent, setAdviceContent] = useState('');
  const [mainContent, setMainContent] = useState('');
  const [isAnalysisFold, setIsAnalysisFold] = useState(false);

  // 流式输出相关状态
  const [displayedAnalysisContent, setDisplayedAnalysisContent] = useState('');
  const [displayedAdviceContent, setDisplayedAdviceContent] = useState('');
  const [isAnalysisStreaming, setIsAnalysisStreaming] = useState(false);
  const [isAdviceStreaming, setIsAdviceStreaming] = useState(false);
  const [showAnalysisCard, setShowAnalysisCard] = useState(false);
  const [showAdviceCard, setShowAdviceCard] = useState(false);
  const [analysisCompleted, setAnalysisCompleted] = useState(false);

  useEffect(() => {
    // 处理主要内容
    let contentWithoutMarkers = contentResult;

    if (processing) {
      // processing为true时，不清除$[根因诊断]和$[优化建议]标签
      // 只清除$[数据分析]等其他标签
      contentWithoutMarkers = contentResult.replace(/\$\[(?!根因诊断|优化建议).*?\]/g, '');
    } else {
      // processing为false时，处理完整的contentResult进行匹配
      const analysisPattern = /\$\[根因诊断\]/g;
      const advicePattern = /\$\[优化建议\]/g;

      const hasAnalysis = analysisPattern.test(contentResult || '');
      const hasAdvice = advicePattern.test(contentResult || '');

      // 设置板块状态
      setHasAnalysisPattern(hasAnalysis);
      setHasAdvicePattern(hasAdvice);

      // 重置流式输出状态
      setShowAnalysisCard(false);
      setShowAdviceCard(false);
      setDisplayedAnalysisContent('');
      setDisplayedAdviceContent('');
      setAnalysisCompleted(false);

      // 处理内容，只清除最后一个匹配的标签和内容
      contentWithoutMarkers = contentResult;

      // 提取根因诊断的最后一个内容
      if (hasAnalysis) {
        // 找到所有$[根因诊断]的匹配位置
        const analysisMatches = [...contentResult.matchAll(/\$\[根因诊断\]/g)];
        if (analysisMatches.length > 0) {
          // 获取最后一个$[根因诊断]的位置
          const lastAnalysisMatch = analysisMatches[analysisMatches.length - 1];
          const lastAnalysisIndex = lastAnalysisMatch.index;

          // 提取最后一个$[根因诊断]的内容
          const contentFromLastAnalysis = contentResult.substring(lastAnalysisIndex);
          const analysisContentMatch = contentFromLastAnalysis.match(/\$\[根因诊断\]([\s\S]*?)(?=\$\[|$)/);
          if (analysisContentMatch && analysisContentMatch[1]) {
            setAnalysisContent(analysisContentMatch[1].trim());

            // 从主内容中移除最后一个$[根因诊断]及其内容
            const endIndex = lastAnalysisIndex + analysisContentMatch[0].length;
            contentWithoutMarkers = contentResult.substring(0, lastAnalysisIndex) + contentResult.substring(endIndex);
          }
        }
      }

      // 提取优化建议的最后一个内容
      if (hasAdvice) {
        // 找到所有$[优化建议]的匹配位置
        const adviceMatches = [...contentWithoutMarkers.matchAll(/\$\[优化建议\]/g)];
        if (adviceMatches.length > 0) {
          // 获取最后一个$[优化建议]的位置
          const lastAdviceMatch = adviceMatches[adviceMatches.length - 1];
          const lastAdviceIndex = lastAdviceMatch.index;

          // 提取最后一个$[优化建议]的内容
          const contentFromLastAdvice = contentWithoutMarkers.substring(lastAdviceIndex);
          const adviceContentMatch = contentFromLastAdvice.match(/\$\[优化建议\]([\s\S]*?)(?=\$\[|$)/);
          if (adviceContentMatch && adviceContentMatch[1]) {
            setAdviceContent(adviceContentMatch[1].trim());

            // 从主内容中移除最后一个$[优化建议]及其内容
            const endIndex = lastAdviceIndex + adviceContentMatch[0].length;
            contentWithoutMarkers = contentWithoutMarkers.substring(0, lastAdviceIndex) + contentWithoutMarkers.substring(endIndex);
          }
        }
      }

      // 清除其他$[]标记（除了保留的$[根因诊断]和$[优化建议]）
      contentWithoutMarkers = contentWithoutMarkers.replace(/\$\[(?!根因诊断|优化建议).*?\]/g, '');
    }

    setMainContent(contentWithoutMarkers.trim());
  }, [contentResult, processing]);

  // 流式输出控制 useEffect - 启动根因诊断
  useEffect(() => {
    if (!processing && hasAnalysisPattern && analysisContent && !showAnalysisCard && !analysisCompleted) {
      // 延迟显示根因诊断卡片，然后开始流式输出
      setTimeout(() => {
        setShowAnalysisCard(true);
        startAnalysisStreaming();
      }, 300);
    }
  }, [processing, hasAnalysisPattern, analysisContent, showAnalysisCard, analysisCompleted]);

  // 监听根因诊断完成，启动优化建议
  useEffect(() => {
    if (!processing && hasAdvicePattern && adviceContent && analysisCompleted && !showAdviceCard) {
      // 根因诊断完成后，延迟显示优化建议卡片
      setTimeout(() => {
        setShowAdviceCard(true);
        startAdviceStreaming();
      }, 500);
    }
  }, [processing, hasAdvicePattern, adviceContent, analysisCompleted, showAdviceCard]);

  // 处理只有优化建议没有根因诊断的情况
  useEffect(() => {
    if (!processing && hasAdvicePattern && adviceContent && !hasAnalysisPattern && !showAdviceCard) {
      // 没有根因诊断时，直接显示优化建议
      setTimeout(() => {
        setShowAdviceCard(true);
        startAdviceStreaming();
      }, 300);
    }
  }, [processing, hasAdvicePattern, adviceContent, hasAnalysisPattern, showAdviceCard]);

  // 流式输出函数
  const startAnalysisStreaming = () => {
    if (!analysisContent) return;

    setIsAnalysisStreaming(true);
    setDisplayedAnalysisContent('');
    let index = 0;

    const timer = setInterval(() => {
      if (index < analysisContent.length) {
        setDisplayedAnalysisContent(analysisContent.slice(0, index + 1));
        index++;
      } else {
        setIsAnalysisStreaming(false);
        setAnalysisCompleted(true); // 标记根因诊断完成
        clearInterval(timer);
      }
    }, 10); // 30ms 间隔，可以调整速度
  };

  const startAdviceStreaming = () => {
    if (!adviceContent) return;

    setIsAdviceStreaming(true);
    setDisplayedAdviceContent('');
    let index = 0;

    const timer = setInterval(() => {
      if (index < adviceContent.length) {
        setDisplayedAdviceContent(adviceContent.slice(0, index + 1));
        index++;
      } else {
        setIsAdviceStreaming(false);
        clearInterval(timer);
      }
    }, 10); // 30ms 间隔，可以调整速度
  };

  return (
    <div>
      <div className={s['analysis-data-card']}>
        <div className={s['analysis-data-card-title']}>
          <div style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
            <img src={dataAnalysisSvg} />
            <span>数据分析</span>
          </div>
          <div
            onClick={() => setIsAnalysisFold(!isAnalysisFold)}
            style={{cursor: 'pointer'}}
          >
            {
              isAnalysisFold
              ? <ChevronDownIcon size="medium"/>
              : <ChevronUpIcon size="medium" />
            }
          </div>
        </div>
        {
          !isAnalysisFold
          && <>
              <div className={s['analysis-data-card-content-wrapper']}>
              {stepContent?.IsHealthy && <img src={greenSuccessSvg} />}
              <ChatMarkdown
                className={s['analysis-data-card-content']}
                content={mainContent}
                markdownProps={{}}
              />
            </div>
            {processing && <DotLoading />}
          </>
        }
      </div>
      {
        showAnalysisCard && hasAnalysisPattern
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={baseAnalysisSvg} />
            <span>根因诊断</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={displayedAnalysisContent}
            markdownProps={{}}
          />
          {isAnalysisStreaming && <DotLoading />}
        </div>
      }
      {
        showAdviceCard && hasAdvicePattern
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={adviceSvg} />
            <span>优化建议</span>
          </div>
          <ChatMarkdown
            className={s['analysis-data-card-content']}
            content={displayedAdviceContent}
            markdownProps={{}}
          />
          {isAdviceStreaming && <DotLoading />}
        </div>
      }
    </div>
    
  );
};

export default DataAnalysisCard;
