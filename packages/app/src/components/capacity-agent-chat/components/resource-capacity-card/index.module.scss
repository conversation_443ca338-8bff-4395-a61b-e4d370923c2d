.resource-capacity-card {
  background: white;
  border-radius: 5px;
  padding: 20px 20px 5px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin: 16px 0;
  color: var(---, rgba(0, 0, 0, 0.9));
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
.no-more {
  padding-bottom: 15px;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 2px;
}

.icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  img {
    width: 19px;
  }
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 90%;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.server-name {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  width: 100%;
  overflow: hidden;
  flex: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hexagon-container {
  position: relative;
  font-size: 0;
  &::before {
    content: '';
    height: var(--hex-before-height, 100%);
    min-height: 50px;
    width: 15px;
    shape-outside: repeating-linear-gradient(
      transparent 0px,
      transparent 48px,
      #f00 50px,
      #f00 0px
    );
    float: left;
  }
}
.select-nest {
  background-color: #006eff;
}
.hexagon-wrapper {
  cursor: pointer;
  width: 33px;
  height: calc(33px * 1.1547);
  clip-path: polygon(0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%, 50% 0%);
  display: inline-block;
  margin: 0 -2px -12px 0;
  position: relative;
  &:hover {
    background-color: #006eff;
  }
}
.hexagon {
  width: 28px;
  height: calc(28px * 1.1547);
  background: #0abf5b;
  clip-path: polygon(0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%, 50% 0%);
  display: inline-block;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.metrics-section {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  gap: 5px;
}

.metric-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.metric-label {
  display: flex;
  align-items: center;
  gap: 5px;
  min-width: var(--metric-label-col-width, auto);
  max-width: var(--metric-label-col-width, none);
  flex-shrink: 0;
}

.metric-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-text {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  // 为了不挤压圆点，留出 dot(8px)+gap(5px)
  max-width: calc(var(--metric-label-col-width, 120px) - 13px);
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.progress-container {
  flex: 1;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  width: var(--progress-width) !important;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 100%
    );
    border-radius: 0 4px 4px 0;
  }
}

.metric-value {
  font-weight: 600;
  color: #374151;
  width: 100px;
  text-align: left;
  flex-shrink: 0;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
}

.view-more {
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
  font-size: 10px;
  transform: translateY(-8px);
}

// Animation for progress bars
@keyframes progressLoad {
  0% {
    width: 0%;
  }
  100% {
    width: var(--target-width);
  }
}

.progress-fill {
  animation: progressLoad 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

// No data styles
.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 20px 0;
  gap: 10px;
}

.no-data-image {
  width: 40px;
  height: 40px;
  opacity: 0.6;
}

.no-data-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}
