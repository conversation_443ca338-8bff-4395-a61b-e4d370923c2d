.date-picker {
  border-radius: 4px;
  background: var(--color-bg-primary-default, #FFF);
  box-shadow: 0 12px 48px -12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  color: var(--color-text-primary, rgba(0, 0, 0, 0.90));
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  .date-picker-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 7px;
  }
  .date-picker-preset-btn {
    display: flex;
    width: 79px;
    padding: 8px 3px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    background: #F2F4F8;
    border: 1px solid #fff;
    color: var(--color-text-primary, rgba(0, 0, 0, 0.90));
    text-align: center;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    &:hover {
      cursor: pointer;
    }
  }
  .date-picker-preset-row {
    display: flex;
    margin-top: 10px;
    align-items: center;
    justify-content: space-around;
  }
  .date-picker-preset-btn-active {
    border: 1px solid #0052D9;
    background: #EDF3FD;
  }
  .date-picker-actions {
    display: flex;
    margin-top: 10px;
    justify-content: flex-end;
  }
  .date-picker-selected-time {
    color: #000;
    font-family: "PingFang SC";
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    margin-top: 10px;
    margin-left: 5px;
  }
  .date-picker-clear-btn {
    // transform: translateY(7px);
  }
  .date-range-picker {
    &:global {
      &>div {
        width: 333px;
        margin-left: 5px;
      }
    }
  }
  .cancel-btn {
    background: #F2F4F8 !important;
    border: none !important;
    color: #000 !important;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }
}