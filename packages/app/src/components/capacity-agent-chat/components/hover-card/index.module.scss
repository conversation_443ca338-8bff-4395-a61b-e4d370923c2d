.hover-card {
  position: fixed;
  bottom: 0;
  display: inline-block;
  cursor: pointer;
  z-index: 1;
  transform: translateX(-50%);

  &:hover {
    z-index: 1010;
  }
}
.pined {
  border: 2px solid #007bff !important;
}
.card-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #E5E7EB;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &.normal {
    width: 250px;
  }

  &.expanded {
    z-index: 1001;
    width: 250px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
    padding-bottom: 15px;
  }
}

// 小卡片样式
.small-card {
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.server-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.server-icon {
  font-size: 12px;
  min-width: 22px;
  display: flex;
  img {
    width: 22px;
  }
}

.server-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  width: 125px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.risk-badge {
  background: #ff6b35;
  color: white;
  padding: 4px 8px;
  border-radius: 40%;
  font-size: 12px;
  font-weight: 550;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 20px;
}

// .risk-badge-normal {

// }

.risk-badge-empty {
  background-color: #BDC8D9 !important;
  font-size: 12px;
  border: 4px solid #E9EEF4;
}

.metrics-compact {
  display: flex;
  flex-direction: column;
}

.metric-row-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.metric-icon {
  font-size: 14px;
  width: 16px;
}

.metric-label {
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90px;
  font-size: 12px;
  font-weight: 550;
}
.metric-value-symbol {
  &::after {
    content: '%';
    position: absolute;
  }
}
.metric-value {
  font-weight: 550;
  color: #333;
  min-width: 32px;
  white-space: nowrap;
  overflow: hidden;
  width: 50px;
  text-overflow: ellipsis;
  text-align: right;
  font-size: 12px;
  padding-right: 5px;
}

.more-link {
  margin-top: 12px;
  color: #007bff;
  font-size: 12px;
  text-align: center;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

// 展开卡片样式
.expanded-card {
  padding: 20px;
  padding-bottom: 0;

  &.has-more-metrics {
    padding-bottom: 0px !important;
  }
}

.expanded-header {
  margin-bottom: 35px;
}

.server-title {
  display: flex;
  align-items: center;
  gap: 12px;

  .server-name {
    font-size: 18px;
    font-weight: 550;
  }

  .server-icon {
    font-size: 20px;
  }
}

.metrics-expanded {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.metric-row-expanded {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.check-more {
  color: #006EFF;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  margin-top: 8px;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;

  .metric-label {
    font-size: 12px;
    color: #333;
    flex: 1;
    font-weight: 550;
  }

  .metric-value {
    font-size: 12px;
    font-weight: 550;
    color: #333;
  }

  .metric-icon {
    font-size: 12px;
    width: 20px;
    display: flex;
    img {
      width: 22px;
    }
  }
}

// 进度条样式
.progress-container {
  margin-left: 30px;
}
.progress-small-container {
  width: 135px !important;
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: #e9ecef;
  border-radius: 5px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

// No data styles
.no-data-container {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.no-data-text {
  margin-left: 30px;
  font-size: 12px;
}

.no-data-expanded {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  flex-direction: column;
}

.no-data-image {
  width: 50px;
}

.no-data-expanded-text {
  margin-top: 15px;
  margin-bottom: 25px;
  font-size: 12px;
}

.check-more-container {
  text-align: right;
  margin-top: 15px;
}