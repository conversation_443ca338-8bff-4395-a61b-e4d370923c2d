import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import s from './index.module.scss';
import { t } from '@tea/app/i18n';
import { useHookstate } from '@hookstate/core';
import globalState from '@src/stores/global.state';
import line from '@src/statics/images/hover-card-line.png';
import cpu from '@src/statics/svg/hover-card-cpu.svg';
import memo from '@src/statics/svg/hover-card-memo.svg';
import disk from '@src/statics/svg/hover-card-disk.svg';
import nodata from '@src/statics/svg/nodata.svg';
import { RiskLevel } from '@src/constants';
import { RiskColor, StatusColorMap } from '@src/constants/color';

interface MetricData {
  label: string;
  value: number;
  color: string;
  icon?: string;
  shortLabel?: string;
}

interface MonitorCardProps {
  metrics?: MetricData[];
  className?: string;
  nodeId?: string;
  arInfo?: AppPluginAPI.PluginAPI;
  hovered?: boolean;
}

const defaultMetrics: MetricData[] = [];

const HoverCard: React.FC<MonitorCardProps> = ({
  metrics = defaultMetrics,
  className = '',
  nodeId = '',
  arInfo,
  hovered = false,
}) => {
  const [isHovered, setIsHovered] = useState(hovered);
  const containerRef = useRef<HTMLDivElement>(null);
  const leaveTimerRef = useRef<any>(null);
  const expandedHoverCardId = useHookstate(globalState.expandedHoverCardId);
  const monitorData = useHookstate(globalState.monitorData);
  const agentCurrentTime = useHookstate(globalState.agentCurrentTime).value;
  const agentShowAllMetrics = useHookstate(globalState.agentShowAllMetrics);
  const data = monitorData[nodeId]?.value;
  const sliceLength = 3;

  const timestampsSec = useMemo(() => data?.Timestamps || [], [data]);
  const periodSec = useMemo(() => data?.Period || 0, [data]);
  const timestampsMs = useMemo(() => (timestampsSec || []).map((s: number) => s * 1000), [timestampsSec]);

  const roundToTwo = (n: any) => {
    if (typeof n !== 'number' || !Number.isFinite(n)) return n;
    return Math.round(n * 100) / 100;
  };

  const currentIndex = useMemo(() => {
    if (!timestampsMs?.length) return -1;
    const periodMs = (periodSec || 0) * 1000;
    if (agentCurrentTime <= timestampsMs[0]) return 0;
    const lastStart = timestampsMs[timestampsMs.length - 1];
    if (periodMs > 0 && agentCurrentTime >= lastStart + periodMs) return timestampsMs.length - 1;
    let left = 0;
    let right = timestampsMs.length - 1;
    let ans = 0;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      if (timestampsMs[mid] <= agentCurrentTime) {
        ans = mid;
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
    return ans;
  }, [agentCurrentTime, timestampsMs, periodSec]);

  const computedMetrics = useMemo(() => {
    const items = data?.NodeMetricItems || [];
    return items.map((m: any) => {
      const value = currentIndex >= 0 ? m?.Values?.[currentIndex] : undefined;
      const level = currentIndex >= 0 ? m?.Levels?.[currentIndex] : undefined;
      // let color = RiskColor.Low;
      // if (level === RiskLevel.High) {
      //   color = RiskColor.High;
      // }
      // if (level === RiskLevel.Medium) {
      //   color = RiskColor.Medium;
      // }
      const color = StatusColorMap[level as keyof typeof StatusColorMap] || RiskColor.Low;
      return {
        label: m?.Name,
        value: roundToTwo(value ?? -1),
        color,
        shortLabel: m?.Name,
        level,
      } as MetricData & { level?: number };
    });
  }, [data, currentIndex]);

  const displayMetrics: MetricData[] = useMemo(() => ((computedMetrics && computedMetrics.length > 0) ? (computedMetrics as any) : metrics), [computedMetrics, metrics]);

  const filteredMetrics: MetricData[] = useMemo(() => (displayMetrics || []).filter((m: any) => m?.value !== undefined && m?.value !== -1), [displayMetrics]);

  const hasMetricData = useMemo(() => filteredMetrics.length > 0, [filteredMetrics]);

  // SmallCard 最多显示3个指标
  const smallCardMetrics = useMemo(() => filteredMetrics.slice(0, sliceLength), [filteredMetrics]);

  // ExpandedCard 根据用户偏好决定显示数量
  const expandedCardMetrics = useMemo(() => {
    const nodeShowAllMetrics = agentShowAllMetrics.value.get(nodeId) || false;
    if (nodeShowAllMetrics) return filteredMetrics;
    return filteredMetrics.slice(0, sliceLength);
  }, [filteredMetrics, agentShowAllMetrics.value, nodeId]);

  const isPined = useMemo(
    () => expandedHoverCardId.value === nodeId,
    [expandedHoverCardId.value, nodeId]
  );
  // 根据全局状态判断当前卡片是否应该展开
  const isExpanded = expandedHoverCardId.value === nodeId;

  const maxLevel = React.useMemo(() => {
    const levels = (computedMetrics ?? [])
      .map(m => m.level)
      .filter((n): n is number => typeof n === 'number');
    return levels.length ? Math.max(...levels) : undefined;
  }, [computedMetrics]);

  const riskText = React.useMemo(() => {
    if (!hasMetricData) return t('暂无');
    if (maxLevel === RiskLevel.UnSupport) return t('不支持');
    if (maxLevel === RiskLevel.High) return t('高');
    if (maxLevel === RiskLevel.Medium) return t('中');
    return t('正常');
  }, [maxLevel, hasMetricData]);

  const isRiskNormal = React.useMemo(() => !(maxLevel === RiskLevel.High || maxLevel === RiskLevel.Medium), [maxLevel]);

  const isUnSupport = useMemo(() => maxLevel === RiskLevel.UnSupport, [maxLevel]);

  const riskBadgeClassName = useMemo(() => {
    if (!hasMetricData) return `${s['risk-badge']} ${s['risk-badge-empty']}`;
    if (isUnSupport) return `${s['risk-badge']} ${s['risk-badge-empty']}`;
    if (isRiskNormal) return `${s['risk-badge']} ${s['risk-badge-normal']}`;
    return s['risk-badge'];
  }, [hasMetricData, isRiskNormal, isUnSupport]);

  const riskBadgeStyle = useMemo(() => {
    if (!hasMetricData || isUnSupport) return undefined;
    const isHighOrMedium = maxLevel === RiskLevel.High || maxLevel === RiskLevel.Medium;
    return { backgroundColor: isHighOrMedium ? RiskColor.High : RiskColor.Low };
  }, [hasMetricData, isUnSupport, maxLevel]);

  const onRefreshCard = useCallback(
    (hovered = false) => {
      if (nodeId) {
        arInfo.removeBar([nodeId]);
        arInfo.createBar([nodeId], {
          width: 40,
          children: (
            <HoverCard
              nodeId={nodeId}
              arInfo={arInfo}
              hovered={hovered}
            />
          ),
        });
      }
    },
    [nodeId]
  );

  const handleMouseEnter = () => {
    setIsHovered(true);
    onRefreshCard(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (expandedHoverCardId.value) {
      arInfo.removeBar([expandedHoverCardId.value]);
      arInfo.createBar([expandedHoverCardId.value], {
        width: 40,
        children: (
          <HoverCard
            nodeId={expandedHoverCardId.value}
            arInfo={arInfo}
          />
        ),
      });
    }
    // 若当前卡片并未被置顶，则在离开时强制刷新为收起态
    if (expandedHoverCardId.value !== nodeId) {
      // onRefreshCard(false);
    }
  };

  // 全局指针移动检测：快速移出时也能可靠收起
  useEffect(() => {
    const onPointerMove = (e: PointerEvent) => {
      const el = containerRef.current;
      if (!el) return;
      const rect = el.getBoundingClientRect();
      const inside = e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom;
      if (!inside && !isExpanded && isHovered) {
        if (leaveTimerRef.current) clearTimeout(leaveTimerRef.current);
        leaveTimerRef.current = setTimeout(() => {
          // 二次确认仍在外部且未置顶
          const r = el.getBoundingClientRect();
          const stillOutside = e.clientX < r.left || e.clientX > r.right || e.clientY < r.top || e.clientY > r.bottom;
          if (stillOutside && !isExpanded) {
            setIsHovered(false);
            onRefreshCard(false);
          }
        }, 30);
      } else if (inside) {
        if (leaveTimerRef.current) clearTimeout(leaveTimerRef.current);
      }
    };
    window.addEventListener('pointermove', onPointerMove, { passive: true });
    return () => {
      window.removeEventListener('pointermove', onPointerMove as any);
      if (leaveTimerRef.current) clearTimeout(leaveTimerRef.current);
    };
  }, [isHovered, isExpanded, onRefreshCard]);

  // 处理卡片点击事件
  const handleCardClick = useCallback(() => {
    if (nodeId) {
      if (expandedHoverCardId.value === nodeId) {
        // expandedHoverCardId.set('');
      } else {
        globalState.set(state => ({
          ...state,
          agentShowResourceCapacity: true,
          agentShowNotificationBanner: true,
          expandedHoverCardId: nodeId,
        }));
      }
    }
  }, [nodeId, expandedHoverCardId]);

  const renderProgressBar = (value: number, color: string, isSmall = false) => (
    <div
      className={
        isSmall ? s['progress-small-container'] : s['progress-container']
      }
    >
      <div className={s['progress-bar']}>
        <div
          className={s['progress-fill']}
          style={{
            width: `${value}%`,
            backgroundColor: color,
          }}
        />
      </div>
    </div>
  );

  const renderSmallCard = () => {
    const hasData = smallCardMetrics.length > 0;
    return (
      <div className={s['small-card']}>
        <div className={s['metrics-compact']}>
          {hasData ? (
            smallCardMetrics.map((metric: any, index: number) => (
              <div key={index} className={s['metric-row-compact']}>
                <span className={s['metric-label']}>
                  {metric.shortLabel ?? metric?.label}
                </span>
                {renderProgressBar(metric.value, metric.color, true)}
                <span className={`${s['metric-value']} ${s['metric-value-symbol']}`}>{metric.value}</span>
              </div>
            ))
          ) : (
            <div className={s['no-data-container']}>
              <img className={s['no-data-image']} src={nodata} />
              <span className={s['no-data-text']}>{isUnSupport ? t('机型不支持') : t('暂无数据')}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const icons = [cpu, memo, disk];
  const renderExpandedCard = () => {
    const hasData = expandedCardMetrics.length > 0;
    return (
      <div className={s['expanded-card']}>
        <div className={s['expanded-header']}>
          <div className={s['server-title']}>
            <span className={s['server-icon']}>
              <img src={line} />
            </span>
            <span className={s['server-name']}>{data?.NodeName ?? ''}</span>
            <div className={riskBadgeClassName} style={riskBadgeStyle}>{riskText}</div>
          </div>
        </div>

        <div className={s['metrics-expanded']}>
          {hasData ? (
            expandedCardMetrics.map((metric: any, index: number) => (
              <div key={index} className={s['metric-row-expanded']}>
                <div className={s['metric-header']}>
                  <span className={s['metric-icon']}>
                    <img src={icons[index]} />
                  </span>
                  <span className={s['metric-label']}>{metric.label}</span>
                  <span className={s['metric-value']}>{metric.value}%</span>
                </div>
                {renderProgressBar(metric.value, metric.color)}
              </div>
            ))
          ) : (
            <div className={s['no-data-expanded']}>
              <img className={s['no-data-image']} src={nodata} />
              <span className={s['no-data-expanded-text']}>{isUnSupport ? t('机型不支持') : t('暂无数据')}</span>
            </div>
          )}
        </div>
        {hasData ? (
          <div className={s['check-more-container']}>
            <span
              className={s['check-more']}
              style={{ cursor: 'pointer' }}
            >
              {t('点击查看')}
            </span>
          </div>
        ) : null}
      </div>
    );
  };

  useEffect(() => {
    // 移除对expandedMode的依赖，现在完全由全局状态控制
  }, []);

  return (
    <div
      ref={containerRef}
      className={`${s['hover-card']} ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleCardClick}
    >
      <div
        className={`${s['card-content']} ${isHovered || isExpanded ? s.expanded : s.normal} ${isPined ? s.pined : ''}`}
      >
        {isHovered || isExpanded ? renderExpandedCard() : renderSmallCard()}
      </div>
    </div>
  );
};

export default HoverCard;
