/* eslint-disable */
import React from 'react';
import { Progress } from 'tdesign-react';
import { RiskLevelEnum } from '@src/constants';
import LowArrowSvg from '@src/statics/svg/low-green-arrow.svg';
import MediumArrowSvg from '@src/statics/svg/mid-yellow-arrow.svg';
import HighArrowSvg from '@src/statics/svg/high-red-arrow.svg'; 
import s from './index.module.scss';

interface IPopoverPanelProps {
  riskInfoItem: any;
}
/**
 * 图元角标组件
 * @returns
 */
export default function PopoverPanel(props: IPopoverPanelProps): React.ReactElement {
  const {
    riskInfoItem,
  } = props;

  return (
    <div className={s['popover-panel']}>
      <div className={s['popover-panel-hd']}>
        <div className={s['popover-panel-hd-name']}>{riskInfoItem?.NodeName ?? '-'}</div>
        <div className={s['popover-panel-hd-rto']}>
          <img src={
            riskInfoItem?.RiskLevel === RiskLevelEnum.HIGH
              ? HighArrowSvg
              : riskInfoItem?.RiskLevel === RiskLevelEnum.MEDIUM
                ? MediumArrowSvg
                : LowArrowSvg
            } alt="rto" />
          <span>{`${riskInfoItem?.MetricValue}%`}</span>
        </div>
      </div>
      <div className={s['popover-panel-bd']}>
        <Progress
          strokeWidth={10}
          label={false}
          percentage={riskInfoItem?.MetricValue}
          color={
            riskInfoItem?.RiskLevel === RiskLevelEnum.HIGH
              ? '#E54545'
              : riskInfoItem?.RiskLevel === RiskLevelEnum.MEDIUM
              ? '#F5A623'
              : '#0CBF5B'
          }
        >
        </Progress>
      </div>
    </div>
  );
}
