.time-line-container {
  position: relative;
  width: 100%;
  padding: 12px 40px;
  background: #eef3fa;
  border-radius: 12px;
  outline: none;
  user-select: none;
  box-sizing: border-box;
}

.time-header {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  font-weight: 500;
}
.start-time {
  margin-left: -30px;
}
.end-time {
  margin-right: -30px;
}
.start-time,
.end-time {
  padding: 2px 8px;
  border-radius: 4px;
  color: #3D3C3C;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
}

.progress-container {
  position: relative;
  padding: 0;
  padding-top: 5px;
}

.progress-bar {
  position: relative;
  height: 15px;
  border-radius: 5px;
  background: #28a745;
  cursor: pointer;
}

.risk-segment {
  position: absolute;
  top: 0;
  height: 100%;
  cursor: pointer;
  transition: transform 0.2s ease;
  &:hover {
    z-index: 2;
  }
}
.risk-medium {
  background: #ffa26b;
}
.risk-high {
  background: #ff5a5f;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  transform: translate(-50%, -14px);
  z-index: 3;
  text-align: center;
  img {
    user-select: none;
    width: 7px;
  }
}

.time-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translate(-50%, 8px);
  background: #333;
  color: #fff;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
  &::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: #333;
  }
}

.risk-time-labels {
  position: relative;
  height: 16px;
  margin-top: 6px;
}
.risk-time-label {
  position: absolute;
  transform: translateX(-50%);
  font-size: 11px;
  color: #666;
}

.time-marks {
  position: relative;
  height: 16px;
  margin-top: 8px;
}
.time-mark {
  position: absolute;
  transform: translateX(-50%);
  font-size: 11px;
  color: #666;
  &:last-child {
    min-width: 60px;
  }
}

.control-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-top: 6px;
}

.play-section {
  display: flex;
  align-items: center;
}
.status-stop-dot {
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: #D9D9D9;
    left: -10px;
    top: 6px;
  }
}
.status-playing-dot {
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: #D9D9D9;
    border-radius: 50%;
    left: -10px;
    top: 6px;
  }
}
.status-text {
  font-size: 11px;
  color: #666;
  margin-left: 15px;
  position: relative;
  width: 35px;
  text-align: center;
}
.speed-section,
.unit-section {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 25px;
}
.label {
  font-size: 11px;
  color: #666;
}
.speed-value {
  min-width: 28px;
  text-align: center;
  font-weight: 600;
  color: var(--text-icon-font-gy-340-placeholder, rgba(0, 0, 0, 0.40));
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "PingFang SC";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  border-radius: 3px;
  border: 1px solid var(--Gray-Gray4-, #DCDCDC);
  background: var(--Gray-White, #FFF);
}
.interval-time {
  text-align: center;
  font-weight: 600;
  color: var(--text-icon-font-gy-340-placeholder, rgba(0, 0, 0, 0.40));
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "PingFang SC";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  border-radius: 3px;
  border: 1px solid var(--Gray-Gray4-, #DCDCDC);
  background: var(--Gray-White, #FFF);
  padding: 0 10px;
}
.plus-btn {
  width: 17px;
  text-align: center;
  font-weight: 600;
  color: var(--text-icon-font-gy-340-placeholder, rgba(0, 0, 0, 0.40));
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "PingFang SC";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  border-radius: 3px;
  border: 1px solid var(--Gray-Gray4-, #DCDCDC);
  background: var(--Gray-White, #FFF);
  &:hover {
    cursor: pointer;
  }
}
.unit-buttons {
  display: flex;
  gap: 4px;
}

.risk-tooltip {
  background: #f6f7fb;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  position: relative;
}
.risk-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #f6f7fb;
}
.tooltip-content {
  padding: 12px 16px;
  min-width: 200px;
  max-width: 320px;
}
.tooltip-title {
  font-size: 14px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 6px;
}
.tooltip-time {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 6px;
}
.tooltip-criteria {
  font-size: 12px;
  color: #334155;
}
.play-btn {
  margin-left: 6px !important;
  margin-right: 6px !important;
}
.play-btn, .nav-btn {
  background: none !important;
  outline: none !important;
  border: none !important;
  display: inline-block !important;
  height: 20px !important;
  padding: 0 !important;
  min-width: 16px !important;
}