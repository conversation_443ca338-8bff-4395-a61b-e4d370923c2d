export type TimeUnit = 'hour' | 'minute' | 'second';

export interface RiskChildSegment {
  startTime: number;
  endTime: number;
  level: number;
  color?: string;
}

export interface RiskSegment {
  startTime: number;
  endTime: number;
  // 数值级别，参考 StatusColorMap（0/1/2/3/-1）
  level: number;
  // 可选的预计算颜色；若未提供，组件内会基于 level 使用 StatusColorMap 映射
  color?: string;
  phenomenon: string;
  criteria: string;
  children?: RiskChildSegment[];
}

export interface TimeLineBarProps {
  startTime: number;
  endTime: number;
  currentTime?: number;
  riskSegments?: RiskSegment[];
  isPlaying?: boolean;
  speed?: number; // X 倍：1,1.5,2,3,5
  timeUnit?: TimeUnit; // 小时/分钟/秒
  period?: number; // 时间尺度(天：86400，小时：3600,5分钟：300,1分钟：60)
  timestamps?: number[]; // 服务端返回的时间戳数组
  levels?: number[]; // 服务端返回的风险等级数组，对应timestamps
  onTimeChange?: (time: number) => void;
  onPlayStateChange?: (isPlaying: boolean) => void;
  onSpeedChange?: (speed: number) => void;
  onTimeUnitChange?: (unit: TimeUnit) => void;
  className?: string;
}


