import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Bubble } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import moveBar from '@src/statics/svg/move-bar.svg';
import playing from '@src/statics/svg/playing.svg';
import playLeft from '@src/statics/svg/play-left.svg';
import playRight from '@src/statics/svg/play-right.svg';
import styles from './index.module.scss';
import { TimeLineBarProps, TimeUnit } from './types';
import { StatusColorMap } from '@src/constants/color';

const SPEED_OPTIONS = [1, 1.5, 2, 3, 5] as const;
const MAX_TIME_MARKS = 6; // 最多显示的时间刻度数量

function clamp(value: number, min: number, max: number) {
  return Math.max(min, Math.min(max, value));
}


const TimeLineBar: React.FC<TimeLineBarProps> = ({
  startTime,
  endTime,
  currentTime = startTime,
  riskSegments = [],
  isPlaying = false,
  speed = 1,
  timeUnit = 'hour',
  period = 3600, // 默认1小时
  timestamps,
  onTimeChange,
  onPlayStateChange,
  onSpeedChange,
  onTimeUnitChange,
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragTime, setDragTime] = useState<number | null>(null);
  const [isFocused, setIsFocused] = useState(false);

  const [keyHoldDirection, setKeyHoldDirection] = useState<-1 | 0 | 1>(0);
  const keyHoldTimerRef = useRef<number | null>(null);

  const progressBarRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const totalDuration = endTime - startTime;
  const timestampsSorted = useMemo(() => {
    if (!timestamps || timestamps.length === 0) return undefined as number[] | undefined;
    const list = [...timestamps].sort((a, b) => a - b);
    return list;
  }, [timestamps]);

  const findNearestTimestamp = useCallback((targetMs: number) => {
    if (!timestampsSorted || timestampsSorted.length === 0) return targetMs;
    let left = 0;
    let right = timestampsSorted.length - 1;
    if (targetMs <= timestampsSorted[0]) return timestampsSorted[0];
    if (targetMs >= timestampsSorted[right]) return timestampsSorted[right];
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const value = timestampsSorted[mid];
      if (value === targetMs) return value;
      if (value < targetMs) left = mid + 1;
      else right = mid - 1;
    }
    const idx = Math.max(1, left);
    const prev = timestampsSorted[idx - 1];
    const next = timestampsSorted[idx] ?? timestampsSorted[idx - 1];
    return (targetMs - prev) <= (next - targetMs) ? prev : next;
  }, [timestampsSorted]);

  const findIndexOfTimestamp = useCallback((timeMs: number) => {
    if (!timestampsSorted || timestampsSorted.length === 0) return -1;
    let left = 0;
    let right = timestampsSorted.length - 1;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const value = timestampsSorted[mid];
      if (value === timeMs) return mid;
      if (value < timeMs) left = mid + 1;
      else right = mid - 1;
    }
    return Math.max(0, Math.min(timestampsSorted.length - 1, left));
  }, [timestampsSorted]);
  const effectiveTime = dragTime ?? currentTime;
  const currentPercent = ((effectiveTime - startTime) / totalDuration) * 100;

  const formatDateTime = useCallback((timestamp: number) => {
    const date = new Date(timestamp);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return t('{{month}}月{{day}}日 {{hours}}:{{minutes}}', { month, day, hours, minutes });
  }, []);

  const formatHhmm = useCallback((timestamp: number) => {
    const date = new Date(timestamp);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}: ${minutes}`;
  }, []);

  // 格式化风险时段时间显示，跨天时显示月日
  const formatRiskTime = useCallback((start: number, end: number) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const durationDays = (endTime - startTime) / (1000 * 60 * 60 * 24);
    if (durationDays > 1) {
      // 跨天显示月日
      const startMonth = startDate.getMonth() + 1;
      const startDay = startDate.getDate();
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();

      const startHours = String(startDate.getHours()).padStart(2, '0');
      const startMinutes = String(startDate.getMinutes()).padStart(2, '0');
      const endHours = String(endDate.getHours()).padStart(2, '0');
      const endMinutes = String(endDate.getMinutes()).padStart(2, '0');

      return `${startMonth}/${startDay} ${startHours}:${startMinutes}~${endMonth}/${endDay} ${endHours}:${endMinutes}`;
    }
    // 同一天显示时分
    return `${formatHhmm(start)}~${formatHhmm(end)}`;
  }, [formatHhmm, startTime, endTime]);

  // 智能格式化时间标记，跨天时显示日期
  const formatTimeMark = useCallback((timestamp: number) => {
    const durationDays = (endTime - startTime) / (1000 * 60 * 60 * 24);

    if (durationDays > 1) {
      // 跨天时显示日期和时间
      const date = new Date(timestamp);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      // 始终显示完整的时分信息
      return t('{{month}}/{{day}} {{hours}}:{{minutes}}', { month, day, hours, minutes });
    }
    // 单天内只显示时间
    return formatHhmm(timestamp);
  }, [startTime, endTime, formatHhmm]);

  // 动态刻度（均匀分布，最多显示指定数量）
  const timeMarks = useMemo(() => {
    const durationMs = endTime - startTime;
    const marks: { time: number; position: number }[] = [];

    // 直接根据时间范围均匀分布刻度点，确保不超过最大显示数量
    const step = durationMs / (MAX_TIME_MARKS - 1);

    for (let i = 0; i < MAX_TIME_MARKS; i++) {
      const time = startTime + step * i;
      const position = (i / (MAX_TIME_MARKS - 1)) * 100;
      marks.push({ time, position });
    }

    return marks;
  }, [startTime, endTime]);

  // 播放：优先对齐离散 timestamps
  useEffect(() => {
    if (!isPlaying) return;
    const timer = window.setInterval(() => {
      if (timestampsSorted && timestampsSorted.length > 0) {
        const target = currentTime + period * 1000 * speed;
        const idx = findIndexOfTimestamp(currentTime);
        let nextTime = timestampsSorted[idx] ?? currentTime;
        if (target >= nextTime) {
          const nextIdx = timestampsSorted.findIndex(t => t >= target);
          const resolvedIdx = nextIdx === -1 ? idx + 1 : nextIdx;
          nextTime = timestampsSorted[resolvedIdx] ?? endTime;
        }
        if (nextTime >= endTime || (idx >= timestampsSorted.length - 1 && nextTime === timestampsSorted[idx])) {
          onTimeChange?.(endTime);
          onPlayStateChange?.(false);
        } else {
          onTimeChange?.(nextTime);
        }
        return;
      }
      const stepPerSecond = period * 1000 * speed;
      const next = currentTime + stepPerSecond;
      if (next >= endTime) {
        onTimeChange?.(endTime);
        onPlayStateChange?.(false);
      } else {
        onTimeChange?.(next);
      }
    }, 1000);
    return () => window.clearInterval(timer);
  }, [isPlaying, speed, currentTime, endTime, period, onTimeChange, onPlayStateChange, timestampsSorted, findIndexOfTimestamp]);

  // 鼠标拖拽
  const updateTimeByClientX = useCallback((clientX: number) => {
    if (!progressBarRef.current) return startTime;
    const rect = progressBarRef.current.getBoundingClientRect();
    const x = clamp(clientX - rect.left, 0, rect.width);
    const percentage = x / rect.width;
    const tMs = startTime + totalDuration * percentage;
    return findNearestTimestamp(tMs);
  }, [startTime, totalDuration, findNearestTimestamp]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setIsFocused(true);
    onPlayStateChange?.(false);
    const newTime = updateTimeByClientX(e.clientX);
    setDragTime(newTime);
    onTimeChange?.(newTime); // 立即更新全局状态
  }, [onPlayStateChange, updateTimeByClientX, onTimeChange]);

  const handleMouseUp = useCallback(() => {
    // 强制重置拖动状态，无论当前状态如何
    if (isDragging) {
      if (dragTime !== null) {
        onTimeChange?.(clamp(dragTime, startTime, endTime));
      }
      setDragTime(null);
    }
    // 确保拖动状态被重置
    setIsDragging(false);
  }, [isDragging, dragTime, startTime, endTime, onTimeChange]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    // 额外的安全检查：确保鼠标按钮确实被按下
    if (e.buttons === 0) {
      // 鼠标按钮没有被按下，强制结束拖动
      handleMouseUp();
      return;
    }

    const newTime = updateTimeByClientX(e.clientX);
    setDragTime(newTime);
    onTimeChange?.(newTime); // 实时更新全局状态
  }, [isDragging, updateTimeByClientX, handleMouseUp, onTimeChange]);

  // 键盘：单击与长按
  const stepOnceByDirection = useCallback((direction: -1 | 1) => {
    if (timestampsSorted && timestampsSorted.length > 0) {
      const idx = findIndexOfTimestamp(currentTime);
      const nextIdx = clamp(idx + direction, 0, timestampsSorted.length - 1);
      const next = timestampsSorted[nextIdx];
      if (next !== currentTime) {
        onTimeChange?.(next);
        onPlayStateChange?.(false);
      }
      return;
    }
    const step = period * 1000;
    const next = clamp(currentTime + direction * step, startTime, endTime);
    if (next !== currentTime) {
      onTimeChange?.(next);
      onPlayStateChange?.(false);
    }
  }, [currentTime, startTime, endTime, period, onTimeChange, onPlayStateChange, timestampsSorted, findIndexOfTimestamp]);

  const startKeyHold = useCallback((direction: -1 | 1) => {
    setKeyHoldDirection(direction);
    stepOnceByDirection(direction);
    // 200ms 后开始以 80ms 间隔连发
    window.setTimeout(() => {
      if (keyHoldDirection === direction) {
        if (keyHoldTimerRef.current) window.clearInterval(keyHoldTimerRef.current);
        keyHoldTimerRef.current = window.setInterval(() => stepOnceByDirection(direction), 80);
      }
    }, 200);
  }, [keyHoldDirection, stepOnceByDirection]);

  const stopKeyHold = useCallback(() => {
    setKeyHoldDirection(0);
    if (keyHoldTimerRef.current) {
      window.clearInterval(keyHoldTimerRef.current);
      keyHoldTimerRef.current = null;
    }
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isFocused) return;
    if (e.key === 'ArrowLeft') {
      if (keyHoldDirection !== -1) startKeyHold(-1);
    } else if (e.key === 'ArrowRight') {
      if (keyHoldDirection !== 1) startKeyHold(1);
    }
  }, [isFocused, keyHoldDirection, startKeyHold]);

  const handleKeyUp = useCallback((e: KeyboardEvent) => {
    if (!isFocused) return;
    if (e.key === 'ArrowLeft' && keyHoldDirection === -1) stopKeyHold();
    if (e.key === 'ArrowRight' && keyHoldDirection === 1) stopKeyHold();
  }, [isFocused, keyHoldDirection, stopKeyHold]);


  // 导航到前/后一个风险段
  const getSortedRisks = useCallback(() => [...riskSegments].sort((a, b) => a.startTime - b.startTime), [riskSegments]);

  // 检查是否到达第一个或最后一个风险时段
  const isAtFirstRisk = useMemo(() => {
    const sorted = getSortedRisks();
    return sorted.length > 0 && currentTime <= sorted[0].startTime;
  }, [currentTime, getSortedRisks]);

  const isAtLastRisk = useMemo(() => {
    const sorted = getSortedRisks();
    return sorted.length > 0 && currentTime >= sorted[sorted.length - 1].startTime;
  }, [currentTime, getSortedRisks]);

  const handlePreviousRisk = useCallback(() => {
    const sorted = getSortedRisks();
    const prev = [...sorted].reverse().find(r => r.startTime < currentTime);
    if (prev) {
      onTimeChange?.(prev.startTime);
      onPlayStateChange?.(false);
    }
  }, [currentTime, getSortedRisks, onTimeChange, onPlayStateChange]);

  const handleNextRisk = useCallback(() => {
    const sorted = getSortedRisks();
    const next = sorted.find(r => r.startTime > currentTime);
    if (next) {
      onTimeChange?.(next.startTime);
      onPlayStateChange?.(false);
    }
  }, [currentTime, getSortedRisks, onTimeChange, onPlayStateChange]);

  // 事件绑定
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      // 添加窗口失去焦点的监听器作为安全机制
      window.addEventListener('blur', handleMouseUp);

      // 添加鼠标进入/离开文档的监听器，只在真正离开浏览器窗口时结束拖动
      const handleMouseEnter = () => {
        // 鼠标重新进入文档，不处理
      };

      const handleMouseLeave = (e: MouseEvent) => {
        // 只有当鼠标真正离开浏览器窗口时才结束拖动
        if (e.clientY <= 0 || e.clientX <= 0
            || e.clientX >= window.innerWidth || e.clientY >= window.innerHeight) {
          handleMouseUp();
        }
      };

      document.addEventListener('mouseenter', handleMouseEnter);
      document.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        window.removeEventListener('blur', handleMouseUp);
        document.removeEventListener('mouseenter', handleMouseEnter);
        document.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
    return () => {
      // 如果没有在拖动，不需要清理
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 组件卸载时清理所有状态
  useEffect(() => () => {
    setIsDragging(false);
    setDragTime(null);
    stopKeyHold();
  }, [stopKeyHold]);

  useEffect(() => {
    if (isFocused) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
      stopKeyHold();
    };
  }, [isFocused, handleKeyDown, handleKeyUp, stopKeyHold]);

  const speedLabel = `${speed}X`;


  return (
    <div
      ref={containerRef}
      className={`${styles['time-line-container']} ${className}`}
      tabIndex={0}
      onFocus={() => setIsFocused(true)}
      onBlur={() => {
        setIsFocused(false); stopKeyHold();
      }}
    >
      <div className={styles['time-header']}>
        <span className={styles['start-time']}>{formatDateTime(startTime)}</span>
        <span className={styles['end-time']}>{formatDateTime(endTime)}</span>
      </div>

      <div className={styles['progress-container']}>
        <div
          ref={progressBarRef}
          className={styles['progress-bar']}
          onMouseDown={handleMouseDown}
        >
          {/* 风险区段 */}
          {riskSegments.map((risk, index) => {
            const left = ((risk.startTime - startTime) / totalDuration) * 100;
            const width = ((risk.endTime - risk.startTime) / totalDuration) * 100;
            // const backgroundColor = risk.color || (StatusColorMap as any)[risk.level];
            return (
              <div
                key={index}
                className={styles['risk-segment']}
                style={{ left: `${left}%`, width: `${width}%` }}
              >
                <Bubble style={{ zIndex: 1001 }} content={
                  <div className={styles['tooltip-content']}>
                    <div className={styles['tooltip-title']}>{risk?.phenomenon}</div>
                    <div className={styles['tooltip-time']}>
                      {formatRiskTime(risk?.startTime, risk?.endTime)}
                    </div>
                    <div className={styles['tooltip-criteria']}>{risk?.criteria}</div>
                  </div>
                }>
                  <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    {(risk.children || []).map((child, ci) => {
                      const childLeft = ((child.startTime - risk.startTime) / (risk.endTime - risk.startTime)) * 100;
                      const childWidth = ((child.endTime - child.startTime) / (risk.endTime - risk.startTime)) * 100;
                      const childBg = child.color || (StatusColorMap as any)[child.level];
                      return (
                        <div
                          key={ci}
                          style={{
                            position: 'absolute',
                            left: `${childLeft}%`,
                            width: `${childWidth}%`,
                            top: 0,
                            bottom: 0,
                            background: childBg,
                          }}
                        />
                      );
                    })}
                  </div>
                </Bubble>
              </div>
            );
          })}

          {/* 进度填充（绿色背景已代表安全，这里只展示指针） */}
          <div
            className={styles['progress-thumb']}
            style={{ left: `${currentPercent}%` }}
          >
            <img src={moveBar} onDragStart={e => e.preventDefault()} draggable={false} />
            {isDragging && (
              <div className={styles['time-tooltip']}>
                {formatDateTime(effectiveTime)}
              </div>
            )}
          </div>
        </div>


      </div>

      {/* 底部刻度 */}
      <div className={styles['time-marks']}>
        {timeMarks.map((m, idx) => (
          <div key={idx} className={styles['time-mark']} style={{ left: `${m.position}%` }}>
            {formatTimeMark(m.time)}
          </div>
        ))}
      </div>

      {/* 控制面板 */}
      <div className={styles['control-panel']}>
        <Bubble content={isAtFirstRisk ? t('已到达第一个风险时段') : t('上一个风险时段')} placement="top">
          <Button
            type="weak"
            onClick={handlePreviousRisk}
            className={styles['nav-btn']}
            disabled={isAtFirstRisk}
          >
            <img src={playLeft} />
          </Button>
        </Bubble>

        <div className={styles['play-section']}>
          <Bubble content={isPlaying ? t('暂停') : t('开始')} placement="top">
            <Button
              type="weak"
              onClick={() => onPlayStateChange?.(!isPlaying)}
              className={styles['play-btn']}
            >
              {isPlaying ? <img src={playing} /> : <img src={playRight} />}
            </Button>
          </Bubble>
          <Bubble content={isAtLastRisk ? t('已到达最后一个风险时段') : t('下一个风险时段')} placement="top">
            <Button
              type="weak"
              onClick={handleNextRisk}
              className={styles['nav-btn']}
              disabled={isAtLastRisk}
            >
              <img src={playRight} />
            </Button>
          </Bubble>
          <span className={isPlaying ? `${styles['status-text']} ${styles['status-playing-dot']}` : `${styles['status-text']}  ${styles['status-stop-dot']}`}>{isPlaying ? t('播放中') : t('暂停中')}</span>
        </div>

        <div className={styles['speed-section']}>
          <span className={styles.label}>{t('速度:')}</span>
          <span className={styles['speed-value']}>{speedLabel}</span>
          <div
            onClick={() => {
              const idx = SPEED_OPTIONS.findIndex(s => s === speed);
              const next = SPEED_OPTIONS[(idx + 1) % SPEED_OPTIONS.length];
              onSpeedChange?.(next);
            }}
            className={styles['plus-btn']}
          >
            +
          </div>
        </div>

        <div className={styles['unit-section']}>
          <span className={styles.label}>{t('时间尺度:')}</span>
          <div className={styles['unit-buttons']}>
            {useMemo(() => {
              let options: { unit: TimeUnit; label: string; description: string }[] = [];

              // 根据服务端返回的Period显示对应的时间尺度
              if (period === 60) {
                // 1分钟
                options = [
                  { unit: 'minute', label: t('1分钟'), description: t('1分钟间隔') },
                ];
              } else if (period === 300) {
                // 5分钟
                options = [
                  { unit: 'minute', label: t('5分钟'), description: t('5分钟间隔') },
                ];
              } else if (period === 3600) {
                // 1小时
                options = [
                  { unit: 'hour', label: t('1小时'), description: t('1小时间隔') },
                ];
              } else if (period === 86400) {
                // 1天
                options = [
                  { unit: 'hour', label: t('1天'), description: t('1天间隔') },
                ];
              } else {
                // 其他情况，显示秒数
                const minutes = Math.floor(period / 60);
                const hours = Math.floor(period / 3600);
                const days = Math.floor(period / 86400);

                let label = '';
                let unit: TimeUnit = 'second';

                if (days > 0) {
                  label = t('{{days}}天', { days });
                  unit = 'hour';
                } else if (hours > 0) {
                  label = t('{{hours}}小时', { hours });
                  unit = 'hour';
                } else if (minutes > 0) {
                  label = t('{{minutes}}分钟', { minutes });
                  unit = 'minute';
                } else {
                  label = t('{{seconds}}秒', { seconds: period });
                  unit = 'second';
                }

                options = [
                  { unit, label, description: t('{{period}}秒间隔', { period }) },
                ];
              }

              return options.map(option => (
                <div
                  key={option.unit}
                  onClick={() => onTimeUnitChange?.(option.unit)}
                  title={option.description}
                  className={styles['interval-time']}
                >
                  {option.label}
                </div>
              ));
            }, [period, timeUnit, onTimeUnitChange])}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimeLineBar;


