/* eslint-disable no-nested-ternary */
import React, { useEffect, useState, useMemo } from 'react';
import { useHookstate } from '@hookstate/core';
import globalState from '@src/stores/global.state';
import { t } from '@tea/app/i18n';
import TimeLineBar from './index';
import { StatusColorMap } from '@src/constants/color';

const App: React.FC = () => {
  const global = useHookstate(globalState);
  const { startDateTime, endDateTime } = global.agentDateRange.get();
  const monitorDataResult = global.monitorDataResult.get();

  // 服务端离散时间点（秒）转换为毫秒
  const rawTimestamps: number[] = (monitorDataResult as any)?.Timestamps || [];
  const timestamps = useMemo(() => (rawTimestamps || []).map(s => s * 1000), [rawTimestamps]);

  // 起止时间优先使用 timestamps，其次使用外部范围
  const startTime = timestamps.length > 0
    ? timestamps[0]
    : (startDateTime ? startDateTime.valueOf() : '');
  const endTime = timestamps.length > 0
    ? timestamps[timestamps.length - 1]
    : (endDateTime ? endDateTime.valueOf() : '');

  // 从agentData获取Period，默认为3600（1小时）
  const period = (monitorDataResult as any)?.Period || 3600;
  const levels: number[] = (monitorDataResult as any)?.Levels || [];

  const [speed, setSpeed] = useState(1);

  // 使用全局状态管理播放状态和当前时间
  const isPlaying = global.agentTimeLineBarPlaying.get();
  const currentTime = global.agentCurrentTime.get();

  // 根据服务端返回的Period自动设置时间尺度
  const autoTimeUnit = useMemo(() => {
    if (period <= 60) {
      return 'minute'; // 1分钟及以下：分钟
    } if (period <= 3600) {
      return 'minute'; // 1小时及以下：分钟
    }
    return 'hour'; // 1小时以上：小时
  }, [period]);

  const [timeUnit, setTimeUnit] = useState<'hour' | 'minute' | 'second'>(autoTimeUnit);

  // 基于 monitorDataResult.Items 和 Levels 生成风险时段：
  // 1) 父段按 Items；2) 父段内 children 合并连续相同 level；3) 父/子颜色由 StatusColorMap 映射
  const riskSegments = useMemo(() => {
    const items: Array<any> = (monitorDataResult as any)?.Items || [];
    const levelSeries: number[] = (monitorDataResult as any)?.Levels || [];
    const tsSeconds: number[] = (monitorDataResult as any)?.Timestamps || [];

    if (!items?.length || !levelSeries?.length || !tsSeconds?.length) return [] as any[];

    // 将秒时间戳转为毫秒数组，便于比较
    const tsMs: number[] = tsSeconds.map(s => s * 1000);

    const segments: Array<{ startTime: number; endTime: number; level: number; color?: string; phenomenon: string; criteria: string; children: Array<{ startTime: number; endTime: number; level: number; color?: string; }>; }> = [];

    items.forEach((item: any) => {
      const segStartMs = Number(item.StartTime) * 1000;
      const segEndMs = Number(item.EndTime) * 1000;

      // 找到该 Item 覆盖的 timestamp 区间索引范围
      let startIdx = -1;
      let endIdx = -1;
      for (let i = 0; i < tsMs.length; i += 1) {
        if (startIdx === -1 && tsMs[i] >= segStartMs) startIdx = i;
        if (tsMs[i] <= segEndMs) endIdx = i;
        if (tsMs[i] > segEndMs) break;
      }
      if (startIdx === -1 || endIdx === -1 || endIdx < startIdx) return;

      // 在 [startIdx, endIdx] 内将连续相同 level 合并为子段
      let runLevel = levelSeries[startIdx];
      let runStartIdx = startIdx;
      const children: Array<{ startTime: number; endTime: number; level: number; color?: string; }> = [];
      for (let i = startIdx + 1; i <= endIdx + 1; i += 1) {
        const levelChanged = i > endIdx || levelSeries[i] !== runLevel;
        if (levelChanged) {
          const childStart = tsMs[runStartIdx];
          // 结束时间取当前 idx 的时间（若 i<=endIdx，则使用 tsMs[i]；否则使用 segEndMs）
          const childEnd = i <= endIdx ? tsMs[i] : Math.min(segEndMs, tsMs[endIdx] + (tsMs[1] - tsMs[0] || period * 1000));
          const color = (StatusColorMap as any)[runLevel];
          const clampedStart = Math.max(childStart, segStartMs);
          const clampedEnd = Math.min(childEnd, segEndMs);
          if (clampedEnd > clampedStart) {
            children.push({
              startTime: clampedStart,
              endTime: clampedEnd,
              level: runLevel,
              color,
            });
          }
          // 开始新 run
          runLevel = i <= endIdx ? levelSeries[i] : runLevel;
          runStartIdx = i;
        }
      }

      // 父段：使用第一个子段的 level/color 作为代表（也可根据业务改为最大 level）
      const parentLevel = children.length > 0 ? children.reduce((max, c) => (c.level > max ? c.level : max), children[0].level) : runLevel;
      const parentColor = (StatusColorMap as any)[parentLevel];
      segments.push({
        startTime: segStartMs,
        endTime: segEndMs,
        level: parentLevel,
        color: parentColor,
        phenomenon: item.Title || t('风险'),
        criteria: item.Description || t('风险时段'),
        children,
      });
    });

    // 过滤掉无效或零长度片段
    return segments.filter(s => Number.isFinite(s.startTime) && Number.isFinite(s.endTime) && s.endTime > s.startTime);
  }, [monitorDataResult, period, t]);

  // 当时间范围或Period变化时，重置当前时间和时间尺度
  useEffect(() => {
    global.agentCurrentTime.set(startTime);
    setTimeUnit(autoTimeUnit);
  }, [startTime, endTime, autoTimeUnit, period]);

  // 防止播放越界
  useEffect(() => {
    if (currentTime > endTime) global.agentCurrentTime.set(endTime);
    if (currentTime < startTime) global.agentCurrentTime.set(startTime);
  }, [currentTime, startTime, endTime]);

  return (
    <div style={{ padding: 20, width: 644, zIndex: 998, position: 'absolute', left: '50%', transform: 'translateX(-50%)', bottom: 120 }}>
      <TimeLineBar
        startTime={startTime}
        endTime={endTime}
        currentTime={currentTime}
        timestamps={timestamps}
        levels={levels}
        riskSegments={riskSegments}
        isPlaying={isPlaying}
        speed={speed}
        timeUnit={timeUnit}
        period={period}
        onTimeChange={(time) => {
          global.agentCurrentTime.set(time);
        }}
        onPlayStateChange={(playing) => {
          global.agentTimeLineBarPlaying.set(playing);
        }}
        onSpeedChange={setSpeed}
        onTimeUnitChange={setTimeUnit}
      />
    </div>
  );
};

export default App;


