import React from 'react';
import { useHookstate } from '@hookstate/core';
import globalState from '@src/stores/global.state';
import { Tooltip } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface AnalysisResultCardProps {
  className?: string;
}

const AnalysisResultCard: React.FC<AnalysisResultCardProps> = ({
  className = '',
}) => {
  const global = useHookstate(globalState);
  const monitorDataResult = global.monitorDataResult.get();

  // 从agentData获取Items数据
  const items = (monitorDataResult as any)?.Items || [];

  // 格式化时间戳为可读格式
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000); // 转换为毫秒
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return t('{{month}}月{{day}}日-{{hours}}:{{minutes}}', { month, day, hours, minutes });
  };

  // 转换Items为风险时段格式，并保留原始起止时间（毫秒）
  const riskPeriods = items.map((item: any) => ({
    timeRange: `${formatTimestamp(item.StartTime)}-${formatTimestamp(item.EndTime)}`,
    description: item.Description || t('风险时段'),
    startMs: Number(item.StartTime) * 1000,
    endMs: Number(item.EndTime) * 1000,
  }));

  const riskCount = items.length;
  const title = riskCount > 0
    ? t('回放分析已完成！共有{{count}}个风险时段！', { count: riskCount })
    : t('回放分析已完成！未发现风险时段');

  return (
    <div className={`${s['analysis-result-card']} ${className}`}>
      <div className={s.header}>
        <div className={s['warning-icon']}>!</div>
        <div className={s.title}>{title}</div>
      </div>

      <div className={s.content}>
        {riskPeriods.length > 0 ? (
          riskPeriods.map((period, index) => (
            <div
              key={index}
              className={s['risk-period-row']}
              onClick={() => {
                // 点击时将时间轴指针跳到该时段开始位置，并停止播放
                global.agentCurrentTime.set(period.startMs);
                global.agentTimeLineBarPlaying.set(false);
              }}
              style={{ cursor: 'pointer' }}
            >
              <div className={s['time-range']}>{period.timeRange}</div>
              <div className={s.description}>
                <Tooltip title={period.description}>
                  {period.description}
                </Tooltip>
              </div>
            </div>
          ))
        ) : (
          <div className={s['no-risk-message']}>
            {t('未发现风险时段')}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalysisResultCard;
