import React, { useEffect, useState, useMemo } from 'react';
import {
  Chat<PERSON>ontent,
  ChatItem,
  ChatReasoning,
  ChatMarkdown,
} from '@tencent/cloud-chat-ui';
import globalState from '@src/stores/global.state';
import { useHookstate } from '@hookstate/core';
import { t } from '@tea/app/i18n';
import { isEmpty, find } from 'lodash';
import { GET_MONITOR_DATA_RESULT, GET_MONITOR_DATA, INSIGHT_NODE } from '@src/constants';
import AnalysisResultCard from '../analysis-result-card';
import ResourceCapacityCard from '../resource-capacity-card';
import HoverCardClickTip from '../hover-card-click-tip';
import NotificationBanner from '../notification-banner';
// import DatePicker from '../date-picker';
import s from './index.module.scss';
import DataAnalysisCard from '../data-analysis-card';
import { ContentType, MessageType, MessageTypeEnum } from '../../index';

interface MessageItemProps {
  item: MessageType;
  processing?: boolean;
  messages?: MessageType[];
  onMessageChange?: (messages: MessageType[]) => void;
  onSendMessage?: (message: string, intention?: string) => void;
  handleSendMessage?: (v: string, intentionParam?: object, intention?: string, options?: {
    addLast?: boolean;
    error?: string;
  }) => void;
}

enum ReasoningStatus {
  loading = 'loading',
  stopped = 'stopped',
  finished = 'finished'
}

const StatusTextMap = {
  [ReasoningStatus.loading]: t('思考中...'),
  [ReasoningStatus.stopped]: t('思考已终止'),
  [ReasoningStatus.finished]: t('思考完成'),
};

// 常量提取到组件外部，避免重复创建
const FIRST_THINKING_STEPS = [GET_MONITOR_DATA_RESULT, GET_MONITOR_DATA];

// 单个消息组件
const MessageItem = (props: MessageItemProps) => {
  const {
    item,
    processing,
    messages,
    onMessageChange,
    handleSendMessage,
  } = props;
  const global = useHookstate(globalState);
  const isUser = item.type === MessageTypeEnum.user;
  const { steps, deepThink, content, costTime = 0 } = item;
  const [count, setCount] = useState(costTime);
  const [isExpanded, setIsExpanded] = useState(true);
  const [reasoningStatus, setReasoningStatus] = useState(ReasoningStatus.finished);
  const [hasSpecialPattern, setHasSpecialPattern] = useState(false);
  const insightNodeStatus = find(item?.steps, item => (
    item.stepKey === INSIGHT_NODE
  )) || {};

  const isFirstThinking = useMemo(() => {
    if (!steps || !Array.isArray(steps) || steps.length === 0) {
      return false;
    }

    // 检查是否所有步骤都是首次思考步骤
    return steps.every(step => step?.stepKey && FIRST_THINKING_STEPS.includes(step.stepKey));
  }, [steps]);

  // 优化全局状态获取，避免重复调用
  const agentShowAnalysisResult = useMemo(() => global.agentShowAnalysisResult.get(), [global.agentShowAnalysisResult]);
  const agentShowResourceCapacity = useMemo(() => global.agentShowResourceCapacity.get(), [global.agentShowResourceCapacity]);
  const agentShowNotificationBanner = useMemo(() => global.agentShowNotificationBanner.get(), [global.agentShowNotificationBanner]);

  useEffect(() => {
    if (!content && (deepThink || !isEmpty(steps))) {
      setReasoningStatus(ReasoningStatus.loading);
      setIsExpanded(true);
    }
    if (content) {
      setReasoningStatus(ReasoningStatus.finished);
      setIsExpanded(false);
    }
  }, [content, steps, deepThink]);

  useEffect(() => {
    const pattern = /\$\[数据分析\]/g;
    setHasSpecialPattern(pattern.test(content || ''));
  }, [content]);

  useEffect(() => {
    let timer;
    if (reasoningStatus === ReasoningStatus.loading && processing) {
      timer = setInterval(() => {
        setCount(prev => prev + 1);
      }, 1000);
    }
    if (reasoningStatus === ReasoningStatus.loading && !processing) {
      setReasoningStatus((isFirstThinking && item?.end) ? ReasoningStatus.finished : ReasoningStatus.stopped);
    }
    return () => timer && clearInterval(timer);
  }, [reasoningStatus, processing, item?.end, isFirstThinking]);

  useEffect(() => {
    onMessageChange?.([...(messages.slice(0, messages.length - 1)), {
      id: messages[messages.length - 1]?.id,
      type: MessageTypeEnum.assistant,
      contentType: ContentType.text,
      steps: messages[messages.length - 1]?.steps,
      deepThink: messages[messages.length - 1]?.deepThink,
      content: messages[messages.length - 1]?.content,
      costTime: count,
    }]);
  }, [count]);

  const renderContent = (item: MessageType) => {
    switch (item.contentType) {
      case ContentType.text: {
        if (isUser) {
          return <ChatItem role='user' className={s['message-user-item']}>
            <ChatContent content={item.content} type='text'/>
          </ChatItem>;
        }
        return (
          <>
            {
              deepThink
              && <ChatReasoning
                isExpanded={isExpanded}
                statusIcon={reasoningStatus}
                statusText={`${StatusTextMap[reasoningStatus]}`}
                time={`${count}S`}
              >
                <ChatMarkdown content={deepThink} />
              </ChatReasoning>
            }
            {
              content && !hasSpecialPattern
              && <ChatItem
                role='assistant'
                className={s['message-assistant-item']}
              >
                <ChatContent content={content} type='text' />
              </ChatItem>
            }
            {
              content && hasSpecialPattern
              && <DataAnalysisCard
                insightNodeStatus={insightNodeStatus}
                processing={processing}
                contentResult={content}
              />
            }
          </>
        );
      }
      case ContentType.default:
        return <div>
          {item?.content?.message ?? t('正在处理中...')}
          </div>;
      default:
        return null;
    }
  };

  return <>
    {renderContent(item)}
    {isFirstThinking && (
      <div style={{ width: '100%' }}>
        {agentShowAnalysisResult && <AnalysisResultCard />}
        {agentShowAnalysisResult && !agentShowResourceCapacity && <HoverCardClickTip />}
        {agentShowResourceCapacity && <ResourceCapacityCard />}
        {agentShowNotificationBanner && <NotificationBanner
          handleSendMessage={handleSendMessage}
         />}
      </div>
    )}
  </>;
};

export default MessageItem;
