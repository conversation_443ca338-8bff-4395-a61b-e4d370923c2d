.showMarks {

}
:global {
  .rc-slider {
    transform: translateX(-4px);
    padding: 3px 0 !important;
    .rc-slider-rail {
      left: 1.01%;
      // width: 98.99%;
    }
    .rc-slider-step {
      height: 8px;
    }
    .rc-slider-track {
      border-radius: 0;
    }
    .rc-slider-track[style*="width: 0%"] + .rc-slider-track {
      border-top-left-radius: 6px !important;  /* 左上角圆角 */
      border-bottom-left-radius: 6px !important;  /* 左下角圆角 */
      border-top-right-radius: 0 !important;  /* 右上角无圆角 */
      border-bottom-right-radius: 0 !important;  /* 右下角无圆角 */
    }
    .rc-slider-track.rc-slider-track-1 {
      border-top-left-radius: 6px !important;  /* 左上角圆角 */
      border-bottom-left-radius: 6px !important;  /* 左下角圆角 */
      border-top-right-radius: 0 !important;  /* 右上角无圆角 */
      border-bottom-right-radius: 0 !important;  /* 右下角无圆角 */
    }
  }

  .rc-slider-disabled {
    background-color: transparent !important;
  }
}