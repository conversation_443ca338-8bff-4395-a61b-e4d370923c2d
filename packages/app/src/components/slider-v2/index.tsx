import React, { useMemo } from 'react';
import type { SliderProps } from 'rc-slider';
import Slider from 'rc-slider';
import { RiskColor } from '@src/constants/color';
import 'rc-slider/assets/index.css';
import { Tooltip } from 'tdesign-react';

import s from './index.module.scss';
export const handleRender: SliderProps['handleRender'] = (node, props) => (
  <Tooltip theme="light" content={`${props.value}%`}>
    {node}
  </Tooltip>
);

interface IProps {
  value?: number[];
  singleValue?: number;
  onValueChange?: (value: number[]) => void;
  disabled?: boolean;
  showMarks?: boolean;
  marksConfig?: Record<string, any>;
  handleStyle?: Record<string, any>;
}

/**
 * Slider组件
 * @param {any} value - 值属性
 * @param {function} onValueChange - value值改变时触发的回调函数
 * @returns React.ReactElement
 */
export default function SliderV2(props: IProps): React.ReactElement {
  const {
    value = [],
    onValueChange = () => undefined,
    disabled = false,
    showMarks = true,
    singleValue,
    marksConfig = null,
    handleStyle,
  } = props;
  const height = 8;
  const marks = showMarks
    ? marksConfig || {
      0: {
        style: {
          width: '35px',
          color: '#888888',
          marginLeft: '3px',
        },
        label: 0,
      },
      25: {
        style: {
          width: '35px',
          color: '#888888',
        },
        label: 25,
      },
      50: {
        style: {
          width: '35px',
          color: '#888888',
        },
        label: 50,
      },
      75: {
        style: {
          width: '35px',
          color: '#888888',
        },
        label: 75,
      },
      100: {
        style: {
          width: '35px',
          marginLeft: '-10px',
          color: '#888888',
        },
        label: 100,
      },
    }
    : {};

  const railColor = useMemo(() => {
    if (value?.[3] === 100) {
      return RiskColor.Medium;
    }
    if (value?.[2] === 100) {
      return RiskColor.Low;
    }
    if (value?.[1] === 100) {
      return RiskColor.LowUsed;
    }
    return RiskColor.High;
  }, [value]);

  return (
    <div style={{ position: 'relative' }}>
      {singleValue !== undefined && <Tooltip theme="light" content={`${singleValue}%`}>
        <div style={{
          position: 'absolute',
          height: 13,
          width: 13,
          zIndex: 2,
          background: '#fff',
          borderRadius: '50%',
          left: `${singleValue >= 100 ? 100 : singleValue}%`,
          transform: 'translateX(-50%)',
          boxShadow: '1px 1px 5px #999',
        }} /></Tooltip>}
      <Slider
        range
        className={s.slider}
        count={3}
        disabled={disabled}
        defaultValue={value}
        value={value}
        pushable={true}
        marks={marks}
        dots={false}
        step={1}
        min={-1}
        allowCross
        trackStyle={[
          { backgroundColor: RiskColor.LowUsed, height },
          { backgroundColor: RiskColor.Low, height },
          { backgroundColor: RiskColor.Medium, height, ...(value?.[3] === 100 ? { 'border-top-right-radius': 6, 'border-bottom-right-radius': 6 } : {}) },
          { backgroundColor: RiskColor.Medium, height },
        ]}
        handleStyle={value.map((v, i) => ({
          backgroundColor: '#fff',
          borderWidth: 2,
          borderColor: '#0D78F6',
          width: 18,
          height: 18,
          opacity: singleValue === undefined ? 1 : 0,
          display: !i ? 'none' : 'block',
          ...handleStyle,
        }))
        }
        // railStyle={{ backgroundColor: '#E96F71', height }}
        dotStyle={{ display: 'none' }}
        onChange={e => onValueChange(e as number[])}
        handleRender={handleRender}
        styles={{
          rail: { backgroundColor: railColor, height },
        }}
      />
    </div>
  );
}
