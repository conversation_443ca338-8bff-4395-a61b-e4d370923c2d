import React, { useState, useEffect } from 'react';
import {
  Select,
  Row,
  Col,
  Card,
  Switch,
  Bubble,
} from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import Slider from '@src/components/slider-v2';
import { periodValueType } from '@src/constants';
import info from '@src/statics/svg/info.svg';
import s from './index.module.scss';

interface IProps {
  disable?: boolean;
  onChange?: (data: any) => void;
  data?: any
  algorithmInfos?: any
  periodTypeInfos?: {
    commonPeriodTypeInfos: any[],
    metricPeriodTypeInfos: any[]
  }
  product: string
}

/**
 * SwitchCard组件
 * @param {any} prop1 - xx属性
 * @param {function} prop2 - xx方法
 * @returns React.ReactElement
 */
export default function SwitchCard(props: IProps): React.ReactElement {
  const { onChange = () => undefined, data, algorithmInfos, periodTypeInfos, product, disable } = props;
  const [visible, setVisible] = useState(false);
  const width = 100;
  // const value = [data?.LowUsed, data?.MediumUsed, data?.MediumThreshold, data?.HighThreshold];
  const value = [0, data?.MediumUsed, data?.MediumThreshold, data?.HighThreshold];
  const algorithmInfosOpts = (algorithmInfos || []).map(v => ({
    value: v.AlgorithmType,
    text: v.AlgorithmName,
  }));

  // 获取特殊指标的周期类型
  const metricPeriodTypeInfos = (periodTypeInfos?.metricPeriodTypeInfos || []).find(v => v?.MetricName?.toLowerCase?.() === data?.Metric?.toLowerCase?.() && v?.Product?.toLowerCase?.() === product?.toLowerCase?.());

  // 如果产品匹配，则使用特殊指标的周期类型，否则使用默认的周期类型
  const isSpecialMetric = metricPeriodTypeInfos?.Product?.toLowerCase?.() === product?.toLowerCase?.() && metricPeriodTypeInfos?.PeriodTypeInfos?.length;
  const periodTypeInfosOpts = (isSpecialMetric ? metricPeriodTypeInfos?.PeriodTypeInfos
    : periodTypeInfos?.commonPeriodTypeInfos || []).map(v => ({
    value: v.PeriodValue,
    text: periodValueType[v.PeriodValue],
  }));
  const periodValue = data?.PeriodValue;
  const algorithmValue = data?.AlgorithmType;
  const algorithmTypeMap = {
    1: t('取数据中高于 95% 数据的值'), // 95分位
    2: t('取数据中高于 80% 数据的值'), // 80分位
    6: t('将数据按顺序排列，取中间位置的数'), // 中位数
  };
  const status = data?.Status;
  const enable = status === 0;

  useEffect(() => {
    setVisible(data?.Status === 0);
  }, [data?.Status]);

  return (
    <div className={`${s.detail} ${disable ? s.disable : ''}`}>
      <Card bordered={false} className={visible ? s.card : s.cardHidden}>
        <Card.Body
          title={data?.MetricName}
          operation={
            <Switch
              disabled={disable}
              value={enable}
              onChange={(v) => {
                onChange({
                  ...data,
                  Status: v ? 0 : 1,
                });
              }}
            ></Switch>
          }
        >
          {visible && (
            <>
              <Row>
                <Col span={12} className={s['col-flex']}>
                  <span>{t('周期')}</span>
                  <Select
                    appearance="button"
                    disabled={disable}
                    matchButtonWidth
                    style={{ width, marginLeft: 15 }}
                    options={periodTypeInfosOpts}
                    value={periodValue}
                    onChange={v => onChange({
                      ...data,
                      PeriodValue: v,
                    })}
                    placeholder={t('请选择')}
                  />
                  <Bubble
                    placement="top"
                    trigger="hover"
                    dark
                    content={<span>{t('该容量监测周期选项将以')} <strong>1{t('小时')}</strong> {t('为颗粒度获取监控数值，以进行进一步的分析。详情可')} <a href='https://cloud.tencent.com/document/product/248/62458' target='_blank' style={{ color: '#fff', textDecoration: 'underline' }} rel="noreferrer">{t('参考文档')}</a>。</span>}
                  >
                    <img
                      style={{ marginLeft: 15 }}
                      src={info}
                      alt=""
                      className={s.info}
                    />
                  </Bubble>
                </Col>
                <Col span={12} className={s['col-flex']}>
                  <span>{t('算法')}</span>
                  <Select
                    disabled={disable}
                    appearance="button"
                    matchButtonWidth
                    value={algorithmValue}
                    style={{ width, marginLeft: 15 }}
                    options={algorithmInfosOpts}
                    onChange={v => onChange({
                      ...data,
                      AlgorithmType: v,
                    })}
                    placeholder={t('请选择')}
                  />
                  {
                    algorithmTypeMap[algorithmValue]
                      ? <Bubble
                        placement="top"
                        trigger="hover"
                        dark
                        content={algorithmTypeMap[algorithmValue]}
                      >
                        <img
                          style={{ marginLeft: 15 }}
                          src={info}
                          alt=""
                          className={s.info}
                        />
                      </Bubble>
                      : null
                  }
                </Col>
              </Row>
              <div style={{ marginTop: 15 }}>
                <Slider disabled={disable} value={value} onValueChange={(v) => {
                  onChange({
                    ...data,
                    LowUsed: v[0],
                    MediumUsed: v[1],
                    MediumThreshold: v[2],
                    HighThreshold: v[3],
                  });
                }} />
              </div>
            </>
          )}
        </Card.Body>
      </Card>
    </div>
  );
}
