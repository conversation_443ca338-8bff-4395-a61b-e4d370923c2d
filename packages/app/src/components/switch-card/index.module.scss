.detail {
  padding: 7px;
  .cardHidden {
    background-color: #fff;
    border: 1px solid #EDF1F7;
    box-shadow: none;
    border-radius: 8px;
    height: 60px;
  }
  .card {
    background-color: #fff;
    border: 1px solid #EDF1F7;
    box-shadow: none;
    border-radius: 8px;
    padding-bottom: 20px;
    :global {

    }
  }
}
.col-flex {
  &>div {
    display: flex;
    align-items: center;
  }
}

.reset {
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #006EFF;
  margin-left: 15px;
  img {
    margin-right: 6px;
  }
  &:hover {
    cursor: pointer;
  }
}
.disable {
  opacity: 0.6;
}