import React from 'react';
import { Bubble, TabPanel, Tabs } from '@tencent/tea-component';
import { cloneDeep } from 'lodash-es';
import ThresholdSlider from '@src/components/slider-box';
import './index.less';

const ThresholdSetting = ({ singleProductThresholdInfo, changeSingleProductThresholdInfo }: {
  singleProductThresholdInfo: any,
  changeSingleProductThresholdInfo: (data) => void
}) => <Tabs
    animated={false}
    className={`thresholds-setting-tab ${singleProductThresholdInfo?.ArchThresholdInfos[0]?.InsType ? '' : 'thresholds-setting-tab-none'}`}
    addon={
      <Bubble content={
        <a href={singleProductThresholdInfo?.InsTypeUrl} target={'_blank'} rel="noreferrer">
          {
            singleProductThresholdInfo?.InsTypeUrlName
          }
        </a>
      }>
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" viewBox="0 0 16 16">
          <path fill="color(display-p3 .5333 .5333 .5333)" fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm8 6c-3.309 0-6-2.691-6-6s2.691-6 6-6 6 2.691 6 6-2.691 6-6 6ZM7 9V4h2v5H7Zm0 3v-2h2v2H7Z" clipRule="evenodd"/>
        </svg>
      </Bubble>
    }
    tabs={
      singleProductThresholdInfo?.ArchThresholdInfos?.map(el => ({
        id: el.InsType,
        label: el.InsTypeName,
      }))
    }
  >
    {
      singleProductThresholdInfo?.ArchThresholdInfos?.map((el, index) => <TabPanel key={`tabP-${index}-${el.Metric}-${el.MetricName}`} id={el.InsType}>
        {
          el.ThresholdConfigInfos?.map((val, i) => <ThresholdSlider
              id={`thresholds-setting-${val.Metric}-${index}-${i}`}
              key={`thresholds-setting-${val.Metric}-${index}-${i}`}
              title={val.MetricName}
              threshold={[val.MediumThreshold, val.HighThreshold]}
              thresholdChange={
                (value) => {
                  // eslint-disable-next-line prefer-destructuring, no-param-reassign
                  val.MediumThreshold = value[0];
                  // eslint-disable-next-line prefer-destructuring, no-param-reassign
                  val.HighThreshold = value[1];
                  // eslint-disable-next-line max-len
                  changeSingleProductThresholdInfo && changeSingleProductThresholdInfo(cloneDeep(singleProductThresholdInfo));
                }
              }
            />)
        }
      </TabPanel>)
    }
  </Tabs>;
export default ThresholdSetting;
