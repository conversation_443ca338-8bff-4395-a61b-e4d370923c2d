.thresholds-setting-tab {
  margin-top: 8px;
  &.thresholds-setting-tab-none {
    .tea-tabs__tabbar {
      display: none;
    }
  }
  .tea-icon {
    background-size: auto;
  }
  .tea-tabs__scroll-area {
    margin-right: 12px;
    flex: initial;
  }
  .tea-btn {
    border-bottom: none;
    padding-top: 7px;
    padding-bottom: 7px;
  }
  .tea-tabs__addons {
    align-items: center;
    margin-top: 8px;
    position: relative;
    svg {
      cursor: pointer;
    }
  }
  .tea-tabs__tabpanel {
    margin-top: 5px;
  }
  .tea-tabs__tabbar {
    &::before {
      display: none;
    }
    .tea-tabs__tabitem {
      margin-right: 0;
      margin-left: -1px;
      position: relative;
      &:nth-child(1) {
        margin-left: 0;
      }
    }
    .tea-tabs__tab {
      border: 1px solid #CED5DF;
      position: relative;
      z-index: 1;
      padding-left: 15px;
      padding-right: 15px;
      &:hover:after {
        display: none;
      }
    }
    .tea-tabs__tab.is-active {
      z-index: 2;
      border-color: #0070FF;
      font-weight: normal;
      color: #0070FF;
      &::after {
        display: none;
      }
    }
  }
}