import React, { useState } from 'react';
import { Button, Modal } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';

const NoResourceModal = ({ content, title, btnText, onClose }: {
  content?: string,
  title?: string,
  btnText?: string,
  onClose?: () => void
}) => {
  const [noResourceVisible, setNoResourceVisible] = useState(true);
  return <Modal
    caption={title || ''}
    visible={noResourceVisible} onClose={
    () => {
      setNoResourceVisible(false);
      onClose && onClose();
    }
  }>
    <Modal.Body>{content}</Modal.Body>
    <Modal.Footer>
      <Button type="primary" onClick={
        () => {
          setNoResourceVisible(false);
          onClose && onClose();
        }
      }>
        {btnText || t('确定')}
      </Button>
    </Modal.Footer>
  </Modal>;
};
export default NoResourceModal;
