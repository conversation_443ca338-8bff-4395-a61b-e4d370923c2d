import React, { useState, useEffect, useRef } from 'react';
import { BasicLine } from '@tencent/tea-chart/lib/basicline';
import { Annotation } from '@tencent/tea-chart/lib/annotation';
import { t } from '@tea/app/i18n';
interface paramsProps {
  // 图表title
  title?: string;
  // 折线图数据，time*value的格式
  data: Array<any>;
  // 高阈值
  highThreshold?: number;
  // 低阈值
  lowThreshold?: number;

}

export function LineChart({ title, data = [], highThreshold, lowThreshold }: paramsProps) {
  const chartRef = useRef(null);
  // 折线图开始时间
  const startTime = data[0]?.time;
  // 折线图结束时间
  const endTime = data[data.length - 1]?.time;
  // 高阈值开始位置
  const [maxStart, setMaxStart] = useState();
  // 高阈值结束位置
  const [maxEnd, setMaxEnd] = useState();
  // 低阈值开始位置
  const [minStart, setMinStart] = useState();
  // 低阈值结束位置
  const [minEnd, setMinEnd] = useState();
  // 折线图数据
  const dataList = data.map(i => i.value);
  // Y轴坐标
  const domain: any = Math.max(...dataList) > 100 ? 'autofix' : [0, 100];
  useEffect(() => {
    if (chartRef.current) {
      chartRef.current.once('chart.rendered', () => {
        if (highThreshold) {
          // 如果有高阈值，绘制阈值线
          const maxStart = chartRef.current.getPosition(startTime, highThreshold);
          const maxEnd = chartRef.current.getPosition(endTime, highThreshold);
          setMaxStart(maxStart);
          setMaxEnd(maxEnd);
        }
        if (lowThreshold) {
          // 如果有低阈值，绘制阈值线
          const minStart = chartRef.current.getPosition(startTime, lowThreshold);
          const minEnd = chartRef.current.getPosition(endTime, lowThreshold);
          setMinStart(minStart);
          setMinEnd(minEnd);
        }
      });
    }
  }, [data]);

  function genTooltip(series) {
    const [{ title, value }] = series;
    return `${title}&nbsp;&nbsp;&nbsp;${value}%`;
  }

  return (
    <div style={{ width: '100%', marginTop: 16 }}>
      <div className="chartHead" style={{ color: 'rgba(0, 0, 0, 0.9)', overflow: 'hidden' }}>
        {title && <div style={{ float: 'left' }}>{title}</div>}
        {(data.length > 0) && <div style={{ float: 'right' }}>
          <span>{t('最大值: ')}{Math.max(...dataList)}</span><span style={{ margin: '0 6px' }}>{t('最小值: ')}{Math.min(...dataList)}</span><span>{t('平均值: ')}{(dataList.reduce((sum, num) => sum + num, 0) / dataList.length).toFixed(2)}</span>
        </div>}
      </div>
      <BasicLine
        scale={{ value: { alias: t('百分比'), domain } }}
        height={200}
        theme={{ color: ['#0072FF'] }}
        position="time*value"
        size={1}
        dataSource={data}
        interaction={['zoom']}
        tooltip={{ enable: true, formatter: genTooltip }}
        chartRef={(ref) => {
          chartRef.current = ref;
        }}
      >
        {(highThreshold || lowThreshold)
          ? <Annotation>
            {<Annotation.Line
              start={maxStart}
              end={maxEnd}
              style={{ stroke: '#E54545' }}
              text={{ position: 'end', content: `${highThreshold}%`, textStyle: 'color:#E54545;font-size:12px', offsetY: -5, offsetX: -32 }}
            />}
            <Annotation.Line
              start={minStart}
              end={minEnd}
              style={{ stroke: '#FFBF42' }}
              text={{ position: 'end', content: `${lowThreshold}%`, textStyle: 'color: #FFBF42;font-size:12px', offsetY: -5, offsetX: -32 }}
            />
          </Annotation> : <></>
          }
      </BasicLine>

    </div>
  );
}
