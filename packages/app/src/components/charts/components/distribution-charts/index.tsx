import React from 'react';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import {
  LineChart,
  BarChart,
} from 'echarts/charts';
import { RiskColor } from '@src/constants/color';
import { sortBy, uniq } from 'lodash-es';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  MarkAreaComponent,
  LegendComponent,
  LegendScrollComponent,
  LegendPlainComponent,
  MarkLineComponent,
} from 'echarts/components';
import {
  CanvasRenderer,
} from 'echarts/renderers';
import { t } from '@tea/app/i18n';

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  MarkAreaComponent,
  LegendComponent,
  LegendScrollComponent,
  LegendPlainComponent,
  MarkLineComponent,
]);

interface IProps {
  data?: any;
  customTicks?: number[];
  loading?: boolean;
}

/**
 * 一个根据提供的数据和自定义刻度生成图表的函数。
 * 它对数据进行量化处理，计算频率和累积频率，
 * 并渲染一个包含频率柱状图系列和累积频率折线图系列的图表。
 *
 * @param {IProps} props - 包含图表数据、自定义刻度和加载状态的对象。
 * @return {JSX.Element} 渲染后的图表组件。
 */
const App = (props: IProps) => {
  const { data = [], customTicks = [] } = props;
  // 状态  // 1.安全无风险 绿色    2.中度负载 中风险 橙色   3. 高度负载 高风险 红色
  // const statusColor = {
  //   3: '#E54545',
  //   2: '#FF8A2A',
  //   1: '#0ABF5B',
  // };

  // 定义目标数组，包含从0到100每隔5的数值
  const targetValues = Array.from({ length: 21 }, (_, i) => i * 5);

  // 量化原始数据，将每个数据点映射到目标数组中最接近的值
  const quantizedData = data.map((num) => {
    // 找到目标数组中与 num 最接近的值
    // eslint-disable-next-line max-len
    const closestIndex = targetValues.reduce(
      (prev, curr, index) => (Math.abs(curr - num) < Math.abs(targetValues[prev] - num)
        ? index
        : prev),
      0
    );
    return targetValues[closestIndex];
  });

  // 计算每个值的出现次数
  const frequencyTimes = Array.from({ length: 21 }, () => 0); // 从 0 到 100 每隔 5 的频率
  quantizedData.forEach((num) => {
    const index = Math.floor(num / 5); // 找到对应的索引
    if (index < frequencyTimes.length) {
      frequencyTimes[index] += 1;
    }
  });

  const total = data.length;

  // 计算频率
  const frequency = frequencyTimes.map(v => parseFloat((v / total).toFixed(4)) * 100);

  // 计算累计频率
  // let cumulativeFrequency = 0;
  // const cumulativeFrequencies = frequencyTimes.map((count) => {
  //   cumulativeFrequency += parseFloat((count / total).toFixed(4));
  //   return parseFloat(cumulativeFrequency.toFixed(4));
  // });

  const isInteger = obj => obj % 1 === 0;
  // 配置ECharts图表选项
  const option = {
    grid: {
      left: '10%',
      right: '11%',
      top: '12%',
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#363A50',
      textStyle: {
        color: '#fff', // 设置字体颜色
      },
      borderColor: '#fff',
      borderWidth: 0,
      borderRadius: 0,
      transitionDuration: 0.2,
      formatter(params) {
        // const value1 = parseFloat(params?.[0]?.value || 0);
        const value2 = parseFloat(params?.[0]?.value || 0);
        // <p>${params[1].axisValue}</p>
        // <p>${t('累计频率')}: ${value1 > 100 ? 100 : value1.toFixed(0)}%</p>
        return `
          <div>
            <p>${t('频率')}: ${!isInteger(value2) ? value2.toFixed(1) : value2}%</p>
        `;
      },
    },
    xAxis: {
      type: 'category',
      name: t('水位(%)'),
      nameGap: 1,
      data: sortBy(uniq([...Array.from({ length: 21 }, (_, i) => i * 5), ...customTicks])),
      axisTick: {
        // 隐藏刻度线
        show: false,
        alignWithLabel: true,
        customValues: customTicks.map(v => `${v}`),
      },
      axisLabel: {
        align: 'center', // 标签的对齐方式
        verticalAlign: 'middle', // 标签的垂直对齐方式
      },
      splitLine: {
        show: true,
        lineStyle: {
          offset: 3,
          width: 2, // 设置分割线宽度
          type: 'dashed', // 设置为虚线
          color: ['#fff', RiskColor.Low, RiskColor.Medium, RiskColor.High],
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        minInterval: 1,
        name: t('频率(%)'),
        splitLine: {
          // show: false,
        },
        min: 0,
        max: 100,
        interval: 25,
        axisLabel: {
          formatter(value) {
            return value ? value : '';
          },
        },
      },
      // {
      //   type: 'value',
      //   name: t('累计频率(%)'),
      //   min: 0,
      //   max: 100,
      //   splitLine: {
      //     // show: false,
      //   },
      //   axisTick: { // 不显示坐标轴刻度线
      //     show: false
      //   },
      //   interval: 25,
      //   axisLabel: {
      //     formatter: function(value) {
      //       return value ? value : ''
      //     },
      //   },
      // },
    ],
    legend: {
      bottom: 10,
      data: [{
        name: t('频率'),
        icon: 'rect',
      },
      // t('累计频率')
      ] },
    series: [
      // {
      //   name: t('累计频率'),
      //   type: 'line',
      //   yAxisIndex: 1,
      //   tooltip: {
      //     valueFormatter(value) {
      //       return `${isNaN(value) ? 0 : value}%`;
      //     },
      //   },
      //   showSymbol: false,
      //   data: cumulativeFrequencies.map((v) => {
      //     const result = parseFloat((v * 100).toFixed(2));
      //     return result >= 100 ? 100 : result;
      //   }),
      // },
      {
        type: 'bar',
        data: frequency,
        name: t('频率'),
        tooltip: {
          valueFormatter(value) {
            return `${isNaN(value) ? 0 : value}%`;
          },
        },
        itemStyle: {
          color: '#006EFF',
        },
        showSymbol: false,
        // markArea: {
        //   silent: true,
        //   data: [
        //     [
        //       {
        //         xAxis: '0',
        //         itemStyle: {
        //           color: '#FBE0E0',
        //         },
        //       },
        //       {
        //         xAxis: `${customTicks[0]}`,
        //         itemStyle: {
        //           color: '#FBE0E0',
        //         },
        //       },
        //     ],
        //     [
        //       {
        //         xAxis: `${customTicks[0]}`,
        //         itemStyle: {
        //           color: '#FFE8D6',
        //         },
        //       },
        //       {
        //         xAxis: `${customTicks[1]}`,
        //         itemStyle: {
        //           color: '#FFE8D6',
        //         },
        //       },
        //     ],
        //     [
        //       {
        //         xAxis: `${customTicks[1]}`,
        //         itemStyle: {
        //           color: '#D7F4E5',
        //         },
        //       },
        //       {
        //         xAxis: `${customTicks[2]}`,
        //         itemStyle: {
        //           color: '#D7F4E5',
        //         },
        //       },
        //     ],
        //     [
        //       {
        //         xAxis: `${customTicks[2]}`,
        //         itemStyle: {
        //           color: '#FFE8D6',
        //         },
        //       },
        //       {
        //         xAxis: `${customTicks[3]}`,
        //         itemStyle: {
        //           color: '#FFE8D6',
        //         },
        //       },
        //     ],
        //     [
        //       {
        //         xAxis: `${customTicks[3]}`,
        //         itemStyle: {
        //           color: '#FBE0E0',
        //         },
        //       },
        //       {
        //         xAxis: '100',
        //         itemStyle: {
        //           color: '#FBE0E0',
        //         },
        //       },
        //     ],
        //   ],
        // },
      },
    ],
  };
  return (
    <ReactEChartsCore
      echarts={echarts}
      option={option}
      showLoading={false}
      loadingOption={{
        text: t('加载中...'),
        showSpinner: true,
        spinnerRadius: 5,
      }}
      notMerge={true}
      lazyUpdate={true}
      theme={'theme_name'}
      style={{ height: '250px' }}
      opts={{}}
    />
  );
};

export default App;
