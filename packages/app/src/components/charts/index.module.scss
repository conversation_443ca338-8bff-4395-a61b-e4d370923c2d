.wrapper {
  // background: #F3F4F7;
  // margin-top: 10px;
  padding: 10px;
  padding-top: 0;
  border-radius: 5px;
  .radios {
    background-color: #fff;
    margin-right: 9px;
    :global {
      input {
        width: auto !important;
      }
    }
  }
}
.collapse {
  border-radius: 6px;
  :global {
    .t-collapse-panel__body {
      background-color: #fff;
    }
    .t-collapse-panel__content {
      padding: 0 !important;
    }
    .t-collapse-panel__header {
      padding: 9px;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
  }
}
.other {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  span:nth-child(1) {
    color: #888;
  }
  span:nth-child(2) {
    margin-left: 10px;
  }
}
.back {
  display: flex;
  align-items: center;
  margin-left: 8px;
  margin-top: 3px;
  img {

    margin-right: 10px;
    &:hover {
      cursor: pointer;
    }
  }
  span {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
  }
}
.illustrate {
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.90);
  padding: 0 10px;
}
.flex {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  &>p{
    display: flex;
    align-items: center;
  }
  .title {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    text-align: left;
    color: #000000;
    max-width: 161px;
    overflow: hidden;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .period {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
    color: #888888;
    margin-left: 10px;
  }
}