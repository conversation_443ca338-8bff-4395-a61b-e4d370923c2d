/* eslint-disable indent */
import React, { useState, useCallback } from 'react';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import { Radio, Loading, Collapse } from 'tdesign-react';
import moment from 'moment';
import { reportEvent, EVENT } from '@src/utils/report';
import { StatusColorMap as statusColor, RiskColor } from '@src/constants/color';
import Slider from '@src/components/slider-v2';
import { describeInsCapacityMetricData } from '@src/api/nodeDrawer';
import DistributionCharts from './components/distribution-charts';
import fetchData from '@src/utils/fetch';
import backOpt from '@src/statics/svg/backOpt.svg';
import { LineChart, BarChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  LegendScrollComponent,
  LegendPlainComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { t, Slot } from '@tea/app/i18n';
import s from './index.module.scss';
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  LegendComponent,
  LegendScrollComponent,
  LegendPlainComponent,
]);
interface IProps {
  onChange?: (value?: string) => void;
  value?: string;
  title?: string;
  data?: any;
  lineData?: any;
  indicator?: any;
  params?: any;
  loading?: boolean;
  algorithmValue?: number;
  multiRegionState?: any;
  isMultiRegionTab?: boolean;
  defaultExpandAll?: boolean; // 是否默认展开
  status?: number; // 状态  // 1.安全无风险 绿色    2.中度负载 中风险 橙色   3. 高度负载 高风险 红色
  statusLabel?: string; // 状态文案  // 1.安全无风险 绿色    2.中度负载 中风险 橙色   3. 高度负载 高风险 红色
}

const { Panel } = Collapse;

/**
 * 一个渲染图表并处理用户交互的功能组件。
 *
 * @param {IProps} props - 组件的属性，包括标题(title)、数据(data)、折线图数据(lineData)、指标(indicator)及参数(params)。
 * @return {JSX.Element} 渲染后的图表组件。
 */
const App = (props: IProps) => {
  const {
    title,
    data,
    lineData,
    params,
    algorithmValue,
    defaultExpandAll = false,
    status,
    statusLabel,
    multiRegionState,
    isMultiRegionTab,
  } = props;
  const { PeriodValue: periodValue } = data?.PeriodTypeInfo ?? {};
  const {
    Metric: metric,
    AlgorithmName: algorithmName = '',
    // LowUsed: lowUsed,
    MediumUsed: mediumUsed,
    HighThreshold: highThreshold,
    MediumThreshold: mediumThreshold,
    Namespace: namespace,
  } = data || {};
  const [val, setVal] = useState('0'); // '0':趋势图 '1':分布图
  const [dayChartVisible, setDayChartVisible] = useState(false); // 是否显示日图表
  const [day, setDay] = useState(''); // 当前查询日期
  // const customTicks = [lowUsed, mediumUsed, mediumThreshold, highThreshold];
  const customTicks = [0, mediumUsed, mediumThreshold, highThreshold];
  const desRegion = {
    DesRegion: isMultiRegionTab ? multiRegionState.multiDRegion : '',
  };
  const sourceRegion = {
    SourceRegion: isMultiRegionTab
            ? multiRegionState?.multiSRegion
            : multiRegionState.singleSRegion,
  };
  // 查询具体某天的日图表
  const {
    result: result1,
    // reload,
    isLoading,
  } = fetchData(
    'DescribeInsCapacityMetricData',
    {
      ...params,
      Metric: metric,
      CalculateMode: 0,
      Namespace: namespace,
      ...(desRegion.DesRegion ? desRegion : {}),
      ...(sourceRegion.SourceRegion ? sourceRegion : {}),
      StartTime: moment(day, 'YYYY/MM/DD')
        .startOf('day')
        .format('YYYY-MM-DDTHH:mm:ssZ'),
      EndTime: moment(day, 'YYYY/MM/DD')
        .endOf('day')
        .format('YYYY-MM-DDTHH:mm:ssZ'),
    },
    describeInsCapacityMetricData,
    !dayChartVisible
  );

  const xData = dayChartVisible
    ? (result1?.Timestamps ?? []).map(v => moment.unix(v).format('HH:mm:ss'))
    : lineData.map(item => item?.time?.split?.(' ')?.[0]);
  const yData = dayChartVisible
    ? (result1?.Values ?? []).map(v => (v < 0 ? null : v))
    : lineData.map(item => (item.value < 0 ? null : item.value));

  const distributionChartsYData = lineData.map(item => (item.value < 0 ? null : item.value));

  // 当点击柱状图时触发的事件处理函数
  const handleBarClick = useCallback(
    (e) => {
      reportEvent({
        key: EVENT.TREND_DRILL_DOWN,
        extraInfo: null,
      });
      // 如果日图表不可见
      if (!dayChartVisible) {
        // 获取点击的数据索引
        const index = e.dataIndex;
        // 根据索引获取对应的日期
        const day = xData[index];
        // 将日期格式化并设置到状态中
        setDay(moment(day, 'YYYY/MM/DD').format(t('YYYY年M月D日')));
        // 设置日图表为可见
        setDayChartVisible(true);
      }
    },
    [xData, yData, dayChartVisible]
  ); // 依赖项数组，包括xData, yData和dayChartVisible

  const formatTip = useCallback(
    (dayChartVisible, params, value) => {
      const val = dayChartVisible ? params?.[0]?.value ?? '' : value ?? '';
      return val !== '' ? `${val}%` : '';
    },
    [],
  );

  const max = yData.every(item => item === 0)
    ? 100
    : Math.max.apply(null, yData);

  // const finalMax = max > 100 ? 100 : max;
  // const finalMax = 100;
  const finalMax = max.toFixed(2);
  const customValues = customTicks.filter(item => item <= finalMax);
  const option = {
    grid: {
      left: '10%',
      right: '6%',
      top: '12%',
    },
    tooltip: {
      trigger: dayChartVisible ? 'axis' : 'item',
      formatter(params) {
        const index = params.dataIndex;
        const name = xData?.[index];
        const value = yData?.[index];
        return `
          <div>
            <p>${
              dayChartVisible
                ? params[0].axisValue
                : moment(name, 'YYYY/MM/DD').format(t('YYYY年M月D日'))
            }</p>
             <p>${algorithmName}: ${formatTip(dayChartVisible, params, value)}</p>
              ${
              !dayChartVisible
                ? `<p><a style="color: #888">${t('单击查看详情')}</a></p>`
                : ''
            }
          </div>
        `;
      },
      // position: 'top',
      backgroundColor: '#363A50',
      textStyle: {
        color: '#fff', // 设置字体颜色
      },
      borderColor: '#fff',
      borderWidth: 0,
      borderRadius: 0,
      transitionDuration: 0.2,
    },
    // dataZoom: [
    //   {
    //     type: 'slider', // 滑块缩放
    //     yAxisIndex: 0,
    //     filterMode: 'empty',
    //     show: true,
    //   },
    // ],
    xAxis: {
      type: 'category',
      data: xData,
      axisTick: {
        // 隐藏刻度线
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      name: t('水位(%)'),
      max: finalMax,
      // interval: 25,
      splitNumber: 4, // 设置 y 轴的分割线数量
      splitLine: {
        show: true,
        lineStyle: {
          width: 2, // 设置分割线宽度
          type: 'dashed', // 设置为虚线
          // color: '#ccc',   // 设置虚线颜色
          // color: [RiskColor.LowUsed, RiskColor.LowUsed, RiskColor.Medium, RiskColor.High],
          color: ['#fff', RiskColor.Low, RiskColor.Medium, RiskColor.High],
        },
      },
      axisTick: {
        alignWithLabel: true,
        customValues, // 防止y轴超出展示背景
      },
      axisLabel: {
        formatter: '{value}',
      },
      // splitArea: {
      //   show: true,
      //   areaStyle: {
      //     color: ['#FBE0E0', '#FFE8D6', '#D7F4E5', '#FFE8D6', '#FBE0E0'], // 交替的背景颜色
      //   },
      // },
    },
    legend: {
      bottom: 10,
      data: [algorithmName || '-'],
      icon: 'rect',
    },
    series: [
      {
        data: yData,
        type: dayChartVisible ? 'line' : 'bar',
        name: algorithmName || '-',
        itemStyle: {
          color: '#006EFF',
        },
        barMaxWidth: 30,
        showSymbol: false,
      },
    ],
  };

  // const parseNumber = useCallback((num) => {
  //   if (num) {
  //     return parseFloat(num).toFixed(2);
  //   }
  //   return '0';
  // }, []);

  return (
    <div
      style={{ borderRadius: 6, border: '1px solid #CFD5DE', marginTop: 10, overflow: 'hidden' }}
    >
      <Collapse
        borderless
        expandIcon
        defaultExpandAll={defaultExpandAll}
        expandIconPlacement={'right'}
        expandOnRowClick
        className={s.collapse}
        onChange={() => {
          reportEvent({
            key: EVENT.CLICK_DETAIL_CARD,
            extraInfo: null,
          });
        }}
      >
        <Panel
          header={
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <p>
                {title}{' '}
                <span
                  style={{
                    backgroundColor: statusColor[algorithmValue < 0 ? '-1' : status],
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    display: 'inline-block',
                    marginLeft: 3,
                  }}
                ></span>
              </p>
              <p>
                {algorithmValue !== undefined && algorithmValue !== null && algorithmValue !== -1
                  ? `${algorithmValue}%`
                  : null}
              </p>
            </div>
          }
        >
          {
            algorithmValue < 0 ? <div className={s.wrapper} style={{ marginLeft: 10, fontSize: 12 }}>{t('无数据')}</div> : <div className={s.wrapper}>
            <div className={s.flex}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                }}
              >
                <p className={s.illustrate}>
                  {t('基于')}<strong><Slot content={t('近 {{periodValue}} 天', { periodValue })} /></strong>
                  {t('数据统计')}，<Slot content={title} />{t('的')} <strong><Slot content={algorithmName || '-'} /></strong>{t('值为')}{' '}
                   <strong>
                    <Slot content={algorithmValue !== undefined || algorithmValue !== null
                      ? `${algorithmValue}%`
                      : '-'} />
                  </strong>
                  ，{t('处于')}<strong style={{ color: status === 1 ? statusColor[0] : statusColor[status] }}><Slot content={statusLabel} /></strong>{t('状态')}。
                </p>
              </div>
              <div style={{ marginTop: 10, paddingLeft: 10, paddingRight: 10 }}>
                <Slider
                  value={[...customTicks]}
                  singleValue={algorithmValue || 0}
                  disabled
                  showMarks={false}
                />
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  marginTop: 15,
                }}
              >
                <span className={s.period}>{t('详细数据')}</span>
                <Radio.Group
                  size="small"
                  className={s.radios}
                  value={val}
                  onChange={(value) => {
                    reportEvent({
                      key: value === '0' ? EVENT.SWITCH_TO_TREND : EVENT.SWITCH_TO_DISTRIBUTE,
                      extraInfo: null,
                    });
                    setVal(value as string);
                  }}
                >
                  <Radio.Button value={'0'}>{t('趋势')}</Radio.Button>
                  <Radio.Button value={'1'}>{t('分布')}</Radio.Button>
                </Radio.Group>
              </div>
            </div>
            {dayChartVisible && val === '0' && (
              <div className={s.back}>
                <img src={backOpt} onClick={() => setDayChartVisible(false)} />
                <span>{day}</span>
              </div>
            )}
            <div style={{ position: 'relative', marginTop: 5 }}>
              <Loading
                indicator
                loading={isLoading}
                size="small"
                text={t('加载中...')}
                preventScrollThrough
                showOverlay
              >
                {val === '0' ? (
                  <ReactEChartsCore
                    echarts={echarts}
                    option={option}
                    notMerge={true}
                    lazyUpdate={true}
                    theme={'theme_name'}
                    style={{ height: '250px' }}
                    onChartReady={() => {
                      //
                    }}
                    showLoading={false}
                    loadingOption={{
                      text: t('加载中...'),
                      showSpinner: true,
                      spinnerRadius: 5,
                    }}
                    onEvents={{
                      click: handleBarClick,
                    }}
                    opts={{}}
                  />
                ) : (
                  <DistributionCharts
                    data={distributionChartsYData}
                    customTicks={customTicks}
                    loading={isLoading}
                  />
                )}
              </Loading>
            </div>
            {/* <div className={s.other}>
              <div>
                <span>Max: </span>
                <span>{parseNumber(indicator.max)}%</span>
              </div>
              <div>
                <span>Min: </span>
                <span>{parseNumber(indicator.min)}%</span>
              </div>
              <div>
                <span>Avg: </span>
                <span>{parseNumber(indicator.avg)}%</span>
              </div>
            </div> */}
          </div>
          }
        </Panel>
      </Collapse>
    </div>
  );
};

export default App;
