import { RiskColor } from '../../constants/color';
import React, { FC } from 'react';

export interface IRiskCircleProps {
  highLoadCount: number;
  mediumLoadCount: number;
  lowLoadCount: number;
  lowUsedCount: number;
  unSupportCount: number;
}

export const RiskCircle: FC<IRiskCircleProps> = (props) => {
  const { highLoadCount = 0, mediumLoadCount = 0, lowLoadCount = 0, lowUsedCount = 0, unSupportCount = 0 } = props;
  const data = [
    { value: highLoadCount, color: RiskColor.High },
    { value: mediumLoadCount, color: RiskColor.Medium },
    { value: lowLoadCount, color: RiskColor.Low },
    { value: lowUsedCount, color: RiskColor.LowUsed },
    { value: unSupportCount, color: RiskColor.UnSupport },
  ];

  const total = data.reduce((acc, item) => acc + item.value, 0);
  let cumulativeValue = 0;

  const getCoordinatesForPercent = (percent: number) => {
    const angle = (percent * 2 * Math.PI) - (Math.PI / 2);
    const x = Math.cos(angle);
    const y = Math.sin(angle);
    return [x, y];
  };

  // 计算非零值的数量
  const nonZeroCount = data.filter(item => item.value > 0).length;

  // 如果所有值都为0，显示对勾图标
  if (total === 0) {
    return <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOCAxNGMwLTcuNzMyLTYuMjY4LTE0LTE0LTE0UzAgNi4yNjggMCAxNHM2LjI2OCAxNCAxNCAxNCAxNC02LjI2OCAxNC0xNHptLTYuNDA3LTMuMTI2TDE5LjEyIDguNGwtNi44MDYgNi44MDZMOC42IDExLjQ5NGwtMi40NzUgMi40NzUgNi4xODggNi4xODggOS4yOC05LjI4M3oiIGZpbGw9IiMwQUJGNUIiLz48L3N2Zz4=" />;
  }

  // 如果只有一个非零值，显示完整的圆
  if (nonZeroCount === 1) {
    const nonZeroItem = data.find(item => item.value > 0);
    return (
      <svg width="28" height="28" viewBox="-1 -1 2 2">
        <circle cx="0" cy="0" r="1" fill={nonZeroItem?.color} />
        <circle cx="0" cy="0" r="0.5" fill="white" />
      </svg>
    );
  }

  return (
    <svg width="28" height="28" viewBox="-1 -1 2 2">
      {data.map((slice, index) => {
        // 跳过值为0的项
        if (slice.value === 0) return null;

        const [startX, startY] = getCoordinatesForPercent(cumulativeValue / total);
        cumulativeValue += slice.value;
        const [endX, endY] = getCoordinatesForPercent(cumulativeValue / total);

        const largeArcFlag = slice.value / total > 0.5 ? 1 : 0;

        return (
          <path
            key={index}
            d={`M ${startX} ${startY} A 1 1 0 ${largeArcFlag} 1 ${endX} ${endY} L 0 0`}
            fill={slice.color}
          />
        );
      })}
      <circle cx="0" cy="0" r="0.5" fill="white" />
    </svg>
  );
};
