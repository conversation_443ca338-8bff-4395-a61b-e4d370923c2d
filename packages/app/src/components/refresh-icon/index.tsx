import React, { FC, useEffect, useState } from 'react';
import { Bubble, Icon } from '@tencent/tea-component';
import { t } from '@tencent/tea-app/lib/i18n';
import globalState from '@src/stores/global.state';
import { useHookstate } from '@hookstate/core';

export interface IRefreshIconProps {
  onClick: () => void;
}

export const RefreshIcon: FC<IRefreshIconProps> = ({ onClick }) => {
  const state = useHookstate(globalState);
  const { isRefreshing } = state.get();
  const [clickable, setClickable] = useState(true);

  useEffect(() => {
    let timer;
    console.info('isRefreshing', isRefreshing, clickable);
    if (isRefreshing === false) {
      setClickable(false);
      timer = setTimeout(() => {
        setClickable(true);
      }, 2000);
    }

    // 清理定时器
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [isRefreshing]);

  const clickIcon = async () => {
    if (isRefreshing || !clickable) {
      return;
    }
    onClick();
  };
  let icon;
  let text;
  if (isRefreshing) {
    icon = 'loading';
    text = t('更新中');
  } else if (clickable) {
    icon = 'refresh-blue';
    text = t('立即更新');
  } else {
    icon = 'success';
    text = t('更新成功');
  }
  return (
    <Bubble content={text}>
    <Icon
      type={icon}
      style={{
        cursor: 'pointer',
        marginLeft: 5,
      }}
      onClick={clickIcon}
    />
  </Bubble>
  );
};
