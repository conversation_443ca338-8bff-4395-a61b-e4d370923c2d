.beeNestWrap {
  position: relative;
  height: calc(100% + 48px);
  font-size: 0;

  &::before {
    content: '';
    height: 100%;
    min-height: 50px;
    width: 15px;
    shape-outside: repeating-linear-gradient(
      transparent 0px,
      transparent 48px,
      #f00 50px,
      #f00 0px
    );
    float: left;
  }

  .nestWrap {
    &:hover {
      background: #006eff;
    }

    cursor: pointer;
    width: 33px;
    height: calc(33px * 1.1547);
    clip-path: polygon(0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%, 50% 0%);
    display: inline-block;
    margin: 0 -2px -12px 0;
    position: relative;

    .nest {
      width: 28px;
      height: calc(28px * 1.1547);
      background: #0abf5b;
      clip-path: polygon(0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%, 50% 0%);
      display: inline-block;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .highNest {
      background: #e54545;
    }

    .middleNest {
      background: #ff8a2a;
    }
    .uselessNest {
      background: #d6f4e4;
    }

    .none {
      background-color: #e7eaef;
    }
  }

  .selectNest {
    background: #006eff;
  }

  .overNestWrap {
    width: 28px;
    height: calc(28px * 1.1547);
    background: #e8ebf0;
    position: relative;
    left: 2px;
    top: -2px;

    &:hover {
      background: #e8ebf0;
    }

    .overNest {
      width: 24px;
      height: calc(24px * 1.1547);
      background: #fff;

      .tea-icon {
        background-size: auto;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.overNestBubble {
  max-width: 350px !important;
}

.beeNestBubble {
  width: 210px !important;

  .bubbleTitle {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
    margin-bottom: 12px;
  }

  .baseInfoKey {
    width: 40% !important;
  }

  .tea-icon {
    background-size: auto;
  }

  .loadingWrap {
    text-align: center;
  }
}
