import React, { FC, useMemo } from 'react';
import { Justify, Text, Status } from '@tencent/tea-component';
import { LevelColorIcon } from '@src/components/level-color-icon';
import { t } from '@tencent/tea-app/lib/i18n';
import s from './index.module.scss';

export type IMetricInfo = { Metric: string, Value: number, MetricRiskLevel: number };

export interface IMetricViewProps {
  metricInfo: IMetricInfo[],
  hoverData: any,
  ccnInfo: {
    isCcn: boolean;
  };
}

export const MetricView: FC<IMetricViewProps> = (props) => {
  const { metricInfo, ccnInfo, hoverData } = props;
  let contentView;
  const isSingleRegion = hoverData?.regionList?.length === 1;
  // eslint-disable-next-line no-nested-ternary
  const description = useMemo(() => (ccnInfo?.isCcn ? isSingleRegion ? '' : t('暂未绑定网络实例') : t('不支持此实例类型')), [ccnInfo]);
  if (metricInfo.length > 0 && !isSingleRegion) {
    contentView = metricInfo.map((item, index) => {
      const leftView = (
        <Text reset theme="strong" style={{ display: 'flex', alignItems: 'center' }}>
          <Text overflow tooltip style={{ maxWidth: 100 }}>{t('{{ text }}', { text: item.Metric })}</Text>
          <LevelColorIcon level={item.Value < 0 ? -1 : item.MetricRiskLevel} />
        </Text>
      );
      const rightView = (
        <Text reset theme="strong">{item?.Value < 0 ? t('暂无数据') : t('{{ text }}%', { text: item.Value })}</Text>
      );
      return (
        <Justify className={s.metricCell} key={`${item.Metric}-${index}`} left={leftView} right={rightView} />
      );
    });
  } else {
    contentView = (
      <Status className={s.status} icon="blank" size="xs" title={t('暂无数据')} description={ description } />
    );
  }


  return (
    <div>
      <div className={s.metricTitle}>
        <Text theme="label">{t('容量指标')}</Text>
      </div>
      {contentView}
    </div>
  );
};
