import React, { useState, useEffect } from 'react';
import {
  Icon,
  Bubble,
  Row,
  Col,
  Text,
  StatusTip,
} from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import { describeInsResourceRealTimeInfo } from '@src/api/nodeDrawer';
import { riskTable } from '@src/constants';
import { reportEvent, EVENT } from '@src/utils/report';
import { ClickHouseConfig } from '@src/components/archive-report/constants';
import { MetricView } from './metric-view';
import './index.less';

const { LoadingTip } = StatusTip;
interface paramsProps {
  // 蜂巢图数据
  data: Array<any>;
  // 选择蜂巢变化时的回调函数
  selectChange?: (data) => void;
  // 架构图id
  MapId?: string;
  // 节点uuid
  NodeUuid?: string;
  // ccn信息
  ccnInfo: {
    isCcn: boolean;
  };
  product?: string;
}

// 蜂巢停留时间
let timeoutId: any = '';

type IHoverData = {
  hoverInfo: any[],
  metricInfo: { Metric: string, Value: number, MetricRiskLevel: number }[],
  regionList: any[];
}

const emptyHoverData: IHoverData = {
  hoverInfo: [],
  metricInfo: [],
  regionList: [],
};

export function BeeNest({
  data = [],
  selectChange,
  MapId,
  NodeUuid,
  ccnInfo,
  product,
}: paramsProps) {
  const isTcHouse = ClickHouseConfig.CLICK_HOUSE === product;
  // 当前选中的蜂巢
  const [selectNest, setSelectNest] = useState<any>({});
  // hover的蜂巢数据
  const [hoverData, setHoverData] = useState<IHoverData>(emptyHoverData);
  // console.info('beeNest', data);
  // 监听选中变化，抛给上层组件
  useEffect(() => {
    selectChange && selectChange(selectNest);
  }, [selectNest]);

  useEffect(() => {
    setSelectNest(data[0]);
  }, [data]);

  // 查询实例资源信息，hover时调用
  const getDescribeInsResourceRealTimeInfo = async (data) => {
    setHoverData(emptyHoverData);
    try {
      const res = await describeInsResourceRealTimeInfo({
        MapId,
        NodeUuid,
        InsId: data.InsId,
        InsRegion: data.Region,
      });
      if (!res?.Error) {
        // 保存当前hover数据，只保留 ShowType为 1 3
        const hoverInfo = (res.InsResourceInfoList || []).filter(i => i.ShowType === 3 || i.ShowType === 1);
        const metricInfo = data.MetricValueList || [];
        setHoverData({
          hoverInfo,
          metricInfo,
          regionList: res?.RegionList ?? [],
        });
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleMouseEnter = (data) => {
    setHoverData({
      hoverInfo: [],
      metricInfo: [],
      regionList: [],
    });
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      getDescribeInsResourceRealTimeInfo(data);
    }, 300);
  };

  const handleMouseLeave = () => {
    clearTimeout(timeoutId);
  };

  return (
    <div className="beeNestWrap">
      {(data || []).slice(0, 100).map((i, index) => (
        <Bubble
          key={`b-${index}`}
          arrowPointAtCenter
          trigger={'hover'}
          transitionTimeout={{
            enter: 0,
            exit: 0,
          }}
          placement="bottom-end"
          className="beeNestBubble"
          content={
            hoverData.hoverInfo.length ? (
              <>
                <Row>
                  {hoverData.hoverInfo.map((i, ind) => (
                    <Col key={`colH-${ind}`} span={24} className="baseInfoCol">
                      <div className="baseInfoKey">
                        {t('{{Key}}', { Key: i.Key })}
                      </div>
                      {i.Value ? (
                        <Text
                          overflow
                          onClick={() => {
                            i.IsSkip && i.Url && window.open(i.Url);
                          }}
                          style={{ cursor: 'pointer' }}
                          theme={i.IsSkip ? 'primary' : 'text'}
                          copyable={i.IsCopy}
                          tooltip={
                            <>
                              {i.Value.split('\n').length > 1
                                ? i.Value.split('\n').map((val, i) => (
                                    <div key={`divVal-${i}`}>
                                      {t('{{Value}}', { Value: val })}
                                    </div>
                                ))
                                : t('{{Value}}', { Value: i.Value })}
                            </>
                          }
                        >
                          {t('{{Value}}', { Value: i.Value })}
                        </Text>
                      ) : (
                        t('暂无')
                      )}
                    </Col>
                  ))}
                  {/* <Col key="dd">
                  <Text theme="label">{t('容量指标')}</Text>
                  </Col> */}
                </Row>
                <MetricView metricInfo={hoverData.metricInfo} ccnInfo={ccnInfo} hoverData={hoverData} />
              </>
            ) : (
              // 加载提示
              <div className="loadingWrap">
                <LoadingTip />
              </div>
            )
          }
        >
          <div
            className={`nestWrap ${
              selectNest?.InsId === i.InsId ? 'selectNest' : ''
            }`}
            key={i.InsId}
            onClick={() => {
              reportEvent({
                key: EVENT.CLICK_NEST_DIAGRAM,
                extraInfo: null,
              });
              setSelectNest(i);
            }}
            onMouseEnter={() => {
              handleMouseEnter(i);
            }}
            onMouseLeave={handleMouseLeave}
          >
            <div className={`nest ${riskTable[i.RiskLevel]}`}></div>
          </div>
        </Bubble>
      ))}
      {data?.length > 100 && (
        <Bubble
          className="overNestBubble"
          arrowPointAtCenter
          placement="bottom"
          content={isTcHouse ? ClickHouseConfig.BASE_BUBBLE_TIP : t(
            '受展示限制，仅展示前100个资源，剩余{{count}}个资源已隐藏',
            { count: data.length - 100 }
          )}
        >
          <div className="nestWrap overNestWrap">
            <div className="nest overNest">
              <Icon type="more" />
            </div>
          </div>
        </Bubble>
      )}
    </div>
  );
}
