import React from 'react';
import { Button } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface IProps {
  saveLoading: boolean;
  onSave: () => void;
  onCancel: () => void;
}

/**
 * 底部操作按钮组件
 */
export default function FooterActions(props: IProps): React.ReactElement {
  const { saveLoading, onSave, onCancel } = props;

  return (
    <div className={s.footer}>
      <Button
        loading={saveLoading}
        onClick={onSave}
        type="primary"
      >
        {t('保存')}
      </Button>
      <Button
        type="weak"
        style={{ marginLeft: 15 }}
        onClick={onCancel}
      >
        {t('取消')}
      </Button>
    </div>
  );
}
