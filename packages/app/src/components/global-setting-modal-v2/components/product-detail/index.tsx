import React from 'react';
import { t } from '@tea/app/i18n';
import SwitchCard from '@src/components/switch-card';
import s from './index.module.scss';

interface ThresholdConfigInfo {
  [key: string]: any;
}

interface AlgorithmInfo {
  [key: string]: any;
}

interface PeriodTypeInfo {
  [key: string]: any;
}

interface IProps {
  disable?: boolean;
  tabData: ThresholdConfigInfo[];
  algorithmInfos: AlgorithmInfo[];
  periodTypeInfos: {
    commonPeriodTypeInfos: PeriodTypeInfo[];
    metricPeriodTypeInfos: PeriodTypeInfo[];
  };
  activeProduct: string;
  onChange: (data: ThresholdConfigInfo, index: number) => void;
}

/**
 * 产品详情组件
 */
export default function ProductDetail(props: IProps): React.ReactElement {
  const {
    tabData,
    disable,
    algorithmInfos,
    periodTypeInfos,
    activeProduct,
    onChange,
  } = props;

  if (!tabData || tabData.length === 0) {
    return <div className={s.container}></div>;
  }

  return (
    <div className={s.container}>
      <p className={s.sectionTitle}>
        {t('指标设置')}
      </p>

      <div className={s.content}>
        {tabData.map((v, i) => (
          <SwitchCard
            key={`SwitchCard--${i}`}
            data={v}
            disable={disable}
            algorithmInfos={algorithmInfos}
            periodTypeInfos={periodTypeInfos}
            product={activeProduct}
            onChange={(data) => {
              onChange(data, i);
            }}
          />
        ))}
      </div>
    </div>
  );
}
