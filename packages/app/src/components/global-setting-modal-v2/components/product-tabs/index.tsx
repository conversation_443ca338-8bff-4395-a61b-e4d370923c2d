import React from 'react';
import { Radio as TRadio } from 'tdesign-react';
import { t } from '@tea/app/i18n';
import { CcnConfig } from '@src/components/archive-report/constants';
import s from './index.module.scss';

interface TabItem {
  InsType: string;
  InsTypeName: string;
}

interface IProps {
  tabs: TabItem[];
  activeTab: string;
  activeProduct: string;
  onTabChange: (tabValue: string) => void;
}

/**
 * 产品标签页组件
 */
export default function ProductTabs(props: IProps): React.ReactElement {
  const { tabs, activeTab, activeProduct, onTabChange } = props;

  // 如果只有一个标签页或没有标签页，不显示标签页组件
  if (!tabs || tabs.length <= 1) {
    return <></>;
  }

  return (
    <div className={s.container}>
      <p className={s.label}>
        {activeProduct?.toLowerCase?.() === CcnConfig.CCNS ? CcnConfig.DETAIL_TITLE : t('实例类型')}
      </p>
      <div className={s.radioWrapper}>
        <TRadio.Group
          variant="primary-filled"
          className={s.radios}
          defaultValue={tabs?.[0]?.InsType || '0'}
          value={!activeTab ? tabs?.[0]?.InsType || '0' : activeTab || '0'}
          onChange={(v) => {
            onTabChange(v as string);
          }}
        >
          {tabs.map((v, i) => (
            <TRadio.Button
              key={`TRadio-${i}`}
              value={v?.InsType || `${i}`}
            >
              {v?.InsTypeName || `${i}`}
            </TRadio.Button>
          ))}
        </TRadio.Group>
      </div>
    </div>
  );
}
