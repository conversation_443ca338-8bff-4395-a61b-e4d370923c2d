/* eslint-disable no-nested-ternary */
import React from 'react';
import { Collapse, Bubble } from '@tencent/tea-component';
import { Skeleton } from 'tdesign-react';
import { t } from '@tea/app/i18n';
import arrowDown from '@src/statics/svg/arrow-down.svg';
import info from '@src/statics/svg/info.svg';
import s from './index.module.scss';

interface ProductItem {
  Product: string;
  ProductName: string;
  ParentProduct?: string;
  ParentProductName?: string;
  ThresholdStatus: number;
}

interface IProps {
  products: ProductItem[];
  activeProduct: string;
  loading: boolean;
  notApplicableShow: boolean;
  onProductSelect: (product: string, productName: string) => void;
  onNotApplicableToggle: () => void;
}

/**
 * 产品列表组件
 */
export default function ProductList(props: IProps): React.ReactElement {
  const {
    products,
    activeProduct,
    loading,
    notApplicableShow,
    onProductSelect,
    onNotApplicableToggle,
  } = props;

  // 前端根据ParentProduct来展示集群
  const groups = new Map();
  const showIndex = new Set();
  products.forEach((element) => {
    if (element?.ParentProduct) {
      if (!groups.get(element.ParentProduct)) {
        groups.set(element.ParentProduct, [element]);
        showIndex.add(`${element.Product}-${element.ProductName}`);
      } else {
        groups.get(element.ParentProduct)?.push(element);
        showIndex.delete(`${element.Product}-${element.ProductName}`);
      }
    } else {
      showIndex.add(`${element.Product}-${element.ProductName}`);
    }
  });

  const notApplicable = products.some(v => v.ThresholdStatus === -1);

  const renderProductItem = (item: ProductItem, index: number, isNotApplicable = false) => {
    const dataName = isNotApplicable ? 'not-applicable-item' : 'li';
    const activeClass = activeProduct === item.Product ? s.active : '';

    return (
      <li
        data-name={dataName}
        className={activeClass}
        title={item.ProductName}
        key={`${item.Product}-${item.ProductName}-${index}`}
        onClick={() => {
          const productName = item?.ParentProductName
            ? `${item.ParentProductName} > ${item.ProductName}`
            : item.ProductName;
          onProductSelect(item.Product, productName);
        }}
      >
        {item?.ParentProduct ? '· ' : ''}{item.ProductName || item.Product || '-'}
      </li>
    );
  };

  const renderGroupedProducts = (item: ProductItem, index: number, isNotApplicable = false) => {
    const groupProducts = products.filter(v => v?.ParentProduct === item.ParentProduct
      && (isNotApplicable ? v.ThresholdStatus === -1 : v.ThresholdStatus > -1));

    const collapseClass = isNotApplicable
      ? `${s.groupCollapse} ${s['not-applicable-item']}`
      : s.groupCollapse;

    return (
      <Collapse
        defaultActiveIds={[]}
        key={`${item.ParentProduct}-${item.ParentProductName}-${index}`}
        className={collapseClass}
      >
        <Collapse.Panel
          id={item.ParentProduct}
          title={item?.ParentProductName || item?.ParentProduct}
        >
          <ul>
            {groupProducts.map((item2, i2) => renderProductItem(item2, i2, isNotApplicable))}
          </ul>
        </Collapse.Panel>
      </Collapse>
    );
  };

  return (
    <div className={s.container}>
      <div className={s.title}>
        <h3>
          {t('默认指标')}{' '}
          <Bubble
            placement="right"
            dark
            trigger="hover"
            content={t('配置各产品的默认监测参数，图元的定制配置将优先于默认参数生效')}
          >
            <img src={info} alt="" className={s.info} />
          </Bubble>
        </h3>
      </div>
      <div className={s.tabs}>
        {loading && !products?.length ? (
          <Skeleton
            theme={'paragraph'}
            animation="flashed"
            style={{ padding: 10 }}
            className={s.skeleton}
          />
        ) : null}
        <ul>
          {/* 正常产品列表 */}
          {products.filter(v => v.ThresholdStatus > -1).map((item, i) => (showIndex?.has?.(`${item.Product}-${item.ProductName}`) ? (
            !item?.ParentProduct
              ? renderProductItem(item, i)
              : renderGroupedProducts(item, i)
          ) : null))}

          {/* 不适用产品标题 */}
          {notApplicable ? (
            <li
              style={{ color: 'rgba(0, 0, 0, 0.40)', fontSize: 10, fontWeight: 400 }}
              className={s['not-applicable-header']}
              onClick={onNotApplicableToggle}
            >
              {t('不适用')} <img className={notApplicableShow ? s.show : s.hide} src={arrowDown} />
            </li>
          ) : null}

          {/* 不适用产品列表 */}
          {notApplicable && notApplicableShow
            && products.filter(v => v.ThresholdStatus === -1).map((item, i) => (showIndex?.has?.(`${item.Product}-${item.ProductName}`) ? (
              !item?.ParentProduct
                ? renderProductItem(item, i, true)
                : renderGroupedProducts(item, i, true)
            ) : null))
          }
        </ul>
      </div>
    </div>
  );
}
