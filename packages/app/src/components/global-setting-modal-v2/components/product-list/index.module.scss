.container {
  width: 213px;
  background-color: #E7EAEF;
  height: 100%;
}

.title {
  h3 {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 8px 15px;
  }
  img {
    width: 16px;
    margin-left: 10px;
  }
}

.info {
  &:hover {
    cursor: pointer;
  }
}

.tabs {
  height: 100%;
  overflow: hidden;
  .active {
    background: #006EFF;
    color: #fff;
  }
  .active[data-name="not-applicable-item"] {
    background: #006EFF;
    color: rgba(255, 255, 255, 0.60) !important;
  }
  ul {
    overflow: auto;
    overflow-x: hidden;
    will-change: transform;
    scroll-behavior: smooth;
    height: calc(100% - 60px);
    li[data-name="li"] {
      &:hover {
        border-top: 1px solid #fff;
        cursor: pointer;
        background: #006EFF;
        color: #fff !important;
      }
    }
    li[data-name="not-applicable-item"] {
      color: var(---, rgba(0, 0, 0, 0.40));
      &:hover {
        border-top: 1px solid #fff;
        cursor: pointer;
        background: #006EFF;
        color: rgba(255, 255, 255, 0.60) !important;
      }
    }
    li {
      box-sizing: border-box;
      padding: 0 15px;
      height: 30px;
      font-size: 12px;
      font-weight: 400;
      line-height: 30px;
      text-align: left;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: PingFang SC;
      border-top: 1px solid #E7EAEF;
      user-select: none;
    }
  }
}

.groupCollapse {
  width: 100%;
  :global {
    .tea-accordion__header {
      height: 30px;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      flex-direction: row-reverse;
      padding-left: 15px;
      padding-right: 15px;
      color: rgba(0, 0, 0, 0.60);
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }
    .tea-icon-arrowup {
      transform: rotate(180deg);
    }
    .is-active {
      background: #F3F4F7;
      position: relative;
      &::after {
        content: '';
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.10) 0%, rgba(243, 244, 247, 0.00) 100%);
        width: 100%;
        height: 6px;
        position: absolute;
        top: 0px;
        left: 0px;
      }
      &::before {
        content: '';
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.10) 0%, rgba(243, 244, 247, 0.00) 100%);
        width: 100%;
        height: 6px;
        position: absolute;
        bottom: -7px;
        left: 0px;
      }
    }
    li {
      border-top-color: #F3F4F7 !important;
      padding-left: 25px !important;
      color: rgba(0, 0, 0, 0.60);
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

.skeleton {
  margin-top: 12px;
  :global {
    .t-skeleton__col {
      background-color: #d9dfe7;
    }
  }
}

.not-applicable-header {
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  user-select: none;
  &:hover {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.60) !important;
    background: #006EFF;
  }
}

.hide {
  transform: rotate(33deg) translateY(1px);
  transform-origin: center;
}

.not-applicable-item {
  color: var(---, rgba(0, 0, 0, 0.4));
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  :global {
    .tea-accordion__header-title {
      color: var(---, rgba(0, 0, 0, 0.4));
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    li {
      color: var(---, rgba(0, 0, 0, 0.4));
    }
  }
}
