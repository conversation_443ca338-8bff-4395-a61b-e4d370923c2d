/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useMemo } from 'react';
import {
  Button,
  StatusTip,
  message,
} from '@tencent/tea-component';
import globalState from '@src/stores/global.state';
import { t } from '@tea/app/i18n';
import close from '@src/statics/svg/close.svg';
import fetchData from '@src/utils/fetch';
import { cloneDeep } from 'lodash-es';
import { reportEvent, EVENT } from '@src/utils/report';
import { ThresholdStatusEnum } from '@src/constants';

import ProductList from './components/product-list';
import ProductTabs from './components/product-tabs';
import ProductDetail from './components/product-detail';
import FooterActions from './components/footer-actions';

import s from './index.module.scss';

import {
  describeArchProductThresholdInfo,
  modifyArchProductThreshold,
  describeArchProductAlgorithmType,
  describeArchProductPeriodType,
} from '@src/api/thresholdsSetting';

const { LoadingTip } = StatusTip;

interface IProps {
  onClose?: () => void;
  visible?: boolean;
  arInfo?: any;
}

/**
 * 全局监测设置-弹窗组件
 * @param {function} onClose - 关闭方法
 * @returns React.ReactElement
 */
export default function GlobalSettingModal(props: IProps): React.ReactElement {
  const [activeProduct, setActiveProduct] = useState('');
  const [activeProductName, setActiveProductName] = useState('');
  const [activeTab, setActiveTab] = useState('');
  const { onClose = () => undefined, visible, arInfo } = props;
  const [state, setState] = useState([]);
  const [productConfig, setProductConfig] = useState([]);
  const [tab, setTab] = useState([]);
  const [saveLoading, setSaveLoading] = useState(false);
  const [changed, setChanged] = useState(false);
  const [notApplicableShow, setNotApplicableShow] = useState(false);
  const currentMapId = arInfo.archInfo.archId;

  const {
    result: archProductThresholdInfoRes,
    reload,
    isLoading: describeArchProductThresholdInfoLoading,
  } = fetchData(
    'DescribeArchProductThresholdInfo',
    {
      MapId: currentMapId,
      IsDefault: 0,
    },
    describeArchProductThresholdInfo
  );

  const { result: archProductAlgorithmTypeRes } = fetchData(
    'DescribeArchProductAlgorithmType',
    null,
    describeArchProductAlgorithmType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const { result: archProductPeriodTypeRes } = fetchData(
    'DescribeArchProductPeriodType',
    null,
    describeArchProductPeriodType,
    false,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
    }
  );

  const { result: defaultArchProductThresholdInfoRes } = fetchData(
    'DescribeDefaultArchProductThresholdInfo',
    {
      MapId: currentMapId,
      IsDefault: 1,
    },
    describeArchProductThresholdInfo
  );

  // 当前配置
  const archProductThresholdInfos = archProductThresholdInfoRes?.ArchProductThresholdInfos || [];

  // 默认配置
  const defaultArchProductThresholdInfos = defaultArchProductThresholdInfoRes?.ArchProductThresholdInfos || [];

  useEffect(() => {
    const archProductThresholdInfos = archProductThresholdInfoRes?.ArchProductThresholdInfos || [];
    // 设置当前设置 总数据
    try {
      setState(JSON.parse(JSON.stringify(archProductThresholdInfos)));
    } catch (error) {
      console.log(error);
    }
    const firstProduct = archProductThresholdInfos?.[0] ?? {};
    // 设置当前产品
    setActiveProduct((archProductThresholdInfos || []).find(v => v?.ThresholdStatus !== ThresholdStatusEnum.NOT_APPLICABLE)?.Product || firstProduct?.Product || '');
    setActiveProductName(firstProduct?.ParentProductName ? `${firstProduct?.ParentProductName} > ${firstProduct?.ProductName}` : firstProduct?.ProductName || '');
    if (archProductThresholdInfos.every(v => v?.ThresholdStatus === ThresholdStatusEnum.NOT_APPLICABLE)) {
      setNotApplicableShow(true);
    }
  }, [archProductThresholdInfoRes]);

  useEffect(() => {
    // 设置当前产品当前tab
    setActiveTab(!activeProduct
      ? archProductThresholdInfos?.[0]?.ArchThresholdInfos?.[0]?.InsType || ''
      : (archProductThresholdInfos || []).find?.(item => item.Product === activeProduct)?.ArchThresholdInfos?.[0]?.InsType || '');
  }, [activeProduct]);

  useEffect(() => {
    const archProductThresholdInfos = state;
    const productConfig = !activeProduct
      ? archProductThresholdInfos?.[0]?.ArchThresholdInfos || []
      : (archProductThresholdInfos || []).find(item => item.Product === activeProduct)?.ArchThresholdInfos || [];
    setProductConfig(productConfig);
    const tab = !activeTab
      ? productConfig?.[0]?.ThresholdConfigInfos || []
      : productConfig.find(item => item?.InsType === activeTab)
        ?.ThresholdConfigInfos
        || productConfig?.[0]?.ThresholdConfigInfos
        || [];
    setTab(tab);
  }, [state, activeProduct, activeTab]);

  const tabsBar = (productConfig || []).filter(v => v?.InsType && v?.InsTypeName);
  const algorithmInfos = archProductAlgorithmTypeRes?.AlgorithmInfos || [];
  const periodTypeInfos = archProductPeriodTypeRes?.PeriodTypeInfos || [];
  const metricPeriodTypeInfos = archProductPeriodTypeRes?.MetricPeriodTypeInfos || [];

  // eslint-disable-next-line max-len
  const defaultProductConfig = defaultArchProductThresholdInfos.find(item => item.Product === activeProduct);
  const curProductConfig = state.find(item => item.Product === activeProduct);
  // eslint-disable-next-line max-len
  const productTabsConfig =    curProductConfig?.ArchThresholdInfos?.find(item => item?.InsType === activeTab) || curProductConfig?.ArchThresholdInfos?.[0];
  // eslint-disable-next-line max-len
  const defaultProductTabs = (
    defaultProductConfig?.ArchThresholdInfos?.find(item => item?.InsType === activeTab) || defaultProductConfig?.ArchThresholdInfos?.[0]
  )?.ThresholdConfigInfos;

  const newTabsConfig = JSON.stringify(defaultProductTabs);
  const oldTabsConfig = JSON.stringify(productTabsConfig?.ThresholdConfigInfos);
  const isDefault = newTabsConfig === oldTabsConfig;

  const currentProductDisabled = useMemo(() => (archProductThresholdInfos ?? []).find(v => v?.Product === activeProduct)?.ThresholdStatus === ThresholdStatusEnum.NOT_APPLICABLE, [archProductThresholdInfos, activeProduct]);

  return (
    visible && (
      <>
        <div
          style={{
            width: '100vw',
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 10,
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}
        ></div>
        <div className={s.container}>
          <ProductList
            products={archProductThresholdInfos}
            activeProduct={activeProduct}
            loading={describeArchProductThresholdInfoLoading}
            notApplicableShow={notApplicableShow}
            onProductSelect={(product, productName) => {
              setActiveProduct(product);
              setActiveProductName(productName);
              setActiveTab('');
            }}
            onNotApplicableToggle={() => setNotApplicableShow(!notApplicableShow)}
          />
          <div className={s.right}>
            <div className={s.close}>
              <img
                src={close}
                alt=""
                className={s.close}
                onClick={() => {
                  onClose();
                }}
              />
            </div>
            <div className={s.name}>
              <p>{activeProductName}</p>
              {!currentProductDisabled && <Button
                type="link"
                disabled={isDefault}
                style={{ marginLeft: 10 }}
                onClick={() => {
                  reportEvent({
                    key: EVENT.RESET_DEFAULT_CONFIG,
                    extraInfo: null,
                  });
                  try {
                    productTabsConfig.ThresholdConfigInfos = JSON.parse(JSON.stringify(defaultProductTabs));
                  } catch (error) {
                    console.log(error);
                  }
                  setState([...state]);
                  const str1 = JSON.stringify(state);
                  const str2 = JSON.stringify(archProductThresholdInfos);
                  if (str1 !== str2) {
                    setChanged(true);
                  } else {
                    setChanged(false);
                  }
                }}
              >
                {t('重置')}
              </Button>}
            </div>
            <div className={changed ? s.content : s['content-full']}>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {describeArchProductThresholdInfoLoading ? (
                  <div style={{ paddingTop: 15 }}>
                    <LoadingTip />
                  </div>
                ) : null}
              </div>

              <ProductTabs
                tabs={tabsBar}
                activeTab={activeTab}
                activeProduct={activeProduct}
                onTabChange={setActiveTab}
              />

              <ProductDetail
                tabData={tab}
                algorithmInfos={algorithmInfos}
                periodTypeInfos={{
                  commonPeriodTypeInfos: periodTypeInfos,
                  metricPeriodTypeInfos,
                }}
                disable={currentProductDisabled}
                activeProduct={activeProduct}
                onChange={(data, index) => {
                  const productConfig = state.find(item => item.Product === activeProduct);
                  const productTabs = (
                    productConfig?.ArchThresholdInfos?.find(item => item?.InsType === activeTab) || productConfig?.ArchThresholdInfos?.[0]
                  )?.ThresholdConfigInfos;
                  productTabs[index] = data;
                  setState([...state]);
                  const str1 = JSON.stringify(state);
                  const str2 = JSON.stringify(archProductThresholdInfos);
                  if (str1 !== str2) {
                    setChanged(true);
                  } else {
                    setChanged(false);
                  }
                }}
              />
            </div>
            {changed && (
              <FooterActions
                saveLoading={saveLoading}
                onSave={() => {
                  setSaveLoading(true);
                  let flag = true;
                  state.forEach((item) => {
                    item.ArchThresholdInfos.forEach((item2) => {
                      item2.ThresholdConfigInfos.forEach((item3) => {
                        if (!item3.PeriodValue) {
                          flag = false;
                        }
                      });
                    });
                  });
                  if (flag) {
                    const params = cloneDeep(state);
                    params.forEach((v) => {
                      if (v.ThresholdStatus === ThresholdStatusEnum.NOT_APPLICABLE) {
                        // eslint-disable-next-line no-param-reassign
                        delete v.ThresholdStatus;
                      }
                    });
                    reportEvent({
                      key: EVENT.ADJUST_DEFAULT_CONFIG,
                      extraInfo: JSON.stringify(state),
                    });
                    modifyArchProductThreshold({
                      MapId: currentMapId,
                      ArchProductThresholdInfos: params,
                    })
                      .then((res) => {
                        if (!res?.Error) {
                          reload();
                          setChanged(false);
                        }
                      })
                      .finally(() => {
                        globalState.set(state => ({
                          ...state,
                          getDescribeNodeLoadInfoKey: +new Date(),
                        }));
                        setSaveLoading(false);
                      });
                  } else {
                    setSaveLoading(false);
                    message.error({
                      content: t('请选择周期'),
                    });
                  }
                }}
                onCancel={() => {
                  try {
                    setState(JSON.parse(JSON.stringify(archProductThresholdInfos || [])));
                  } catch (error) {
                    console.log(error);
                  }
                  setChanged(false);
                }}
              />
            )}
          </div>
        </div>
      </>
    )
  );
}
