.container {
  width: 679px;
  height: 525px;
  position: absolute;
  left: 50%;
  top: 50%;
  display: flex;
  transform: translateX(-50%) translateY(-50%);
  z-index: 11;
  background-color: #fff;
  box-shadow: 0px 0px 10px 2px #0000001A;
  border-radius: 8px;
  overflow: hidden;
}

.loading {
  height: 525px;
}
.col-flex {
  &>div {
    display: flex;
    align-items: center;
  }
}

.right {
  background-color: #fff;
  flex: 1;
  position: relative;
  padding-top: 15px;
  overflow: hidden;
  .close {
    margin-right: 10px;
    display: flex;
    justify-content: flex-end;
    img {
      width: 16px;
      cursor: pointer;
    }
  }
  .name {
    display: flex;
    border-bottom: 1px solid #E7EAEF;
    padding-bottom: 5px;
    margin-top: -7px;
    padding-left: 15px;
    padding-right: 15px;
    p {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 500;
      text-align: left;
    }
  }

  .content {
    height: calc(100% - 85px);
    overflow: auto;
    overflow-x: hidden;
  }
  .content-full {
    height: calc(100% - 40px);
    overflow: auto;
    overflow-x: hidden;
  }
  .detail {
    padding: 15px;
    .card {
      background-color: #fff;
      border: 1px solid #EDF1F7;
      box-shadow: none;
      border-radius: 8px;
      padding-bottom: 20px;

    }
  }

}
