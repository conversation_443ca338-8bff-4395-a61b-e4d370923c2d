import React, { useEffect, useState } from 'react';
import {
  Slider,
} from '@tencent/tea-component';
import './index.less';

interface Props {
  // 阈值滑块id,要保持唯一性
  id: any
  // 阈值title
  title?: string;
  // 阈值
  threshold?: any;
  // 阈值变化回调
  thresholdChange?: (value) => void
}

const ThresholdSlider = ({ id = '', title, threshold, thresholdChange }: Props) => {
  // 滑块刻度线
  const marks = [{ value: 0 }, { value: 25 }, { value: 50 }, { value: 75 }, { value: 100 }];
  // 阈值
  const [value, setValue] = useState([0, 0] as any);
  const setColor = (value) => {
    if (id) {
      const dom :any = document.querySelector(`#${id} .tea-capacity-slider__bar`);
      dom.style.background = `linear-gradient(to right, #77CD7E 0%, #77CD7E ${value[0]}%, #FF963E ${value[0]}%, #FF963E ${value[1]}%, #F6545A ${value[1]}%, #F6545A 100%)`;
    }
  };
  useEffect(() => {
    const [start = 0, end] = threshold;
    setColor([start, end]);
    setValue([start, end]);
  }, [threshold]);
  return (
    <div className="threshold-slider">
      {title && <div className="title">{title}</div>}
      <div className="slider-wrap" id={id}>
        <Slider
          rangeMode
          min={0}
          max={100}
          marks={marks}
          value={value}
          enableTrackTip
          onUpdate={value => setColor(value)}
          onChange={(value) => {
            setValue(value);
            thresholdChange(value);
          }}
        />
      </div>
    </div>
  );
};

export default ThresholdSlider;
