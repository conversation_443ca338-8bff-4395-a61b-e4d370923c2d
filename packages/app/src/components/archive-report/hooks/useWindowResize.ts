/* eslint-disable no-param-reassign */
import { useRef, useEffect } from 'react';

interface Scale {
  width: string;
  height: string;
}

const dealScale = (scale: Scale) => {
  if (scale.width < '1') {
    scale.width = '1';
  }
  if (scale.height < '1') {
    scale.height = '1';
  }
  // 4k屏放大一点
  if (scale.width > '1.85') {
    scale.width = `${Number(scale.width) + 0.2}`;
  }
  if (scale.height > '1.85') {
    scale.height = `${Number(scale.height) + 0.2}`;
  }
};

export const calcRateStyle = (scaleInfo: boolean = false) => {
  // 设计稿尺寸（px）
  const baseWidth = 1920;
  const baseHeight = 1080;
  // 需保持的比例（默认1.77778）
  const baseProportion = parseFloat((baseWidth / baseHeight).toFixed(5));
  // 当前宽高比
  const currentRate = parseFloat((window.innerWidth / window.innerHeight).toFixed(5));
  const scale: Scale = {
    width: '1',
    height: '1',
  };
  if (currentRate > baseProportion) {
    // 表示更宽
    scale.width = (
      (window.innerHeight * baseProportion)
      / baseWidth
    ).toFixed(5);
    scale.height = (window.innerHeight / baseHeight).toFixed(5);
  } else {
    // 表示更高
    scale.height = (
      window.innerWidth
      / baseProportion
      / baseHeight
    ).toFixed(5);
    scale.width = (window.innerWidth / baseWidth).toFixed(5);
  }
  dealScale(scale);
  return scaleInfo ? scale : {
    transform: `scale(${Number(scale.width)}, ${Number(scale.height)}) translate(-50%, -50%)`,
    transformOrigin: 'left top',
  };
};

export const useWindowResize = () => {
  const screenRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  // 设计稿尺寸（px）
  const baseWidth = 1920;
  const baseHeight = 1080;

  // 需保持的比例（默认1.77778）
  const baseProportion = parseFloat((baseWidth / baseHeight).toFixed(5));

  const calcRate = () => {
    // 当前宽高比
    const currentRate = parseFloat((window.innerWidth / window.innerHeight).toFixed(5));

    const scale: Scale = {
      width: '1',
      height: '1',
    };

    if (screenRef.current) {
      if (currentRate > baseProportion) {
        // 表示更宽
        scale.width = (
          (window.innerHeight * baseProportion)
          / baseWidth
        ).toFixed(5);
        scale.height = (window.innerHeight / baseHeight).toFixed(5);
      } else {
        // 表示更高
        scale.height = (
          window.innerWidth
          / baseProportion
          / baseHeight
        ).toFixed(5);
        scale.width = (window.innerWidth / baseWidth).toFixed(5);
      }
      dealScale(scale);
      screenRef.current.style.transform = `scale(${Number(scale.width)}, ${Number(scale.height)}) translate(-50%, -50%)`;
      // 添加transform-origin以确保正确的缩放原点
      screenRef.current.style.transformOrigin = 'left top';
    }
  };

  const handleResize = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = setTimeout(() => {
      calcRate();
    }, 200);
  };

  useEffect(() => {
    calcRate(); // 初始化时计算一次
    window.addEventListener('resize', handleResize);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return {
    screenRef,
    calcRate,
  };
};
