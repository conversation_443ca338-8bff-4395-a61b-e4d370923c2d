/* eslint-disable @typescript-eslint/naming-convention */
import React from 'react';
import { message } from '@tencent/tea-component';
import { ReportView } from '../../constants';
import A4 from './components/a4';
import Wide from './components/wide';

interface ArchiveReportOverviewProps {
  Sigma: any;
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  arcData?: any;
  onChangePage?: () => void;
  view?: ReportView;
  onChangeView?: () => void;
  data?: any;
  exportReport?: (success: () => void, error: () => void) => void;
}

const ArchiveReportOverview: React.FC<ArchiveReportOverviewProps> = (props) => {
  const {
    visible,
    view,
    Sigma,
    data,
    exportReport,
  } = props;

  if (visible && view === ReportView.A4) {
    return <A4 {...props} Sigma={Sigma} data={data} exportReport={exportReport} message={message} />;
  }
  if (visible && view === ReportView.WIDE) {
    return <Wide {...props} Sigma={Sigma} data={data} exportReport={exportReport} message={message} />;
  }
  return null;
};

export default ArchiveReportOverview;
