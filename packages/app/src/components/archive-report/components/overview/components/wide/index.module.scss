$layout-padding: 40px;

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: scale(1) translate(-50%, -50%);
  z-index: 11;
  background-color: #1842be;
  width: 1122px;
  height: 793px;
  .content {
    height: 748px;
    .summary {
      min-height: 205px;
      background-image: url('../../../../statics/bg.png');
      background-repeat: no-repeat;
      background-position: 97% 20px;
      background-size: 43%;
      padding-left: $layout-padding;
      padding-right: $layout-padding;
      box-sizing: border-box;
      overflow: hidden;
      .header {
        display: flex;
        align-items: center;
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        margin-top: 40px;
        height: 30px;
      }
      .title {
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        margin-left: 7px;
      }
      .desc {
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        margin-top: 5px;
      }
      .row {
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-direction: row;
        margin-top: 11px;
        &:nth-child(1) {
          margin-top: 5px;
        }
        .col {
          &:nth-child(1) {
            width: 140px;
          }
          &:nth-child(2) {
            width: 300px;
            display: flex;
            align-items: center;
            span {
              margin-left: 8px;
              background: rgba(0, 0, 0, 0.4);
              padding: 0 3px;
            }
          }
        }
      }
    }
    .chart {
      height: 438px;
      padding-left: $layout-padding;
      padding-right: $layout-padding;
      box-sizing: border-box;
      overflow: hidden;
      margin-top: 30px;
      .box {
        background-color: #fff;
        height: 100%;
        padding: 12px 15px;
        box-sizing: border-box;
        overflow: hidden;
        .title {
          color: var(---2, #0042BF);
          /* Heading 3 */
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
        }
        .sigma {
          border-radius: 9px;
          border: 1px solid var(---, #CFD5DE);
          margin-top: 15px;
          overflow: hidden;
        }
        .echart {
          height: 370px;
          flex-direction: column;
          margin-top: 15px;
          display: flex;
        }
      }
    }
  }
  :global {
    svg {
      overflow: auto !important;
    }
  }
}

.box {
  display: flex;
  .left {
    flex: 1;
  }
  .right {
    flex: 1;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 44px;
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
    .wrapper {
      display: flex;
      align-items: center;
    }
    .rotate {
      display: flex;
      width: 30px;
      height: 30px;
      justify-content: center;
      align-items: center;
      border-radius: 15px;
      margin-left: 15px;
      background: var(--color-bg-4, #363A50);
      &:hover {
        cursor: pointer;
      }
    }
    .title {
      border-radius: 15px;
      background: var(---4, #363A50);
      display: flex;
      height: 30px;
      padding: 0px 10px;
      align-items: center;
      gap: 5px;
      color: #fff;
      &:hover {
        cursor: pointer;
      }

      img {
        width: 16px;
        height: 16px;
      }

      span {
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
    }

    .closeIcon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      opacity: 0.6;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  button {
    display: flex;
    height: 26px;
    padding: 2px 20px;
    justify-content: center;
    align-items: center;
    gap: 5px;
    border-radius: 15px;
    background: var(---0, #006EFF);
    /* tea-button-shadow */
    box-shadow: 0px 2px 4px 0px rgba(0, 35, 11, 0.20);
    outline: none;
    border: none;
    color: #FFF;
    text-align: center;
    /* Body-M */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
  }
}
