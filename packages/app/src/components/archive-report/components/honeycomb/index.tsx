import React, { useState, useEffect } from 'react';
import { ReportView, CcnConfig } from '../../constants';
import s from './index.module.scss';
import { RiskLevel } from '../../../../constants';
import { StatusColorMap } from '../../../../constants/color';
interface HoneycombProps {
  data?: Array<any>;
  pageSize?: number;
  rowSize?: number;
  width?: number;
  view?: ReportView;
  print?: boolean;
  product?: string;
}


const Honeycomb: React.FC<HoneycombProps> = ({
  data = [],
  rowSize = 10,
  width = 22,
  view = ReportView.A4,
  print = false,
  product = '',
}) => {
  const getHighestRiskItem = (items) => {
    // 先按 InstanceId 和 RiskLevel 降序排序
    const sortedItems = [...items].sort((a, b) => {
      if (a.InstanceId !== b.InstanceId) {
        return a.InstanceId.localeCompare(b.InstanceId);
      }
      return b.RiskLevel - a.RiskLevel;
    });
    // 取出每个 InstanceId 中 RiskLevel 最高的项
    const result = [];
    let currentInstanceId: string | null = null;

    for (const item of sortedItems) {
      if (item.InstanceId !== currentInstanceId) {
        result.push(item);
        currentInstanceId = item.InstanceId;
      }
    }

    return result;
  };

  const isCcn = product?.toLowerCase?.() === CcnConfig.CCNS;
  const sortedData = isCcn ? getHighestRiskItem(data)?.sort?.((a, b) => b?.RiskLevel - a?.RiskLevel) : data?.sort?.((a, b) => b?.RiskLevel - a?.RiskLevel);
  const isWide = view === ReportView.WIDE;
  const pageSize = rowSize * 3 - 1;
  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(sortedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const currentData = sortedData.slice(startIndex, startIndex + pageSize);

  const getStatusColor = (status: number) => StatusColorMap[status as RiskLevel];

  const renderHoneycombGrid = () => {
    const topRow = currentData.slice(0, rowSize);
    const middleRow = currentData.slice(rowSize, rowSize * 2 - 1);
    const bottomRow = currentData.slice(rowSize * 2 - 1, pageSize);
    return (
      <div className={s.honeycombWrapper}>
          <div className={s.honeycombRow}>
            {topRow.map((item, index) => (
              <div
                key={`top-${index}`}
                className={s.honeycomb}
                style={{ backgroundColor: getStatusColor(item.RiskLevel), width: `${width}px`, height: `${width - 2}px` }}
              >
              </div>
            ))}
          </div>
          <div className={`${s.honeycombRow} ${s.middleRow}`}>
            {middleRow.map((item, index) => (
              <div
                key={`middle-${index}`}
                className={s.honeycomb}
                style={{ backgroundColor: getStatusColor(item.RiskLevel), width: `${width}px`, height: `${width - 2}px` }}
              >
              </div>
            ))}
          </div>
          <div className={s.honeycombRow}>
            {bottomRow.map((item, index) => (
              <div
                key={`bottom-${index}`}
                className={`${s.honeycomb} ${print && index === 9 && data.length > pageSize ? s.printHoneycomb : ''}`}
                style={{ backgroundColor: getStatusColor(item.RiskLevel), width: `${width}px`, height: `${width - 2}px` }}
              >
              </div>
            ))}
          </div>
      </div>
    );
  };

  useEffect(() => {
    setCurrentPage(1);
    return () => {
      setCurrentPage(1);
    };
  }, [data]);
  return (
    <div className={s.honeycombContainer} style={isWide ? { height: 100 } : {}}>
      {renderHoneycombGrid()}
      {totalPages > 1 && !print && (
        <div className={s.pagination} style={isWide ? { marginTop: 32 } : {}}>
          <span
            className={`${s.arrow} ${currentPage === 1 ? s.disabled : ''}`}
            onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
          >
            ◀
          </span>
          <span className={s.pageInfo}>
            {currentPage} / {totalPages}
          </span>
          <span
            className={`${s.arrow} ${currentPage === totalPages ? s.disabled : ''}`}
            onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
          >
            ▶
          </span>
        </div>
      )}
    </div>
  );
};

export default Honeycomb;
