.honeycombContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  margin-left: -6px;
}

.honeycombWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
  height: 60px;
  // justify-content: center;
}

.honeycombRow {
  display: flex;
  gap: 2px;
  margin: 0 auto;
  // width: fit-content;
  width: 100%;

  &.middleRow {
    transform: translateX(12px);
    width: 100%;
    justify-content: flex-start;
  }
}
.printHoneycomb {
  background-color: #CFD5DE !important;
  &:before {
    content: '';
    width: 20px;  /* 略小于外层 */
    height: 18px;
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
    background-color: white;
    position: absolute;
    top: 1px;
    left: 1px;
  }
  &:after {
    content: '...';
    position: absolute;
    right: 7px;
    top: -3px;
    transform: rotate(-30deg);
    color: #CFD5DE;
  }
}
.honeycomb {
  position: relative;
  width: 22px;
  height: 20px;
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
  transition: all 0.3s ease;
  transform: rotate(30deg) scale(1);
  &:hover {
    transform:  rotate(30deg) scale(1.05);
  }
}

.value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.pagination {
  display: flex;
  align-items: center;
  font-size: 10px;
  color: #333;
  width: 100%;
  justify-content: right;
  margin-right: 15px;
  margin-top: 5px;
}

.arrow {
  cursor: pointer;
  user-select: none;
  color: #666;

  &.disabled {
    color: #ccc;
    cursor: not-allowed;
  }

  &:not(.disabled):hover {
    color: #333;
  }
}

.pageInfo {
  min-width: 40px;
  text-align: center;
}
