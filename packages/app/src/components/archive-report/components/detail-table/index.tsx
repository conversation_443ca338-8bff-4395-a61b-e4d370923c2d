import React from 'react';
import { orderBy } from 'lodash-es';
import up from '../../statics/up.svg';
import down from '../../statics/down.svg';
import styles from './index.module.scss';
import { ReportView, CcnConfig, ClickHouseConfig, DorisConfig } from '../../constants';
import { t } from '@tencent/tea-app/lib/i18n';
import { StatusColorMap } from '../../../../constants/color';

interface IProps {
  view?: ReportView;
  print?: boolean;
  data?: Array<any>;
  currentPage?: number;
  product?: string;
}

const DetailTable: React.FC<IProps> = ({
  view = ReportView.A4,
  print = false,
  data = [],
  currentPage = 1,
  product = '',
}) => {
  const isTcHouse = ClickHouseConfig.CLICK_HOUSE === product?.toLowerCase?.();
  const isDoris = DorisConfig.DORIS === product?.toLowerCase?.();
  const sortData = orderBy(data, ['RiskLevel', 'InstanceId'], ['desc', 'desc']);
  const isWide = view === ReportView.WIDE;
  const noInstanceType = sortData?.every(v => !v?.InstanceType) || sortData.length === 0;
  const metricNameMap = {
    [ClickHouseConfig.CLICK_HOUSE]: ClickHouseConfig.INSTANCE_TYPE_NAME,
    [DorisConfig.DORIS]: DorisConfig.INSTANCE_TYPE_NAME,
  };
  const instanceNameMap = {
    [ClickHouseConfig.CLICK_HOUSE]: ClickHouseConfig.INSTANCE_NAME,
    [DorisConfig.DORIS]: DorisConfig.INSTANCE_NAME,
  };

  // isTcHouse 新增一列用于展示ip
  if (isTcHouse || isDoris) {
    sortData.forEach((element) => {
      element.MetricItems.unshift({
        MetricName: metricNameMap[product?.toLowerCase?.()],
        MetricValue: element?.InstanceIp,
      });
    });
  }

  const columns = Array.from(new Set(sortData?.flatMap(item => item?.MetricItems?.map(v => v?.MetricName as string) || []).filter(Boolean))) as string[];
  const hasRegion = sortData?.some(v => v?.Region);
  if (product?.toLowerCase?.() === CcnConfig.CCNS || hasRegion) {
    columns.push(CcnConfig.COLUMN);
  }

  // Fill missing MetricItems
  const filledSortData = sortData?.map((item) => {
    const existingMetrics = item?.MetricItems || [];
    const filledMetrics = columns.map((metricName) => {
      const existingMetric = existingMetrics.find(m => m?.MetricName === metricName);
      return existingMetric || null;
    });
    return {
      ...item,
      MetricItems: filledMetrics,
    };
  });

  const printPageSize = 15;
  const displayData = print
    ? filledSortData?.slice(
      (currentPage === 1 ? 0 : 4 + (currentPage - 2) * printPageSize),
      (currentPage === 1 ? 4 : 4 + (currentPage - 1) * printPageSize)
    )
    : filledSortData;

  const slice = !isWide ? 4 : 7;
  const keepTwoDecimalPlaces = (num: number) => {
    if (num === -1) {
      return '-';
    }
    return `${parseFloat(num.toFixed(2))}%`;
  };

  const fixedLen = (noInstanceType ? 1 : 2);
  let columnsLength = columns?.length + fixedLen;
  columnsLength = columnsLength >= slice + fixedLen ? slice + fixedLen : columnsLength;
  const tdStyle = { width: `${(100 / columnsLength / 100) * (isWide ? 1014 : 506)}px` };

  const renderMetricCell = (column: string, index: number, item: any) => {
    const metricItem = item?.MetricItems?.[index];

    // ClickHouse IP 列
    if (column === ClickHouseConfig.INSTANCE_TYPE_NAME || column === DorisConfig.INSTANCE_TYPE_NAME) {
      return (
        <div className={styles.usageCell} style={tdStyle}>
          <p>
            <span>{metricItem?.MetricValue || '-'}</span>
          </p>
        </div>
      );
    }

    // CCN 区域列
    if (column === CcnConfig.COLUMN) {
      return (
        <div className={styles.usageCell} style={{ ...tdStyle, display: 'flex', alignItems: 'flex-start', justifyContent: 'center', gap: 0 }}>
          <span>{item?.Region?.SourceRegion}</span>
          <span style={{ marginTop: 2 }}>
            {item?.Region?.DestRegion ? `${item?.Region?.DestRegion}` : ''}
          </span>
        </div>
      );
    }

    // 默认指标列
    return (
      <div className={styles.usageCell} style={tdStyle}>
        <p className={styles.greyText}>
          <span>{(metricItem?.Period || '-') + t('天')}</span>
          <span>-</span>
          <span>{metricItem?.AlgorithmName || '-'}</span>
        </p>
        <p className={styles.flex}>
          <span className={styles.dotWrapper}>
            <span
              className={styles.dot}
              style={{
                backgroundColor: StatusColorMap[metricItem?.RiskLevel] || 'transparent',
              }}
            />
            {metricItem ? keepTwoDecimalPlaces(metricItem?.MetricValue ?? 0) : '-'}
          </span>
          {metricItem?.Rate ? (
            <span className={metricItem.Rate > 0 ? styles.up : styles.down}>
              <img src={metricItem.Rate > 0 ? up : down} alt={metricItem.Rate > 0 ? 'up' : 'down'} />
              {keepTwoDecimalPlaces(metricItem.Rate ?? 0)}
            </span>
          ) : (
            <span>&nbsp;-</span>
          )}
        </p>
      </div>
    );
  };

  return (
    <div
      style={print ? { height: '100%', overflow: 'hidden' } : {}}
      className={`${styles.tableContainer} ${isWide ? styles.wideTableContainer : ''}`}
      key={Math.random()}
    >
      <table className={styles.table}>
        <thead>
          <tr>
            <th style={tdStyle}>
              <span style={tdStyle}>{isTcHouse || isDoris ? instanceNameMap[product?.toLowerCase?.()] : t('实例')}</span>
            </th>
            { !noInstanceType && <th style={tdStyle}>
              <span style={tdStyle}>{t('类型')}</span>
            </th> }
            {columns?.slice(0, slice).map(column => column && (
              <th key={column} style={tdStyle}>
                <span style={tdStyle}>{t(column)}</span>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {displayData?.map(item => item && (
            <tr key={item?.InstanceId || Math.random()}>
              <td style={tdStyle}>
                <div className={styles.instanceCell} style={tdStyle}>
                  <p title={item?.InstanceName || '-'} className={styles.instanceName}>{item?.InstanceName || '-'}</p>
                  <p title={item?.InstanceId || '-'} className={styles.instanceId}>{item?.InstanceId || '-'}</p>
                </div>
              </td>
              { !noInstanceType && <td>
                <p style={tdStyle}>{item?.InstanceType || '-'}</p>
              </td> }
              {columns?.slice(0, slice).map((column, index) => (
                <td key={`${column}-${index}`} style={tdStyle}>
                  {renderMetricCell(column, index, item)}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DetailTable;
