.wideTableContainer {
  .table {
    font-size: 11px !important;
  }
  th, td {
    &>p,&>span,&>div{
    }
  }
}
.tableContainer {
  width: 100%;
  height: 230px; // 固定高度，超出时显示滚动条
  overflow-y: auto;
  position: relative;
  overflow-x: hidden;
  .greyText {
    color: rgba(0, 0, 0, 0.40);
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border-radius: 3px;
  }
}
.dotWrapper {
  display: flex;
  align-items: center;
}
.flex {
  display: flex;
  align-items: self-end;
}
.dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  margin-right: 3px;
  border-radius: 50%;
  background: #E54545;
}
.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 10px;
  box-sizing: border-box;
  td,th,tr,thead,tbody{
    box-sizing: border-box;
  }
  thead {
    position: sticky;
    top: 0;
    background: #F3F4F7;
    z-index: 1;
  }

  th, td {
    padding: 5px 0;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
    &>p,&>span,&>div{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  tr {
    th:nth-child(1) {
      &>p,&>span,&>div{
      }
    }
    td:nth-child(1) {
      &>p,&>span,&>div{
      }
    }
  }

  th {
    font-weight: 500;
    color: #000;
    span {
      display: inline-block;
      box-sizing: border-box;
      text-indent: 5px;
    }
  }
}

.instanceCell {
  display: flex;
  flex-direction: column;

  .instanceName {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .instanceId {
    font-size: 10px;
    color: var(---, rgba(0, 0, 0, 0.40));
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.usageCell {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
  p {
    width: 100%;
    white-space: pre-wrap;
  }
  .up {
    color: #f5222d;
    font-size: 9px;
    padding-left: 2px;
    img {
      transform: translateY(1px);
    }
  }

  .down {
    color: #52c41a;
    font-size: 9px;
    padding-left: 2px;
    img {
      transform: translateY(1px);
    }
  }
}