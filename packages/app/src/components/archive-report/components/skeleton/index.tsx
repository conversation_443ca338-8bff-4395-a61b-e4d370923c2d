import React from 'react';
import classNames from 'classnames';
import styles from './index.module.scss';

interface SkeletonProps {
  className?: string;
  style?: React.CSSProperties;
  type?: 'rect' | 'circle';
}

const Skeleton: React.FC<SkeletonProps> = ({
  className,
  style,
  type = 'rect',
}) => (
    <div
      className={classNames(
        styles.skeleton,
        styles[type],
        className
      )}
      style={style}
    />
);

export default Skeleton;
