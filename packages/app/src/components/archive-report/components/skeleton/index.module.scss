.skeleton {
  background: #f2f2f2;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: flash 1.5s infinite;
  }
}

@keyframes flash {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.rect {
  width: 100%;
  height: 16px;
  border-radius: 4px;
  margin: 8px 0;
}

.circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}
