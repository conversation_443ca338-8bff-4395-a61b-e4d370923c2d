$layout-padding: 23px;

.container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: scale(1) translate(-50%, -50%);
  z-index: 11;
  background-color: #1842be;
  width: 1122px;
  height: 793px;
  background-repeat: no-repeat;
  background-position: right 20px;
  background-size: 60%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  .dotBox {
    display: flex !important;
    align-items: center;
    justify-content: end;
    width: 100%;
    padding-right: 15px;
    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 5px;
      margin-left: 5px;
    }
  }
  .uninvolved {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 130px;
    img {
      width: 73px;
    }
    p {
      margin-top: 7px;
      color: rgba(0, 0, 0, 0.40);
    }
  }
  .box {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 40px;
    padding-top: 35px;
    .header {
      display: flex;
      align-items: center;
      background-color: #1842be;
      width: 70px;
      img {
        width: 13px;
      }
      &:hover {
        cursor: pointer;
      }
      span {
        color: #FFF;
        text-align: center;
        /* Body-M */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
      }
    }
    .content {
      margin-top: 10px;
      background-color: #fff;
      padding: 14px;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      .leftRight {
        display: flex;
        height: 335px;
        .leftContainer {
          width: 54%;
        }
        .rightContainer {
          flex: 1;
          padding-left: 20px;
        }
      }
    }
  }
  .sigma {
    border: 1px solid #CFD5DE;
    border-radius: 9px;
    overflow: hidden;
    box-sizing: border-box;
  }
  .name {
    color: var(---2, #0042BF);
    /* Heading 3 */
    font-family: "PingFang SC";
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    margin-top: 8px;
  }
  .chart {
    .desc, .pie {
      width: 100%;
      .left, .right {
        width: 50%;
      }
    }
  }
  .grey {
    color: rgba(0, 0, 0, 0.40);
    font-family: "PingFang SC";
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    position: relative;
    top: -8px;
  }
  .pie {
    flex-direction: column;
    display: flex;
    margin-top: 5px;
  }
  .desc {
    display: flex;
    p {
      margin-top: 10px;
      display: flex;
      align-items: center;
      span {
        display: inline-block;
      }
      &>span:nth-child(1) {
        color: var(---, rgba(0, 0, 0, 0.40));
        /* Body-R */
        font-family: "PingFang SC";
        font-size: 11px;
        font-style: normal;
        font-weight: 400;
        width: 85px;
      }
      &>span:nth-child(2) {
        overflow: hidden;
        color: var(---, rgba(0, 0, 0, 0.90));
        text-overflow: ellipsis;
        /* Body-R */
        font-family: "PingFang SC";
        font-size: 11px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
  :global {
    svg {
      overflow: auto !important;
    }
  }
}
