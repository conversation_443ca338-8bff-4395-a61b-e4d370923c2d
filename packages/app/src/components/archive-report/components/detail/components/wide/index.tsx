/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/naming-convention */
import React, { useState, useEffect } from 'react';
import globalState from '@src/stores/global.state';
import SigmaComponent from '../../../sigma';
import Chart from '../../../detail-chart';
import Honeycomb from '../../../honeycomb';
import Table from '../../../detail-table';
import left from '../../../../statics/left.svg';
import Header from '../../../header';
import Skeleton from '../../../skeleton';
import uninvolved from '../../../../statics/uninvolved.svg';
import { RiskColor } from '../../../../../../constants/color';
import s from './index.module.scss';
import { t } from '@tencent/tea-app/lib/i18n';
import {
  useWindowResize,
  calcRateStyle,
} from '../../../../hooks/useWindowResize';
import { ReportView, ReportType, CcnConfig } from '../../../../constants';

interface ArchiveReportDetailProps {
  Sigma: any;
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  arcData?: any;
  onChangePage?: () => void;
  view?: ReportView;
  onChangeView?: () => void;
  data?: any;
  activeKey?: string;
  setActiveKey?: (key: string) => void;
  exportReport?: (success: () => void, error: () => void) => void;
  message?: any;
  digitalAssets?: boolean;
}

const ArchiveReportDetail: React.FC<ArchiveReportDetailProps> = (props) => {
  const {
    Sigma,
    onClose = () => undefined,
    visible,
    arInfo,
    arcData,
    onChangePage,
    // view,
    onChangeView,
    data,
    activeKey,
    setActiveKey,
    exportReport,
    message,
    digitalAssets = false,
  } = props;
  const [loading, setLoading] = useState(true);
  const { screenRef } = useWindowResize();
  const scaleStyle = calcRateStyle();
  // 生命周期
  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const item = data?.NodeLoadDetailItems?.find((item: any) => item?.NodeUuid === activeKey) || {};
  const instanceLoadInfosFilter = item?.InstanceLoadInfos?.filter(v => v?.InstanceType !== '');
  const isCcn = item?.Product?.toLowerCase?.() === CcnConfig.CCNS;
  // 运营端下载
  const onDownload = (type: ReportType, downloadHash: string) => {
    globalState.exportState.set(state => ({
      ...state,
      downloadData: {
        type,
        downloadHash,
      },
    }));
  };

  return (
    visible && (
      <>
        {!digitalAssets && <div
          style={{
            width: '100vw',
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 10,
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}
        ></div>}
        <div
          className={s.container}
          id="archive-report"
          ref={screenRef}
          style={scaleStyle}
        >
          <Header
            onClose={onClose}
            archiveStatus={data?.ArchiveStatus}
            url={data?.DigitalAssetUrl}
            reportFile={data?.ReportFile}
            excelFile={data?.ExcelFile}
            arInfo={arInfo}
            onChangeView={onChangeView}
            exportReport={exportReport}
            onDownload={onDownload}
            digitalAssets={digitalAssets}
          />
          <div className={s.box}>
            <p className={s.header} onClick={onChangePage}>
              <img src={left} alt={t('返回')} />
              <span style={{ marginLeft: 5 }}>{t('返回概览')}</span>
            </p>
            <div className={s.content}>
              <div className={s.leftRight}>
                <div className={s.leftContainer}>
                  <div className={s.sigma} style={{ width: 543, height: 322 }}>
                    <SigmaComponent
                      digitalAssets={digitalAssets}
                      Sigma={Sigma}
                      arcData={arcData}
                      width={546}
                      height={322}
                      id="sigma-detail"
                      data={data}
                      activeKey={activeKey}
                      setActiveKey={setActiveKey}
                      arInfo={arInfo}
                      message={message}
                    />
                  </div>
                </div>
                <div className={s.rightContainer}>
                  {loading && (
                    <>
                      <Skeleton
                        type="rect"
                        style={{ width: '30%', opacity: 1 }}
                      />
                      <Skeleton
                        type="rect"
                        style={{ width: '100%', opacity: 1 }}
                      />
                      <Skeleton
                        type="rect"
                        style={{ width: '20%', opacity: 1 }}
                      />
                      <Skeleton
                        type="rect"
                        style={{ width: '30%', opacity: 0.8 }}
                      />
                      <Skeleton
                        type="rect"
                        style={{ width: '100%', opacity: 0.5 }}
                      />
                      <Skeleton
                        type="rect"
                        style={{ width: '20%', opacity: 0.3 }}
                      />
                    </>
                  )}
                  {!loading && (
                    <>
                      <p className={s.name}>{item?.NodeName || '-'}</p>
                      <div className={s.chart}>
                        <div className={s.desc}>
                          <div className={s.left}>
                            <p>
                              <span>{t('产品类型')}</span>
                              <span>{item?.ProductName}</span>
                            </p>
                            <p>
                              <span>{t('容量分布')}</span>
                              <span className={s.dotBox} style={{ position: 'relative', right: '-230px' }}>
                                <span className={s.dot} style={{ backgroundColor: RiskColor.High }}></span>
                                <span>{item?.HighLoadCount || 0}</span>
                                <span className={s.dot} style={{ backgroundColor: RiskColor.Medium }}></span>
                                <span>{item?.MediumLoadCount || 0}</span>
                                <span className={s.dot} style={{ backgroundColor: RiskColor.LowUsed }}></span>
                                <span>{item?.LowUsageCount || 0}</span>
                                <span className={s.dot} style={{ backgroundColor: RiskColor.Low }}></span>
                                <span>{item?.LowLoadCount || 0}</span>
                              </span>
                            </p>
                          </div>
                          <div className={s.right}>
                            <p>
                              <span>{t('绑定资源数')}</span>
                              <span>{item?.InstanceCount}</span>
                            </p>
                          </div>
                        </div>
                        <div className={s.pie}>
                          <div className={s.left} style={{ width: '100%' }}>
                            <Honeycomb
                              width={30}
                              rowSize={14}
                              view={ReportView.WIDE}
                              data={item?.InstanceLoadInfos || []}
                              product={item?.Product}
                            />
                          </div>
                          <div className={s.right} style={{ width: '80%' }}>
                            <p className={s.grey}>
                            <span>{isCcn ? CcnConfig.REPORT_TITLE : t('机型分布')}</span>
                            </p>
                            {instanceLoadInfosFilter?.length ? <Chart view={ReportView.WIDE} data={instanceLoadInfosFilter || []} /> : (<div className={s.uninvolved}>
                              <img src={uninvolved} alt={t('icon')} />
                              <p>{t('不涉及')}</p>
                            </div>)}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
              <div className={s.table}>
                <div className={s.desc}>
                  <p>
                    <span>{t('实例详情')}</span>
                  </p>
                </div>
                <div style={{ marginTop: 16 }}></div>
                <Table view={ReportView.WIDE} data={item?.InstanceLoadInfos || []} product={item?.Product} />
              </div>
            </div>
          </div>
        </div>
      </>
    )
  );
};

export default ArchiveReportDetail;
