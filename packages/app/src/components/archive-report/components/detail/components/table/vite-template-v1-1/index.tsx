/* eslint-disable @typescript-eslint/naming-convention */
import React from 'react';
import Table from '../../../../detail-table';
import left from '../../../../../statics/left.svg';
import logo from '../../../../../statics/logo.svg';
import s from './index.module.scss';
import { t } from '@tencent/tea-app/lib/i18n';
import { useWindowResize, calcRateStyle } from '../../../../../hooks/useWindowResize';
import { ArchiveStatus, ReportView } from '../../../../../constants';

interface ArchiveReportDetailProps {
  Sigma: any;
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  arcData?: any;
  onChangePage?: () => void;
  view?: ReportView;
  onChangeView?: () => void;
  archiveStatus?: ArchiveStatus;
  print?: boolean;
  data?: any;
  currentPage?: number;
  activeKey: string;
}

const ArchiveReportDetail: React.FC<ArchiveReportDetailProps> = (props) => {
  const {
    visible,
    onChangePage,
    // view,
    print = false,
    data,
    currentPage,
    activeKey,
  } = props;
  const { screenRef } = useWindowResize();
  const scaleStyle = print ? {
    height: 826,
    position: 'static' as const,
    transform: 'none',
  } : calcRateStyle();
  const tableData = data?.NodeLoadDetailItems?.find((item: any) => item?.NodeUuid === activeKey) || {};
  return (
    visible && (
      <>
        {(!print) && <div
          style={{
            width: '100vw',
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 10,
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}
        ></div>}
        <div className={s.container} id="archive-report" ref={print ? null : screenRef} style={scaleStyle}>
          <div className={s.box}>
            <p className={s.header} style={print ? { width: '100%' } : { }} onClick={onChangePage}>
              <img src={print ? logo : left} alt={t('icon')} />
              <span style={{ marginLeft: 5 }}>{ !print ? t('返回概览') : `${data?.Title ?? '-'}`}</span>
            </p>
            <div className={s.content} style={print ? { height: 750, overflow: 'hidden' } : {}}>
              <div className={s.table}>
                <Table print={print} data={tableData?.InstanceLoadInfos || []} currentPage={currentPage} product={tableData?.Product} />
              </div>
            </div>
          </div>
        </div>
      </>
    )
  );
};

export default ArchiveReportDetail;
