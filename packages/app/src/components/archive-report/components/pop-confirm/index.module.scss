.wrapper {
  display: inline-block;
}

.popConfirm {
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;

  &.visible {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  &::before {
    content: '';
    z-index: 2;
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    width: 12px;
    height: 12px;
    background: #fff;
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
  }
}

.content {
  position: relative;
  background: #fff;
  padding: 18px;
  width: 250px;
  height: 122px;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 0;
}

.title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 550;
}

.subtitle {
  font-size: 12px;
  color: #666;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  margin-right: 4px;
  button {
    padding: 4px 0;
    font-weight: 500;
    border: none;
    cursor: pointer;
    font-size: 12px;
    background: none;
    color: rgba(0, 0, 0, 0.9);

    &:hover {
      opacity: 0.8;
    }
  }
}

.sureButton {
  color: #1890ff;
}

.cancelButton {
  color: #666;
}
