import React, { useState, useCallback, ReactElement, cloneElement, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import styles from './index.module.scss';
import { t } from '@tea/app/i18n';

interface PopConfirmProps {
  title?: string;
  onSure?: () => void;
  onCancel?: () => void;
  sureText?: string;
  cancelText?: string;
  trigger?: 'click' | 'hover';
  children: ReactElement;
  subtitle?: string;
}

const PopConfirm: React.FC<PopConfirmProps> = ({
  title = t('未归档的报告关闭后无法再次访问'),
  onSure,
  onCancel,
  sureText = t('确定'),
  cancelText = t('取消'),
  trigger = 'click',
  children,
  subtitle = t('确定要关闭?'),
}) => {
  const [visible, setVisible] = useState(false);
  const [ready, setReady] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);

  const updatePosition = useCallback(() => {
    if (triggerRef.current && popupRef.current && visible) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const popupRect = popupRef.current.getBoundingClientRect();

      const top = triggerRect.bottom + window.scrollY + 8;
      const left = triggerRect.left + window.scrollX + (triggerRect.width - popupRect.width) / 2;

      setPosition({ top, left });
      requestAnimationFrame(() => {
        setReady(true);
      });
    }
  }, [visible]);

  useEffect(() => {
    if (!visible) {
      setReady(false);
    }
  }, [visible]);

  // 监听窗口变化
  useEffect(() => {
    if (visible) {
      const handleResize = () => {
        updatePosition();
      };

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleResize);

      // 初始化位置
      updatePosition();

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleResize);
      };
    }
  }, [visible, updatePosition]);

  const handleSure = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setVisible(false);
    onSure?.();
  }, [onSure]);

  const handleCancel = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setVisible(false);
    onCancel?.();
  }, [onCancel]);

  const handleTrigger = useCallback(() => {
    if (trigger === 'click' && !visible) {
      setVisible(true);
    }
  }, [trigger, visible]);

  const handleMouseEnter = useCallback(() => {
    if (trigger === 'hover') {
      setVisible(true);
    }
  }, [trigger]);

  const handleMouseLeave = useCallback(() => {
    if (trigger === 'hover') {
      setVisible(false);
    }
  }, [trigger]);

  const popup = visible && createPortal(
    <div
      ref={popupRef}
      className={`${styles.popConfirm} ${ready ? styles.visible : ''}`}
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 10001,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={styles.content}>
        <div className={styles.title}>{title}</div>
        <div className={styles.subtitle}>{subtitle}</div>
        <div className={styles.actions}>
          <button className={styles.sureButton} onClick={handleSure}>
            {sureText}
          </button>
          <button className={styles.cancelButton} onClick={handleCancel}>
            {cancelText}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );

  return (
    <div ref={triggerRef} className={styles.wrapper}>
      {cloneElement(children, {
        onClick: (e: React.MouseEvent) => {
          e.stopPropagation();
          handleTrigger();
        },
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
      })}
      {popup}
    </div>
  );
};

export default PopConfirm;
