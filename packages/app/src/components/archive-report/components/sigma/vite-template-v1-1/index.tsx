/* eslint-disable import/no-duplicates */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable max-len */
import React, { useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { map, mapKeys } from 'lodash';
import type Sigma from '@tencent/sigma-editor';
import type { ShapeVnode } from '@tencent/sigma-editor';
import { ShapeTypeEnum } from '../../../constants/sigmaEditor';
import { RiskCircle } from '../../../../risk-circle';
import { getShapesByKeys, POSITION_MAP, getForeignObjectId, addSvgToDetail, getValidString } from '../../../tools/sigmaEditor';
import { t } from '@tea/app/i18n';
import { calcRateStyle } from '../../../hooks/useWindowResize';

interface IProps {
  id?: any;
  arcData?: any;
  Sigma: typeof Sigma;
  width: number;
  height: number;
  data?: any;
  interact?: boolean;
  activeKey?: string;
  setActiveKey?: (key: string) => void;
  print?: boolean;
  arInfo?: any;
  message?: any;
  digitalAssets?: boolean;
  onInit?: (sigmaDom: any) => void;
}

/**
 * SigmaEditor组件
 * @param {any} prop1 - xx属性
 * @param {function} prop2 - xx方法
 * @returns React.ReactElement
 */
const SigmaEditor: React.FC<IProps> = (props) => {
  const { arcData, id, Sigma, width = 436, height = 233, data, interact = true, activeKey, setActiveKey, print = false, arInfo, digitalAssets, onInit = () => undefined } = props;
  const sigmaContainer = useRef<any>(undefined);
  const timer = useRef<any>(null);
  const sigmaRef = useRef<any>(undefined);
  const nodeRef = useRef<any>(activeKey);
  const scale: any = calcRateStyle(true);
  const fixSafariSvgText = () => {
    if (sigmaContainer.current) {
      const svg = sigmaContainer.current.querySelector('svg');
      const foreignObjectArray = svg?.querySelectorAll('foreignObject');
      foreignObjectArray?.forEach((foreignObject: any) => {
        // eslint-disable-next-line no-param-reassign
        foreignObject.querySelector('div').style.position = '';
        // eslint-disable-next-line no-param-reassign
        foreignObject.querySelector('div').style.top = '';
      });
    }
  };

  // 创建内容条
  const createBar = (
    shapeKeys: string[],
    param: AppPluginAPI.BarProps,
    position: AppPluginAPI.BarPosition = 'top',
    sigma: any,
  ): boolean => {
    try {
      if (!(shapeKeys instanceof Array) || !sigma) {
        console.error(t('节点id或编辑器对象不存在, 无法执行创建'));
        return false;
      }
      const shapes: ShapeVnode[] = getShapesByKeys(sigma, shapeKeys);
      shapes?.forEach((shape) => {
        const root = document.createElement('div');
        root.setAttribute('id', getForeignObjectId(shape.key));
        root.setAttribute('class', 'foreign-object-div');
        root.style.setProperty('width', 'fit-content');
        root.style.setProperty('height', 'fit-content');
        const {
          x, y, width, height, children,
        } = param ?? {};
        const mode = sigma.getMode();
        const defaultOffset = mode === 'SIGMA_GRAPH_MODE_3D'
          ? POSITION_MAP[position].d3Offset
          : POSITION_MAP[position].d2Offset;
        sigma.bindStickyUnit(shape.key, root, {
          element: 'foreignObject',
          width: width ?? 100,
          height: height ?? 30,
          offset: {
            x: x ?? defaultOffset.x,
            y: y ?? defaultOffset.y,
          },
          class: POSITION_MAP[position].class,
        });
        // 暂存创建的root跟unit
        // eslint-disable-next-line react/no-deprecated
        ReactDOM.render(<>{children}</>, root);
      });
      return true;
    } catch (e: any) {
      console.error(e?.message);
      return false;
    }
  };


  const transformNodeByConfigInfo = (supportedNodes, arcNodes, arInfo) => {
    const parent = document.getElementById('sigma-detail');
    if (supportedNodes && arcNodes) {
      const allNodes = arInfo?.getArchNodes() || arcNodes;
      // eslint-disable-next-line no-restricted-syntax
      for (const key in allNodes) {
        if (allNodes[key].type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
          if (!supportedNodes.includes(allNodes[key]?.key)) {
            // eslint-disable-next-line
            const element = parent?.querySelector(`[id="${key}"]`) as HTMLElement;
            if (element) {
              element.style.opacity = '0.4';
            }
          }
        }
      }
    }
  };

  useEffect(() => {
    if (sigmaContainer.current) {
      sigmaRef.current = new Sigma(sigmaContainer.current, {
        callbacks: {
          onShapeClick: (e: any, shape: any) => {
            const item = data?.NodeLoadDetailItems?.find((item: any) => item?.NodeUuid === shape?.key);
            if (item) {
              interact && sigmaRef.current?.zoomNodeToMid?.(shape?.key, 'sigma-detail');
              setActiveKey?.(shape?.key);
              nodeRef.current = shape?.key;
            } else {
              let container = document.getElementById('capacity-report-message');
              const box = document.getElementById('archive-report');
              if (!container) {
                container = document.createElement('div');
                container.setAttribute('id', 'capacity-report-message');
                box.appendChild(container);
                container.style.cssText = 'position: absolute;left: 50%;top: 7%;transform: translateX(-50%);background: #FFE8D5;padding: 6px 10px; font-size: 11px;border-radius: 4px;color:#FF7200;';
                container.innerText = t('不支持该图元');
              } else {
                container.style.display = 'block';
                clearTimeout(timer.current);
              }
              timer.current = setTimeout(() => {
                container.style.display = 'none';
              }, 1500);
              if (activeKey && interact) {
                // sigmaRef.current?.zoomNodeToMid?.(nodeRef.current, 'sigma-detail');
                const shapes = (sigmaRef.current as any)?.core?.data?.shapes;
                const shape = shapes?.[nodeRef.current];
                shape && sigmaRef.current?.setShapeChecked?.([shape], true);
              }
              sigmaRef.current?.toggleShapeChecked?.([shape]);
            }
          },
          onCheckedChange: (checkedShapes: any) => {
            if (!checkedShapes.length) {
              //
            }
          },
          onDocClick: () => {
            //
          },
        },
      });
      // arcData?.Detail - 客户端数据
      // data?.ArchDetail - 服务端数据
      if (arcData?.Detail || data?.ArchDetail) {
        let products = [];
        const { ProductDiagramList: productDiagramList = '[]', ArchDetail: archDetail = '' } = data || {};
        try {
          products = JSON.parse(productDiagramList);
        } catch (error) {
          products = [];
        }
        const mapShape = map(products, (item) => mapKeys(
          item,
          (value, key) => key.substring(0, 1).toLowerCase() + key.substring(1),
        ));
        sigmaRef.current.loadShapes(mapShape);
        const detail = getValidString(arcData?.Detail || archDetail);
        const initData = addSvgToDetail(detail);
        sigmaRef?.current?.initWithTPLGraphData(initData);
        sigmaRef?.current?.zoomGraphFit?.();
        sigmaRef?.current?.setUneditable?.();
        fixSafariSvgText();
        const shapes = (sigmaRef.current as any)?.core?.data?.shapes;
        data?.NodeLoadDetailItems?.forEach((item: any) => {
          createBar([item?.NodeUuid], {
            children: <RiskCircle
              highLoadCount={item?.HighLoadCount ? parseInt(item?.HighLoadCount, 10) : 0}
              mediumLoadCount={item?.MediumLoadCount ? parseInt(item?.MediumLoadCount, 10) : 0}
              lowLoadCount={item?.LowLoadCount ? parseInt(item?.LowLoadCount, 10) : 0}
              lowUsedCount={item?.LowUsageCount ? parseInt(item?.LowUsageCount, 10) : 0}
              unSupportCount={item?.UnSupportCount ? parseInt(item?.UnSupportCount, 10) : 0}
            />,
          }, 'top', sigmaRef?.current);
        });
        if (interact && activeKey) {
          sigmaRef.current?.zoomNodeToMid?.(activeKey, 'sigma-detail');
          const shape = shapes?.[activeKey];
          shape && sigmaRef.current?.setShapeChecked?.([shape], true);
        }
        if (print) {
          // hack:放到  生成pdf那里 去缩放， sigma缩放 - 居中 有问题
          // document.getElementById('v1-1').style.cssText = `transform: scale(${window.innerWidth / 590}); margin: 0 auto; padding: 0; width: 580px; overflow: auto; transform-origin: center top;`;
        }
        transformNodeByConfigInfo(data?.NodeLoadDetailItems?.map((item) => item?.NodeUuid), shapes, arInfo);
      }
      onInit?.(sigmaRef.current);
    }
    return () => {
      clearTimeout(timer.current);
      timer.current = null;
    };
  }, []);
  return (
    <div id={id} style={(!print && !digitalAssets) ? { transform: `scale(${1 / scale.width},${1 / scale.height})`, transformOrigin: 'left top' } : {}}>
      <div ref={sigmaContainer} style={(!print && !digitalAssets) ? { width: width * scale.width, height: height * scale.height } : { width, height }} />
    </div>
  );
};

export default SigmaEditor;
