.wideContainer {
  .legendContainer {
    width: 215px !important;
    margin-top: 0 !important;
    height: 225px !important;
    padding: 0 3px 0 20px !important;
    .legendItem {
      padding-left: 20px !important;
      padding: 0 !important;
      .colorBlock {
        width: 15px !important;
        height: 15px !important;
        margin-right: 3px !important;
        margin-left: 3px !important;
        display: inline-block;
      }

      .name {
        font-size: 12px !important;
        font-weight: 550 !important;
        width: 145px !important;
      }
    }
  }
  .wideLoadLevel {
    width: 445px !important;
    height: 92px !important;
    margin-top: 20px !important;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 创建2列,每列宽度相等 */
    grid-template-rows: repeat(2, 1fr);    /* 创建2行,每行高度相等 */
    gap: 10px 15px;
    padding-left: 15px;
    &>div {
      margin-top: 0 !important;
    }
  }
  .chartContent {
    .text {
      font-size: 15px !important;
      font-weight: 550 !important;
    }
    .number {
      font-size: 37px !important;
      font-weight: 500 !important;
    }
  }
}
.chartContainer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  .loadLevel {
    margin-top: 12px;
    right: 0;
    top: 0;
    height: 100%;
    z-index: 0;
    width: 37%;
  }
  .legendContainer {
    width: 110px;
    // height: 215px;
    overflow-y: auto;
    margin-top: 8px;
    position: absolute;
    padding: 0 3px 4px 2px;
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 2px;

      &:hover {
        background: #999;
      }
    }

    .legendItem {
      display: flex;
      align-items: center;
      transition: all 0.2s;
      &:hover {
        opacity: 0.8;
      }

      &.active {
        &>div {
          background: var(---, #FFF);
          /* tea-card-shadow */
          box-shadow: 0px 2px 4px 0px rgba(54, 58, 80, 0.32);
        }

      }

      .colorBlock {
        width: 10px;
        height: 10px;
        margin-right: 3px;
        margin-left: 3px;
        display: inline-block;
      }

      .name {
        flex: 1;
        font-size: 11px;
        color: #333;
        // margin-right: 8px;
        border: none;
        outline: none;
        font-weight: 400 !important;
        width: 85px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        caret-color: transparent;
        user-select: none;
        font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
                "Helvetica Neue", "Microsoft YaHei", "微软雅黑", -apple-system,
                BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Source Han Sans SC",
                "Source Han Sans CN", "思源黑体 CN", sans-serif;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .chartContent {
    flex: 1;
    position: relative;
    z-index: 2;
    margin-top: 12px;
    .totalResources {
      font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
                "Helvetica Neue", "Microsoft YaHei", "微软雅黑", -apple-system,
                BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Source Han Sans SC",
                "Source Han Sans CN", "思源黑体 CN", sans-serif;
      position: absolute;
      left: 25%;
      top: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      pointer-events: none;

      .number {
        font-family: "PingFang SC";
        font-size: 26px;
        font-weight: 500;
        color: #000;
      }

      .text {
        font-family: "PingFang SC";
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        margin-top: 4px;
        font-weight: bolder;
        transform: scale(0.8);
      }
    }
  }
}