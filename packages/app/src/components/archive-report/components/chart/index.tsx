/* eslint-disable max-len */
import React, { useState, useRef, useMemo } from 'react';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import { <PERSON><PERSON><PERSON> } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
} from 'echarts/components';
import { CanvasRenderer, SVGRenderer } from 'echarts/renderers';
import { t } from '@tencent/tea-app/lib/i18n';
import {
  LabelLayout,
} from 'echarts/features';
import warningIcon from '../../statics/warning.svg';
import checkedIcon from '../../statics/checked.svg';
import checkedDotIcon from '../../statics/checked-dot.svg';
import LoadLevel from '../load-level';
import { ReportView } from '../../constants';
import s from './index.module.scss';

echarts.use([
  TitleComponent,
  TooltipComponent,
  <PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ayout,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
]);

interface ChartProps {
  onLegendClick?: (name: string) => void;
  view?: ReportView;
  print?: boolean;
  data?: any;
}

const Chart: React.FC<ChartProps> = ({ onLegendClick, view = ReportView.A4, data = [] }) => {
  const colors = ['#001780', '#0058DF', '#2A86FF', '#7FB7FF', '#D5E7FF', '#E7EAEF', '#B7C1CE', '#0042BF', '#525E76'];
  data?.NodeLoadDetailItems?.forEach(e => {
    e.HighLoadCount = parseInt(e.HighLoadCount, 10) || 0;
    e.MediumLoadCount = parseInt(e.MediumLoadCount, 10) || 0;
    e.LowLoadCount = parseInt(e.LowLoadCount, 10) || 0;
    e.LowUsageCount = parseInt(e.LowUsageCount, 10) || 0;
  });
  const [selectedLegend, setSelectedLegend] = useState<string>('');
  const [show, setShow] = useState<boolean>(true);
  const isWide = view === ReportView.WIDE;
  const chartRef = useRef<ReactEchartsCore>(null);
  const legendRef = useRef<HTMLDivElement>(null);
  const resortedData = React.useMemo(() => {
    if (!data?.NodeLoadDetailItems?.length) return [];

    // 1. 先按 InstanceCount 降序排序
    const sortedItems = [...data.NodeLoadDetailItems].sort((a, b) => b.InstanceCount - a.InstanceCount);

    // fix 服务端打印数据中 InstanceCount等为字符串的情况
    sortedItems.forEach((item) => {
      item.InstanceCount = parseInt(item.InstanceCount ?? 0, 10);
      item.HighLoadCount = parseInt(item.HighLoadCount ?? 0, 10);
      item.MediumLoadCount = parseInt(item.MediumLoadCount ?? 0, 10);
      item.LowLoadCount = parseInt(item.LowLoadCount ?? 0, 10);
      item.LowUsageCount = parseInt(item.LowUsageCount ?? 0, 10);
    });

    // 2. 按 ProductName 去重和合并
    const productMap = new Map();
    sortedItems.forEach((item) => {
      if (productMap.has(item.ProductName)) {
        const existing = productMap.get(item.ProductName);
        existing.InstanceCount += item.InstanceCount;
        existing.HighLoadCount += item.HighLoadCount;
        existing.MediumLoadCount += item.MediumLoadCount;
        existing.LowLoadCount += item.LowLoadCount;
        existing.LowUsageCount += item.LowUsageCount;
        existing.InstanceLoadInfos = [...existing.InstanceLoadInfos, ...item.InstanceLoadInfos];
      } else {
        productMap.set(item.ProductName, { ...item });
      }
    });

    // 3. 转换回数组并处理超过9条的情况
    let result = Array.from(productMap.values());

    if (result.length > 9) {
      const first8 = result.slice(0, 8);
      const others = result.slice(8).reduce(
        (acc, curr) => {
          acc.InstanceCount += curr.InstanceCount;
          acc.HighLoadCount += curr.HighLoadCount;
          acc.MediumLoadCount += curr.MediumLoadCount;
          acc.LowLoadCount += curr.LowLoadCount;
          acc.LowUsageCount += curr.LowUsageCount;
          acc.InstanceLoadInfos = [...acc.InstanceLoadInfos, ...curr.InstanceLoadInfos];
          return acc;
        },
        {
          ProductName: t('其他'),
          NodeName: t('其他'),
          NodeUuid: 'others',
          Product: 'others',
          InstanceCount: 0,
          HighLoadCount: 0,
          MediumLoadCount: 0,
          LowLoadCount: 0,
          LowUsageCount: 0,
          InstanceLoadInfos: [],
        }
      );
      result = [...first8, others];
    }

    return result;
  }, [data]);

  const chartData = useMemo(() => {
    if (!resortedData?.length) return [];
    return resortedData.map((item, index) => ({
      ...item,
      name: item?.ProductName || item?.Product || item?.NodeName,
      value: item?.InstanceCount || 0,
      itemStyle: { color: colors[index] },
    }));
  }, [resortedData]);

  const left = isWide ? '70%' : '40%';
  const option = {
    animation: !print,
    tooltip: {
      trigger: 'item',
      formatter: '{b} <br/>{c} ({d}%)',
      textStyle: {
        color: 'rgba(0, 0, 0, 0.90)',
        fontFamily: 'PingFang SC',
        fontSize: 12,
        fontWeight: 400,
      },
      backgroundColor: '#fff',
      borderWidth: 0,
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowBlur: 10,
      shadowOffsetX: 0,
      shadowOffsetY: 2,
    },
    series: [
      {
        type: 'pie',
        radius: isWide ? ['70%', '90%'] :  ['50%', '70%'],
        center: [left, '50%'],
        avoidLabelOverlap: true,
        label: {
          show: true,
          position: 'outer',
          formatter: '{b}: {c}',
          fontSize: 10,
          fontFamily: 'PingFang SC',
          color: '#333',
          z: 2,
          overflow: 'break',
          width: 80,
          labelLayout: {
            moveOverlap: 'shiftY',
          },
          normal: {
            show: false,
          },
          emphasis: {
            show: false,
            formatter: '{b}: {c}',
            backgroundColor: '#fff',
            color: '#CFD5DE',
            padding: [6, 10],
            fontSize: 10,
            borderRadius: 4,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 8,
            shadowOffsetX: 0,
            shadowOffsetY: 2,
            z: 2,
            rich: {
              b: {
                fontSize: 10,
                fontWeight: 500,
                padding: [0, 0, 4, 0],
              },
              c: {
                fontSize: 10,
                fontWeight: 'bold',
              },
            },
          },
        },
        labelLine: {
          show: true,
          normal: {
            show: false,
            lineStyle: {
              width: 1,
              type: 'solid',
              color: '#CFD5DE',
            },
          },
          emphasis: {
            show: true,
            length: 5,
            length2: 5,
            minTurnAngle: 90,
            smooth: 0.2,
            lineStyle: {
              width: 1,
              type: 'solid',
              color: '#CFD5DE',
            },
          },
        },
        emphasis: {
          scale: true,
          trigger: 'item',
          scaleSize: 5,
          blurScope: 'none',
          itemStyle: {
            shadowBlur: 0,
            opacity: 1,
          },
          focus: 'series',
        },
        data: chartData,
        zlevel: 1,
      },
    ],
  };
  const events = useMemo(() => ({
    click: (params: any) => {
      if (params.componentType === 'series') {
        const { name } = params;
        setSelectedLegend(params.name);
        scrollToLegend(params.name);
        if (chartRef.current) {
          const chart = chartRef.current.getEchartsInstance();
          chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
          });
          chart.dispatchAction({
            type: 'highlight',
            name,
          });
          chart.dispatchAction({
            type: 'showTip',
            name,
          });
        }
      }
    },
  }), []);
  const handleLegendClick = (name: string) => {
    setSelectedLegend(name);
    onLegendClick?.(name);
    if (chartRef.current) {
      const chart = chartRef.current.getEchartsInstance();
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
      });
      chart.dispatchAction({
        type: 'hideTip',
      });

      chart.dispatchAction({
        type: 'highlight',
        name,
      });
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        name,
        position: isWide ? 'top' : 'right',
      });
    }
  };

  const scrollToLegend = (name: string) => {
    const legendEl = legendRef.current?.querySelector(`[data-name="${name}"]`);
    if (legendEl && legendRef.current) {
      legendEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      legendEl.querySelector('input')?.focus?.();
    }
  };

  const onBlur = () => {
    setSelectedLegend('');
    setShow(false);
    setTimeout(() => {
      setShow(true);
    }, 180);
    if (chartRef.current) {
      const chart = chartRef.current.getEchartsInstance();
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
      });
    }
  };

  const severelyHighLoads = selectedLegend ? resortedData?.find((item: any) => item?.ProductName === selectedLegend)?.HighLoadCount : resortedData?.reduce((pre, cur) => pre + parseInt(cur?.HighLoadCount, 10), 0);
  const highLoads = selectedLegend ? resortedData?.find((item: any) => item?.ProductName === selectedLegend)?.MediumLoadCount : resortedData?.reduce((pre, cur) => pre + parseInt(cur?.MediumLoadCount, 10), 0);
  const normalLoad = selectedLegend ? resortedData?.find((item: any) => item?.ProductName === selectedLegend)?.LowLoadCount : resortedData?.reduce((pre, cur) => pre + parseInt(cur?.LowLoadCount, 10), 0);
  const notFullyUsed = selectedLegend ? resortedData?.find((item: any) => item?.ProductName === selectedLegend)?.LowUsageCount : resortedData?.reduce((pre, cur) => pre + parseInt(cur?.LowUsageCount, 10), 0);
  return (
    <div className={`${s.chartContainer} ${isWide ? s.wideContainer : ''}`} style={isWide ? { flexDirection: 'column' } : {}}>
      <div style={{ width: '100%', height: isWide ? '212px' : '100%', display: 'flex' }}>
        <div className={s.legendContainer} ref={legendRef} style={{ zIndex: 3 }}>
          {chartData.map((item, index) => (
            <div
              key={`${item.name}-${index}`}
              className={`${s.legendItem} ${selectedLegend === item.name ? s.active : ''}`}
              data-name={item.name}
            >
              <div style={{ display: 'flex', alignItems: 'center', padding: '3px' }}>
                <span
                  className={s.colorBlock}
                  style={{ backgroundColor: item.itemStyle?.color }}
                />
                <input readOnly title={item.name} onFocus={() => handleLegendClick(item.name)} onBlur={onBlur} className={s.name} value={item.name} />
              </div>
            </div>
          ))}
        </div>
        <div className={s.chartContent}>
          <div className={s.totalResources} style={{ left }}>
            <div className={s.text}>{t('本次共扫描资源')}</div>
            <div className={s.number}>{data?.TotalInstanceCount ?? 0}</div>
          </div>
          <ReactEchartsCore
            ref={chartRef}
            echarts={echarts}
            option={option}
            notMerge={true}
            lazyUpdate={true}
            onEvents={events}
            // opts={print ? { renderer: 'svg' } : {}}
            style={{ height: '100%', width: '100%' }}
          />
        </div>
        {view === ReportView.A4 && (
          <div className={s.loadLevel} style={!isWide ? { position: 'absolute' } : {}}>
            <LoadLevel numberVisible={show} icon={warningIcon} textColor='#FFF' text={t('严重高负载')} number={severelyHighLoads} color='#E54545' />
            <LoadLevel numberVisible={show} icon={warningIcon} textColor='#FFF' text={t('高负载')} number={highLoads} color='#FF7200' />
            <LoadLevel numberVisible={show} icon={checkedIcon} textColor='#FFF' text={t('正常负载')} number={normalLoad} color='#0ABF5B' />
            <LoadLevel numberVisible={show} icon={checkedDotIcon} textColor='#0ABF5B' text={t('未充分使用')} number={notFullyUsed} color='#D6F4E4' />
        </div>
        )}
      </div>
      {view !== ReportView.A4 && (
        <div className={s.wide} style={{ height: 115 }}>
          <div className={`${s.loadLevel} ${s.wideLoadLevel}`}>
            <LoadLevel numberVisible={show} icon={warningIcon} textColor='#FFF' text={t('严重高负载')} number={severelyHighLoads} color='#E54545' />
            <LoadLevel numberVisible={show} icon={warningIcon} textColor='#FFF' text={t('高负载')} number={highLoads} color='#FF7200' />
            <LoadLevel numberVisible={show} icon={checkedIcon} textColor='#FFF' text={t('正常负载')} number={normalLoad} color='#0ABF5B' />
            <LoadLevel numberVisible={show} icon={checkedDotIcon} textColor='#0ABF5B' text={t('未充分使用')} number={notFullyUsed} color='#D6F4E4' />
          </div>
        </div>
      )}
    </div>
  );
};

export default Chart;
