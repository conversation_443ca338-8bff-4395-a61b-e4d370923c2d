.legendContainer {
  // width: 110px;
  // height: 215px;
  overflow-y: auto;
  margin-top: 8px;
  position: absolute;
  padding: 0 3px 0 2px;
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 2px;

    &:hover {
      background: #999;
    }
  }

  .legendItem {
    display: flex;
    align-items: center;
    padding: 1px 0;
    transition: all 0.2s;
    &:hover {
      opacity: 0.8;
    }

    &.active {
      &>div {
        background: var(---, #FFF);
        /* tea-card-shadow */
        box-shadow: 0px 2px 4px 0px rgba(54, 58, 80, 0.32);
      }

    }

    .colorBlock {
      width: 10px;
      height: 10px;
      margin-right: 3px;
      margin-left: 3px;
      display: inline-block;
    }

    .name {
      flex: 1;
      font-size: 11px;
      color: #333;
      // margin-right: 8px;
      border: none;
      outline: none;
      width: 60px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      caret-color: transparent;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
                "Helvetica Neue", "Microsoft YaHei", "微软雅黑", -apple-system,
                BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Source Han Sans SC",
                "Source Han Sans CN", "思源黑体 CN", sans-serif;
      &:hover {
        cursor: pointer;
      }
    }
  }
}