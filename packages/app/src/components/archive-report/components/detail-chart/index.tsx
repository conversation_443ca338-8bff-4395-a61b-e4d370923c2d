/* eslint-disable max-len */
import React, { useRef, useState, useMemo } from 'react';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import {
  TooltipComponent,
  LegendComponent,
} from 'echarts/components';
import { PieChart } from 'echarts/charts';
import { ReportView } from '../../constants';
import {
  LabelLayout,
} from 'echarts/features';
import { SVGRenderer } from 'echarts/renderers';
import { t } from '@tencent/tea-app/lib/i18n';
import s from './index.module.scss';
// 注册必需的组件
echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  SVGRenderer,
  LabelLayout,
]);

interface DetailChartProps {
  view?: ReportView;
  onLegendClick?: (name: string) => void;
  data?: Array<any>;
}

const DetailChart: React.FC<DetailChartProps> = ({ view = ReportView.A4, onLegendClick, data = [] }) => {
  const isWide = view === ReportView.WIDE;
  const legendRef = useRef<HTMLDivElement>(null);
  const [selectedLegend, setSelectedLegend] = useState<string>('');
  const chartRef = useRef<ReactEchartsCore>(null);

  const chartData = useMemo(() => {
    // 按 InstanceType 分组并计数
    const groupedData = data.reduce((acc, curr) => {
      const type = curr.InstanceType || t('其他');
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 转换为数组并排序
    const sortedData = Object.entries(groupedData)
      .map(([name, value]) => ({
        name,
        value: value as number,
      }))
      .sort((a, b) => (b.value as number) - (a.value as number));

    // 处理前三个和其他
    const topThree = sortedData.slice(0, 3);
    const others = sortedData.slice(3);

    // 如果有其他数据，合并到"其他"类别
    if (others.length > 0) {
      const othersSum = others.reduce((sum, item) => sum + item.value, 0);
      // 如果原来就有"其他"类别，加到其总和中
      const existingOthers = topThree.find(item => item.name === '其他');
      if (existingOthers) {
        existingOthers.value += othersSum;
      } else {
        topThree.push({
          name: t('其他'),
          value: othersSum,
        });
      }
    }

    // 添加颜色配置
    const colors = ['#0052D9', '#2B85FF', '#454B66', '#CFD5DE'];
    return topThree.map((item, index) => ({
      ...item,
      itemStyle: { color: colors[index] },
    }));
  }, [data]);

  const option = {
    animation: !print,
    tooltip: {
      trigger: 'item',
      formatter: '{b} <br/>{c} ({d}%)',
      textStyle: {
        color: 'rgba(0, 0, 0, 0.90)',
        fontFamily: 'PingFang SC',
        fontSize: 12,
        fontWeight: 400,
      },
      backgroundColor: '#fff',
      borderWidth: 0,
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowBlur: 10,
      shadowOffsetX: 0,
      shadowOffsetY: 2,
    },
    // legend: {
    //   type: 'scroll',
    //   orient: 'vertical',
    //   left: 0,
    //   top: 'center',
    //   bottom: 'center',
    //   itemWidth: 10,
    //   itemHeight: 10,
    //   icon: 'rect',
    //   pageIconSize: 7,
    //   pageIconColor: '#666',
    //   pageIconInactiveColor: '#aaa',
    //   pageTextStyle: {
    //     fontSize: isWide ? 12 : 11,
    //     color: '#666',
    //   },
    //   textStyle: {
    //     fontSize: isWide ? 12 : 11,
    //     color: 'rgba(0,0,0,0.9)',
    //   },
    //   formatter: (name: string) => `${name}`,
    // },
    series: [
      {
        type: 'pie',
        radius: ['55%', '75%'],
        center: ['67%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          fontSize: isWide ? 12 : 10,
          color: 'rgba(0,0,0,0.9)',
          formatter: '{d}%',
          overflow: 'break',
          lineHeight: 14,
          padding: [1, 3],
          position: 'outside',
          distanceToLabelLine: 5,
        },
        emphasis: {
          label: {
            show: true,
            backgroundColor: '#fff',
            color: '#000',
            padding: [1, 3],
            borderRadius: 4,
          },
        },
        blur: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
        },
        labelLine: {
          show: true,
          length: 6,
          length2: 6,
          maxSurfaceAngle: 360,
          smooth: true,
        },
        data: chartData,
      },
    ],
  };

  const handleLegendClick = (name: string) => {
    setSelectedLegend(name);
    onLegendClick?.(name);
    if (chartRef.current) {
      const chart = chartRef.current.getEchartsInstance();
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
      });
      chart.dispatchAction({
        type: 'hideTip',
      });

      chart.dispatchAction({
        type: 'highlight',
        name,
      });
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        name,
        position: 'top',
      });
    }
  };
  const scrollToLegend = (name: string) => {
    const legendEl = legendRef.current?.querySelector(`[data-name="${name}"]`);
    if (legendEl && legendRef.current) {
      legendEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      legendEl.querySelector('input')?.focus?.();
    }
  };
  const onBlur = () => {
    setSelectedLegend('');
    if (chartRef.current) {
      const chart = chartRef.current.getEchartsInstance();
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
      });
    }
  };
  const events = useMemo(() => ({
    click: (params: any) => {
      if (params.componentType === 'series') {
        const { name } = params;
        setSelectedLegend(params.name);
        scrollToLegend(params.name);
        if (chartRef.current) {
          const chart = chartRef.current.getEchartsInstance();
          chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
          });
          chart.dispatchAction({
            type: 'highlight',
            name,
          });
          chart.dispatchAction({
            type: 'showTip',
            name,
          });
        }
      }
    },
  }), []);
  return (
    <div style={{ position: 'relative' }}>
      <div className={s.legendContainer} ref={legendRef} style={{ zIndex: 3 }}>
        {chartData.map(item => (
          <div
            key={item.name}
            className={`${s.legendItem} ${selectedLegend === item.name ? s.active : ''}`}
            data-name={item.name}
          >
            <div style={{ display: 'flex', alignItems: 'center', padding: '3px' }}>
              <span
                className={s.colorBlock}
                style={{ backgroundColor: item.itemStyle?.color }}
              />
              <input readOnly style={isWide ? { width: 80 } : {}} title={item.name} onFocus={() => handleLegendClick(item.name)} onBlur={onBlur} className={s.name} value={item.name} />
            </div>
          </div>
        ))}
      </div>
      <ReactEchartsCore
        ref={chartRef}
        onEvents={events}
        echarts={echarts}
        option={option}
        style={{ height: isWide ? '128px' : '100px' }}
        opts={{ renderer: 'svg' }}
      />
    </div>
  );
};

export default DetailChart;
