import React, { useCallback } from 'react';
import classNames from 'classnames';
import close from '../../statics/close.svg';
import archive from '../../statics/archive.svg';
import archived from '../../statics/archived.svg';
import rotate from '../../statics/rotate.svg';
import loading from '../../statics/loading.svg';
import download from '../../statics/download.svg';
import pdf from '../../statics/pdf.svg';
import excel from '../../statics/excel.svg';
import arrowDown from '../../statics/arrow-down.svg';
import PopConfirm from '../pop-confirm';
import s from './index.module.scss';
import { t } from '@tencent/tea-app/lib/i18n';
import { ArchiveStatus, ReportType } from '../../constants';

interface ArchiveReportOverviewProps {
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  arcData?: any;
  onChangeView?: () => void;
  archiveStatus?: ArchiveStatus;
  exportReport?: (success: () => void, error: () => void) => void;
  url?: string;
  reportFile?: string; // 运营端pdf文件地址
  excelFile?: string; // 运营端excel文件地址
  onDownload?: (type: ReportType, downloadHash: string) => void;
  digitalAssets?: boolean;
}

const Header: React.FC<ArchiveReportOverviewProps> = (props) => {
  const {
    onClose = () => undefined,
    arInfo,
    onChangeView,
    archiveStatus,
    exportReport,
    url,
    reportFile,
    excelFile,
    digitalAssets,
    onDownload = () => undefined,
  } = props;

  const isISA = arInfo?.env === 'ISA';

  const onDownloadReport = useCallback(
    (type: ReportType) => {
      // 运营端 && 未归档中
      if (isISA && archiveStatus !== ArchiveStatus.归档中) {
        onDownload?.(type, `${new Date().getTime()}`);
        if (!reportFile || !excelFile) {
          exportReport(
            () => {
              //
            },
            () => {
              //
            }
          );
        }
      }
    },
    [exportReport, reportFile, excelFile, archiveStatus, isISA]
  );

  return (
    <div className={s.box}>
      <div className={s.header}>
        <div className={s.wrapper}>
          {!isISA && !digitalAssets && (
            <div className={s.left}>
              {(archiveStatus === ArchiveStatus.待归档
                || archiveStatus === ArchiveStatus.归档失败
                || archiveStatus === ArchiveStatus.已导出) && (
                <>
                  <div
                    className={`${s.title} ${s.hover}`}
                    onClick={() => {
                      exportReport(
                        () => {
                          //
                        },
                        () => {
                          //
                        }
                      );
                    }}
                  >
                    <img src={archive} alt={t('归档报告')} />
                    <span>{t('归档报告')}</span>
                  </div>
                  <p style={{ paddingRight: 15 }}>
                    {t('归档至"数字资产"，以进')}
                    <strong>{t('行查看、共享、下载')}</strong>
                  </p>
                </>
              )}
              {archiveStatus === ArchiveStatus.归档中 && (
                <>
                  <div className={`${s.title}`}>
                    <img
                      src={loading}
                      className={s.loading}
                      alt={t('归档报告')}
                    />
                    <span>{t('归档中...')}</span>
                  </div>
                </>
              )}
              {archiveStatus === ArchiveStatus.归档完成 && (
                <>
                  <div className={`${s.title} ${!isISA ? s.hover : ''}`}>
                    <img src={archived} alt={t('归档报告')} />
                    {!isISA ? (
                      <span
                        onClick={() => {
                          window.open(`${location.origin}${url}`, '_blank');
                        }}
                        rel="noreferrer"
                      >
                        {t('已归档')}
                      </span>
                    ) : (
                      <span>{t('已归档')}</span>
                    )}
                  </div>
                </>
              )}
            </div>
          )}
          <div
            className={s.rotate}
            onClick={onChangeView}
            style={{ marginLeft: (isISA || digitalAssets) ? 0 : 15 }}
          >
            <img src={rotate} alt={t('旋转')} />
          </div>
          {isISA && !digitalAssets && (
            <div className={s.download}>
              <div className={s['download-report']}>
                <img src={download} alt={t('下载报告')} />
                <span>{t('下载报告')}</span>
                <img src={arrowDown} alt={t('下载报告')} />
              </div>
              <div className={s['download-options']}>
                {archiveStatus === ArchiveStatus.归档中 && (
                  <div className={s['download-mask']}>
                    <img
                      src={loading}
                      className={s.loading}
                      alt={t('归档报告')}
                    />
                    <span>{t('生成中...')}</span>
                  </div>
                )}
                <div
                  className={s['download-option']}
                  onClick={() => onDownloadReport(ReportType.PDF)}
                >
                  <img src={pdf} alt={t('下载报告')} />
                  <span>{t('PDF版本')}</span>
                </div>
                <div
                  className={s['download-option']}
                  onClick={() => onDownloadReport(ReportType.EXCEL)}
                >
                  <img src={excel} alt={t('下载报告')} />
                  <span>{t('Excel版本')}</span>
                </div>
              </div>
            </div>
          )}
        </div>
        {(() => {
          if (
            (
              archiveStatus === ArchiveStatus.待归档
              || archiveStatus === ArchiveStatus.归档中
              || archiveStatus === ArchiveStatus.已导出
            ) && !digitalAssets
          ) {
            if (!isISA) {
              return (
                <PopConfirm
                  title={
                    archiveStatus === ArchiveStatus.待归档 || archiveStatus === ArchiveStatus.已导出
                      ? t('未归档的报告关闭后无法再次访问')
                      : t('正在归档中')
                  }
                  onSure={() => onClose(true)}
                  subtitle={
                    archiveStatus === ArchiveStatus.待归档 || archiveStatus === ArchiveStatus.已导出
                      ? t('确定要关闭?')
                      : t('请稍后前往数字资产查看归档结果')
                  }
                >
                  <img src={close} alt={t('关闭')} className={s.closeIcon} />
                </PopConfirm>
              );
            }
            return (
              <img
                src={close}
                alt={t('关闭')}
                onClick={() => onClose(true)}
                className={s.closeIcon}
              />
            );
          }
          return (
            <img
              src={close}
              alt={t('关闭')}
              className={
                classNames(s.closeIcon, {
                  [s['closeIconAssets']]: digitalAssets
                })
              }
              onClick={() => onClose(true)}
            />
          );
        })()}
      </div>
    </div>
  );
};

export default Header;
