import React from 'react';
import styles from './index.module.scss';

interface AlertTagProps {
  icon?: any;
  text?: string;
  textColor?: string;
  number?: number;
  color?: string;
  numberVisible?: boolean;
}

const AlertTag: React.FC<AlertTagProps> = ({ icon, text, number, color = 'red', textColor = '#333', numberVisible = true }) => (
    <div className={styles.container}>
      <div
        className={styles.diagonal}
        style={{ backgroundColor: color }}
      />
      <div className={styles.content}>
        <div
          className={styles.icon}
          style={{ color }}
        >
          <img src={icon} />
        </div>
        <span className={styles.text} style={{ color: textColor }}>{text}</span>
      </div>
      <span className={styles.number} style={{ visibility: numberVisible ? 'visible' : 'hidden' }}>{number}</span>
    </div>
);

export default AlertTag;
