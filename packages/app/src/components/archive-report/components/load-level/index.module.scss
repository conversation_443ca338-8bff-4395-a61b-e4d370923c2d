.container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 8px;
  background-color: #f8f9fa;
  overflow: hidden;
  font-size: 12px;
  margin-top: 5%;
}

.diagonal {
  position: absolute;
  left: -10px;
  top: 0;
  width: 60%;
  height: 100%;
  transform: skewX(-20deg);
}

.content {
  display: flex;
  align-items: center;
  gap: 0;
  z-index: 1;
}

.icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  img {
    width: 12px;
  }
}

.text {
  font-size: 11px;
  color: #333;
  font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
                "Helvetica Neue", "Microsoft YaHei", "微软雅黑", -apple-system,
                BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Source Han Sans SC",
                "Source Han Sans CN", "思源黑体 CN", sans-serif;
}

.number {
  font-size: 18px;
  color: #333;
  z-index: 1;
}