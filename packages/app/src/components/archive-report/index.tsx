import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { useHookstate } from '@hookstate/core';
import { app } from '@tea/app';
import globalState from '@src/stores/global.state';
import Sigma from '@tencent/sigma-editor/dist/index.es';
import { reportEvent, EVENT, ROTATE_REPORT_TYPE } from '@src/utils/report';
import {
  describeCapacityReportTask,
} from '@src/api/export';
import ArchiveReportOverview from './components/overview';
import ArchiveReportDetail from './components/detail';
import { ReportView, ReportPage, ReportTaskStatus } from './constants';
import { Status } from '../../pages/handle-area/components/loading-timer';
import './index.module.scss';
import { t } from '@tencent/tea-app/lib/i18n';

interface ArchiveReportProps {
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  exportReport?: (success: () => void, error: () => void) => void;
  digitalAssets?: boolean; // 是否是数字资产
  ArchId?: string;
  ResultId?: string;
  setAssetsModalExtraInfo?: any;
}

const ArchiveReport: React.FC<ArchiveReportProps> = (props) => {
  const currentMapId = props?.ArchId || '';
  const key = useRef(0);
  const originData = useHookstate(globalState.exportState.data).get();
  const data: any = JSON.parse(JSON.stringify(originData));
  const [page, setPage] = useState(ReportPage.OVERVIEW);
  const [view, setView] = useState(ReportView.A4);
  const [activeKey, setActiveKey] = useState(data?.NodeLoadDetailItems?.[0]?.NodeUuid);
  // props?.digitalAssets && console.log(t('数字资产数据：'), props);
  const exportReportError = useCallback(
    () => {
      globalState.exportState.set(state => ({
        ...state,
        status: Status.ERROR,
        timer: null,
        seconds: 0,
      }));
    },
    [],
  );

  /**
   * @function getReport
   * @description 1. 查询报告 2. 如果没有生成报告任务就创建报告
   * @param {Void} - no params
   * @return {Void} - no return
   * @example
   * getReport()
   */
  const getReport = async () => {
    let res;
    const user = await app.user.current();
    globalState.exportState.set(state => ({
      ...state,
      data: {},
    }));
    try {
      res = await describeCapacityReportTask({
        MapId: currentMapId,
        ReportId: props?.ResultId,
        Username: user.nickName,
      });
    } catch (err) {
      console.log(err);
    }
    key.current = +new Date();
    if (!res || res?.Error || Object.keys(res).length === 0) {
      exportReportError();
      return;
    }
    if (!res?.TaskId) {
      //
    } else {
      switch (res?.Status) {
        case ReportTaskStatus.进行中: {
          globalState.exportState.set(state => ({
            ...state,
            status: Status.LOADING,
          }));
          setTimeout(() => {
            getReport();
          }, 2000);
          break;
        }
        case ReportTaskStatus.已完成: {
          globalState.exportState.set(state => ({
            ...state,
            status: Status.SUCCESS,
            data: res,
          }));
          break;
        }
        case ReportTaskStatus.失败: {
          exportReportError();
          break;
        }
        default: {
          break;
        }
      }
    }
  };
  useMemo(() => {
    props?.setAssetsModalExtraInfo?.({
      showAssetsReportHead: false, // 控制显示报告弹窗头部
      isCustomizeAssetsContentWidth: true, // 自定义报告弹窗内容宽度，不使用其默认宽度
      assetsReportModalClassName: 'capacity-monitoring-assets-modal-wrap', // 自定义报告弹窗类名
    });
  }, []);

  useEffect(() => {
    if (props?.digitalAssets) {
      getReport();
    }
    return () => {
      props?.digitalAssets && globalState.exportState.set(state => ({
        ...state,
        data: {},
      }));
    };
  }, [props?.digitalAssets]);

  return (
    page === ReportPage.OVERVIEW ? (
      <ArchiveReportOverview
        {...props}
        Sigma={Sigma}
        onChangePage={() => setPage(ReportPage.DETAIL)}
        view={view}
        onChangeView={() => {
          setView(view === ReportView.A4 ? ReportView.WIDE : ReportView.A4);
          reportEvent({
            key: EVENT.ROTATE_REPORT,
            extraInfo: view === ReportView.A4 ? ROTATE_REPORT_TYPE.VERTICAL : ROTATE_REPORT_TYPE.HORIZONTAL,
          });
        }}
        data={data}
        key={key.current}
        exportReport={props?.exportReport}
      />
    ) : (
      <ArchiveReportDetail
        {...props}
        Sigma={Sigma}
        onChangePage={() => setPage(ReportPage.OVERVIEW)}
        view={view}
        key={key.current}
        onChangeView={() => {
          setView(view === ReportView.A4 ? ReportView.WIDE : ReportView.A4);
          reportEvent({
            key: EVENT.ROTATE_REPORT,
            extraInfo: view === ReportView.A4 ? ROTATE_REPORT_TYPE.VERTICAL : ROTATE_REPORT_TYPE.HORIZONTAL,
          });
        }}
        data={data}
        activeKey={activeKey || data?.NodeLoadDetailItems?.[0]?.NodeUuid}
        setActiveKey={setActiveKey}
        exportReport={props?.exportReport}
      />
    )
  );
};

export default ArchiveReport;
