import { t } from '@tencent/tea-app/lib/i18n';
export enum ArchiveStatus {
  待归档 = 0,
  归档中 = 1,
  归档完成 = 2,
  归档失败 = 3,
  已导出 = 4,
};

export enum ReportView {
  A4 = 'A4',
  WIDE = 'wide',
};

export enum ReportPage {
  OVERVIEW = 'overview',
  DETAIL = 'detail',
};

export enum ReportTaskStatus {
  未开始 = 0,
  进行中 = 1,
  已完成 = 2,
  失败 = 3,
};

export enum ReportType {
  PDF = 'pdf',
  EXCEL = 'excel',
}

export const CcnConfig = {
  CCNS: 'ccns',
  COLUMN: t('地域信息'),
  REPORT_TITLE: t('限速方式分布'),
  DETAIL_TITLE: t('限速方式'),
  SINGLE: t('地域出口限速'),
  MULTIPLE: t('地域间限速'),
};

export const ClickHouseConfig = {
  CLICK_HOUSE: 'clickhouse',
  BASE_BUBBLE_TIP: t('可通过“生成报告”下载全量列表'),
  INSTANCE_TYPE_NAME: t('Node节点IP'),
  INSTANCE_NAME: t('集群'),
};

export const DorisConfig = {
  DORIS: 'doris',
  INSTANCE_TYPE_NAME: t('BE节点IP'),
  INSTANCE_NAME: t('集群'),
  BASE_AREA_NAME: t('可用区'),
};

