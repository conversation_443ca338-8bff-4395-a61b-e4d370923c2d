/* eslint-disable @typescript-eslint/no-namespace */
export namespace ExportCapacityReportTaskNS {
  export interface Params {
    MapId?: string;            // 架构图 ID
    Username?: string;         // 用户名
    TaskId?: string;           // 任务 ID
    ArchDetail?: string;       // 架构图SVG渲染数据
    ReportVersion?: string;    // 数字资产参数(报告版本)
    ReportSource?: string;     // 数字资产参数(报告来源)
    ReportName?: string;       // 数字资产参数(报告名称)
    SdkName?: string;          // 数字资产参数(插件名称)
    ArchiveType?: number;      // 归档类型(0: 归档数字资产，-1:不归档)
  }
  export interface Response {
    ReportFile?: string;       // PDF文件地址
    ArchiveStatus?: number;    // 状态(0 待处理 1 处理中，2 已完成，3 失败 4 已导出)
    DigitalAssetUrl?: string;  // 数字资产跳转地址
    ExcelFile?: string;        // Excel 文件地址
  }
}

export namespace DescribeCapacityReportTaskNS {
  interface ReportInstanceMetricItem {
    MetricName?: string;       // 指标名称
    MetricValue?: number;      // 指标值
    AlgorithmType?: number;    // 算法类型
    Period?: number;           // 周期(天)
    Rate?: number;             // 环比
    AlgorithmName?: string;    // 算法名称
    RiskLevel?: number;        // 指标风险等级
  }
  interface ReportInstanceLoadInfo {
    InstanceId?: string;           // 实例ID
    InstanceName?: string;         // 实例名称
    InstanceType?: string;         // 实例机型
    RiskLevel?: number;            // 风险等级
    MetricItems?: ReportInstanceMetricItem[]; // 实例指标
  }
  interface ReportNodeLoadDetailItem {
    NodeUuid?: string;          // 节点ID
    NodeName?: string;          // 节点名称
    Product?: string;           // 产品类型
    ProductName?: string;       // 产品名称
    InstanceCount?: number;     // 实例数量
    HighLoadCount?: number;     // 严重高负载数量
    MediumLoadCount?: number;   // 高负载数量
    LowUsageCount?: number;     // 未充分使用数量
    LowLoadCount?: number;      // 正常负载
    InstanceLoadInfos?: ReportInstanceLoadInfo[]; // 实例负载信息
  }
  export interface Params {
    MapId: string;               // 架构图 ID
    ReportId?: string;           // 容量报告 ID
    Username?: string;           // 用户名
    ProductDiagramList?: string; // 云架构 SVG 渲染数据
    ArchDetail?: string;         // 云架构 SVG 渲染数据
  }
  export interface Response {
    TaskId?: string;                     // 任务 ID
    Status?: number;                     // 状态(0 未开始，1 进行中，2 已完成，3 失败)
    ReportFile?: string;                 // 报告PDF文件
    Title?: string;                      // 标题
    CreateTime?: string;                 // 创建时间
    MapUuid?: string;                    // 架构图ID
    MapName?: string;                    // 架构图名称
    CustomerName?: string;               // 客户名称
    AppId?: number;                     // 应用ID
    TotalInstanceCount?: number;         // 总实例数
    NodeLoadDetailItems?: ReportNodeLoadDetailItem[]; // 节点负载明细
    ArchDetail?: string;                 // 架构图SVG渲染数据
    ArchiveStatus?: number;              // 归档状态(0 待归档 1 归档中，2 归档完成，3 归档失败)
    DigitalAssetUrl?: string;            // 数字资产跳转地址
    ProductDiagramList?: string;         // 云产品绘图数据列表
  }
}

export namespace UpdateArchScanReportArchiveInfoNS {
  export interface Params {
    ResultIds: string[];       // 在线报告Id，必选且必填
    ArchIds?: string[];        // 架构图ID，数组，非必选
    ReportVersion?: string;    // 报告版本
    ReportName?: string;       // 报告名称
    DeleteStatus?: boolean;    // 是否删除，false否，true删除
    ReportUrl?: string;        // 报告下载链接
    ReportSource?: string;     // 报告来源
    SdkName?: string;          // 插件名称
    ArchName?: string;         // 归档架构图名称
  }
  export type Response = unknown;
}

export namespace DescribeProductDiagramListNS {
  interface ProductDiagramItem {
    Name?: string | null;             // 英文简称，允许为 null
    EnNameShow: string;               // 用于展示的图元英文名称
    Description?: string | null;      // 英文全称，允许为 null
    DescriptionShow: string;          // 用于展示的图元描述信息
    CName?: string | null;            // 中文名，允许为 null
    CNameShow: string;                // 用于展示的中文名
    Category?: string | null;         // 产品分类，允许为 null
    D2: string;                      // 2d数据
    D3: string;                      // 3d数据
    Type?: string | null;             // 类型，允许为 null
    CloudCategory?: string | null;    // 云产品分类，允许为 null
    SupportBinding: boolean;          // 是否支持绑定资源。不返回则不支持
    NeedShow: boolean;                // 是否显示到左侧栏
    Keywords: string[];               // 用于简写搜索的关键字数组
  }
  export type Params = object;
  export interface Response {
    ProductDiagramList: ProductDiagramItem[];
  }
}

export namespace ResetCapacityReportTaskNS {
  export interface Params {
    MapId: string;  // 架构图 ID
    TaskId?: string; // 任务 ID
  }
  export type Response = unknown;
}
