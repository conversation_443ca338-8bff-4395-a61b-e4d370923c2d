import { app } from '@tea/app';
import { EnvEnum } from '@src/constants';
import { getLanguageParam } from '@src/utils/getLanguageParam';
import { isDev } from '@src/utils/common';
// import { getStorage, setStorage } from '@src/utils/storage';

import { archInfo } from '@src/utils/caching';
import {
  DescribeArchThresholdConfigStatusParams,
  DescribeNodeLoadInfoParams,
  UpdateNodeResourceCapacityMetricParams,
} from './handleArea.type';
import request from '../utils/request';
import { t } from '@tea/app/i18n';
const basePath = `${isDev ? '' : ''}`;

// 查询架构图监控指标配置状态
export const describeArchThresholdConfigStatus = (data: DescribeArchThresholdConfigStatusParams): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeArchThresholdConfigStatusV2' : ''}`,
      data: { ...data, Action: 'DescribeArchThresholdConfigStatusV2' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchThresholdConfigStatusV2`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
          tipLoading: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

// 查询架构图节点容量负载信息
export const describeNodeLoadInfo = (data: DescribeNodeLoadInfoParams): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeNodeLoadInfoV2New' : ''}`,
      data: { ...data, Action: 'DescribeNodeLoadInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeNodeLoadInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
          tipLoading: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

// 查询架构图节点容量负载信息
// eslint-disable-next-line max-len
export const updateNodeResourceCapacityMetric = (data: UpdateNodeResourceCapacityMetricParams, showTips?: boolean): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=UpdateNodeResourceCapacityMetricV2' : ''}`,
      data: { ...data, Action: 'UpdateNodeResourceCapacityMetricV2' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}UpdateNodeResourceCapacityMetricV2`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
          tipLoading: showTips,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

