import { app } from '@tea/app';
import request, { ApiResponse } from '../utils/request';
import { getLanguageParam } from '@src/utils/getLanguageParam';
import { archInfo } from '@src/utils/caching';
import { EnvEnum } from '@src/constants';
import { isDev } from '@src/utils/common';
import { DescribeCapacityMetricInfoV2NewNS } from './nodeDrawer.type';
import { t } from '@tea/app/i18n';
const basePath = `${isDev ? '' : ''}`;

/**
 * @doc https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=DescribeCapacityMetricInfoV2New
 * @description 查询产品容量监控维度信息V2New
 * @param data 请求参数
 * <AUTHOR>
 * @returns Promise<any>
 */
export const describeCapacityMetricInfo = (data: DescribeCapacityMetricInfoV2NewNS.Params): Promise<ApiResponse<DescribeCapacityMetricInfoV2NewNS.Response>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeCapacityMetricInfoV2New' : ''}`,
      data: { ...data, Action: 'DescribeCapacityMetricInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeCapacityMetricInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

// 查询架构图节点资源容量分布
export const describeNodeResourceLoadInfo = (data): Promise<ApiResponse<any>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeNodeResourceLoadInfoV2New' : ''}`,
      data: { ...data, Action: 'DescribeNodeResourceLoadInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeNodeResourceLoadInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

// 查询实例资源信息
export const describeInsResourceRealTimeInfo = (data): Promise<ApiResponse<any>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeInsResourceRealTimeInfoV2' : ''}`,
      data: { ...data, Action: 'DescribeInsResourceRealTimeInfoV2' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeInsResourceRealTimeInfoV2`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

// 查询实例容量指标数据
export const describeInsCapacityMetricData = (data): Promise<ApiResponse<any>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeInsCapacityMetricDataV2New' : ''}`,
      data: { ...data, Action: 'DescribeInsCapacityMetricDataV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeInsCapacityMetricDataV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

/**
 * 获取云顾问区域列表信息
 * @param data - 请求参数
 * @returns Promise<any> - 返回区域信息列表
 * @doc https://cloud.tencent.com/document/product/1596/77930
 * @description
 * - ISA 环境下通过 /1/interface 接口获取
 * - 非 ISA 环境通过 CAPI V3 接口获取
 * - 如遇到未授权操作错误(AuthFailure.UnauthorizedOperation)，将显示 CAM 权限提示
 */
export const describeRegions = (data: { ProductId: string }): Promise<ApiResponse<any>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/interface${isDev ? '?api=DescribeRegions' : ''}`,
      data: { ...data, Action: 'DescribeRegions' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeRegions`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

/**
 * 获取云顾问区域和可用区信息
 * @returns Promise<any> - 返回区域和可用区信息列表
 * @doc
 * @description
 */
export const describeRegionsAndZones = (): Promise<ApiResponse<any>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return;
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'region',
          cmd: `${basePath}DescribeRegionsAndZones`,
          data: {
            Version: '2022-06-27',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};
