import { app } from '@tea/app';
import request, { ApiResponse } from '../utils/request';
import { getLanguageParam } from '@src/utils/getLanguageParam';
import { archInfo } from '@src/utils/caching';
import { EnvEnum } from '@src/constants';
import { isDev } from '@src/utils/common';
import { t } from '@tea/app/i18n';
import {
  ExportCapacityReportTaskNS,
  DescribeCapacityReportTaskNS,
  UpdateArchScanReportArchiveInfoNS,
  DescribeProductDiagramListNS,
  ResetCapacityReportTaskNS,
} from './export.type';

const basePath = `${isDev ? '' : ''}`;

/**
 * @doc https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=DescribeCapacityReportTask
 * @description 容量监测查询生成报告的任务结果
 * @param data 请求参数
 * <AUTHOR>
 * @returns Promise<any>
 */
export const describeCapacityReportTask = (data: DescribeCapacityReportTaskNS.Params): Promise<ApiResponse<DescribeCapacityReportTaskNS.Response>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeCapacityReportTask' : ''}`,
      data: { ...data, Action: 'DescribeCapacityReportTask' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeCapacityReportTask`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
          // highCapacity: true,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

/**
 * @doc https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=CreateCapacityReportTask
 * @description 发起生成报告任务
 * @param data 请求参数
 * <AUTHOR>
 * @returns Promise<any>
 */
export const createCapacityReportTask = (data): Promise<ApiResponse<any>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=CreateCapacityReportTask' : ''}`,
      data: { ...data, Action: 'CreateCapacityReportTask' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}CreateCapacityReportTask`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};


/**
 * @doc https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=ExportCapacityReportTask
 * @description 容量插件导出报告文件
 * @param data 请求参数
 * <AUTHOR>
 * @returns Promise<any>
 */
export const exportCapacityReportTask = (data: ExportCapacityReportTaskNS.Params): Promise<ApiResponse<ExportCapacityReportTaskNS.Response>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=ExportCapacityReportTask' : ''}`,
      data: { ...data, Action: 'ExportCapacityReportTask' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}ExportCapacityReportTask`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

/**
 * @doc https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateArchScanReportArchiveInfo
 * @description 更新架构图巡检报告归档信息
 * @param data 请求参数
 * <AUTHOR> @returns Promise<any>
 */
export const updateArchScanReportArchiveInfo = (data: UpdateArchScanReportArchiveInfoNS.Params): Promise<ApiResponse<UpdateArchScanReportArchiveInfoNS.Response>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=UpdateArchScanReportArchiveInfo' : ''}`,
      data: { ...data, Action: 'UpdateArchScanReportArchiveInfo' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}UpdateArchScanReportArchiveInfo`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};


/**
 * @doc https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeProductDiagramList
 * @description 获取云产品绘图数据列表
 * @param data 请求参数
 * <AUTHOR> @returns Promise<any>
 */
export const describeProductDiagramList = (data: DescribeProductDiagramListNS.Params): Promise<ApiResponse<DescribeProductDiagramListNS.Response>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/interface${isDev ? '?api=DescribeProductDiagramList' : ''}`,
      data: { ...data, Action: 'DescribeProductDiagramList' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeProductDiagramList`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

/**
 * @doc https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=ResetCapacityReportTask
 * @description 容量插件重置报告任务
 * @param data 请求参数
 * <AUTHOR> @returns Promise<any>
 */
export const resetCapacityReportTask = (data: ResetCapacityReportTaskNS.Params): Promise<ApiResponse<ResetCapacityReportTaskNS.Response>> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=ResetCapacityReportTask' : ''}`,
      data: { ...data, Action: 'ResetCapacityReportTask' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}ResetCapacityReportTask`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};
