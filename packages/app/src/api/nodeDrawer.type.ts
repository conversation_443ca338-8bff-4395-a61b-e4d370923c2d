/* eslint-disable @typescript-eslint/no-namespace */

export namespace DescribeCapacityMetricInfoV2NewNS {
  interface ArchProductPeriod {
    PeriodType?: number;  // 周期类型(1:天，2：周 ，3：月)
    PeriodValue?: number; // 周期值，例如 3,7,14,30
  }
  interface MonitorMetricInfoV2 {
    Metric?: string;             // 监控指标
    MetricName?: string;         // 监控指标名称
    Namespace?: string;          // 命名空间
    Dimension?: string;          // 维度
    HighThreshold?: number;      // 高风险负载值
    MediumThreshold?: number;    // 中风险负载值
    LowThreshold?: number;       // 低风险负载值
    LowUsed?: number;            // 低使用率高风险
    MediumUsed?: number;         // 低使用率中风险
    AlgorithmType?: number;      // 算法类型
    AlgorithmValue?: string;     // 算法值
    AlgorithmName?: string;      // 算法名称
    PeriodTypeInfo?: ArchProductPeriod; // 周期数据
    IsMultiRegion?: boolean;     // 是否为多地域指标标识
  };
  export interface Params {
    MapId?: string;    // 架构图Id
    NodeUuid?: string; // 架构图节点Id
    InsId?: string;    // 实例Id
  }
  export interface Response {
    MonitorMetricInfos: MonitorMetricInfoV2[]; // 容量监控维度信息，必选且不允许为 null
    TotalCount?: number;                        // 数量，非必选且不允许为 null
  }
}

export namespace DescribeInsResourceRealTimeInfoV2NS {
  interface InsResourceInfo {
    ShowType: number;  // 展示类型
    [key: string]: any; // 其他可能的字段
  }

  export interface Response {
    InsResourceInfoList?: InsResourceInfo[]; // 实例资源信息列表
    RegionList?: string[]; // 地域列表
    Error?: {
      Message?: string;
    };
  }
}
