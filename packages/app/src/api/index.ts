import { app } from '@tea/app';
import request from '../utils/request';
import { getLanguageParam } from '@src/utils/getLanguageParam';
import { getStorage/* , setStorage*/ } from '@src/utils/storage';
import { EnvEnum } from '@src/constants';
import { isDev } from '@src/utils/common';
import {
  DescribeArchParams,
} from './index.type';
import { archInfo } from '@src/utils/caching';
import { t } from '@tea/app/i18n';

const basePath = `${isDev ? '' : ''}`;


export const describeArch = (data: DescribeArchParams): Promise<any> => {
  const appId = location.search?.split('appid')?.[1]?.split('&')?.[0]?.split('=')?.[1];
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/interface${isDev ? '?api=DescribeArch' : ''}`,
      data: { ...data, Action: 'DescribeArch', OnlyData: true, CustomerAppId: Number(appId), Username: getStorage('engName') },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArch`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
          tipLoading: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};

// 查询评估项信息
export const describeArchNodeConfigInfo = (data?: any): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/arch${isDev ? '?api=DescribeArchNodeConfigInfo' : ''}`,
      data: { ...data, Action: 'DescribeArchNodeConfigInfo' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchNodeConfigInfo`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
          reject(err);
        }
      });
  });
};
