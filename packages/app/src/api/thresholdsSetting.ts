import { app } from '@tea/app';
import { getLanguageParam } from '@src/utils/getLanguageParam';
import { isDev } from '@src/utils/common';
import { archInfo } from '@src/utils/caching';
import { EnvEnum } from '@src/constants';
import {
  DescribeArchProductThresholdInfoParams,
  ModifyArchProductThresholdParams,
  DescribeCapacityProductListParams,
} from './thresholdsSetting.type';
import request from '../utils/request';
import { t } from '@tea/app/i18n';

const basePath = `${isDev ? '' : ''}`;

// 查询架构图各产品容量监测指标阈值，支持架构图维度和节点维度查询
export const describeArchProductThresholdInfo = (data: DescribeArchProductThresholdInfoParams): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeArchProductThresholdInfoV2New' : ''}`,
      data: { ...data, Action: 'DescribeArchProductThresholdInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchProductThresholdInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

/**
 * @api https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchProductAlgorithmType
 * @description 查询架构图产品算法类型
 * @returns
 */
export const describeArchProductAlgorithmType = (): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeArchProductAlgorithmType' : ''}`,
      data: { Action: 'DescribeArchProductAlgorithmType' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchProductAlgorithmType`,
          data: {
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};
/**
 * @api https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchProductPeriodType
 * @description 查询架构图产品周期类型
 * @returns
 */
export const describeArchProductPeriodType = (): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeArchProductPeriodType' : ''}`,
      data: { Action: 'DescribeArchProductPeriodType' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchProductPeriodType`,
          data: {
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

// 修改架构图各产品容量监测指标阈值，支持架构图维度和节点维度查询
export const modifyArchProductThreshold = (data: ModifyArchProductThresholdParams): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=ModifyArchProductThresholdV2New' : ''}`,
      data: { ...data, Action: 'ModifyArchProductThresholdV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}ModifyArchProductThresholdV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

/**
 * @api https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchProductPredictionInfoV2New
 * @description 查询产品容量预测信息接口V2New
 * @returns
 */
export const describeArchProductPredictionInfo = (data: {
  MapId: string;
  NodeUuidSet: string;
}): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeArchProductPredictionInfoV2New' : ''}`,
      data: { ...data, Action: 'DescribeArchProductPredictionInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchProductPredictionInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

/**
 * @api https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=ModifyArchProductPredictionInfoV2New
 * @description 查询产品容量预测信息接口V2New
 * @returns
 */
export const modifyArchProductPredictionInfoV2New = (data: {
  MapId: string;
  AppId?: string | number;
  GlobalSetting: {
    ResourceTimes?: number;
    PeriodType?: number;
    PeriodValue?: number;
  };
  AccurateSettings: {
    ProductName?: string;
    PredictionNodeInfos?: {
      ItemName?: string;
      ResourceTimes?: string;
      NodeId?: number;
      Id?: number;
      Product?: string;
    }[]
  }[];
}): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=ModifyArchProductPredictionInfoV2New' : ''}`,
      data: { ...data, Action: 'ModifyArchProductPredictionInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}ModifyArchProductPredictionInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

/**
 * @api https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=StartCapacityPredictionTaskV2New
 * @description 容量预测开始任务
 * @returns
 */
export const startCapacityPredictionTaskV2New = (data: {
  MapId: string;
}): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=StartCapacityPredictionTaskV2New' : ''}`,
      data: { ...data, Action: 'StartCapacityPredictionTaskV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}StartCapacityPredictionTaskV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

/**
 * @api https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchProductPredictionResultInfoV2New
 * @description 获取容量预测结果
 * @returns
 */
export const describeArchProductPredictionResultInfoV2New = (data: {
  MapId: string;
  TaskId: string | number;
  SessionId: string | number;
}): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeArchProductPredictionResultInfoV2New' : ''}`,
      data: { ...data, Action: 'DescribeArchProductPredictionResultInfoV2New' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeArchProductPredictionResultInfoV2New`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

// 修改架构图各产品容量监测指标阈值，支持架构图维度和节点维度查询
export const describeCapacityProductList = (data: DescribeCapacityProductListParams): Promise<any> => {
  if (archInfo.env === EnvEnum.ISA) {
    return request({
      method: 'post',
      url: `/capacityPlugin${isDev ? '?api=DescribeCapacityProductListV2' : ''}`,
      data: { ...data, Action: 'DescribeCapacityProductListV2' },
    });
  }
  return new Promise((resolve, reject) => {
    app.capi
      .requestV3(
        {
          serviceType: 'advisor',
          cmd: `${basePath}DescribeCapacityProductListV2`,
          data: {
            ...data,
            Version: '2020-07-21',
            Language: getLanguageParam(),
          },
          regionId: 1,
        },
        {
          tipErr: false,
          tipLoading: false,
        }
      )
      .then((res) => {
        resolve(res.data.Response);
      })
      .catch(async (err) => {
        if (err?.code === 'AuthFailure.UnauthorizedOperation') {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          reject(err);
          app.tips.error(err?.msg || err?.toString?.() || t('未知错误'));
        }
      });
  });
};

