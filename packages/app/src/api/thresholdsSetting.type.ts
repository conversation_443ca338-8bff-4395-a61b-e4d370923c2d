export interface DescribeArchProductThresholdInfoParams {
  MapId: string,
  NodeUuidSet?: Array<string>,
  IsDefault?: number,
  Products?: Array<string>
}

export interface ModifyArchProductThresholdParams {
  MapId: string,
  ModifyMode?: number,
  ArchProductThresholdInfos?: Array<{
    Product: string,
    ProductName: string,
    ArchThresholdInfos: Array<{
      InsType: string,
      InsTypeName: string,
      ThresholdConfigInfos: Array<{
        Metric: string,
        MetricName: string,
        HighThreshold: number,
        MediumThreshold: number,
        LowThreshold: number
      }>
    }>
  }>,
  NodeUuidSet?: Array<string>,
  ProductIsDefaultInfos?: Array<{
    Product: string,
    IsDefault: number
  }>
}

export interface DescribeCapacityProductListParams {
  MapId: string,
}
