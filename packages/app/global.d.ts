declare module '*.module.scss' {
  const classes: { [key: string]: string };
  export default classes;
}
declare module '*.svg' {
  const content: any;
  export default content;
}

declare const div: HTMLDivElement;
declare const span: HTMLSpanElement;
declare const a: HTMLAnchorElement;
declare const p: HTMLParagraphElement;
declare const h1: HTMLHeadingElement;
declare const h2: HTMLHeadingElement;
declare const h3: HTMLHeadingElement;
declare const h4: HTMLHeadingElement;
declare const h5: HTMLHeadingElement;
declare const h6: HTMLHeadingElement;
declare const button: HTMLButtonElement;
declare const input: HTMLInputElement;
declare const textarea: HTMLTextAreaElement;
declare const select: HTMLSelectElement;
declare const option: HTMLOptionElement;
declare const img: HTMLImageElement;
declare const svg: SVGSVGElement;
declare const ul: HTMLUListElement;
declare const li: HTMLLIElement;
declare const form: HTMLFormElement;
declare const label: HTMLLabelElement;
declare const table: HTMLTableElement;
declare const tr: HTMLTableRowElement;
declare const td: HTMLTableCellElement;
declare const th: HTMLTableCellElement;
declare const canvas: HTMLCanvasElement;
declare const audio: HTMLAudioElement;
declare const video: HTMLVideoElement;
declare const iframe: HTMLIFrameElement;
declare const script: HTMLScriptElement;
declare const style: HTMLStyleElement;
declare const link: HTMLLinkElement;
declare const meta: HTMLMetaElement;
declare const head: HTMLHeadElement;
declare const body: HTMLBodyElement;
declare const title: HTMLTitleElement;
declare const nav: HTMLElement;
declare const section: HTMLElement;
declare const article: HTMLElement;
declare const aside: HTMLElement;
declare const footer: HTMLElement;
declare const header: HTMLElement;
declare const main: HTMLElement;
declare const time: HTMLTimeElement;
declare const progress: HTMLProgressElement;
declare const meter: HTMLMeterElement;
declare const details: HTMLDetailsElement;
declare const summary: HTMLElement;
declare const dialog: HTMLDialogElement;
declare const menu: HTMLMenuElement;
declare const menuitem: HTMLElement;
declare const datalist: HTMLDataListElement;
declare const output: HTMLOutputElement;
declare const fieldset: HTMLFieldSetElement;
declare const legend: HTMLLegendElement;
declare const hr: HTMLHRElement;
declare const br: HTMLBRElement;
declare const pre: HTMLPreElement;
declare const code: HTMLElement;
declare const blockquote: HTMLQuoteElement;
declare const q: HTMLQuoteElement;
declare const cite: HTMLElement;
declare const em: HTMLElement;
declare const strong: HTMLElement;
declare const small: HTMLElement;
declare const mark: HTMLElement;
declare const del: HTMLModElement;
declare const ins: HTMLModElement;
declare const sub: HTMLElement;
declare const sup: HTMLElement;
declare const abbr: HTMLElement;
declare const address: HTMLElement;
declare const b: HTMLElement;
declare const i: HTMLElement;
declare const u: HTMLElement;
declare const s: HTMLElement;
declare const strike: HTMLElement;
declare const big: HTMLElement;
declare const tt: HTMLElement;
declare const kbd: HTMLElement;
declare const samp: HTMLElement;
declare const dfn: HTMLElement;
declare const bdi: HTMLElement;
declare const bdo: HTMLElement;
declare const ruby: HTMLElement;
declare const rt: HTMLElement;
declare const rp: HTMLElement;
declare const wbr: HTMLElement;
declare const picture: HTMLPictureElement;
declare const source: HTMLSourceElement;
declare const track: HTMLTrackElement;
declare const map: HTMLMapElement;
declare const area: HTMLAreaElement;
declare const object: HTMLObjectElement;
declare const param: HTMLParamElement;
declare const embed: HTMLEmbedElement;
declare const portal: HTMLParamElement;
declare const slot: HTMLSlotElement;
declare const template: HTMLTemplateElement;
