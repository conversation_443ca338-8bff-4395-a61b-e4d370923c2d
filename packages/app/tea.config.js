/**
 * Tea 项目配置
 * @type {import("@tencent/tea-types/config").Configuration}
 */
module.exports = {
  command: {
    dev: {
      port: 8323,
      https: true,
    },
  },
  classPrefix: 'tea-capacity',
  webpack: (config, { MiniCssExtractPlugin }) => {
    const devMode = config.mode === 'development';
    config.module.rules.push({
      test: /\.(sa|sc)ss$/,
      use: [
        {
          loader: MiniCssExtractPlugin.loader,
          options: {
            hmr: devMode,
          },
        },
        {
          loader: 'css-loader',
          options: {
            modules: {
              localIdentName: '[name]__[local]--[hash:base64:5]',
            },
          },
        },
        'sass-loader',
      ],
    }, {
      test: /\.js$/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env'],
        },
      },
    });
    return config;
  },
  buffet: {
    productId: 1336,
    zh: [
      {
        site: 1,
        route: 'capacity-monitoring-sdk',
      },
    ],
  },
};
