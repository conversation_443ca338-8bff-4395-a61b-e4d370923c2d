/**
 * 提供给垂直应用的基座平台API
 * <AUTHOR>
 * @since 2023-11-22
 */
declare namespace AppPluginAPI {
  export interface IResourceList {
    hasResource: boolean; // 是否有绑定资源
    DiagramId: string; // 图id
    ProductType: string; // 云产品类型
    NodeName: string; // 节点名称
    NodeInitName?: string; // 节点原本名称
    NodeRegion: string; // 地域
    BindingType: 'instance' | 'tag' | 'fuzzy'; // 绑定类型
    Conditions: {
      Key: string;
      Value: string;
    }[];
    Tags: {
      Key: string;
      Value: string;
    }[];
    FuzzyKeys: {
      Key: string;
      Value: string;
    }[];
    ResourceList: {
      InstanceId: string; // 资源id
      Attributes: string; // 云产品属性
    }[];
  }
  // tea-component Drawer组件的部分props
  export interface DrawerProps {
    size?: 'm' | 'l';
    children?: React.ReactNode;
    title?: React.ReactNode;
    footer?: React.ReactNode;
    subtitle?: React.ReactNode;
    extra?: object;
    style?: React.CSSProperties;
    className?: string;
  }

  export interface Operations {
    key?: string; // 节点key,切换key可以强制更新Bubble
    icon?: any; // 传该参数则iconNode无效，img元素渲染
    iconClass?: string; // 为渲染icon的img添加class
    iconNode?: React.ReactNode; // 需要更加自定义能力则传该参数
    desc: string;
    onClick: () => void;
    disabled?: boolean; // 是否禁用，默认不禁用
    hoverText?: string | React.ReactNode; // 操作项hover文案
    color?: string; // 文字颜色
    hoverTextExtraProps?: object; // 自定义属性，tea的Bubble组件属性均可以支持
  }

  export interface ProgressOptionsType {
    key?: string; // 节点key,切换key可以强制更新Bubble
    progressNum?: number; // 当前异步任务进度（0-100）
    showProgressBar?: boolean; // 是否展示进度条
    progressDesc?: string; // 进度条文案
    showStopButton?: boolean; // 是否展示停止按钮
    onStop?: () => void; // 点击停止按钮时触发
    stopButtonHoverText?: string; // 停止按钮hover文案
    hoverText?: string | React.ReactNode; // 操作项hover文案
    hoverTextExtraProps?: object | any; // 自定义属性，tea的Bubble组件属性均可以支持
  }

  export interface BarProps {
    x?: number; // 容器x位置
    y?: number; // 容器y位置
    width?: number; // 容器宽度
    height?: number; // 容器高度
    children: React.ReactNode; // 容器内容（React组件）
  }

  export interface IArchInfo {
    archId: string; // 架构图id
    archTitle: string; // 架构图title
    archVersion: string; // 架构图version
    nodeList?: IResourceList[]; // 架构图nodeList
  }

  export type BarPosition = 'top' | 'bottom' | 'left' | 'right';

  export interface SDKToolsProps {
    placement: BarPosition; // 弹出位置, 默认 top
    overlayClassName: string; // 自定义弹层className
    overlayStyle: React.CSSProperties; // 自定义弹框style
    destroyOnClose: boolean; // 关闭工具栏时是否销毁节点, 默认 false
    children: React.ReactElement; // 工具栏内容, 默认为<></>
  }

  export interface SigmaVNode {
    key: string; // 节点在前端的唯一key
    type: string; // 类型
    category: string; // 分组
    name: string; // 名称-英文缩写
    data: { d2: string; d3: string }; // 节点图标，d2为2d模式下的图标，d3为3d模式下的图标
    cName: string; // 名称-中文
    label: string; //
    description: string; // 节点描述
    group?: string[];
    groupVpc?: string[];
    groupSubnet?: string[];
    groupSecurity?: string[];
    lock: boolean; // 是否锁定
    editable: string[];
    isAreaShape: boolean;
    is3DShape: boolean; // 是否为3d图形
    isChecked: boolean; // 是否被选中
    position: { x: number; y: number }; // 节点在画布里的位置，画布起点为(0, 0)
    customize: {
      label?: string; // 节点名称，用户编辑后的最终名称，如果不存在请使用cName
      descNote?: string; // 节点描述信息，用户编辑后的最终名称，如果不存在请使用description
      region?: string; // 绑定了资源才有该属性
      zone?: string; // 绑定了资源才可能有该属性
      subnet?: string; // 绑定了资源才可能有该属性
      vpc?: string; // 绑定了资源才可能有该属性
    }; // 存放资源绑定数据
    width: number; // 节点宽度
    height: number; // 节点高度
    forever: boolean;
  }

  export interface ShapeClickParam {
    event: MouseEvent; // 鼠标事件
    node: SigmaVNode; // 点击的节点数据
  }

  // d2Icon, d3Icon分别为2d, 3d模式下图元的缩略图
  export type ShapeWidthIcon = SigmaVNode & {
    d2Icon?: string;
    d3Icon?: string;
  };

  export interface ShapeCategory {
    category: string;
    shapes: ShapeWidthIcon[];
  }

  // init方法返回的
  export interface SDKCallbacks {
    onShapeClick?: (params: ShapeClickParam) => void; // 当点击节点时触发
    onDocClick?: (e: MouseEvent) => void; // 当画布被点击时触发
    onShapeMouseOver?: (params: ShapeClickParam) => void; // 当节点hover时触发
    onShapeMouseOut?: (params: ShapeClickParam) => void; // 当节点hover结束时触发
  }

  export interface PluginAPI {
    openSubscription: () => void;
    closeSubscription: () => void;
    getCommonData: (timeout: number) => Promise<any>; // 获取当前架构图的公共数据,异步，可自定义超时时间，默认是10秒\
    switchEdit: () => void; // 切换编辑模式
    navigateTo: (url: string) => void; // 跳转到指定url,参数如/advisor/assess，租户端特有
    /**
     * 获取当前画布所有节点数据
     * 按需使用，前端获取当前画布信息，只能表示打开架构图时的状态
     * 获取当前时刻架构图准确状态请调用相关接口获取架构图信息
     * 建议无必要都使用接口获取架构图节点信息
     * */
    getArchNodes: () => SigmaVNode[];
    initOver: () => void;
    // 切换别的sdk, extraParams为启动时传入插件的参数
    changeSdk: (sdkName: string, extraParams?: any) => void;
    // 单独或批量添加节点class
    addNodeClass: (keys: string[] | string, className: string, opt?: {classLevel?: 'g' | 'svg'}) => void;
    // 单独或批量移除节点class
    removeNodeClass: (keys: string[] | string, className: string, opt?: {classLevel?: 'g' | 'svg'}) => void;
    // 移除所有addNodeClass方法添加的class
    removeAllNodeClass: () => void;

    setProgressParams: (num: number, desc: string, opts?: {
      showStopButton?: boolean; // 是否展示停止按钮
      onStop?: () => void; // 点击停止按钮时触发
      stopButtonHoverText?: string; // 停止按钮hover文案
      hoverText?: string | React.ReactNode; // 操作项hover文案
      hoverTextExtraProps?: object | any; // 自定义属性，tea的Bubble组件属性均可以支持
    }) => void; // 设置当前异步任务进度，num为0~100,调用此方法认为异步任务进行中
    setAsyncTaskStop: () => void; // 设置异步任务结束
    getAsyncTaskIsRunning: () => boolean; // 获取异步任务是否正在运行
    showTools: () => void; // 显示工具栏(新)
    shutdownTools: () => void; // 关闭工具栏(新)
    setOperations: (config: AppPluginAPI.Operations[]) => void; // 设置工具栏操作项
    /**
   * @desc 提供给插件方的自定义上报
   * @param params
   * type: string; // 自定义click/run/config等
   * target?: string[]; // 节点key列表
   * action: string; // 自定义动作名称
   * extraInfo?: string; // 其他自定义信息,转成字符串形式
   */
    pluginReport: (params: {type: string;target: string[]; action: string; extraInfo: string;}) => void;
    openDrawer: () => void; // 打开右侧抽屉
    closeDrawer: () => void; // 关闭右侧抽屉
    setDrawerProps: (props: DrawerProps) => void; // 设置抽屉内容
    openTools: () => void; // 打开插件工具栏
    closeTools: () => void; // 关闭插件工具栏
    setTools: (props: SDKToolsProps) => void; // 设置插件工具栏内容
    createBar: (
      keys: string[], // 节点id列表
      props: BarProps, // 具体内容
      position?: BarPosition// 位置
    ) => void; // 设置节点内容条
    removeBar: (keys: string[], position?: BarPosition) => void; // 关闭节点底部内容条 keys: 节点id列表 position: 位置
    clearBar: () => boolean; // 关闭所有节点所有位置的内容条
    clearNodeBar: (keys?: string[]) => boolean; // 关闭指定节点所有位置的内容条
    clearPositionBar: (position: AppPluginAPI.BarPosition) => boolean; // 关闭所有节点指定位置的内容条
    setSlotComponent: (props: React.ReactNode) => void; // 设置插槽内容, 基座提供了一个插槽, 用于放置插件全局的Modal, Notification等组件
    toggleResourceBindButton: (show: boolean) => void;// 是否展示查看绑定资源按钮（开启后，左键单击节点会在节点右侧展示操作栏，提供【查看资源】按钮）
    env: 'CONSOLE' | 'ISA';// 当前运行环境，CONSOLE-租户端（腾讯云控制台），ISA-运营端（内网环境）
    shapeCategoryList: ShapeCategory[]; // 画布支持的所有图元列表
    archInfo: IArchInfo; // 架构图元数据
    getNodeList: () => IResourceList[]; // 架构图nodeList建议都通过该方法来获取最新nodeList,因为初始架构图会轮询任务检查是否有失效节点
    userName: string; // 当前登录用户名
    getPluginSessionId: () => string; // 插件当前使用的sessionId，用于上报数据
    extraParams: any; // 插件启动时传入的参数
  }

  // 垂直应用SDK对象
  export interface SDK {
    init: (api: PluginAPI) => SDKCallbacks; // 初始化方法，加载垂直应用sdk时调用
    destroy: () => boolean; // 销毁方法，卸载垂直应用sdk时调用
  }
}
