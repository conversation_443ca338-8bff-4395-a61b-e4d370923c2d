腾讯云控制台 CAPACITY-MONITORING 前端 SDK 项目
======================================

![当前版本](https://img.shields.io/badge/当前版本-alpha-green.svg)

查看[发布日志](./CHANGELOG.md)。

---

本 SDK 为腾讯云控制台业务业务提供 CAPACITY-MONITORING 能力。

> TODO：提供具体的能力说明/应用场景

---

## 使用方法

### 在 Tea 项目中引入

如果业务的控制台项目是使用 Tea 框架生成的，采取下面的方式引入：

```js
import { app, SDKLoader } from '@tencent/tea-app';
import { LoadingTip, ErrorTip } from "@tencent/tea-component";

async function () {
  const capacityMonitoring = await app.sdk.use('capacity-monitoring-sdk');
  // 通过 capacityMonitoring 调用 API
  console.log(capacityMonitoring.hello());
}

function Component() {
  return (
    <SDKLoader
      sdkNames={["capacity-monitoring-sdk"]}
      loading={<LoadingTip />}
      error={(error, retry) => <ErrorTip onRetry={retry} />}
    >
      {([{ SDKComponent }]) => <SDKComponent message="" />}
    </SDKLoader>
  );
}
```

### 传统控制台 JS 项目引入

如果业务还没有升级到 Tea 框架中，则可以通过 seajs 的方式来引入 SDK：

```js
const sdk = seajs.require('sdk');

async function () {
  const capacityMonitoring = await sdk.use('capacity-monitoring-sdk');
  // 通过 capacityMonitoring 调用 API
  console.log(capacityMonitoring.hello());
}
```

---

## API

> TODO：补充具体 API 的使用说明

### `capacityMonitoringSDK.hello()`

SDK 测通方法，输出一个 Hello 字符串。

```js
capacityMonitoringSDK.hello();
```

### 


## 类型定义

```
npm i @tencent/tea-sdk-capacity-monitoring-types --registry=http://r.tnpm.oa.com --proxy=http://127.0.0.1:12639
```

```tsx
type SDK = typeof import("@tencent/tea-sdk-capacity-monitoring-types");
```

---

## 开发

### 开发工具

本项目依赖 tea-cli 进行工程化开发，请先安装。

```
npm i @tencent/tea-cli -g --registry=http://r.tnpm.oa.com --proxy=http://127.0.0.1:12639
```

### 本地开发

使用 tea dev 命令启动本地开发服务器：

```
tea dev
```

然后需要使用 Whistle 代理本地开发，代理规则如下：

```
/\/qcloud\/tea\/app\/([\w-]+)(\.(zh|en|jp|ko|dev))?(\.\w{10})?\.(js|css)(\.map)?/ http://127.0.0.1:8322/$1.$5$6
/\/qcloud\/tea\/app\/(.+\/)?__webpack_hmr$/ http://127.0.0.1:8322/__webpack_hmr
/\/qcloud\/tea\/app\/(.+\.)?(\w+)\.(hot-update\.(js|json))$/ http://127.0.0.1:8322/$1$2.$3
```

完整的原理和流程可以参考 http://tapd.oa.com/tcp_access/markdown_wikis/view/#1020399462010220759

### 组件

使用 Tea 组件进行开发，文档地址：http://tea.tencent.com/component

### 接口

跟控制台交互的部分，使用 tea-app 进行，文档地址：http://tea.tencent.com/app

### 构建

```
tea build
```

### 提交版本

提交版本前请先构建。

```
tea commit -m '修改说明'
```

提交后到 Buffet 系统发布即可。

### 类型定义构建/发布

```
npm run build-types
```

```
npm run publish-types
```

### 目录说明

- `src/sdk.ts` SDK 入口文件，对外导出 SDK API
- `src/components` 存放使用到的组件
- `src/utils` 存放公共工具方法
