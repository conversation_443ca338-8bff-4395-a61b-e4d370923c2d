/* eslint-disable no-unused-vars */
import _, { max } from 'lodash';
import store, { SET_ROOT_ACTION } from '../store';
import { SIGMA_GRAPH_MODE_3D, EXCLUDE_PROPERTYS } from '../util/constants';
import { clearConnectLineToolBox, createConnectLineToolBox } from '../util/toolbox';
import renders from '../renders';
export class CmdStack {
  undoList = []; // 用来进行撤销的历史记录
  redoList = []; // 用来进行重做的历史记录
  listeners = {}; // 用来存储撤销回退变化后的监听函数

  cmdGroupCache = {
    type: 'groupCmd',
    cmdGroup: [],
  };

  cmdGroupFlag = false;
  maxCmdGroupCache = {
    type: 'groupCmd',
    cmdGroup: [],
  };
  maxCmdGroupFlag = false;
  tbVnode = null;

  isShowToolBox(v) {
    const vnodes = Array.isArray(v) ? v : [v];
    return this.tbVnode && vnodes.some(({ key }) => key === this.tbVnode.key);
  }
  checkParentSticky(core, v) {
    const vnodes = Array.isArray(v) ? v : [v];
    const { shapes } = core.data;
    for (const { attach, key } of vnodes) {
      if (!attach) continue;
      const parent = shapes[attach];
      if (!parent) continue;
      if (!parent.sticky) parent.sticky = key;
    }
  }

  onCmdListChange = () => {
    const status = this.getUndoStatus();
    Object.values(this.listeners).forEach((fn) => {
      fn(status);
    });
  };
  /**
   * 推送命令到存储栈
   * @param {any} step
   * @param {object} opt
   * @param {boolean} opt.groupBegin 命令分组开始，传入这个后后面必须传入一次groupEnd来结束命令分组
   * @param {boolean} opt.groupEnd 命令分组结束，groupBegin到groupEnd的命令撤销时会一次执行
   * @param {boolean} opt.isSub 命令需要合并到前一个命令，成组，撤销时会一次执行
   */
  pushUndo = (step, opts = {}) => {
    const { groupBegin, groupEnd, isSub, maxGroupBegin, maxGroupEnd } = opts;
    const { undoList, redoList } = this;

    if (!this.cmdGroupFlag && isSub && undoList.length > 0) {
      let groupCmd = {};
      const prevCmd = undoList.pop();
      if (prevCmd.type === 'groupCmd') {
        groupCmd = prevCmd;
      } else {
        groupCmd = {
          type: 'groupCmd',
          cmdGroup: [prevCmd],
        };
      }
      groupCmd.cmdGroup.push(step);
      undoList.push(groupCmd);
      redoList.length = 0; // 新操作来了后清空redo历史记录
      return;
    }

    if (maxGroupBegin) {
      this.maxCmdGroupFlag = true;
      return;
    }

    if (groupBegin === true) {
      this.cmdGroupFlag = true;
    }

    if (this.cmdGroupFlag === true && !this.maxCmdGroupFlag) {
      this.cmdGroupCache.cmdGroup.push(step);
    }

    if (groupEnd === true && !this.maxCmdGroupFlag) {
      this.cmdGroupFlag = false;
      undoList.push(_.clone(this.cmdGroupCache));
      this.cmdGroupCache = {
        type: 'groupCmd',
        cmdGroup: [],
      };
      redoList.length = 0; // 新操作来了后清空redo历史记录
      this.onCmdListChange();
      return;
    }

    if (groupEnd === true && this.maxCmdGroupFlag) {
      this.cmdGroupCache.cmdGroup.push(step);
      this.cmdGroupFlag = false;
    }

    if (this.maxCmdGroupFlag) {
      if (this.cmdGroupFlag) {
        this.cmdGroupCache.cmdGroup.push(step);
      } else {
        if (this.cmdGroupCache.cmdGroup.length > 0) {
          this.maxCmdGroupCache.cmdGroup.push(this.cmdGroupCache);

          this.cmdGroupCache = {
            type: 'groupCmd',
            cmdGroup: [],
          };
          redoList.length = 0; // 新操作来了后清空redo历史记录
        } else {
          this.maxCmdGroupCache.cmdGroup.push(step);
        }
      }
    }

    if (maxGroupEnd === true) {
      this.maxCmdGroupFlag = false;
      undoList.push(_.cloneDeep(this.maxCmdGroupCache));
      this.maxCmdGroupCache = {
        type: 'groupCmd',
        cmdGroup: [],
      };
      redoList.length = 0; // 新操作来了后清空redo历史记录
      this.onCmdListChange();
      return;
    }

    if (this.cmdGroupFlag === true) {
      return;
    }

    if (this.maxCmdGroupFlag === true) {
      return;
    }

    undoList.push(step);
    redoList.length = 0; // 新操作来了后清空redo历史记录
    this.onCmdListChange();
  };

  // 存储提供给外部的SetState操作
  saveUndoableState = (newState, oldState, setState) => {
    const data = {
      type: 'undoableState',
      data: newState,
      oldData: oldState,
      setState,
    };
    this.pushUndo(data);
  };

  // 存储提供给外部的完全自定义的操作命令
  saveUndoableCmd = (cmd) => {
    const data = { type: 'undoableCmd', cmd };
    this.pushUndo(data);
  };

  // 获取undo操作
  getUndo = () => {
    const { undoList, redoList } = this;
    if (undoList?.length > 0) {
      const lastAction = undoList.pop();
      redoList.push(lastAction);
      this.onCmdListChange();
      return _.cloneDeep(lastAction);
    }
  };

  // 获取redo操作
  getRedo = () => {
    const { undoList, redoList } = this;
    if (redoList.length > 0) {
      const lastAction = redoList.pop();
      undoList.push(lastAction);
      this.onCmdListChange();
      return _.cloneDeep(lastAction);
    }
  };

  redo = (core) => {
    const { mode, uneditable } = store.getState();
    if (uneditable) return;
    const doRedo = (cmdData) => {
      const { type, updateType, data, oldData, setState, cmd, cmdGroup } = cmdData;
      const is3D = mode === SIGMA_GRAPH_MODE_3D;
      core.allShapeNotChecked();
      switch (type) {
        case 'add': // 下一步是新增，重做的时候需要新增该节点
          // 有oldData说明这步是替换，使用替换操作
          if (oldData.length === 1) {
            const fixedShapes = is3D ? data : core.transform(data);
            core.replace(fixedShapes[0], {
              _historical: false,
              needCheckType: false,
            });
            core._setShapeChecked(fixedShapes, true);
            fixedShapes?.forEach((shape) => {
              if (shape?.groups) {
                const groupKeys = Object.keys(shape?.groups);
                groupKeys.forEach((groupKey) => {
                  const group = core?.data?.shapes?.[groupKey];
                  group?.component?.fire('rank-change');
                  group?.component?.fire('size-change');
                });
              }
            });
          } else if (data.length > 0) {
            const fixedShapes = is3D ? data : core.transform(data);
            this.checkParentSticky(core, fixedShapes);
            core.add(fixedShapes, { _historical: false, isHistoryAction: true });
            core._setShapeChecked(fixedShapes, true);
            fixedShapes?.forEach((shape) => {
              if (shape?.groups) {
                const groupKeys = Object.keys(shape?.groups);
                groupKeys.forEach((groupKey) => {
                  const group = core?.data?.shapes?.[groupKey];
                  group?.component?.fire('rank-change');
                  group?.component?.fire('size-change');
                });
              }
            });
          }
          break;
        case 'update': // 下一步是修改，重做的时候需要恢复成新的数据
          core._updateNodes(data, updateType);
          data?.forEach((shape) => {
            if (shape?.groups) {
              const groupKeys = Object.keys(shape?.groups);
              groupKeys.forEach((groupKey) => {
                const group = core?.data?.shapes?.[groupKey];
                group?.component?.fire('rank-change');
                group?.component?.fire('size-change');
              });
            }
          });
          break;
        case 'delete': // 下一步是删除，重做的时候需要删除该节点
          store.dispatch({
            type: SET_ROOT_ACTION,
            value: {
              willClearWhenGroupDeleted: false,
            },
          });
          const realShapes = core._linkUndoNodesInData(data);
          core.remove(realShapes, { _historical: false });
          store.dispatch({
            type: SET_ROOT_ACTION,
            value: {
              willClearWhenGroupDeleted: true,
            },
          });
          break;
        case 'setStyles': // 上一步是样式修改，撤销的时候需要恢复该节点
          const { keys, style } = data[0];
          core.setStyles(style, keys, { _historical: false });
          const nodes = keys.map(key => core.data.shapes[key]);
          core._setShapeChecked(nodes, true);
          break;
        case 'undoableState': // 上一步是可撤状态的变化，恢复新的状态即可
          setState(data);
          break;
        case 'undoableCmd': // 上一步是可撤销命令，执行execute方法
          cmd?.execute?.();
          break;
        case 'groupCmd': // 上一步多步操作打包的组，一起都执行了
          cmdGroup.forEach(c => doRedo(c));
          break;
        default:
          break;
      }
    };
    const res = this.getRedo();
    if (res) {
      window.clearMouseLineEvents && window.clearMouseLineEvents();
      clearConnectLineToolBox();
      doRedo(res);
      core.lines.clear();
    }
  };

  undo = (core) => {
    const { mode, uneditable } = store.getState();
    if (uneditable) return;
    const doUndo = (cmdData) => {
      const { type, updateType, data, oldData, setState, cmd, cmdGroup } = cmdData;
      const is3D = mode === SIGMA_GRAPH_MODE_3D;
      core.allShapeNotChecked();
      switch (type) {
        case 'add': // 上一步是新增，撤销的时候需要删除该节点
          // 说明这步是替换，，使用替换操作
          if (oldData.length === 1) {
            const fixedOldShapes = is3D ? oldData : core.transform(oldData);
            core.replace(fixedOldShapes[0], {
              _historical: false,
              needCheckType: false,
            });
            core._setShapeChecked(fixedOldShapes, true);
          } else if (data.length > 0) {
            const realNodes = core._linkUndoNodesInData(data);
            core.remove(realNodes, { _historical: false });
          }
          break;
        case 'update': // 上一步是修改，撤销的时候需要恢复成旧的数据
          core._updateNodes(oldData, updateType);
          break;
        case 'delete': // 上一步是删除，撤销的时候需要恢复该节点
          const fixedPositionShape = is3D ? data : core.transform(data);
          this.checkParentSticky(core, fixedPositionShape);
          core.add(fixedPositionShape, { _historical: false });
          core._setShapeChecked(fixedPositionShape, true);
          if (this.isShowToolBox(fixedPositionShape)) {
            createConnectLineToolBox(core, this.tbVnode);
          }
          setTimeout(() => {
            fixedPositionShape?.forEach((shape) => {
              const group = core?.data?.shapes?.[shape?.key];
              group?.component?.fire('rank-change');
              group?.component?.fire('size-change');
              if (shape?.groups) {
                const groupKeys = Object.keys(shape?.groups);
                groupKeys.forEach((groupKey) => {
                  const group = core?.data?.shapes?.[groupKey];
                  group?.component?.fire('rank-change');
                  group?.component?.fire('size-change');
                });
              }
              if (shape?.relations) {
                const relationsKeys = Object.keys(shape?.relations);
                relationsKeys.forEach((relationsKey) => {
                  const group = core?.data?.shapes?.[relationsKey];
                  group?.component?.fire('rank-change');
                  group?.component?.fire('size-change');
                });
              }
            });
          }, (0));
          break;
        case 'setStyles': // 上一步是样式修改，撤销的时候需要恢复该节点
          const { keys, style } = oldData[0];
          core.setStyles(style, keys, { _historical: false });
          const nodes = keys.map(key => core.data.shapes[key]);
          core._setShapeChecked(nodes, true);
          break;
        case 'undoableState': // 上一步是可撤状态的变化，恢复旧的状态即可
          setState(oldData);
          break;
        case 'undoableCmd': // 上一步是可撤销命令，执行undo方法
          cmd?.undo?.();
          break;
        case 'groupCmd': // 上一步多步操作打包的组，一起都执行了,需要从后向前执行
          cmdGroup.reverse().forEach(c => doUndo(c));
          break;
        default:
          break;
      }
    };
    const res = this.getUndo();
    if (res) {
      window.clearMouseLineEvents && window.clearMouseLineEvents();
      clearConnectLineToolBox();
      doUndo(res);
      core.lines.clear();
    }
  };

  /**
   * @typedef {Object} ActionOption
   * @property {string} type - 类型 'add' | 'delete' | 'update' | 'setStyles'
   * @property {string | string[]} updateType - 更新类型 更新节点时具体的更新类型-也就是触发的事件类型
   * @property {nodes[]} data - 新的节点数据
   * @property {nodes[]} oldData - 新的节点数据
   * @property {boolean} groupBegin - 一组操作开始, 成组的操作会撤销重做时会按顺序一起执行
   * @property {boolean} groupEnd - 一组操作结束
   * @property {boolean} isSub - 是否子命令，子命令会并到前一个命令，一起撤销重做
   * @property {boolean} maxGroupBegin - 大组操作开始，既里面可能还包括groupBegin groupEnd的操作
   * @property {boolean} maxGroupEnd - 一大组操作结束
   *
   * 操作历史存储， 仅存储 2.5D 模式下的数据，如果当前是 2D 那么需要转换后存储
   *@param {ActionOption} option - 参数
   */
  saveAction = ({
    type = 'update',
    updateType,
    data = [],
    oldData = [],
    ...opts
  }) => {
    if (type === 'setStyles') {
      const step = { type, updateType, data, oldData };
      this.pushUndo(_.cloneDeep(step), opts);
      return;
    }
    const { mode } = store.getState();
    const is3D = mode === SIGMA_GRAPH_MODE_3D;
    const list = [];
    const oldList = [];
    for (const vnode of data) {
      const v = _.cloneDeep(vnode);
      EXCLUDE_PROPERTYS.forEach((item) => {
        delete v[item];
      });
      v.isChecked = false;
      list.push(is3D ? v : renders.reduction(v));
    }
    for (const vnode of oldData) {
      const v = _.cloneDeep(vnode);
      EXCLUDE_PROPERTYS.forEach((item) => {
        delete v[item];
      });
      v.isChecked = false;
      oldList.push(is3D ? v : renders.reduction(vnode));
    }
    oldData = null;
    const step = { type, updateType, data: list, oldData: oldList };
    this.pushUndo(_.cloneDeep(step), opts);
  };

  // 调整删除操作内容，排序元素，以便能正常生成
  processDeleteCmd = (core) => {
    const { undoList } = this;
    const lastCmd = undoList[undoList.length - 1];
    if (lastCmd && lastCmd.type === 'groupCmd' && lastCmd.cmdGroup.find(it => it.type === 'delete')) {
      const deletedNodes = [];
      const newCmdGroup = [];
      lastCmd.cmdGroup.forEach((subCmd) => {
        if (subCmd.type === 'delete') {
          deletedNodes.push(...subCmd.data);
        } else {
          newCmdGroup.push(subCmd);
        }
      });
      if (deletedNodes.length > 0) {
        newCmdGroup.push({
          type: 'delete',
          data: core._getSortedShapes(deletedNodes, 'history'),
        });
        deletedNodes.length = 0;
      }
      lastCmd.cmdGroup = newCmdGroup;
    }
  };

  // 清空撤销重做记录
  clearCmd = () => {
    this.undoList = [];
    this.redoList = [];
    this.onCmdListChange();
  };

  /**
   * 将react中的setState操作包装成可撤销的；
   * @param {setState} react.dispatch 修改数据的操作
   * @returns setStateWithUndoable
   */
  undoableSetState = (setState) => {
    const setStateWithUndoable = (state) => {
      setState((oldState) => {
        this.saveUndoableState(state, oldState, setState);
        return state;
      });
    };

    return setStateWithUndoable;
  };
  /**
   * 推送由开发者完全自定义的操作命令
   * @param {cmd} 某一步操作的命令对象, 必须包含run、undo、redo 方法 { execute(){}, undo(){} }
   * @param {cmd.execute} function 重做方法
   * @param {cmd.undo} function 撤销方法
   * @returns cme.run()结果
   */
  pushCmd = (cmd) => {
    if (!_.isObject(cmd)) {
      throw new Error('cmd 必须是对象');
    }
    if (!['execute', 'undo'].every(fn => _.isFunction(cmd[fn]))) {
      throw new Error('cmd 必须定义execute、undo方法');
    }
    this.saveUndoableCmd(cmd);
    this.onCmdListChange();
    return cmd.execute();
  };

  /**
   * 获取撤销重做列表情况
   * @returns {res} object
   * @returns {res.undoSteps} number 有多少可撤销步骤
   * @returns {res.redoSteps} number 有多少可重做步骤
   * @returns {res.hasUndo} number 是否可以撤销
   * @returns {res.hansRedo} number 是否可重做
   */
  getUndoStatus = () => {
    const undoSteps = this.undoList.length;
    const redoSteps = this.redoList.length;

    return {
      undoSteps,
      redoSteps,
      hasUndo: undoSteps > 0,
      hasRedo: redoSteps > 0,
    };
  };

  index = 0;

  /**
   * 添加撤销回退列表变化后的监听函数
   * @param {listener} 监听函数
   * @returns {removeListenerFn} 撤销监听的函数，调用后撤销监听
   */
  addListener = (listener) => {
    if (_.isFunction(listener)) {
      const key = `l${this.index}`;
      this.index += 1;
      this.listeners[key] = listener;
      return () => {
        this.listeners[key] = null;
        delete this.listeners[key];
      };
    }
    throw new Error('sigma undo addListener 的参数必须是function');
  };
}

const cmdStack = new CmdStack();
export default cmdStack;
