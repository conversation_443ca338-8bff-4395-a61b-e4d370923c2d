import { v4 as uuid } from 'uuid';
import store from '../../store';
import { SVG } from '@svgdotjs/svg.js';

const WRAPPER_CLASSNAME = 'sticky-units';

class VnodeStickyUnit {
  constructor({
    vnode,
    offset = { x: 0, y: 0 },
    child,
    element = 'foreignObject',
    width,
    height,
    ...attr
  }) {
    this.id = uuid();
    this.vnode = vnode;
    this.offset = offset;
    this.child = child;
    this.element = element;
    this.width = width || vnode.width;
    this.height = height || vnode.height;
    this.attr = attr;

    this._init();
  }

  _wrapper = null;
  _element = null;
  _main = null;

  _init() {
    const { id, vnode, offset, child, element, width, height, attr } = this;
    const { position: { x, y } } = vnode;
    const { x: ox, y: oy } = offset;

    this._initWrapper();
    this._element = new SVG().attr({
      class: 'unit',
      id,
      x: x + ox,
      y: y + oy,
    });
    this._wrapper.add(this._element);
    this._main = this._element.element(element).attr({
      width,
      height,
      overflow: 'visible',
      ...attr,
    });
    this._main.add(SVG(child));

    this._bindEvent();

    if (!vnode.stickyUnits) vnode.stickyUnits = {};
    vnode.stickyUnits[this.id] = null;
    vnode.stickyUnits[this.id] = this;
  }

  _bindEvent() {
    const { vnode } = this;
    const events = ['position-change.unit', 'style-changed.unit'];
    vnode.component.off(events);
    vnode.component.on(events, () => {
      this._positionChange();
    });

    vnode.component.off('deleted.unit');
    vnode.component.on('deleted.unit', () => {
      this.remove();
    });
  }

  _positionChange() {
    const { vnode, offset } = this;
    const { position: { x, y } } = vnode;
    const { x: ox, y: oy } = offset;
    this._element.attr({
      x: x + ox,
      y: y + oy,
    });
  }

  updateChild(child) {
    this.child = child;
    this._main.clear();
    this._main.add(SVG(child));
  }

  updateOffset(offset) {
    this.offset = offset;
    this._positionChange();
  }

  remove() {
    this._element.remove();
    delete this.vnode.stickyUnits[this.id];
  }

  _initWrapper() {
    const { container: { gCache } } = store.getState();
    this._wrapper = gCache.findOne(`.${WRAPPER_CLASSNAME}`);
    if (!this._wrapper) {
      this._wrapper = gCache.group().attr({ class: WRAPPER_CLASSNAME });
    }

    this._wrapper.front();
  }
}

export {
  VnodeStickyUnit,
};
