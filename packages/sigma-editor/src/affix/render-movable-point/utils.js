import { SVG } from '@svgdotjs/svg.js';
import { MovablePoint } from '.';
import store from '../../store';
import { getValidConnection } from '../../util/connections';
import { CIRCLE_CLASS_PREFIX, MOVABLE_POINTS_CLASS, SIGMA_PRODUCT_SHAPE } from '../../util/constants';

let memo = null;
// isUpdate 为true，更新 memo，memo不为空，直接使用memo，memo为空使用v
export const setMovablePoints = (core, v, isUpdate, is3D) => {
  const isMovingLine = core._getMovingLineShape();
  if (!isMovingLine) return;

  let vnode;
  if (isUpdate) {
    memo = v;
    vnode = memo;
  } else if (!memo) {
    vnode = v;
  } else {
    vnode = memo;
  }
  const { container: { gCache } } = store.getState();
  const { connectable, position: { x, y } } = vnode;
  clearMovablePoints();
  // 不可连接的 node
  if (!connectable) return;
  // if (isMovingLine.data.start.vkey === vnode.key) return;

  const wrapper = new SVG().attr({ x, y, overflow: 'visible', class: MOVABLE_POINTS_CLASS });
  gCache.add(wrapper);
  gCache.remember(MOVABLE_POINTS_CLASS, wrapper);

  const c = getValidConnection(vnode, isMovingLine?.styles?.lineType?.value);
  const offset = 10;
  const aps = [
    [c.t0.x - offset, c.t0.y - offset],
    [c.r0.x + offset, c.r0.y - offset],
    [c.b0.x + offset, c.b0.y + offset],
    [c.l0.x - offset, c.l0.y + offset],
  ];
  wrapper.polygon(aps).attr({
    fill: 'none',
    'stroke-width': offset * 2,
    stroke: 'transparent',
  });

  for (const k in c) {
    const position = c[k];
    const point = new MovablePoint({
      key: vnode.key,
      position,
      className: `${CIRCLE_CLASS_PREFIX}${k}`,
      direction: k,
      isPolyline: isMovingLine?.styles?.lineType?.value === 'polyline',
      is3D,
      isProduct: vnode?.type === SIGMA_PRODUCT_SHAPE,
    });
    wrapper.add(point.element);
  }
};

export const clearMovablePoints = () => {
  const { container: { gCache } } = store.getState();
  memo = null;
  const wrapper = gCache.remember(MOVABLE_POINTS_CLASS);
  if (!wrapper) return;
  wrapper?.remove();
  gCache.forget(MOVABLE_POINTS_CLASS);
};
