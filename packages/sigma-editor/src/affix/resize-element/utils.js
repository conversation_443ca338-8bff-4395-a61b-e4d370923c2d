import store from '../../store';
import { SIGMA_GRAPH_MODE_2D, SIGMA_GRAPH_MODE_3D, BASE_HALF_GRID_2D } from '../../util/constants';
import { getStylesVal, transform2DTo3D } from '../../util/tools';

export const RESIZE_CLASSNAME = 'resize-points';
export const RESIZE_DIRECTION = ['top', 'top-right', 'right', 'right-bottom', 'bottom', 'bottom-left', 'left', 'left-top'];

export function getResizePoints(width, height, is3D, startPoint = { x: 0, y: 0 }) {
  const { x, y } = startPoint;
  const points = {
    [RESIZE_DIRECTION[0]]: {
      position: { x: width / 2, y: 0 },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'n-resize', [SIGMA_GRAPH_MODE_3D]: 'ne-resize' },
    },
    [RESIZE_DIRECTION[1]]: {
      position: { x: width, y: 0 },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'ne-resize', [SIGMA_GRAPH_MODE_3D]: 'e-resize' },
    },
    [RESIZE_DIRECTION[2]]: {
      position: { x: width, y: height / 2 },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'e-resize', [SIGMA_GRAPH_MODE_3D]: 'se-resize' },
    },
    [RESIZE_DIRECTION[3]]: {
      position: { x: width, y: height },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'se-resize', [SIGMA_GRAPH_MODE_3D]: 's-resize' },
    },
    [RESIZE_DIRECTION[4]]: {
      position: { x: width / 2, y: height },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 's-resize', [SIGMA_GRAPH_MODE_3D]: 'sw-resize' },
    },
    [RESIZE_DIRECTION[5]]: {
      position: { x: 0, y: height },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'sw-resize', [SIGMA_GRAPH_MODE_3D]: 'w-resize' },
    },
    [RESIZE_DIRECTION[6]]: {
      position: { x: 0, y: height / 2 },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'w-resize', [SIGMA_GRAPH_MODE_3D]: 'nw-resize' },
    },
    [RESIZE_DIRECTION[7]]: {
      position: { x: 0, y: 0 },
      cursor: { [SIGMA_GRAPH_MODE_2D]: 'nw-resize', [SIGMA_GRAPH_MODE_3D]: 'n-resize' },
    },
  };

  for (const key in points) {
    const element = points[key];
    element.position.x += x;
    element.position.y += y;

    if (is3D) element.position = transform2DTo3D(element.position, true);
  }

  return points;
}

export function formatRectPoints(vnode) {
  const is3D  = store.getState().mode === SIGMA_GRAPH_MODE_3D;
  const { data, styles } = vnode;
  const { points } = data;
  const radius = getStylesVal(styles.borderRadius);
  const offsets = {
    'left-top': [{ x: 0, y: radius }, { x: radius, y: 0 }],
    'top-right': [{ x: -radius, y: 0 }, { x: 0, y: radius }],
    'right-bottom': [{ x: 0, y: - radius }, { x: - radius, y: 0 }],
    'bottom-left': [{ x: radius, y: 0 }, { x: 0, y: - radius }],
  };
  return Object.entries(points).reduce((acc, [key, { position: { x, y } }]) => {
    if (offsets[key]) {
      const [o1, o2] = offsets[key];
      const t1 = is3D ? transform2DTo3D(o1, true) : o1;
      const t2 = is3D ? transform2DTo3D(o2, true) : o2;
      acc += `${x + t1.x},${y + t1.y} ${x + t2.x},${y + t2.y} `;
    }
    return acc;
  }, '');
}

/**
 * 检查组的大小有没有全部覆盖innerVnodes
 * @param {*} group
 * @param {*} innerVnodes
 */
export function checkGroupSize(group, innerVnodes) {
  const buffer = 10;
  const { x, y, w, h } = group;
  const rightBottom = { // 组右下角坐标
    x: x + w,
    y: y + h,
  };
  return innerVnodes.every((node) => {
    const { x: vx, y: vy, w: vw, h: vh } = node;
    const vRightBottom = { // 子元素右下角坐标
      x: vx + vw,
      y: vy + vh,
    };
    const result = (vx - BASE_HALF_GRID_2D >= x || Math.abs(vx - BASE_HALF_GRID_2D - x) < buffer)
    && (vRightBottom.x + BASE_HALF_GRID_2D <= rightBottom.x || Math.abs(vRightBottom.x + BASE_HALF_GRID_2D - rightBottom.x) < buffer)
    && (vy - BASE_HALF_GRID_2D >= y || Math.abs(vy - BASE_HALF_GRID_2D - y) < buffer)
    && (vRightBottom.y + BASE_HALF_GRID_2D <= rightBottom.y  || Math.abs(vRightBottom.y + BASE_HALF_GRID_2D - rightBottom.y) < buffer);
    return result;
  });
}

/**
 * 判断tke组缩小当临界点后， 再放大时鼠标是否超过临界点
 * @param {*} dragPoint
 * @param {*} movePoint
 * @param {*} direction
 */
export function checkGroupBoundary(dragPoint, movePoint, direction) {
  let flag = false;
  const { x: dx, y: dy } = dragPoint;
  const { x: mx, y: my } = movePoint;
  if (direction === 'left' && mx < dx) {
    flag = true;
  }

  if (direction === 'top' && my < dy) {
    flag = true;
  }

  if (direction === 'right' && mx > dx) {
    flag = true;
  }

  if (direction === 'bottom' && my > dy) {
    flag = true;
  }

  if (direction === 'left-top' && (mx < dx || my < dy)) {
    flag = true;
  }

  if (direction === 'top-right' && (mx > dx || my < dy)) {
    flag = true;
  }

  if (direction === 'right-bottom' && (mx > dx || my > dy)) {
    flag = true;
  };

  if (direction === 'bottom-left' && (mx < dx || my > dy)) {
    flag = true;
  }

  return flag;
}
