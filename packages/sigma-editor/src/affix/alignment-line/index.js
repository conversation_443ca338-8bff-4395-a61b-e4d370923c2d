import { SVG } from '@svgdotjs/svg.js';
import { transform2DTo3D, transform3DTo2D } from '../../util/tools';
import store, { ALIGNMENT_LINES_ADD, ALIGNMENT_LINES_SORT } from '../../store';
import { SIGMA_GRAPH_MODE_2D } from '../../util/constants';
import { ALIGNMENT_LINE_CLASSNAME, getLinearEquation, getTargetLineProjection, searchLineBoundary } from './utils';
const ALIGNMENTS_LINE_MIN_DISTANCE = 5;

class AlignmentLines {
  constructor() {
    this.min = ALIGNMENTS_LINE_MIN_DISTANCE;
    this.className = ALIGNMENT_LINE_CLASSNAME;
  }

  add(
    vnode,
    is3D,
    isFlat,
    startPoint,
  ) {
    const { width, height } = vnode;
    const lines = this.getPlaneAlignmentLines(
      vnode.position,
      width,
      height,
      is3D,
      isFlat,
      startPoint,
    );

    store.dispatch({
      type: ALIGNMENT_LINES_ADD,
      value: [vnode.key, lines],
    });
  }

  /**
   * 对参考线进行合并去重排序，mousedown 的时候触发
   */
  sort(willUpdateNodes) {
    if (!(willUpdateNodes && willUpdateNodes.length > 0)) return;
    const willKeys = willUpdateNodes.map(({ key }) => key);
    const { alignments } = store.getState();

    function traversal(acc, list, dir) {
      list.forEach((item) => {
        const { k, b } = item;
        const id = `${k}${b}`;
        const current = acc[dir][id];
        // 合并、去重、内部排序
        if (current) {
          let newLines = item.line;
          newLines = [...new Set([...current.line.map(i => JSON.stringify(i)), ...item.line.map(i => JSON.stringify(i))])].map(i => JSON.parse(i));
          newLines.sort((a, b) =>  a[dir] - b[dir]);
          current.line = newLines;
        } else {
          acc[dir][id] = item;
        }
      });
    }

    const list = [...alignments.lines.entries()].reduce((acc, [key, { x, y }]) => {
      if (willKeys.includes(key)) return acc;
      traversal(acc, x, 'x');
      traversal(acc, y, 'y');

      return acc;
    }, {
      x: {},
      y: {},
    });

    // 外部排序
    const xLines = Object.values(list.x);
    const yLines = Object.values(list.y);
    xLines.sort((a, b) => a.b - b.b);
    yLines.sort((a, b) => a.b - b.b);
    store.dispatch({
      type: ALIGNMENT_LINES_SORT,
      value: {
        x: xLines,
        y: yLines,
      },
    });
  }

  /**
   * 获取平面元素，如 image icon text 等元素的延长线坐标
   */
  getPlaneAlignmentLines(
    position,
    width,
    height,
    is3D,
    isFlat,
    startPoint = { x: -width / 2, y: - height },
  ) {
    const { mode } = store.getState();
    const { x: posX, y: posY } = mode === SIGMA_GRAPH_MODE_2D ? position : transform3DTo2D(position);

    let { x: startX, y: startY } = startPoint;
    startX += posX;
    startY += posY;
    let lines;

    // 顺序是 上 右 下 左
    const keyPoints = [
      { x: startX, y: startY },
      { x: startX + width / 2, y: startY },
      { x: startX + width, y: startY },
      { x: startX + width, y: startY + height / 2 },
      { x: startX + width, y: startY + height },
      { x: startX + width / 2, y: startY + height },
      { x: startX, y: startY + height },
      { x: startX, y: startY + height / 2 },
    ];

    // x 为横轴，y为竖轴
    const d2Lines = {
      x: [
        [keyPoints[0], keyPoints[2]],
        [keyPoints[7], keyPoints[3]],
        [keyPoints[6], keyPoints[4]],
      ],
      y: [
        [keyPoints[0], keyPoints[6]],
        [keyPoints[1], keyPoints[5]],
        [keyPoints[2], keyPoints[4]],
      ],
    };

    const d3Lines = {
      x: d2Lines.x.map(i => i.map(j => transform2DTo3D(j))),
      y: d2Lines.y.map(i => i.map(j => transform2DTo3D(j))),
    };

    if (!is3D) {
      lines = d2Lines;
    } else if (isFlat) {
      lines = {
        x: [d3Lines.x[2]],
        y: d3Lines.y,
      };
    } else {
      lines = d3Lines;
    }

    return {
      x: lines.x.map((line) => {
        const { k, b } = getLinearEquation(...line);
        return { line, k, b };
      }),
      y: lines.y.map((line) => {
        const { k, b } = getLinearEquation(...line);
        return { line, k, b };
      }),
    };
  }

  // 获取相邻的平行线
  getRelationLines(tkey) {
    const { alignments } = store.getState();
    const currentLines = alignments.lines.get(tkey);
    const { sortX, sortY } = alignments;
    // 剔除全局变量中与当前组件延长线斜率 k 不相等的线
    const xLines = sortX.filter(({ k }) => k === currentLines.x[0].k);
    const yLines = sortY.filter(({ k }) => k === currentLines.y[0].k);

    const xs = []; const ys = [];
    // 遍历该组件的横向和纵向六条延长线
    for (let i = 0; i < currentLines.x.length; i++) {
      const current = currentLines.x[i];
      // 二分查找找出邻近的延长线
      const target = searchLineBoundary(current, xLines, this.min, 'x');
      if (!target) continue;
      const [left, right] = target;
      for (let j = left; j <= right; j++) {
        const element = xLines[j];
        const { line, k, b } = element;
        // 获取投影的点
        const [p1, p2] = getTargetLineProjection(current.line, k, b, 'x');
        const points = [p1, p2, ...line].sort((a, b) => a.x - b.x);
        xs.push(points);
      }
    }

    for (let i = 0; i < currentLines.y.length; i++) {
      const current = currentLines.y[i];
      // 二分查找找出邻近的延长线
      const target = searchLineBoundary(current, yLines, this.min, 'y');
      if (!target) continue;
      const [left, right] = target;
      for (let j = left; j <= right; j++) {
        const element = yLines[j];
        const { line, k, b } = element;
        // 获取投影的点
        const [p1, p2] = getTargetLineProjection(current.line, k, b, 'y');
        const points = [p1, p2, ...line].sort((a, b) => a.y - b.y);
        ys.push(points);
      }
    }
    return xs.concat(ys);
  }

  draw(key) {
    const lines = this.getRelationLines(key);
    this.clear();

    const wrapper = new SVG().group({ class: this.className });

    lines.forEach((item) => {
      const { length } = item;
      const group = wrapper.group();
      group
        .line(
          item[0].x,
          item[0].y,
          item[length - 1].x,
          item[length - 1].y,
        )
        .attr({
          'stroke-width': 1,
          stroke: '#999',
          'stroke-dasharray': '4,2',
        });
      item.forEach(({ x, y }) => {
        group.line(
          x - 2,
          y - 2,
          x + 2,
          y + 2,
        ).attr({
          'stroke-width': 1,
          stroke: '#999',
        });

        group.line(
          x + 2,
          y - 2,
          x - 2,
          y + 2,
        ).attr({
          'stroke-width': 1,
          stroke: '#999',
        });
      });

      const { container: { gCache } } = store.getState();
      gCache.add(wrapper);
    });

    // console.timeEnd('draw relation lines');
  }

  clear() {
    const { container: { gCache } } = store.getState();
    const wrapper = gCache.findOne(`.${this.className}`);
    wrapper?.remove();
  }
}

export { AlignmentLines };
