/**
   * 求经过两个点的直线方程
   * @param {*} p1
   * @param {*} p2
   * 返回 k b
   * 如果线段垂直 k = Infinity b = x1
   */
export function getLinearEquation(p1, p2) {
  const { x: x1, y: y1 } = p1;
  const { x: x2, y: y2 } = p2;
  if (x2 - x1 === 0) {
    return {
      k: Infinity,
      b: x1,
    };
  }
  const k = (y2 - y1) / (x2 - x1);
  return {
    k,
    b: y1 - k * x1,
  };
}

// 求线段在直线方程上的投影
export function getTargetLineProjection(line, k, b, dir) {
  const [p1, p2] = line;
  if (dir === 'x') {
    const y1 = p1.x * k + b;
    const y2 = p2.x * k + b;
    return [
      { x: p1.x, y: y1 },
      { x: p2.x, y: y2 },
    ];
  }
  if (dir === 'y') {
    if (k === Infinity) {
      return [
        { x: b, y: p1.y },
        { x: b, y: p2.y },
      ];
    }
    const x1 = (p1.y - b) / k;
    const x2 = (p2.y - b) / k;
    return [
      { x: x1, y: p1.y },
      { x: x2, y: p2.y },
    ];
  }
}

// 对参考线段的集合进行合并、去重、排序
export function concatRelationLines(lines, dir) {
  const list = lines.reduce((acc, { points, k, b }) => {
    const key = `${k}${b}`;
    const current = acc[key];
    let newPoints = points;
    // 合并并去重
    if (current) newPoints = [...new Set([...current.map(i => JSON.stringify(i)), ...points.map(i => JSON.stringify(i))])].map(i => JSON.parse(i));
    // 排序
    newPoints.sort((a, b) => {
      if (dir === 'x') return a.x - b.x;
      if (dir === 'y') return a.y - b.y;
      return 0;
    });
    acc[key] = newPoints;
    return acc;
  }, {});
  return Object.values(list);
}

// 二分查找
export function searchLineBoundary(current, lines, gap, dir) {
  const p1 = current.line[0];
  const { length } = lines;

  function getDiff(k, b) {
    if (dir === 'x') {
      return p1.x * k + b - p1.y;
    }

    if (dir === 'y') {
      return b - current.b;
    }
  }

  // 查找左边界
  function searchLeft() {
    let left = 0;
    let right = length - 1;
    while (left <= right) {
      const middle = ~~((left + right) / 2);
      const middleEl = lines[middle];
      const middleDiff = getDiff(middleEl.k, middleEl.b);

      if (middleDiff < - gap) {
        if (middle === length - 1) return;

        const middleNextEl = lines[middle + 1];
        const middleNextDiff = getDiff(middleNextEl.k, middleNextEl.b);

        if (Math.abs(middleNextDiff) <= gap) {
          return middle + 1;
        }
        left = middle + 1;
      } else {
        right = middle - 1;
      }
    }
  }

  // 查找右边界
  function searchRight() {
    let left = 0;
    let right = length - 1;
    while (left <= right) {
      const middle = ~~((left + right) / 2);
      const middleEl = lines[middle];
      const middleDiff = getDiff(middleEl.k, middleEl.b);

      if (middleDiff > gap) {
        if (middle === 0) return;
        const middlePreEl = lines[middle - 1];
        const middlePreDiff = getDiff(middlePreEl.k, middlePreEl.b);

        if (Math.abs(middlePreDiff) <= gap) return middle - 1;

        right = middle - 1;
      } else {
        left = middle + 1;
      }
    }
  }

  const left = searchLeft();
  const right = searchRight();

  if (left === undefined && right === undefined) return null;
  if (left  !== undefined && right !== undefined && left <= right) return [left, right];
  if (left  !== undefined) return [left, length - 1];
  if (right  !== undefined) return [0, right];
}

export const ALIGNMENT_LINE_CLASSNAME = 'aligment-line';
