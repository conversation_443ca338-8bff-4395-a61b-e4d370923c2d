import { DEFAULT_CCN_ICON, SIGMA_CCN_SHAPE } from '../../util/constants';
import { SigmaBaseGroupShape } from './BaseGroupShape';

export class SigmaCcnShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '云联网 CCN';
    this.styles.icon.default = DEFAULT_CCN_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#354D7F';
    this.styles.strokeStyle.default = 'dashed';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = 'transparent';
  }
  name = 'CCN';
  cName = '云联网 CCN';
  type = SIGMA_CCN_SHAPE;
}

