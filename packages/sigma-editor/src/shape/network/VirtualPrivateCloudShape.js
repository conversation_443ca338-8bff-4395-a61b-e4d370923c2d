import { DEFAULT_VPC_ICON, SIGMA_VPC_SHAPE } from '../../util/constants';
import { SigmaBaseGroupShape } from './BaseGroupShape';

export class SigmaVPCShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '虚拟私有网络 VPC';
    this.styles.icon.default = DEFAULT_VPC_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#354D7F';
    this.styles.strokeStyle.default = 'dashed';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = 'transparent';
  }
  name = 'VPC';
  cName = '虚拟私有网络';
  type = SIGMA_VPC_SHAPE;
}
