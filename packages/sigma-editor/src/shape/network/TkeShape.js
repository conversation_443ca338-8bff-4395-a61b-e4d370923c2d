import { DEFAULT_TKE_ICON, SIGMA_TKE_SHAPE } from '../../util/constants';
import { SigmaBaseGroupShape } from './BaseGroupShape';

export class SigmaTkeShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    Object.assign(this.styles, {
      showTips: {
        name: '是否显示状态提示',
        type: 'boolean',
        default: true,
        value: null,
      },
    });
    this.styles.label.default = 'TKE 集群 TKE Cluster';
    this.styles.icon.default = DEFAULT_TKE_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#006EFF';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = '#D5E7FE';
  }
  name = 'TKE GROUP';
  cName = 'TKE 集群 TKE Cluster';
  type = SIGMA_TKE_SHAPE;
}

