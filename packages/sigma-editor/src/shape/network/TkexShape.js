import { DEFAULT_TKE_ICON, SIGMA_TKE_SHAPE } from '../../util/constants';
import { SigmaBaseGroupShape } from './BaseGroupShape';

export class SigmaTkexShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    Object.assign(this.styles, {
      showTips: {
        name: '是否显示状态提示',
        type: 'boolean',
        default: true,
        value: null,
      },
    });
    this.styles.label.default = 'TKEX 集群 TKEX Cluster';
    this.styles.icon.default = DEFAULT_TKE_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#006EFF';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = '#D5E7FE';
  }
  name = 'TKEX';
  cName = 'TKEX 集群 TKEX Cluster';
  type = SIGMA_TKE_SHAPE;
}

