import { SigmaBaseGroupShape } from '../network/BaseGroupShape';
import {
  SIGMA_AUTOSCALING_SHAPE,
  DEFAULT_AUTO_SCALING_ICON,
} from '../../util/constants';

export class SigmaAutoScalingShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '未命名弹性伸缩';
    this.styles.icon.value = DEFAULT_AUTO_SCALING_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#2A86FF';
    this.styles.strokeStyle.default = 'dashed';
  }

  name = 'AUTO SCALING';
  cName = '弹性伸缩';
  type = SIGMA_AUTOSCALING_SHAPE;
}
