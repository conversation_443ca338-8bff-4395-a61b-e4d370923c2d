import { SigmaBaseGroupShape } from '../network/BaseGroupShape';
import {
  SIGMA_AVAILABILITYZONE_SHAPE,
  DEFAULT_AVAILABILITY_ZONE_ICON,
} from '../../util/constants';

export class SigmaAvailabilityZoneShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '可用区 Zone';
    this.styles.icon.default = DEFAULT_AVAILABILITY_ZONE_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#3178AF';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = '#F5F9FF';
  }

  name = 'ZONE';
  cName = '可用区';
  type = SIGMA_AVAILABILITYZONE_SHAPE;
}
