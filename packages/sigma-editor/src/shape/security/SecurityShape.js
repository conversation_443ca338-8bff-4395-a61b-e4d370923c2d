import _ from 'lodash';
import Shape from '../Shape';
import {
  SHAPE_CATEGORY_SECURITY,
  SIGMA_SECURITY_SHAPE,
} from '../../util/constants';

export class SigmaSecurityShape extends Shape {
  type = SIGMA_SECURITY_SHAPE;
  category = SHAPE_CATEGORY_SECURITY;
  connectable = true;
  linkable = true;
  connection = [];
  data = {};
  gIndex = 8;

  constructor({
    name,
    cName,
    category,
    description,
    connection,
    d3,
    d2,
    positionOffsetY = 0,
    positionOffsetX = 0,
  } = {}) {
    super();
    this.name = name;
    this.cName = cName;
    this.description = description;
    this.connection = connection;
    if (category) {
      this.category = category;
    }
    this.positionOffsetY = positionOffsetY;
    this.positionOffsetX = positionOffsetX;
    this.data = { d2, d3 };
  }

  create = () => {
    const vnode = this._create();
    vnode.data = this.data;
    return _.cloneDeep(vnode);
  };
}
