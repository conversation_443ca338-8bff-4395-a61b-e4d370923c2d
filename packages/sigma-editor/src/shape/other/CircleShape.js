import _ from 'lodash';
import Shape from '../Shape';
import {
  SHAPE_CATEGORY_COMMON,
  SIGMA_CIRCLE_SHAPE,
  getEmptyConnections,
} from '../../util/constants';

export class SigmaCircleShape extends Shape {
  name = 'CIRCLE';
  cName = '点';
  type = SIGMA_CIRCLE_SHAPE;
  category = SHAPE_CATEGORY_COMMON;
  connectable = true;
  connection = getEmptyConnections();
  d2_connection = getEmptyConnections();
  editable = ['color', 'radius', 'opacity', 'hide'];
  gIndex = 7;
  styles = {
    color: { name: '颜色', type: 'color', default: '#000000', value: null },
    radius: { name: '半径', type: 'number', default: 3, value: null },
    opacity: { name: '透明度', type: 'number', default: 1, value: null },
    hide: { name: '隐藏', type: 'boolean', default: false, value: null },
  };

  create = (point) => {
    const vnode = this._create();
    vnode.position.x = point.x;
    vnode.position.y = point.y;
    return _.cloneDeep(vnode);
  };
}
