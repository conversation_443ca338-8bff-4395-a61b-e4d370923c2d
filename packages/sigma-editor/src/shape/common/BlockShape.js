import _ from 'lodash';
import Shape from '../Shape';
import {
  SHAPE_CATEGORY_COMMON,
  STATIC_CHECKED_STROKE_COLOR,
  SIGMA_BLOCK_SHAPE,
} from '../../util/constants';

export class SigmaBlockShape extends Shape {
  type = SIGMA_BLOCK_SHAPE;
  category = SHAPE_CATEGORY_COMMON;
  name = 'BLOCK';
  cName = '基础元素';
  editable = ['fill', 'width', 'height', 'depth'];
  gIndex = 8;
  styles = {
    staticStrokeColor: STATIC_CHECKED_STROKE_COLOR,
    fill: { name: '填充', type: 'color', default: '#ececed', value: null },
    width: { name: '宽', type: 'number', default: 2, value: null },
    height: { name: '高', type: 'number', default: 1, value: null },
    depth: { name: '长', type: 'number', default: 2, value: null },
  };
  connectable = true;
  linkable = true;

  data = {
    sideTop: [
      { x: 0, y: 0 },
      { x: 32, y: 19 },
      { x: 0, y: 37 },
      { x: -32, y: 19 },
    ],
    sideLeft: [
      { x: -32, y: 56 },
      { x: 0, y: 74 },
      { x: 0, y: 37 },
      { x: -32, y: 19 },
    ],
    sideRight: [
      { x: 0, y: 74 },
      { x: 32, y: 56 },
      { x: 32, y: 19 },
      { x: 0, y: 37 },
    ],
    sideOutline: [
      { x: 0, y: 0 },
      { x: -32, y: 19 },
      { x: -32, y: 56 },
      { x: 0, y: 74 },
      { x: 32, y: 56 },
      { x: 32, y: 19 },
    ],
  };

  create = () => {
    const vnode = this._create();
    vnode.data = this.data;
    return _.cloneDeep(vnode);
  };
}
