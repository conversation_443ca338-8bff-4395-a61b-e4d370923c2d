import _ from 'lodash';
import Shape from '../Shape';
import { SHAPE_CATEGORY_COMMON, SIGMA_IMAGE_SHAPE } from '../../util/constants';

export class SigmaImageShape extends Shape {
  name = 'IMAGE';
  cName = '图片';
  type = SIGMA_IMAGE_SHAPE;
  data = {};
  editable = ['image', 'scale', 'is3d', 'isFlat', 'route'];
  gIndex = 9;
  styles = {
    scale: { name: '缩放', type: 'number', default: 1, value: null },
    route: { name: '旋转', type: 'select', default: 'e', value: null },
    image: {
      name: '图片',
      type: 'file',
      default: 'https://3dserver.diagram.woa.com/assets/statics/images/d90d3332-cbf1-4740-90fe-af6872dede6b.png',
      value: null,
    },
    is3d: { name: '3D', type: 'boolean', default: true, value: null },
    isFlat: { name: '垂直', type: 'boolean', default: false, value: null },
  };
  category = SHAPE_CATEGORY_COMMON;
  connectable = true;
  linkable = true;
  connection = null;

  create = () => {
    const vnode = this._create();
    vnode.data = this.data;
    return _.cloneDeep(vnode);
  };
}
