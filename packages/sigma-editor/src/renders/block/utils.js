import _ from 'lodash';
import {
  getStylesVal,
  getSigmaPoint,
  transform2DTo3D,
} from '../../util/tools';
import {
  BASE_GRID_2D,
  BASE_GRID_Y,
  BASE_STEP_X,
  BASE_STEP_Y,
  CONNECTION_BOTTOM_KEYS,
} from '../../util/constants';
import { d2_connection } from '../../util/connections';
import store from '../../store';

export function computeBlockPolygon(polygon, side, styles) {
  const height = getStylesVal(styles.height) - 1;
  const depth = getStylesVal(styles.depth) - 1;
  const width = getStylesVal(styles.width) - 1;
  const poly = _.cloneDeep(polygon);
  for (let i = 0; i < poly.length; i++) {
    if (side === 'top') {
      if ((width > 0 && i === 1) || (width > 0 && i === 2)) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x + BASE_STEP_X * width * 2,
          y: y + BASE_STEP_Y * width * 2,
        });
      }
      if (height > 0) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({ x, y: y - BASE_STEP_Y * height * 4 });
      }
      if ((depth > 0 && i === 2) || (depth > 0 && i === 3)) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x - BASE_STEP_X * depth * 2,
          y: y + BASE_STEP_Y * depth * 2,
        });
      }
    }
    if (side === 'left') {
      if ((width > 0 && i === 1) || (width > 0 && i === 2)) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x + BASE_STEP_X * width * 2,
          y: y + BASE_STEP_Y * width * 2,
        });
      }
      if ((height > 0 && i === 2) || (height > 0 && i === 3)) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({ x, y: y - BASE_STEP_Y * height * 4 });
      }
      if (depth > 0) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x - BASE_STEP_X * depth * 2,
          y: y + BASE_STEP_Y * depth * 2,
        });
      }
    }
    if (side === 'right') {
      if (width > 0) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x + BASE_STEP_X * width * 2,
          y: y + BASE_STEP_Y * width * 2,
        });
      }
      if ((height > 0 && i === 2) || (height > 0 && i === 3)) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({ x, y: y - BASE_STEP_Y * height * 4 });
      }
      if ((depth > 0 && i === 0) || (depth > 0 && i === 3)) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x - BASE_STEP_X * depth * 2,
          y: y + BASE_STEP_Y * depth * 2,
        });
      }
    }
    if (side === 'outline') {
      if (width > 0 && i !== 0 && i !== 1 && i !== 2) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x + BASE_STEP_X * width * 2,
          y: y + BASE_STEP_Y * width * 2,
        });
      }
      if (height > 0 && i !== 2 && i !== 3 && i !== 4) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({ x, y: y - BASE_STEP_Y * height * 4 });
      }
      if (depth > 0 && i !== 0 && i !== 4 && i !== 5) {
        const { x, y } = poly[i];
        poly[i] = getSigmaPoint({
          x: x - BASE_STEP_X * depth * 2,
          y: y + BASE_STEP_Y * depth * 2,
        });
      }
    }
  }
  return poly;
}

export function setBlockConnection(vnode) {
  const { styles } = vnode;
  const depth = getStylesVal(styles.depth);
  const width = getStylesVal(styles.width);

  for (const key in d2_connection) {
    vnode.d2_connection[key] = {};
    const element = d2_connection[key];
    vnode.d2_connection[key].x = width / 2 * element.x;
    vnode.d2_connection[key].y = depth / 2 * element.y;
  }

  if (vnode.sticky) {
    const { shapes } = store.getState().core.data;
    const child = shapes[vnode.sticky];
    if (child?.height) {
      CONNECTION_BOTTOM_KEYS.forEach((key) => {
        vnode.d2_connection[key].y += child.height;
      });
    }
  }

  for (const key in vnode.d2_connection) {
    const element = vnode.d2_connection[key];
    vnode.connection[key] = transform2DTo3D(element, true);
    vnode.connection[key].y += BASE_GRID_Y;
  }
}

export function setBlockSize(vnode) {
  const { styles } = vnode;
  const depth = getStylesVal(styles.depth);
  const width = getStylesVal(styles.width);
  vnode.width = BASE_GRID_2D * width;
  vnode.height = BASE_GRID_2D * depth;
}
