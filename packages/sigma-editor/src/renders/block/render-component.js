import { SVG } from '@svgdotjs/svg.js';
import store from '../../store';
import { BASE_GRID_2D, SIGMA_GRAPH_MODE_3D } from '../../util/constants';
import { computeBlockPolygon } from './utils';
import { arrayPointsToStringPoints, getStylesVal } from '../../util/tools';
import { getTransitionColor } from '../../util/color';

class BlockRenderComponent {
  constructor({ vnode }) {
    this.vnode = vnode;
    this._resetMode();
    this._init();
  }
  _render = null;
  _setSize = null;
  _setColor = null;
  _is3D = null;
  _group = null;
  _style = null;
  _element = {};

  _init() {
    const { vnode } = this;
    const { key, position: { x, y } } = vnode;
    const component = new SVG().group()
      .attr({ key, id: key });
    const svg = new SVG('<svg></svg>').attr({
      x,
      y,
      overflow: 'visible',
      class: 'position',
    });
    component.add(svg);

    this._group = svg.group();

    this._render();

    vnode.component = component;
  }

  styleChange({ isSizeChange, isColorChange }) {
    this._resetMode();
    if (isSizeChange) {
      this._setSize();
    }

    if (isColorChange) {
      this._setColor();
    }
  }

  checkedChange() {
    this._resetMode();
    this._setColor();
  }

  _3dRender() {
    const { data, styles, isChecked } = this.vnode;

    const {
      staticStrokeColor,
      fill,
      leftFill,
      rightFill,
      soldeStroke,
    } = this._style;

    const sideTopData = computeBlockPolygon(data.sideTop, 'top', styles);
    const sideLeftData = computeBlockPolygon(data.sideLeft, 'left', styles);
    const sideRightData = computeBlockPolygon(data.sideRight, 'right', styles);
    const outlineData = computeBlockPolygon(data.sideOutline, 'outline', styles);

    this._element.sideTop = this._group
      .polygon(arrayPointsToStringPoints(sideTopData))
      .attr({
        fill,
        class: 'sideTop',
      });
    this._element.sideLeft = this._group
      .polygon(arrayPointsToStringPoints(sideLeftData))
      .attr({
        fill: leftFill,
        stroke: soldeStroke,
        class: 'sideLeft',
      });
    this._element.sideRight = this._group
      .polygon(arrayPointsToStringPoints(sideRightData))
      .attr({
        fill: rightFill,
        stroke: soldeStroke,
        class: 'sideRight',
      });
    this._element.outline = this._group.polygon(arrayPointsToStringPoints(outlineData)).attr({
      fill: 'none',
      stroke: isChecked ? staticStrokeColor : '#000000',
      class: 'outline',
    });
  }

  _2dRender() {
    const { isChecked } = this.vnode;
    const { staticStrokeColor, fill, depth, width } = this._style;
    this._element.rect = this._group.rect(
      width * BASE_GRID_2D,
      depth * BASE_GRID_2D,
    ).attr({
      fill,
      'stroke-width': isChecked ? 2 : null,
      stroke: isChecked ? staticStrokeColor : null,
      class: 'rect',
    });
  }

  _3dSetSize() {
    const {
      styles,
      data,
    } = this.vnode;
    const {
      sideTop,
      sideLeft,
      sideRight,
      outline,
    } = this._element;

    const sideTopData = computeBlockPolygon(data.sideTop, 'top', styles);
    const sideLeftData = computeBlockPolygon(data.sideLeft, 'left', styles);
    const sideRightData = computeBlockPolygon(data.sideRight, 'right', styles);
    const outlineData = computeBlockPolygon(data.sideOutline, 'outline', styles);

    sideTop.attr({ points: arrayPointsToStringPoints(sideTopData) });
    sideLeft.attr({
      points: arrayPointsToStringPoints(sideLeftData),
    });
    sideRight.attr({
      points: arrayPointsToStringPoints(sideRightData),
    });
    outline.attr({
      points: arrayPointsToStringPoints(outlineData),
    });
  }

  _2dSetSize() {
    const { depth, width } = this._style;
    this._element.rect.attr({
      width: width * BASE_GRID_2D,
      height: depth * BASE_GRID_2D,
    });
  }

  _3dSetColor() {
    const { isChecked } = this.vnode;
    const {
      staticStrokeColor,
      fill,
      leftFill,
      rightFill,
      soldeStroke,
    } = this._style;

    const {
      sideTop,
      sideLeft,
      sideRight,
      outline,
    } = this._element;

    sideTop.attr({ fill });
    sideLeft.attr({
      fill: leftFill,
      stroke: soldeStroke,
    });
    sideRight.attr({
      fill: rightFill,
      stroke: soldeStroke,
    });
    outline.attr({
      stroke: isChecked ? staticStrokeColor : '#000000',
    });
  }

  _2dSetColor() {
    const { isChecked } = this.vnode;
    const { staticStrokeColor, fill } = this._style;
    this._element.rect.attr({
      'stroke-width': isChecked ? 2 : null,
      stroke: isChecked ? staticStrokeColor : null,
      fill,
    });
  }

  _resetMode() {
    this._is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    this._render = this._is3D ? this._3dRender : this._2dRender;
    this._setSize = this._is3D ? this._3dSetSize : this._2dSetSize;
    this._setColor = this._is3D ? this._3dSetColor : this._2dSetColor;
    this._resetStyle();
  }

  _resetStyle() {
    const { styles, isChecked } = this.vnode;
    const { staticStrokeColor } = styles;

    if (this._is3D) {
      const offsetLight = isChecked ? 0.2 : 0;
      const defaultFill = getStylesVal(styles.fill);
      const fill = getTransitionColor(defaultFill, 0.98 + offsetLight);
      const leftFill = getTransitionColor(fill, 0.88);
      const rightFill = getTransitionColor(fill, 0.78);
      const soldeStroke = isChecked
        ? staticStrokeColor
        : getTransitionColor(fill, 0.5);

      this._style = {
        staticStrokeColor,
        fill,
        leftFill,
        rightFill,
        soldeStroke,
      };
    } else {
      const fill = getTransitionColor(getStylesVal(styles.fill), 0.9);
      const depth = getStylesVal(styles.depth);
      const width = getStylesVal(styles.width);

      this._style = {
        staticStrokeColor,
        fill,
        depth,
        width,
      };
    }
  }
}


export {
  BlockRenderComponent,
};
