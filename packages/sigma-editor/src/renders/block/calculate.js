import {
  BASE_GRID_2D,
  BASE_GRID_Y,
  SIGMA_GRAPH_MODE_3D,
} from '../../util/constants';
import { computeBlockPolygon } from './utils';
import {
  transform3DTo2D,
  transform2DTo3D,
  getStylesVal,
} from '../../util/tools';

export const transform = (vnode) => {
  vnode.position = transform3DTo2D(vnode.position, false, BASE_GRID_2D, BASE_GRID_2D);
  return vnode;
};

export const reduction = (vnode) => {
  vnode.position = transform2DTo3D({
    x: vnode.position.x - BASE_GRID_2D,
    y: vnode.position.y - BASE_GRID_2D,
  });
  return vnode;
};

export const getflat = (vnode, mode) => {
  const {
    position: { x, y },
    data,
    styles,
  } = vnode;
  const h = getStylesVal(styles.height);
  if (mode === SIGMA_GRAPH_MODE_3D) {
    const sideTopData = computeBlockPolygon(data.sideTop, 'top', styles);
    return sideTopData.map(p => ({ x: p.x + x, y: p.y + y + h * BASE_GRID_Y }));
  }
  const w = getStylesVal(styles.width) * BASE_GRID_2D;
  const d = getStylesVal(styles.depth) * BASE_GRID_2D;
  return [
    { x, y },
    { x, y: y + d },
    { x: x + w, y: y + d },
    { x: x + w, y },
  ];
};
