import {
  SIGMA_GRAPH_MODE_3D,
} from '../../util/constants';
import { setBlockConnection, setBlockSize } from './utils';
import store from '../../store';
import { BlockRenderComponent } from './render-component';
import ShapeManager from '../../shape/shapeManager';

const shapeManager = ShapeManager.getManager();

const getStartPoint = is3D => (is3D ? { x: 45, y: 45 } : { x: 0, y: 0 });

export const render = (core, vnode) => {
  vnode.renderComponent = null;
  vnode.renderComponent = new BlockRenderComponent({ vnode });
  const { component } = vnode;

  component.on('style-changed', (e) => {
    const { initial, data = {} } = e.detail || {};

    const isSizeChange = initial || data.width || data.height || data.depth;
    const isColorChange = initial || data.fill;

    if (isSizeChange) {
      const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;

      // 设置宽高
      setBlockSize(vnode);
      // 设置 connection
      setBlockConnection(vnode);

      // 参考线
      const startPoint = getStartPoint(is3D);
      core.lines.add(vnode, is3D, false, startPoint);
    }

    // 重置尺寸和样式的变化
    vnode.renderComponent.styleChange({
      isSizeChange,
      isColorChange,
    });
  });

  // 监听 attach 变化
  component.on('attach-change', () => {
    // 设置 connection
    setBlockConnection(vnode);
  });

  component.on('position-change', () => {
    shapeManager.remove([vnode.key]);
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { position: { x, y } } = vnode;
    const pos = component.findOne('.position');
    pos.attr({ x, y });

    const startPoint = getStartPoint(is3D);
    core.lines.add(vnode, is3D, false, startPoint);
    core.lines.draw(vnode.key);
  });

  component.on('checked-change', (e) => {
    const { initial } = e.detail || {};
    !initial && vnode.renderComponent.checkedChange();
  });

  component.on('mouseover', () => {
    const { mode } = store.getState();
    const is3D = mode === SIGMA_GRAPH_MODE_3D;
    if (!is3D) return;
    component.attr({
      opacity: .6,
    });
  });

  component.on('mouseout', () => {
    component.attr({
      opacity: 1,
    });
  });

  vnode.component = component;
  return component;
};
