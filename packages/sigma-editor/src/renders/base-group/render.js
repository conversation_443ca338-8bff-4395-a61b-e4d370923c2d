import {
  GROUP_SHAPE_TYPES,
  SIGMA_GRAPH_MODE_3D,
  STATIC_CHECKED_STROKE_COLOR,
  STATIC_DEFAULT_2D_STROKE_COLOR,
} from '../../util/constants';
import { handleManualGroupSizeChange, updateRectangularVnode } from './utils';
import { getStylesVal } from '../../util/tools';
import RectangleRender from '../rectangle';
import { handleVnodeResizePoints } from '../rectangle/utils';
import store from '../../store';
import cmdStack from '../../core/cmd';
import { pureClone } from '../../util/nodes';

export const render = (core, vnode, mode) => {
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const { styles } = vnode;
  const geometry = getStylesVal(styles.geometry);
  if (!vnode.relations) vnode.relations = {};

  if (geometry === 'manual') {
    vnode.data.canResize = true;
  }

  if (geometry === 'rectangular') {
    vnode.data.canResize = false;
    updateRectangularVnode(core, vnode, is3D);
  }

  const component = RectangleRender.render(core, vnode, mode);

  // 样式变化
  component.on('style-changed', (event) => {
    const { initial = false, data = {}, styles } = event.detail;

    const geometry = getStylesVal(styles.geometry);
    // 非初始化
    if (!initial) {
      if (data.geometry) {
        if (geometry === 'manual') {
          vnode.data.canResize = true;
        }
        if (geometry === 'rectangular') {
          vnode.data.canResize = false;
          component.fire('relations-change', { isEnd: false });
        }
        setTimeout(() => {
          handleVnodeResizePoints(vnode, undefined, core);
        }, 0);
      }

      if (data.padding && geometry === 'rectangular') {
        component.fire('relations-change', { isEnd: false });
      }
    } else {
      vnode.resizePoints = null;
      setTimeout(() => {
        handleVnodeResizePoints(vnode, undefined, core);
      }, 0);
    }
  });

  // relations-change
  component.on('relations-change', (event) => {
    const { vnodesPrevSnapshots = [], isEnd = true } = event.detail || {};
    if (vnodesPrevSnapshots.length === 0) {
      vnodesPrevSnapshots.push(pureClone(vnode));
    }

    const geometry = getStylesVal(vnode.styles.geometry);
    if (geometry === 'rectangular') {
      const { mode } = store.getState();
      const is3D = mode === SIGMA_GRAPH_MODE_3D;
      const { component } = vnode;
      updateRectangularVnode(core, vnode, is3D);

      component.fire('position-change');
      component.fire('size-change', {
        animating: true,
        isEnd,
      });
      core.lines.clear();

      // 监听子元素
      const relationsNodes = core._getRelationShapes(vnode);
      for (const child of relationsNodes) {
        const events = [
          'style-changed.relations',
          'attach-change.relations',
        ];
        child.component?.off(events);
        child.component?.on(events, () => {
          component.fire('relations-change', { isEnd: false });
        });
      }
    }

    if (isEnd) {
      cmdStack.saveAction({
        updateType: ['size-change', 'position-change'],
        data: [vnode],
        oldData: vnodesPrevSnapshots,
        isSub: true,
      });
    }
  });

  // 当前组的子组发生变化时，要 check 一下子组的层级，
  // 如果子组的层级低于当前组，需要 insertAfter
  component.on('rank-change', () => {
    const { shapes } = core.data;
    const level = component.position();

    for (const key in vnode.relations) {
      const child = shapes[key];
      if (child?.component && GROUP_SHAPE_TYPES.includes(child.type)) {
        const childLevel = child.component.position();
        if (childLevel < level) {
          child.component.insertAfter(component);
        }

        child.component.fire('rank-change');
      }
    }
  });

  // 触发预选择状态
  component.on('pre-select', (e) => {
    const { key } = e.detail;
    const main = component.findOne('.main');
    if (key && key === vnode.key) {
      main.attr({
        'stroke-width': 4,
        stroke: STATIC_CHECKED_STROKE_COLOR,
      });
    } else {
      const { styles } = vnode;
      const stroke = getStylesVal(styles.stroke);
      const strokeWidth = getStylesVal(styles.strokeWidth);
      main.attr({
        'stroke-width': strokeWidth,
        stroke,
      });
    }
  });

  component.on('size-change', (e) => {
    const { isEnd = false, pure = false, vnodeCopyBeforeUpdate } = e.detail || {};
    if (pure) return;
    const { groups = {} } = vnode;
    const { shapes } = core.data;
    for (const key in groups) {
      const parent = shapes[key];
      parent?.component?.fire(
        'relations-change',
        {
          isEnd: isEnd !== false,
        },
      );
    }

    // 手动模式下，改变组的拖拽关系
    const geometry = getStylesVal(vnode.styles.geometry);
    if (geometry === 'manual' && isEnd) {
      handleManualGroupSizeChange(vnode);
      vnode.component.fire('relations-change', { vnodesPrevSnapshots: vnodeCopyBeforeUpdate ? [vnodeCopyBeforeUpdate] : [], isEnd });
    }
  });


  component.on('deleted', (e) => {
    const { willClearWhenGroupDeleted } = store.getState();
    if (!willClearWhenGroupDeleted) return;
    const { shapes } = core.data;
    const children = [];
    for (const key in vnode.relations) {
      const child = shapes[key];
      child && children.push(child);
    }
    children.length > 0 && core.remove(children, { ...e.detail, isSub: true });
  });

  component.on('checked-change', () => {
    const { isChecked, styles } = vnode;
    const main = component.findOne('.main');
    const defaultStroke = getStylesVal(styles.stroke);
    if (isChecked) {
      main.attr({
        stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
      });
    } else {
      const baseBorderStrokeColor = getStylesVal(styles.baseBorderStroke);
      main.attr({
        stroke: defaultStroke ?? baseBorderStrokeColor,
      });
    }
  });

  return component;
};
