import {
  getIntersectionPolyCoordinates,
  get2DIntersectionPolyCoordinates,
  getStylesVal,
  transform2DTo3D,
  transform3DTo2D,
  parseObjectPointsToArray,
  isVnodeGroupable,
} from '../../util/tools';
import {
  BASE_GRID_2D, BASE_SIZE_2D, GROUP_SHAPE_TYPES, SIGMA_TKE_SHAPE,
} from '../../util/constants';
import renderer from '..';
import store from '../../store';
import { getAreaSize, getOverlapSize } from 'overlap-area';

function getEmptyRelationOffset(padding, is3D) {
  const offset = BASE_GRID_2D * padding * 2;
  const point = { x: offset, y: offset };
  return is3D ? transform2DTo3D(point) : point;
}

export function getPolySize(polyPoints, is3D) {
  const [top, , bottom] = polyPoints;
  const width = bottom.x - top.x;
  const height = bottom.y - top.y;
  if (is3D) {
    const { x, y } = transform3DTo2D({ x: width, y: height });
    return {
      width: x,
      height: y,
    };
  }
  return {
    width,
    height,
  };
}

export function getContourPoints(core, vnode, is3D) {
  const { shapes } = core.data;
  const { relations = {}, styles } = vnode;
  const padding = getStylesVal(styles.padding);
  const shapeContourCoordinates = [];
  // relations 为 0 时，获取偏移量
  const { x: dx, y: dy } = getEmptyRelationOffset(padding, is3D);

  const getIntersection = is3D ? getIntersectionPolyCoordinates : get2DIntersectionPolyCoordinates;

  const rKeys = Object.keys(relations);
  if (rKeys.length > 0) {
    for (const key of rKeys) {
      if (!shapes[key]) continue;
      shapeContourCoordinates.push(renderer.getflat(shapes[key]));
    }
  }

  // 没有 relations
  if (!(rKeys.length && shapeContourCoordinates.length)) {
    const { x, y } = vnode.position;
    shapeContourCoordinates.push([...new Array(4)].map(() => ({ x: x + dx, y: y + dy })));
  }

  const polyPoints = getIntersection(
    shapeContourCoordinates,
    padding,
  );


  return polyPoints;
}

export const BASE_GROUP_MIN_WIDTH = BASE_SIZE_2D * 2;
export const BASE_GROUP_MIN_HEIGHT = BASE_SIZE_2D * 2;


export function updateRectangularVnode(core, vnode, is3D) {
  const polyPoints = getContourPoints(
    core,
    vnode,
    is3D,
  );
  const { width, height } = getPolySize(polyPoints, is3D);
  vnode.position.x = polyPoints[0].x;
  vnode.position.y = polyPoints[0].y;
  vnode.width = Math.max(width, BASE_GROUP_MIN_WIDTH);
  vnode.height = Math.max(height, BASE_GROUP_MIN_HEIGHT);
}

function checkIsParentGroup(shapes, child, group) {
  if (Object.keys(child.groups).length === 0) return false;
  for (const key in child.groups) {
    if (key === group.key) return true;
    if (checkIsParentGroup(shapes, shapes[key], group)) return true;
  }

  return false;
}
// 判断两个元素是否是同一个组的兄弟节点
function checkIsSibling(v1, v2) {
  if (!(v1 && v2)) return false;
  const g1 = Object.keys(v1.groups);
  const g2 = Object.keys(v2.groups);
  if (g1.length === 0 && g2.length === 0) return true;
  if (g1.length === 0 || g2.length === 0) return false;
  return g1.some(key => g2.includes(key));
}
/** 手动调节组的几种情况
* 框选的如果有groups 且 不是当前组的子元素 或者 兄弟节点，返回值
* 不能成组的元素不行
* groups 为空，但不能是拖拽组的父节点
*/
export function handleManualGroupSizeChange(gv) {
  // 如果该组类型是tkegroup类型，则不允许tke组通过改变组大小进行与其他图元成组
  if (gv?.type === SIGMA_TKE_SHAPE) {
    return;
  }
  const { shapes } = store.getState().core.data;
  const { relations } = gv;
  const map = {};
  for (const key in relations) {
    const vnode = shapes[key];
    if (!vnode) continue;
    map[key] = {
      vnode,
      status: 1,
    };
  }

  const gp = parseObjectPointsToArray(renderer.getflat(gv));
  for (const key in shapes) {
    if (key === gv.key) continue;
    const vnode = shapes[key];
    const { groups, type } = vnode;

    if (Object.keys(groups).length !== 0 && !relations[key] && !checkIsSibling(gv, vnode)) continue;
    if (!isVnodeGroupable(vnode)) continue;
    // group 为 空 的元素，但不能是拖拽组的父节点
    if (GROUP_SHAPE_TYPES.includes(type) && checkIsParentGroup(shapes, gv, vnode)) continue;

    const vp = parseObjectPointsToArray(renderer.getflat(vnode));
    const vSize = getAreaSize(vp);
    const area = getOverlapSize(gp, vp);
    if (area > vSize / 2) { // 通知新增
      if (map[key]?.status === 1) continue; // 已经有了，不操作
      map[key] = {
        vnode,
        status: 2,
      };
    } else if (map[key]) { // 通知删除
      map[key] = {
        vnode,
        status: 0,
      };
    }
  }

  for (const key in map) {
    const { vnode, status } = map[key];
    if (status === 2) {
      gv.relations[vnode.key] = vnode.type;
      vnode.groups[gv.key] = gv.type;

      gv.component.fire('rank-change');
    }
    if (status === 0) {
      delete gv.relations[vnode.key];
      delete vnode.groups[gv.key];
    }
  }
}
