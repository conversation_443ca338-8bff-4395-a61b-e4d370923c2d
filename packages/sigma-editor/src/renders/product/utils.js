import { BASE_GRID_2D, BASE_GRID_X, BASE_HALF_GRID_2D, BASE_HALF_GRID_Y, CONNECTION_BOTTOM_KEYS, STATIC_DEFAULT_2D_FILL_COLOR } from '../../util/constants';
import { getStylesVal, transform2DTo3D, getPlaneShape2dConnection, getPlaneShape2dPolylineConnection } from '../../util/tools';
import store from '../../store';
import { cloneDeep } from 'lodash';

export function generate3dShape(main) {
  return `<svg width="128" height="128" viewBox="0 0 128 128">
    <g fill="none" fill-rule="evenodd" transform="scale(1.28)">
      <path class="checked-base" fill="transparent" pointer-events="none" fill-rule="evenodd" clip-rule="evenodd" d="M150 71.2088L50.3317 129.128L-50 71.2025L49.6662 13.2812L150 71.2088Z" />
    
      <path class="fill-light base-bg-path" d="M98.001 68.003L50.078 95.852 1.836 68l47.922-27.85z"/>
      <path class="base-fill outline" fill-rule="nonzero" d="M49.754 39.332L99.413 68 50.076 96.674.416 68l49.338-28.668zm.324 56.521L98 68 49.758 40.15 1.836 68l48.242 27.852v.001z"/>
      <path class="base-stroke outline" d="M50.042 96.66l-.01 3.265L.419 71.257l.009-3.264z"/>
      <path class="base-stroke outline" d="M99.448 67.998l.009 3.257-49.448 28.667L50 96.665z"/>

      ${main}
    </g>
</svg>`;
}

export function render3dComponent(component, styles, isChecked = false, hasBind) {
  const { staticCheckedFill } = styles;
  const pattern = getStylesVal(styles.pattern);
  styles.fillDark.default = pattern === 'gary' ? '#E2E6EC' : '#D2F0FF';
  const darkColor = getStylesVal(styles.dark);
  const fillLightColor = getStylesVal(styles.fillLight);
  const fillDarkColor = getStylesVal(styles.fillDark);
  const baseFillColor = getStylesVal(styles.baseFill);
  const baseBgColor = getStylesVal(styles.baseBgColor);
  const baseStrokeColor = getStylesVal(styles.baseStroke);
  const baseBorderStrokeColor = getStylesVal(styles.baseBorderStroke);

  const fillLight = component.find('.fill-light');
  const baseBgPath = component.find('.base-bg-path');
  const fillDark = component.find('.fill-dark');
  const baseFill = component.find('.base-fill');
  const baseStroke = component.find('.base-stroke');
  const dark = component.find('.dark');
  const outline = component.find('.outline');
  const strokeLight = component.find('.stroke-light');
  const strokeDark = component.find('.stroke-dark');
  const strokeOutline = component.find('.stroke-outline');

  dark.attr({ fill: darkColor });
  fillLight.attr({ fill: fillLightColor });
  fillDark.attr({ fill: hasBind === undefined ? fillDarkColor : hasBind ? '#006EFF' : '#E2E6EC' });
  baseFill.attr({ fill: baseFillColor });

  if (baseBgColor) {
    // 底座颜色
    baseBgPath.attr({ fill: baseBgColor });
  }
  if (baseStrokeColor) {
    baseStroke.attr({ fill: baseBorderStrokeColor });
    baseFill.attr({ fill: baseBorderStrokeColor });
  } else {
    baseStroke.attr({ fill: baseStrokeColor });
  }
  strokeLight.attr({ stroke: fillLightColor });
  strokeDark.attr({ stroke: darkColor });

  const checkedBase = component.find('.checked-base');
  if (isChecked) {
    outline.attr({ fill: staticCheckedFill });
    strokeOutline.attr({ stroke: staticCheckedFill });

    checkedBase.attr({
      fill: '#006eff',
      opacity: .1,
    });
  } else {
    checkedBase.attr({
      fill: 'transparent',
      opacity: 0,
    });
  }
}

export const PRODUCT_SHAPE_SIZE = BASE_GRID_2D * 2;
export const PRODUCT_SHAPE_OFFSET_X = BASE_GRID_2D * 2 + BASE_HALF_GRID_2D;
export const PRODUCT_SHAPE_OFFSET_Y = BASE_HALF_GRID_2D;


export function replaceFillDarkColor(d2, color) {
  return d2?.replaceAll(new RegExp(STATIC_DEFAULT_2D_FILL_COLOR, 'ig'), color);
}

export const PRODUCT_MAIN_COMMON_STYLES = {
  width: 60,
  height: 60,
  x: 15,
  y: 15,
  class: 'main',
};

function setProduct2dConnection(vnode) {
  vnode.d2_connection = cloneDeep(getPlaneShape2dConnection(0, 0, 90, 90));
  // 新增一个自动折线的吸附点
  vnode.d2_polyline_connection = cloneDeep(getPlaneShape2dPolylineConnection(0, 0, 90, 90));
  if (vnode.sticky) {
    const { shapes } = store.getState().core.data;
    const child = shapes[vnode.sticky];
    if (child?.height) {
      CONNECTION_BOTTOM_KEYS.forEach((key) => {
        vnode.d2_connection[key].y += child.height;
      });
      CONNECTION_BOTTOM_KEYS.forEach((key) => {
        vnode.d2_polyline_connection[key].y += child.height;
      });
    }
  }
}

function setProduct3dConnection(vnode) {
  const { d2_connection: d2, d2_polyline_connection } = vnode;
  vnode.connection = {};
  vnode.d3_polyline_connection = {};
  for (const key in d2) {
    const p = transform2DTo3D(d2[key], true);
    vnode.connection[key] = {
      x: p.x + BASE_GRID_X,
      y: p.y + BASE_HALF_GRID_Y * 3,
      w: d2[key]?.w,
      h: d2[key]?.h,
      isMid: d2[key]?.isMid,
    };
  }
  for (const key in d2_polyline_connection) {
    const p = transform2DTo3D(d2_polyline_connection[key], true);
    vnode.d3_polyline_connection[key] = {
      x: p.x + BASE_GRID_X,
      y: p.y + BASE_HALF_GRID_Y * 3,
      w: d2_polyline_connection[key]?.w,
      h: d2_polyline_connection[key]?.h,
      isMid: d2_polyline_connection[key]?.isMid,
      x3d: d2_polyline_connection[key]?.x3d,
      y3d: d2_polyline_connection[key]?.y3d,
    };
  }
}

export function setProductConnection(vnode) {
  // 处理2d
  setProduct2dConnection(vnode);

  // 处理3d
  setProduct3dConnection(vnode);
}
