import store from '../../store';
import { BASE_GRID_2D, SIGMA_GRAPH_MODE_2D } from '../../util/constants';
import { getStylesVal } from '../../util/tools';

export function getSplit(iconstr) {
  const start = Math.floor(iconstr.length / 2);
  const end = Math.floor(iconstr.length / 2) + 24;
  return iconstr.substring(start, end);
}
export const ICON_SHAPE_ORIGINAL_SIZE =  BASE_GRID_2D * 2;

export function parseStyle(vnode) {
  const { styles } = vnode;
  const is3D = store.getState().mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
  const isFlat = getStylesVal(styles.isFlat);
  return { is3D, isFlat };
}
