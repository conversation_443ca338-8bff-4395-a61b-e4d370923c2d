import { STATIC_CHECKED_STROKE_COLOR, SELECT_CURSOR } from '../../../../util/constants';
import { getStylesVal, transform2DTo3D, transform3DTo2D, getUseLinePoints } from '../../../../util/tools';
import { DrawArrow } from '../common/arrow';
import { DrawLine } from '../common/draw-line';
import { POINT_WRAPPER_CLASSNAME, getStickyPoint } from '../render-straight-line/utils';
import { ChildPoint } from './point';
import { findPolylinePoints } from './utils';
import { pureClone } from '../../../../util/nodes';
import cmdStack from '../../../../core/cmd';
import store from '../../../../store';


class RenderPolyline {
  constructor({
    vnode,
  }) {
    this.vnode = vnode;
    this._init();
  }
  _points = [];
  _polyline = null; // 直线实例
  _arrow = null;
  _pointWrapper = null;
  _startUpdate = false;
  _start = { x: 0, y: 0 };

  _init() {
    const { vnode } = this;
    this._findPoints();
    this._arrow = new DrawArrow({ vnode });
    this._polyline = new DrawLine({ vnode, arrow: this._arrow });
    this._initPointWrapper();
    this._initPoints();
  }

  _initPoints() {
    const { vnode } = this;
    const { points } = vnode.data;
    const { length } = points;
    const p = {
      start: points[0],
      end: points[length - 1],
    };
    this._points = ['start', 'end'].map((side) => {
      const position = p[side];
      const child = new ChildPoint({
        vnode,
        parent: this,
        position,
        side,
      });
      this._pointWrapper.add(child.element);
      return child;
    });
  }

  _initPointWrapper() {
    const { component } = this.vnode;

    this._pointWrapper = component.findOne(`.${POINT_WRAPPER_CLASSNAME}`);
    if (!this._pointWrapper) {
      this._pointWrapper = component.group().attr({
        class: POINT_WRAPPER_CLASSNAME,
      });
    } else {
      this._pointWrapper.clear();
    }
  }

  pointChange(side, point, isEnd) {
    this.vnode.component.fire('side-change', { side, point, isEnd });
  }
  // 该场景start为右边中间，end为左边中间，仅限此场景，专门为tke折线使用
  simpleFindPoints(start, end) {
    const is3D = store.getState().mode === 'SIGMA_GRAPH_MODE_3D';

    // 初始化起点终点坐标
    const startPoint = is3D ? transform3DTo2D(start) : start;
    const endPoint = is3D ? transform3DTo2D(end) : end;

    // 检测起点终点是否在同一个x轴上/y轴上
    if (startPoint.x === endPoint.x || startPoint.y === endPoint.y) {
      return [
        start,
        end,
      ];
    }

    const { x, y } = startPoint;
    const { x: ex, y: ey } = endPoint;

    const midX = (x + ex) / 2;

    const m1 = is3D ? transform2DTo3D({ x: midX, y }) : { x: midX, y };
    const m2 = is3D ? transform2DTo3D({ x: midX, y: ey }) : { x: midX, y: ey };

    return [
      start,
      m1,
      m2,
      end,
    ];
  }

  _findPoints(regenerate = true) {
    // 判断是否用简单寻路模式
    const isSimpleMode = () => {
      let useSimple = false;
      const { core = {} } = store.getState();
      const { shapes = {} } = core?.data ?? {};
      const startOptions = this?.vnode?.data?.start ?? {};
      const { vkey: startVkey, dir: startDir } = startOptions ?? {};
      const endOptions = this?.vnode?.data?.end ?? {};
      const { vkey: endVkey, dir: endDir } = endOptions ?? {};
      const startNode = shapes?.[startVkey] ?? {};
      const endNode = shapes?.[endVkey] ?? {};

      if (startNode?.position?.x < endNode?.position?.x
        && startDir === 'r2' && endDir === 'l2'
        && startNode.type === 'SIGMA_PRODUCT_SHAPE'
        && endNode.type === 'SIGMA_PRODUCT_SHAPE') {
        useSimple = true;
      }
      return useSimple;
    };

    const init = getUseLinePoints();
    const { data } = this.vnode;
    const useDataPoints = (!regenerate || this.vnode.hasDragged || init) && data.points?.length > 0;
    const { length } = data.points;
    const start = data.points[0];
    const end = data.points[length - 1];

    const path = useDataPoints
      ? data.points
      : isSimpleMode() ? this.simpleFindPoints(start, end)
        : findPolylinePoints(start, end);
    data.points = path;
    this._updatePoints();
  }

  _updatePoints() {
    if (this._startUpdate) return;
    this._startUpdate = true;
    const { points } = this.vnode.data;
    const { length } = points;
    this._points[0]?.update(points[0]);
    this._points[1]?.update(points[length - 1]);
    this._startUpdate = false;
  }

  updatePath(opts) {
    const { regenerate, onlyStyle } = opts ?? {};
    if (!onlyStyle) {
      this._findPoints(regenerate);
    }
    this._arrow.render();
    this._polyline.updatePath();
  }

  updateStyle() {
    this._polyline.updateStyle();
  }

  updateChecked(is) {
    const lineColor = getStylesVal(this.vnode.styles.lineColor);
    const color = is ? STATIC_CHECKED_STROKE_COLOR : lineColor;

    this._polyline.l1.attr({
      stroke: color,
    });
    this._polyline.arrow.start?.attr({
      fill: color,
    });
    this._polyline.arrow.end?.attr({
      fill: color,
    });

    if (is) {
      this.removeDragPoint();
      this.initDragPoint();
    } else {
      this.removeDragPoint();
    }

    for (const child of this._points) {
      child.element[is ? 'show' : 'hide']();
    }
  }

  initDragPoint() {
    const { vnode } = this;
    const is3D = store.getState().mode === 'SIGMA_GRAPH_MODE_3D';
    const { points } = this.vnode.data;
    if (points.length < 4) return;
    vnode.dragPoints?.forEach((point) => {
      point.remove();
    });
    vnode.dragPoints = [];
    const dragPoints = [];
    for (let i = 1; i < points.length - 2; i++) {
      let direction = '';
      const prePoint = is3D ? transform3DTo2D(points[i]) : points[i];
      const nextPoint = is3D ? transform3DTo2D(points[i + 1]) : points[i + 1];
      direction = (Math.abs(prePoint.x - nextPoint.x) < 1) ? 'x' : 'y';
      const startPoint = points[i];
      const endPoint = points[i + 1];
      const currDragPoint = {
        x: (startPoint.x + endPoint.x) / 2,
        y: (startPoint.y + endPoint.y) / 2,
        direction,
        index: i,
      };
      dragPoints.push(currDragPoint);
    }
    const { doc } = store.getState();
    const pointElement = [];
    dragPoints?.forEach((point) => {
      const element = doc.group();
      element.c0 = element.rect(6, 6)
        .fill('white')
        .stroke({ color: 'blue', width: 2 })
        .move(point.x - 3, point.y - 3);

      element.direction = point.direction;
      element.index = point.index;
      element.draggable();
      element.on('dragstart', this._startDrag.bind(this));
      element.on('dragmove', e => this._drag({ e, element }));
      element.on('dragend', this._endDrag.bind(this));
      element.on('mouseover', () => this._mouseover());
      element.on('mouseout', () => this._mouseout());
      pointElement.push(element);
    });
    vnode.dragPoints = [...vnode.dragPoints, ...pointElement];
  }

  removeDragPoint() {
    const { vnode } = this;
    vnode.dragPoints?.forEach((point) => {
      point.remove();
    });
    vnode.dragPoints = [];
  }

  resetDragPoint() {
    this.removeDragPoint();
    this.initDragPoint();
  }


  _startDrag(e) {
    const { box } = e.detail;
    const { cx, cy } = box;
    this._start.x = cx;
    this._start.y = cy;
    this._makeSnapshot();
  }

  _drag(params) {
    this._setCursor('move');
    const { e, element } = params;
    const { core, scale, mode } = store.getState();
    const { box } = e.detail;
    const { cx, cy } = box;
    const p = { x: this._start.x + (cx - this._start.x) * scale, y: this._start.y + (cy - this._start.y) * scale };
    const { vnode } = this;
    const { data } = vnode;
    const { index, direction } = element;
    const is3D = mode === 'SIGMA_GRAPH_MODE_3D';
    const point = is3D ? transform3DTo2D(p) : p;
    e.preventDefault();

    const strandedPoint = core.__getStandardPosition(point);
    if (is3D) {
      [index, index + 1].forEach((ind) => {
        const d2point = transform3DTo2D(data.points[ind]);
        d2point[direction] = strandedPoint[direction];
        const newPoint = transform2DTo3D(d2point);
        data.points[ind].x = newPoint.x;
        data.points[ind].y = newPoint.y;
      });
    } else {
      [index, index + 1].forEach((ind) => {
        data.points[ind][direction] = strandedPoint[direction];
      });
    }
    this.vnode.hasDragged = true;
    this._polyline.updatePath();
    // 更新points的位置
    this.removeDragPoint();
    this.initDragPoint();
    this.vnode.component.fire('part-change');
  }

  _endDrag() {
    this._makeHistory();
    this._setCursor('auto');
  }

  _mouseover() {
    // element.c0.animate(DEFAULT_ANIMATION_SETTING).attr({ opacity: .5 });
    this._setCursor('pointer');
  }

  _mouseout() {
    const { doc } = store.getState();
    if (doc.attr('cursor') !== 'move') {
      this._setCursor('auto');
    }
  }

  _setCursor(cursor) {
    const { doc, dropzone } = store.getState();
    doc.attr({ cursor });
    dropzone.css('cursor', cursor === 'auto' ? SELECT_CURSOR : cursor);
  }

  _getStandard(e, position) {
    const { scale } = store.getState();
    const { box } = e.detail;
    const { cx, cy } = box;
    const { x: sx, y: sy } = this._start;
    const { x, y } = position;
    const p = {
      x: x + scale * (cx - sx),
      y: y + scale * (cy - sy),
    };
    const is3D = store.getState().mode === 'SIGMA_GRAPH_MODE_3D';
    const sp = getStickyPoint(p, is3D);
    // _start 的作用是计算移动的差值，一定是要对应到画布上的点
    this._start.x = cx + (sp.x - p.x) / scale;
    this._start.y = cy + (sp.y - p.y) / scale;
    return sp;
  }

  _makeSnapshot() {
    this.vnodePrevSnapshot = pureClone(this.vnode);
  }

  _makeHistory() {
    cmdStack.saveAction({
      updateType: 'rerender-line',
      data: [this.vnode],
      oldData: [this.vnodePrevSnapshot],
    });
  }
}

export {
  RenderPolyline,
};
