import renders from '../../..';
import { AStar<PERSON>inder, DiagonalMovement, Grid, Heuristic, Util } from '../../../../affix/path-finding';
import store from '../../../../store';
import { BASE_HALF_GRID_2D, SIGMA_GRAPH_MODE_3D } from '../../../../util/constants';
import { get2DSigmaPoint, isVnodeGroupable, transform2DTo3D, transform3DTo2D } from '../../../../util/tools';
import { pureClone } from '../../../../util/nodes';

class FindPolylinePoints {
  constructor({
    start,
    end,
  }) {
    const { mode, core } =  store.getState();
    this.is3D = mode === SIGMA_GRAPH_MODE_3D;
    const { shapes } = core.data;
    this.shapes = shapes;
    // 初始化起点终点坐标
    this.startPoint = this.is3D ? transform3DTo2D(start) : start;
    this.endPoint = this.is3D ? transform3DTo2D(end) : end;
    // 解决拖动的时候乱飞的问题
    this.startPoint = get2DSigmaPoint(this.startPoint);
    this.endPoint = get2DSigmaPoint(this.endPoint);
  }
  _grids = null;
  _w = null;
  _h = null;
  _ox = null;
  _oy = null;

  run() {
    this._setView();
    this._setTarget('start');
    this._setTarget('end');

    this._grids = new Grid(this._w, this._h);
    this._resetGrid();
    this._find();
  }

  _find() {
    const { start: [sx, sy], end: [ex, ey] } = this;
    const finder = new AStarFinder({
      heuristic: Heuristic.default.manhattan,
      diagonalMovement: DiagonalMovement.Never,
      // trackJumpRecursion: true,
    });
    // 从A*算法角度，结合grids坐标，从结束点到开始点，生成的线（对比直接开始点到结束点）更加平滑
    const path = finder.findPath(ex, ey, sx, sy, this._grids);
    // 由于从结束点到开始点，所以数组得翻转
    path.reverse();
    // 距离过近的时候，返回起止点
    if (path.length < 2) {
      this.polyline = [this._revert(this.start), this._revert(this.end)];
    } else {
      this.polyline = Util.compressPath(path).map(p => this._revert(p));
    }
  }

  _resetGrid() {
    const { shapes, _format, _ox, _oy, is3D } = this;
    for (const key in shapes) {
      // 组应该剔除
      // if (!isVnodeGroupable(shapes[key]) || GROUP_SHAPE_TYPES.includes(type)) continue;
      if (!isVnodeGroupable(shapes[key])) continue;
      // 把 3d 转换成 2d
      const vnode = is3D ? renders.transform(pureClone(shapes[key])) : shapes[key];
      const hasConnection = Object.keys(vnode?.d2_connection ?? {})?.length;
      if (!hasConnection) {
        continue;
      }

      const { position: { x, y } } = vnode;
      const { width, height } = this._getVnodeSize(vnode);
      // 剔除 0
      if (!(width && height)) continue;
      const dx = _format(x - _ox);
      const dy = _format(y - _oy);
      const dw = _format(width);
      const dh = _format(height);

      // 如果在节点内部，则该元素整个都不会被设置为障碍物
      if (this._isInside(dx, dy, dw, dh)) continue;
      for (let i = 0; i <= dh; i++) {
        // 待处理，应该先遍历出来所有的不可能的点，然后通过扩散的方式找到能连的点
        for (let j = 0; j <= dw; j++) {
          const p = [dx + j, dy + i];
          const is = this._isSameSide(p);
          if (!is) {
            this._grids.setWalkableAt(p[0], p[1], false);
            continue;
          }
          // 目标点不在边上，而是在内部
          // if (!(i === 0 || j === 0 || i === dh || j === dw)) {
          //   this[is] = [dx, dy + ~~(dh / 2)];
          //   this[`${is}Point`] = this._revert(this[is]);
          //   this._grids.setWalkableAt(this[is][0], this[is][1], true);
          // }
        }
      }
    }
  }

  _getVnodeSize(vnode) {
    const { d2_connection: { t0, t2, r0, b2 } } = vnode;
    return {
      width: r0.x - t0.x,
      height: b2.y - t2.y,
    };
  }

  _isInside(dx, dy, dw, dh) {
    const { start: s, end: e } = this;
    if (s[0] > dx && s[0] < (dx + dw) && s[1] > dy && s[1] < (dy + dh)) return 'start';
    if (e[0] > dx && e[0] < (dx + dw) && e[1] > dy && e[1] < (dy + dh)) return 'end';
    return null;
  }

  // _isInborder(dx, dy, dw, dh) {
  //   const { start: s, end: e } = this;
  //   if ([dx, dx + dw].includes(s[0]) || [dy, dy + dh].includes(s[1])) return 'start';
  //   if ([dx, dx + dw].includes(e[0]) || [dy, dy + dh].includes(e[1])) return 'end';
  //   return null;
  // }

  _isSameSide(p) {
    const { start: s, end: e } = this;
    const isStart = s[0] === p[0] && s[1] === p[1];
    const isEnd = e[0] === p[0] && e[1] === p[1];
    if (isStart) return 'start';
    if (isEnd) return 'end';
    return null;
  }

  _setTarget(side) {
    const { _ox, _oy, _format } = this;
    const { x, y } = this[`${side}Point`];
    const dx = _format(x - _ox);
    const dy = _format(y - _oy);

    this[side] = [dx, dy];
  }

  _setView() {
    const { shapes, is3D, _format, startPoint, endPoint } = this;
    const min = { x: Infinity, y: Infinity };
    const max = { x: - Infinity, y: - Infinity };
    for (const key in shapes) {
      if (!isVnodeGroupable(shapes[key])) continue;
      // 把 3d 转换成 2d
      const vnode = is3D ? renders.transform(pureClone(shapes[key])) : shapes[key];
      const { position: { x, y }, width, height } = vnode;

      if (min.x > x) min.x = x;
      if (min.y > y) min.y = y;
      if (max.x < x + width) max.x = x + width;
      if (max.y < y + height) max.y = y + height;
    }

    // start 和 end 也加进来，兼容正在连线时的场景
    [startPoint, endPoint].forEach(({ x, y }) => {
      if (min.x > x) min.x = x;
      if (min.y > y) min.y = y;
      if (max.x < x) max.x = x;
      if (max.y < y) max.y = y;
    });


    this._ox = min.x - BASE_HALF_GRID_2D * 4;
    this._oy = min.y - BASE_HALF_GRID_2D * 4;
    this._w = _format(max.x - min.x + BASE_HALF_GRID_2D * 8);
    this._h = _format(max.y - min.y + BASE_HALF_GRID_2D * 8);
  }

  _format(size) {
    return Math.round(size / BASE_HALF_GRID_2D);
  }

  _revert([x, y]) {
    const { _ox, _oy, is3D } = this;
    const point = { x: _ox + x * BASE_HALF_GRID_2D, y: _oy + y * BASE_HALF_GRID_2D };

    return is3D ? transform2DTo3D(point) : point;
  }
}

function findPolylinePoints(start, end) {
  const finder = new FindPolylinePoints({
    start,
    end,
  });
  finder.run();

  return finder.polyline;
}

export { findPolylinePoints };
