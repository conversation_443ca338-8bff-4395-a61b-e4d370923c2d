import { SVG } from '@svgdotjs/svg.js';
import { SELECT_CURSOR, SIGMA_GRAPH_MODE_3D, STATIC_CHECKED_FILL_COLOR } from '../../../../util/constants';
import { POINT_CLASSNAME } from '../render-straight-line/utils';
import store from '../../../../store';
import cmdStack from '../../../../core/cmd';
// import { get2DSigmaPoint, getSigmaPoint } from '../../../../util/tools';
import { pureClone } from '../../../../util/nodes';

class ChildPoint {
  constructor({
    position,
    parent,
    vnode,
    side = 'start',
  }) {
    this.position = position;
    this.parent = parent;
    this.vnode = vnode;
    this.side = side;

    this._init();
  }

  // _start = { x: 0, y: 0 };
  _is3D = false;

  _init() {
    const { x, y } = this.position;

    this.element = new SVG().group()
      .attr({ class: POINT_CLASSNAME });

    this.c0 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 8,
      fill: STATIC_CHECKED_FILL_COLOR,
      opacity: 0,
      'pointer-events': 'none',
    });

    this.c1 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 4,
      'stroke-width': 2,
      fill: STATIC_CHECKED_FILL_COLOR,
      stroke: '#fff',
    });

    this.c2 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 12,
      fill: 'transparent',
    });


    this.element.draggable({ relative: true });

    this.element.on('dragstart', this._startDrag.bind(this));
    this.element.on('dragmove', this._drag.bind(this));
    this.element.on('dragend', this._endDrag.bind(this));

    this.element.on('mouseover', this._mouseover.bind(this));
    this.element.on('mouseout', this._mouseout.bind(this));
  }

  _startDrag() {
    this._is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    // const { box } = e.detail;
    // const { cx, cy } = box;
    // this._start.x = cx;
    // this._start.y = cy;

    this._makeSnapshot();
  }

  _drag(e) {
    e.preventDefault();
    this._setCursor('move');

    const p = this._getStandard(e);
    this.parent.pointChange(this.side, p);
  }

  _endDrag(e) {
    const point = this._getStandard(e);

    this.parent.pointChange(this.side, point, true);
    this._makeHistory();

    this._setCursor('auto');
  }

  _getStandard(e) {
    // const { scale } = store.getState();
    const { box } = e.detail;
    const { cx, cy } = box;
    // const { x: sx, y: sy } = this._start;
    // const { x, y } = this.position;
    // const p = {
    //   x: x + scale * (cx - sx),
    //   y: y + scale * (cy - sy),
    // };
    const p = { x: cx, y: cy };

    // const handler = this._is3D ? getSigmaPoint : get2DSigmaPoint;
    // const sp = handler(p);
    // _start 的作用是计算移动的差值，一定是要对应到画布上的点
    // this._start.x = cx + (sp.x - p.x) / scale;
    // this._start.y = cy + (sp.y - p.y) / scale;
    return p;
  }

  _mouseover() {
    this._setCursor('pointer');
  }

  _mouseout() {
    const { doc } = store.getState();
    if (doc.attr('cursor') !== 'move') {
      this._setCursor('auto');
    }
  }

  update({ x, y }) {
    this.position = { x, y };
    this.c0.attr({
      cx: x,
      cy: y,
    });
    this.c1.attr({
      cx: x,
      cy: y,
    });

    this.c2.attr({
      cx: x,
      cy: y,
    });
  }

  _setCursor(cursor) {
    const { doc, dropzone } = store.getState();
    doc.attr({ cursor });
    dropzone.css('cursor', cursor === 'auto' ? SELECT_CURSOR : cursor);
  }

  _makeSnapshot() {
    this.vnodePrevSnapshot = pureClone(this.vnode);
  }

  _makeHistory() {
    cmdStack.saveAction({
      updateType: 'rerender-line',
      data: [this.vnode],
      oldData: [this.vnodePrevSnapshot],
    });
  }
}

export { ChildPoint };
