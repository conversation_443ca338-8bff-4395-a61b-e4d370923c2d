import { DEFAULT_ANIMATION_SETTING } from '../../../../util/constants';
import { getStylesVal } from '../../../../util/tools';
import { getLineOffset, LINE_STROKE_DASHARRAY, LINE_WRAPPER_CLASSNAME } from '../../utils';

class DrawLine {
  constructor({
    vnode,
    arrow,
  }) {
    this.vnode = vnode;
    this.arrow = arrow;
    this._initWrapper();
    this._init();
  }

  _init() {
    const { vnode } = this;
    const { styles } = vnode;
    const line = getStylesVal(styles.line);
    const lineWidth = getStylesVal(styles.lineWidth);
    const lineColor = getStylesVal(styles.lineColor);

    const points = this._formatPoints();
    this.l1 = this.element.polyline(points).attr({
      'stroke-width': lineWidth ? lineWidth : 2,
      'stroke-linecap': line === 'dashed' ? 'butt' : null,
      'stroke-dasharray': line === 'dashed' ? LINE_STROKE_DASHARRAY : null,
      stroke: lineColor,
      fill: 'none',
      class: 'l1',
    });

    this.l2 = this.element.polyline(points)
      .attr({
        'stroke-width': 20,
        stroke: 'transparent',
        fill: 'none',
        class: 'l2',
      });

    this.updateFlow(points);
  }

  _initWrapper() {
    const { component } = this.vnode;
    this.element = component.findOne(`.${LINE_WRAPPER_CLASSNAME}`);
    if (!this.element) {
      this.element = component.group().attr({
        class: LINE_WRAPPER_CLASSNAME,
      });
    } else {
      this.element.clear();
    }
  }

  _formatPoints() {
    const {
      isStartArrow,
      isEndArrow,
      startDegree,
      endDegree,
      is3D,
    } = this.arrow;
    const { points } = this.vnode.data;

    return points.reduce((acc, { x, y }, index) => {
      if (index === 0 && isStartArrow) {
        const { x: offsetX, y: offsetY } = getLineOffset(startDegree + 180, is3D);
        acc.push([x + offsetX, y + offsetY]);
        return acc;
      }
      if ((index === points.length - 1) && isEndArrow) {
        const { x: offsetX, y: offsetY } = getLineOffset(endDegree, is3D);
        acc.push([x + offsetX, y + offsetY]);
        return acc;
      }

      acc.push([x, y]);
      return acc;
    }, []);
  }

  updatePath({ animating = false } = {}) {
    const points = this._formatPoints();
    const el1 = animating ? this.l1.animate(DEFAULT_ANIMATION_SETTING) : this.l1;
    const el2 = animating ? this.l2.animate(DEFAULT_ANIMATION_SETTING) : this.l2;
    el1.plot(points);
    el2.plot(points);

    this.updateFlow(points);
  }

  updateStyle() {
    const { vnode } = this;
    const { styles } = vnode;
    const line = getStylesVal(styles.line);
    const lineWidth = getStylesVal(styles.lineWidth);
    const lineColor = getStylesVal(styles.lineColor);

    this.l1.attr({
      'stroke-width': lineWidth ? lineWidth : 2,
      'stroke-linecap': line === 'dashed' ? 'butt' : null,
      'stroke-dasharray': line === 'dashed' ? LINE_STROKE_DASHARRAY : null,
      stroke: lineColor,
    });

    this.arrow.start?.attr({
      fill: lineColor,
    });
    this.arrow.end?.attr({
      fill: lineColor,
    });
  }

  updateFlow(points) {
    const { vnode } = this;
    const { styles } = vnode;
    const lineFlows = getStylesVal(styles.lineFlow);
    const lineStart = getStylesVal(styles.lineStart);

    if (vnode.isExport) {
      return;
    }

    if (this.runner3) {
      this.runner3?.unschedule?.();
    }
    if (this.l3) {
      this.l3?.remove?.();
    }

    if (this.runner4) {
      this.runner4?.unschedule?.();
    }
    if (this.l4) {
      this.l4?.remove?.();
    }

    if (lineFlows === 'none') {
      return;
    }
    if (lineFlows === 'flow') {
      const pointsLength = points.reduce((pv, cv, index) => {
        if (index > 0) {
          const item = points[index - 1];
          pv += Math.abs(cv[0] - item[0]) + Math.abs(cv[1] - item[1]);
        }
        return pv;
      }, 0);

      if (pointsLength === 0) {
        return;
      }

      const time = 250 * (Math.round(pointsLength / 40)) ;
      const animateTime = time > 1000 ? time : 1000;

      const color = '#006eff';

      const renderBaseFlowLine = points => this.element.polyline(points)
        .attr({
          'stroke-width': 2,
          'stroke-dasharray': `60, ${pointsLength}`,
          'stroke-dashoffset': 0,
          'stroke-linecap': 'round',
          stroke: color,
          fill: 'transparent',
          class: 'l3',
          filter: `drop-shadow(0.5px 0.5px 0.5px ${color})`,
        });

      const renderShadowFlowLine = points => this.element.polyline(points)
        .attr({
          'stroke-width': 2,
          'stroke-dasharray': `54, ${pointsLength}`,
          'stroke-dashoffset': -3,
          'stroke-linecap': 'round',
          stroke: color,
          fill: 'transparent',
          class: 'l4',
          filter: `drop-shadow(0.5px 0.5px 1px ${color})`,
        });

      const isTwoWay = lineStart === 'arrow';

      this.l3 = renderBaseFlowLine(points);

      this.runner3 = this.l3.animate(animateTime, '<>');

      this.l4 = renderShadowFlowLine(points);
      this.runner4 = this.l4.animate(animateTime, '<>');

      if (isTwoWay) {
        this.l3.attr({ 'stroke-dashoffset': 60 });
        this.l4.attr({ 'stroke-dashoffset': 54 });
        this.runner3.attr({ 'stroke-dashoffset': -pointsLength }).loop(true, true, 0);
        this.runner4.attr({ 'stroke-dashoffset': -pointsLength }).loop(true, true, 0);
      } else {
        this.runner3.attr({ 'stroke-dashoffset': -pointsLength }).loop();
        this.runner4.attr({ 'stroke-dashoffset': -pointsLength }).loop();
      }
    }
  }
}

export { DrawLine };
