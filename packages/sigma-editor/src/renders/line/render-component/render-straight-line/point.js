import { SVG } from '@svgdotjs/svg.js';
import cmdStack from '../../../../core/cmd';
import { DEFAULT_ANIMATION_SETTING, SELECT_CURSOR, SIGMA_GRAPH_MODE_3D, STATIC_CHECKED_FILL_COLOR } from '../../../../util/constants';
import { POINT_CLASSNAME, getStickyMovablePoint, getStickyPoint, setUserUseDeletePoint, getUserUseDeletePoint } from './utils';
import { setCheckedStyles } from '../../utils';
import store from '../../../../store';
import { pureClone } from '../../../../util/nodes';

class ChildPoint {
  constructor({
    position,
    parent,
    vnode,
    status = 'raw', // raw 没有被拖动过 mature 被拖动过
    index,
    isStart = false, // 是否是起点
    isEnd = false, // 是否是终点
  }) {
    this.position = position;
    this.parent = parent;
    this.vnode = vnode;
    this.status = status;
    this.index = index;
    this.isStart = isStart;
    this.isEnd = isEnd;
    this._isDragging = false;

    this._init();
  }

  _start = { x: 0, y: 0 };
  _is3D = false;
  _sideVnode = null;

  _init() {
    const { x, y } = this.position;

    this.element = new SVG().group()
      .attr({ class: POINT_CLASSNAME });

    this.c0 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 8,
      fill: STATIC_CHECKED_FILL_COLOR,
      opacity: 0,
      'pointer-events': 'none',
    });

    this.c1 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 4,
      'stroke-width': 2,
      ...this._getCircleStyles(),
    });

    this.c2 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 12,
      fill: 'transparent',
    });


    this.element.draggable();
    this.element.on('mouseover', this._mouseover.bind(this));
    this.element.on('mouseout', this._mouseout.bind(this));

    this.element.on('dragstart', this._startDrag.bind(this));
    this.element.on('dragmove', this._drag.bind(this));
    this.element.on('dragend', this._endDrag.bind(this));
    // 双击删除
    this.element.on('dblclick', this._dblclick.bind(this));
  }

  _getCircleStyles() {
    if (this.status === 'raw') {
      return {
        stroke: STATIC_CHECKED_FILL_COLOR,
        fill: '#fff',
      };
    }
    if (this.status === 'mature') {
      return {
        fill: STATIC_CHECKED_FILL_COLOR,
        stroke: '#fff',
      };
    }
  }

  _startDrag(e) {
    this._is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { box } = e.detail;
    const { cx, cy } = box;
    this._start.x = cx;
    this._start.y = cy;

    this._setSideVnode();
    this._makeSnapshot();
    this._isDragging = true;
  }

  _drag(e) {
    e.preventDefault();
    if (this.isStart) {
      this.vnode.data.start.isMove = true;
    }
    if (this.isEnd) {
      this.vnode.data.end.isMove = true;
    }
    setCheckedStyles(this.vnode);
    if (this.status === 'raw') {
      const { data } = this.vnode;
      this.status = 'mature';
      data.points[this.index].status = 'mature';

      this.parent.addRawPoints(this.index);
    }
    this._setCursor('move');

    const p = this._getStandard(e);
    this.parent.pointChange(this.index, p);
  }

  _endDrag(e) {
    if (this.isStart) {
      this.vnode.data.start.isMove = false;
    }
    if (this.isEnd) {
      this.vnode.data.end.isMove = false;
    }
    const point = this._getStandard(e);

    this.parent.pointChange(this.index, point, true);
    this._makeHistory();

    this._setCursor('auto');
    setCheckedStyles(this.vnode);
    this._isDragging = false;
  }

  _getStandard(e) {
    const { scale } = store.getState();
    const { box } = e.detail;
    const { cx, cy } = box;
    const { x: sx, y: sy } = this._start;
    const { x, y } = this.position;
    const p = {
      x: x + scale * (cx - sx),
      y: y + scale * (cy - sy),
    };

    if (this._sideVnode) {
      const c = getStickyMovablePoint(this._sideVnode, p, this._is3D);
      if (c) {
        this._start.x = cx + (c.x - p.x) / scale;
        this._start.y = cy + (c.y - p.y) / scale;
        return c;
      }
      this._start.x = cx;
      this._start.y = cy;
      return p;
    }
    const sp = getStickyPoint(p, this._is3D);
    // _start 的作用是计算移动的差值，一定是要对应到画布上的点
    this._start.x = cx + (sp.x - p.x) / scale;
    this._start.y = cy + (sp.y - p.y) / scale;
    return sp;
  }

  _setSideVnode() {
    const { core } = store.getState();
    const { shapes } = core.data;
    const { index, vnode } = this;
    const { start, end, points } = vnode.data;
    const { length } = points;
    if (index === 0) {
      this._sideVnode = shapes[start.vkey];
    } else if (index === length - 1) {
      this._sideVnode = shapes[end.vkey];
    } else {
      this._sideVnode = null;
    }
  }

  update({ x, y }) {
    this.position = { x, y };
    this.c0.attr({
      cx: x,
      cy: y,
    });
    this.c1.attr({
      cx: x,
      cy: y,
      ...this._getCircleStyles(),
    });

    this.c2.attr({
      cx: x,
      cy: y,
    });
  }

  _mouseover() {
    const vnode = this.parent?.vnode?.data?.points;
    const hasUsed = getUserUseDeletePoint();
    if (this.status === 'mature' && !this.tip && this.index !== 0 && this.index !== vnode.length - 1 && !hasUsed && !this._isDragging) {
      const { doc } = store.getState();
      const { position } = this;
      const group = doc.group()
        .opacity(0);

      // 在此处添加浮层提示双击可以删除
      const tip = doc.text('双击删除')
        .font({
          size: 15,
          family: 'Arial',
          weight: 200,
        })
        .attr({
          stroke: '#fff',
          // 'stroke-width': 0.5,
        });
      const textSize = tip.bbox();

      const rect = doc.rect(textSize.width + 20, textSize.height + 20)
        .stroke({ width: 1, color: '#202020' })
        .radius(4); ;
      tip.move(10, 10);
      group.add(rect);
      group.add(tip);
      group.animate(500).opacity(0.8);
      group.move(position.x - 40, position.y - 50);
      this.tip = group;
    }

    // this.c1.animate(DEFAULT_ANIMATION_SETTING).attr({ r: 6 });
    this.c0.animate(DEFAULT_ANIMATION_SETTING).attr({ opacity: .2 });
    this._setCursor('pointer');
  }

  _mouseout() {
    if (this.tip) {
      this.tip.remove();
      this.tip = null;
    }
    // this.c1.animate(DEFAULT_ANIMATION_SETTING).attr({ r: 4 });
    this.c0.animate(DEFAULT_ANIMATION_SETTING).attr({ opacity: 0 });

    const { doc } = store.getState();
    if (doc.attr('cursor') !== 'move') {
      this._setCursor('auto');
    }
  }

  _setCursor(cursor) {
    const { doc, dropzone } = store.getState();
    doc.attr({ cursor });
    dropzone.css('cursor', cursor === 'auto' ? SELECT_CURSOR : cursor);
  }

  _dblclick(e) {
    e.stopPropagation();
    this._makeSnapshot();
    this.parent.delete(this.index);
    this._makeHistory();
    setUserUseDeletePoint();
    if (this.tip) {
      this.tip.remove();
      this.tip = null;
    }
  }

  _makeSnapshot() {
    this.vnodePrevSnapshot = pureClone(this.vnode);
  }

  _makeHistory() {
    cmdStack.saveAction({
      updateType: 'rerender-line',
      data: [this.vnode],
      oldData: [this.vnodePrevSnapshot],
    });
  }
}

export { ChildPoint };
