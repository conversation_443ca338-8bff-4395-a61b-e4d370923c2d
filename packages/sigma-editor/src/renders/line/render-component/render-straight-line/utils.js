import { getTwoPointDistance } from '../../utils';
import { get2DSigmaPoint, getSigmaPoint } from '../../../../util/tools';
import { getValidConnection } from '../../../../util/connections';

export const POINT_WRAPPER_CLASSNAME = 'points';
export const POINT_CLASSNAME = 'point';

const POINT_STICKY_MIN_DISTANCE = 8;
export function getStickyPoint(p, is3D) {
  const handler = is3D ? getSigmaPoint : get2DSigmaPoint;
  const sp = handler(p);
  const distance = getTwoPointDistance(sp, p, is3D);
  if (distance <= POINT_STICKY_MIN_DISTANCE) {
    return sp;
  }
  return p;
}

export function getStickyMovablePoint(vnode, p, is3D) {
  const { position: { x, y } } = vnode;
  const c = getValidConnection(vnode);
  for (const key in c) {
    const el = c[key];
    const cp = {
      x: x + el.x,
      y: y + el.y,
    };
    const distance = getTwoPointDistance(cp, p, is3D);
    if (distance <= POINT_STICKY_MIN_DISTANCE) {
      return cp;
    }
  }
}

export function setUserUseDeletePoint() {
  localStorage.setItem('USER_USE_DELETE_POINT', 'true');
}

export function getUserUseDeletePoint() {
  return localStorage.getItem('USER_USE_DELETE_POINT') === 'true';
}
