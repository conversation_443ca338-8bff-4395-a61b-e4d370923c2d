import store from '../../../store';
import { SIGMA_GRAPH_MODE_3D } from '../../../util/constants';
import { getStylesVal } from '../../../util/tools';
import { updateLineDataWhenMove } from '../utils';
import { RenderPolyline } from './render-polyline';
import { RenderStraightLine } from './render-straight-line';

class RenderComponent {
  constructor({
    vnode,
  }) {
    this.vnode = vnode;

    this.updateType();
  }

  _render = null;

  updateType() {
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { vnode } = this;
    const { styles, component } = vnode;
    const type = getStylesVal(styles.lineType);
    this._render = null;
    component?.clear();
    // data.points = null;  // 只有切换直线折线才需要
    this._initPoints();
    // 已有连线再刷新的时候可以不用调用这个方法
    updateLineDataWhenMove(vnode, 'end', is3D);
    this._render?.removeDragPoint?.();
    if (type === 'straight') {
      this._render = new RenderStraightLine({ vnode });
    }

    if (type === 'polyline') {
      this._render = new RenderPolyline({ vnode });
    }
  }

  _initPoints() {
    const { data, styles } = this.vnode;
    const type = getStylesVal(styles.lineType);
    const isArray = Array.isArray(data.points);
    const { length } = data?.points || [];

    if (type === 'straight' && (!isArray || length <= 2)) {
      const start = { ...this._getSidePoint(data, 'start'), status: 'mature' };
      const end = { ...this._getSidePoint(data, 'end'), status: 'mature' };
      data.points = [
        start,
        {
          x: (start.x + end.x) / 2,
          y: (start.y + end.y) / 2,
          start: 'raw',
        },
        end,
      ];
    }

    if (type === 'polyline' && (!isArray || length <= 1)) {
      data.points = [
        this._getSidePoint(data, 'start'),
        this._getSidePoint(data, 'end'),
      ];
    }
  }

  _getSidePoint(data, side) {
    const { core, mode } = store.getState();
    const is3D = mode === SIGMA_GRAPH_MODE_3D;
    const { shapes } = core.data;

    const { type, vkey, isFixed, dir } = data[side];
    if (['mouse', 'dragging'].includes(type)) {
      return { x: data[side].x, y: data[side].y };
    }

    const { position: { x, y }, connection, d2_connection } = shapes[vkey];
    const c = is3D ? connection : d2_connection;
    if (isFixed) {
      return {
        x: c[dir].x + x,
        y: c[dir].y + y,
      };
    }
    return { x, y };
  }

  updateStyle() {
    this._render.updateStyle();
  }

  updatePath(opts) {
    this._render.updatePath(opts);
  }

  updateChecked(is) {
    this._render.updateChecked(is);
  }

  resetDragPoint() {
    this._render?.resetDragPoint?.();
  }

  removeDragPoint() {
    this._render?.removeDragPoint?.();
  }
}

export {
  RenderComponent,
};
