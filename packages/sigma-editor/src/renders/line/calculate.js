import { transform3DTo2D, transform2DTo3D } from '../../util/tools';

export const transform = (vnode) => {
  const { start, end, points } = vnode.data;
  const { x: sx, y: sy } = transform3DTo2D({ x: start.x, y: start.y });
  const { x: ex, y: ey } = transform3DTo2D({ x: end.x, y: end.y });
  vnode.data.start.x = sx;
  vnode.data.start.y = sy;
  vnode.data.end.x = ex;
  vnode.data.end.y = ey;
  vnode.data.points = points.map(({ x, y, status }) => ({
    ...transform3DTo2D({ x, y }, true),
    status,
  }));
  return vnode;
};

export const reduction = (vnode) => {
  const { start, end, points } = vnode.data;
  const { x: sx, y: sy } = transform2DTo3D({ x: start.x, y: start.y });
  const { x: ex, y: ey } = transform2DTo3D({ x: end.x, y: end.y });
  vnode.data.start.x = sx;
  vnode.data.start.y = sy;
  vnode.data.end.x = ex;
  vnode.data.end.y = ey;
  vnode.data.points = points.map(({ x, y, status }) => ({
    ...transform2DTo3D({ x, y }, true),
    status,
  }));
  return vnode;
};

export const getflat = vnode => [vnode.position];
