import {
  getPointToPointDegree,
  getStylesVal,
  transform3DTo2D,
} from '../../util/tools';
import {
  SIGMA_GRAPH_MODE_3D,
  LINE_ARROW_OFFSET,
  // CONNECTION_CENTER,
  SIGMA_LINE_SHAPE,
} from '../../util/constants';
import store from '../../store';
import { getValidConnection } from '../../util/connections';
import { clearMovablePoints, setMovablePoints } from '../../affix/render-movable-point';

/**
 *
 * @param {*} p1 起点坐标 {x, y}
 * @param {*} p2 终点坐标， 同p1
 * @returns 返回箭头旋转偏移角度 deg
 */
export function getAmplitudeDegrees(p1, p2, mode) {
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const { angle } = getPointToPointDegree(p1, p2);
  if (is3D) {
    switch (true) {
      case angle > -150 && angle <= -90:
        return angle - 150 + Math.cos(((angle + 150) * Math.PI) / 180) * 30;
      case angle > -90 && angle <= -30:
        return angle - 150 + Math.sin(((angle - 150) * Math.PI) / 180) * 15;
      case angle > -30 && angle <= 30:
        return angle - 150 - Math.sin(((angle - 150) * Math.PI) / 180) * 30;
      case angle > 30 && angle <= 90:
        return angle - 150 - Math.cos(((angle + 150) * Math.PI) / 180) * 30;
      case angle > 90 && angle <= 150:
        return angle - 150 - Math.sin(((angle - 150) * Math.PI) / 180) * 15;
      default:
        return angle - 150 + Math.sin(((angle - 150) * Math.PI) / 180) * 30;
    }
  }
  return angle - 90;
}

// 当连线起点元素运动时，更新 data 里的 points 和 start end，
// 调用此函数时要保证 data 里的信息已提前更新
export function updateLineDataWhenMove(lineVnode, side, is3D) {
  const { core } = store.getState();
  const { shapes } = core.data;
  const { data, styles } = lineVnode;
  const type = getStylesVal(styles.lineType);
  // 自动折线的更新逻辑和直线相同
  const is = type === 'polyline' || data.points.length <= 3;

  const other = side === 'start' ? 'end' : 'start';
  const sideFixed = data[side].isFixed;
  const otherFixed = data[other].isFixed;
  const isMoving = isLineMoving(lineVnode, side);

  // 起点固定或者正在移动，从data里取 x y，与connection相加，再更新points
  if (isMoving) {
    const { x, y } = data[side];
    updateLinePoints(data, type, side, x, y);
  } else if (sideFixed) {
    initSidePoints(shapes, type, data, side, is3D);
  }

  // 直线，另一端固定了，且处于拖动中，直接返回，4.25 fixed
  if (is && otherFixed && isMoving) return;

  // 正在连线或拖拽某一端，更新另一端
  if (is && !otherFixed && isMoving) {
    const res = getLineSingleSidePoints(lineVnode, type, is3D, other);
    if (!res) return;
    const { position: { x, y }, direction } = res;
    updateLinePoints(data, type, other, x, y, direction);
    return;
  }

  // 一条直线，没有 mature 控制点
  if (is) {
    // 起点和终点都固定，静态更新起点
    if (sideFixed && otherFixed) return;

    // 起点固定，终点不固定，动态更新终点，包括 end.dir 方向
    if (sideFixed) {
      const res = getLineSingleSidePoints(lineVnode, type, is3D, other);
      if (!res) return;
      const { position: { x, y }, direction } = res;
      updateLinePoints(data, type, other, x, y, direction);
      return;
    }
    // 起点不固定，终点固定，需要更新起点，包括 start.dir 方向
    if (otherFixed) {
      const res = getLineSingleSidePoints(lineVnode, type, is3D, side);
      if (!res) return;
      const { position: { x, y }, direction } = res;
      updateLinePoints(data, type, side, x, y, direction);
      return;
    }

    // 起点和终点都不固定
    const output = getLineTwoSidePoints(core, lineVnode, is3D);
    if (!output) return;
    const [
      { position: { x: sx, y: sy }, direction: sDir },
      { position: { x: ex, y: ey }, direction: eDir },
    ] = output;
    updateLinePoints(data, type, 'start', sx, sy, sDir);
    updateLinePoints(data, type, 'end', ex, ey, eDir);
    return;
  }

  // 起点不固定且是折线
  if (!sideFixed) {
    const res = getLineSingleSidePoints(lineVnode, type, is3D, side);
    if (!res) return;
    const { position: { x, y }, direction } = res;
    updateLinePoints(data, type, side, x, y, direction);
  }
}

function initSidePoints(shapes, type, data, side, is3D) {
  const { vkey, dir } = data[side];
  if (!shapes[vkey]) return;
  const { position: { x, y }, connection, d2_connection } = shapes[vkey];
  const c = is3D ? connection : d2_connection;
  data[side].x = x;
  data[side].y = y;
  if (c[dir]) {
    updateLinePoints(data, type, side, c[dir].x + x, c[dir].y + y);
  } else {
    const midDir = dir.split('-')[2];
    updateLinePoints(data, type, side, c[midDir].x + x, c[midDir].y + y);
  }
}

// 注意这里不要更新 data 里的 x y，data 里的 x y 表示的是起止元素的 position
function updateLinePoints(data, type, side, x, y, direction) {
  direction && (data[side].dir = direction);

  // 如果是自动折线，只更新端点
  const isStraight = type === 'straight';
  if (side === 'start') {
    data.points[0].x = x;
    data.points[0].y = y;
    if (isStraight) {
      data.points[1].x = (data.points[2].x + x) / 2;
      data.points[1].y = (data.points[2].y + y) / 2;
    }
    return;
  }

  if (side === 'end') {
    const { length } = data.points;
    data.points[length - 1].x = x;
    data.points[length - 1].y = y;
    if (isStraight) {
      data.points[length - 2].x = (data.points[length - 3].x + x) / 2;
      data.points[length - 2].y = (data.points[length - 3].y + y) / 2;
    }
  }
}

// side: 'start' \ 'end'，有一端固定的时候求起止点
export function getLineSingleSidePoints(lineVnode, type, is3D, side = 'start') {
  const { shapes } = store.getState().core.data;
  const { data } = lineVnode;
  const { points } = data;
  const { length } = points;
  const targetInfo = data[side];
  if (!targetInfo) return;
  const vnode = shapes[targetInfo.vkey];
  if (!vnode) return;
  const { position } = vnode;
  const c = getValidConnection(vnode);
  const isStraight = type === 'straight';

  const direction = Object.entries(c).reduce((acc, [dir, { x, y }]) => {
    acc[dir] = { x: x + position.x, y: y + position.y };
    return acc;
  }, {});
  let point;
  // 这里区分连线的类型，直线和曲线的起止点不一样
  if (isStraight) {
    point = side === 'end' ? points[length - 3] : points[2];
  } else {
    point = side === 'end' ? points[0] : points[length - 1];
  }
  const result = getConnectionPoint(direction, point, is3D);
  return {
    vnode,
    ...result,
  };
}

export function getTwoPointDistance(p1, p2, is3D) {
  if (is3D) {
    const { x, y } = transform3DTo2D({ x: p1.x - p2.x, y: p1.y - p2.y }, true);
    return (x ** 2 + y ** 2) ** 0.5;
  }
  return ((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2) ** 0.5;
}

function getPointMap(direction, p, is3D) {
  const { x, y } = p;
  const { t2, r2, b2, l2 } = direction;
  const dt = { x: x - t2.x, y: y - t2.y };
  const dr = { x: x - r2.x, y: y - r2.y };
  const db = { x: x - b2.x, y: y - b2.y };
  const dl = { x: x - l2.x, y: y - l2.y };

  const pt = is3D ? transform3DTo2D(dt) : dt;
  const pr = is3D ? transform3DTo2D(dr) : dr;
  const pb = is3D ? transform3DTo2D(db) : db;
  const pl = is3D ? transform3DTo2D(dl) : dl;

  if (pl.x >= 0 && pr.x <= 0) {
    return ['t2', 'b2'];
  }

  if (pt.y >= 0 && pb.y <= 0) {
    return ['l2', 'r2'];
  }

  return ['l2', 'r2'];
}

export function getConnectionPoint(direction, p, is3D) {
  let distance = Infinity; let tp; let td;
  // const map = limit === 'center' ? CONNECTION_CENTER : Object.keys(direction);
  const map = getPointMap(direction, p, is3D);

  for (const key of map) {
    const current = direction[key];
    const _distance = getTwoPointDistance(current, p, is3D);
    if (distance > _distance) {
      distance = _distance;
      tp = current;
      td = key;
    }
  }

  return {
    position: tp,
    direction: td,
  };
}

function getDirectionMap(start, end, is3D) {
  const { position: sp, d2_connection: sd2c, connection: sc, width: sw, height: sh } = start;
  const { position: ep, d2_connection: ed2c, connection: ec, width: ew, height: eh } = end;
  const sd = is3D ? sc : sd2c;
  const ed = is3D ? ec : ed2c;
  const p1 = { x: sp.x + (sd.t0.x + sd.b0.x) / 2, y: sp.y + (sd.t0.y + sd.b0.y) / 2 };
  const p2 = { x: ep.x + (ed.t0.x + ed.b0.x) / 2, y: ep.y + (ed.t0.y + ed.b0.y) / 2 };

  const _d = { x: p2.x - p1.x, y: p2.y - p1.y };
  const diff = is3D ? transform3DTo2D(_d) : _d;

  if (Math.abs(diff.x) <= Math.max(sw, ew) / 2 + 1) {
    return {
      sd: ['b2', 't2'],
      ed: ['b2', 't2'],
    };
  }
  if (Math.abs(diff.y) <= Math.max(sh, eh) / 2 + 1) {
    return {
      sd: ['l2', 'r2'],
      ed: ['l2', 'r2'],
    };
  }
  return {
    sd: ['l2', 'r2'],
    ed: ['b2', 't2'],
  };
}

// 起点和终点都不固定的情况，求起止点
export function getLineTwoSidePoints(core, lineVnode, is3D) {
  const { start, end } = lineVnode.data;
  const { shapes } = core.data;
  const startVnode = shapes[start.vkey];
  const endVnode = shapes[end.vkey];
  // 此时必须保证起止点都存在
  if (!(startVnode && endVnode)) return;
  // 连线不能连到连线上，需要屏蔽这种场景
  if (SIGMA_LINE_SHAPE === startVnode.type || endVnode.type === SIGMA_LINE_SHAPE) return;
  const { position: sp } = startVnode;
  const { position: ep } = endVnode;

  const { sd, ed } = getDirectionMap(startVnode, endVnode, is3D);
  const c1 = is3D ? startVnode.connection : startVnode.d2_connection;
  const c2 = is3D ? endVnode.connection : endVnode.d2_connection;
  const d1 = sd.map((dir) => {
    const { x, y } = c1[dir];
    return { x: x + sp.x, y: y + sp.y };
  });
  const d2 = ed.map((dir) => {
    const { x, y } = c2[dir];
    return { x: x + ep.x, y: y + ep.y };
  });

  // const c1 = getValidConnection(startVnode);
  // const c2 = getValidConnection(endVnode);
  // const d1 = Object.entries(c1).reduce((acc, [dir, { x, y }]) => {
  //   acc[dir] = { x: x + sp.x, y: y + sp.y };
  //   return acc;
  // }, {});
  // const d2 = Object.entries(c2).reduce((acc, [dir, { x, y }]) => {
  //   acc[dir] = { x: x + ep.x, y: y + ep.y };
  //   return acc;
  // }, {});
  return getConnectionDynamicPoints(d1, d2, is3D);
}

// 元素对元素，只找中心点
export function getConnectionDynamicPoints(d1, d2, is3D) {
  let distance = Infinity;
  const output = [];
  // for (const i of CONNECTION_CENTER) {
  for (const i in d1) {
    const start = d1[i];
    // for (const j of CONNECTION_CENTER) {
    for (const j in d2) {
      const end = d2[j];
      const _distance = getTwoPointDistance(start, end, is3D);
      if (distance > _distance) {
        distance = _distance;
        output[0] = {
          direction: i,
          position: start,
        };
        output[1] = {
          direction: j,
          position: end,
        };
      }
    }
  }
  return output;
}

/**
 * 箭头和线段重叠的部分不够尖，箭头要缩进一点
 */
export const getLineOffset = (deg, is3D) => {
  const d = is3D ? deg + 90 : deg;
  const dx = LINE_ARROW_OFFSET * Math.sin(d * Math.PI / 180);
  const dy = LINE_ARROW_OFFSET * Math.cos(d * Math.PI / 180);

  return {
    x: is3D ? - (0.707 * dx - 0.707 * dy) : - dx,
    y: is3D ? 0.409 * dx + 0.409 * dy : dy,
  };
};

// 如果传 side，就是判断单边的
export function isLineMoving(vnode, side) {
  if (!vnode) return;
  const { type, data } = vnode;
  if (type !== SIGMA_LINE_SHAPE) return false;
  if (side) {
    return ['mouse', 'dragging'].includes(data[side].type);
  }

  return (
    data.end.type === 'mouse'
    || data.start.type === 'dragging'
    || data.end.type === 'dragging'
  );
}
// 设置或判断
export function setLineMovingStatus(vnode, side, status) {
  const { component } = vnode;
  if (side && status && vnode.data[side].type !== status) {
    vnode.data[side].type = status;
    if (status === 'vnode') clearMovablePoints();
    if (status === 'dragging') {
      const { core } = store.getState();
      const { shapes } = core.data;
      const sideVnode = shapes[vnode.data[side].vkey];
      sideVnode && setMovablePoints(core, sideVnode);
    }
  }
  const is = isLineMoving(vnode);
  component?.attr({
    'pointer-events': is ? 'none' : 'auto',
  });
  return is;
}

export function setCheckedStyles(vnode) {
  const { isChecked, renderComponent, data } = vnode;
  const { end, start } = data;
  const { uneditable }  = store.getState();
  if (uneditable) {
    renderComponent?.updateChecked(false);
    return;
  }
  if (end?.isMove || start?.isMove) {
    // 如果端点正在移动则不显示中间点
    renderComponent?.updateChecked(false);
    return;
  }
  renderComponent?.updateChecked(isChecked);
}

export function getSideVnodeEvents(key) {
  return [
    'style-changed',
    'position-change',
    'attach-change',
    'size-change',
  ].map(i => `${i}.line-${key}`);
}

export function bindSideVnodeEvent(core, lineVnode, side, initial) {
  const { key, data } = lineVnode;
  if (initial) data[side].prevKey = null;
  const { vkey } = data[side];
  const { prevKey } = data[side];

  if (initial || prevKey !== vkey) {
    const { shapes } = core.data;
    const vnode = shapes[vkey];
    const events = getSideVnodeEvents(key);
    const prev = shapes[prevKey];

    prev?.component?.off(events);
    vnode?.component.on(events, (e) => {
      lineVnode.component.fire('update-position', { ...e.detail, side });
    });

    data[side].prevKey = vkey;
  }
}

export const ARROW_WRAPPER_CLASSNAME = 'arrows';
export const LINE_WRAPPER_CLASSNAME = 'lines';
export const LINE_STROKE_DASHARRAY = '8, 6';
