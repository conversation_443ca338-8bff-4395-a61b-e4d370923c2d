import { SVG } from '@svgdotjs/svg.js';
import {
  DEFAULT_ANIMATION_SETTING,
  SIGMA_GRAPH_MODE_3D,
  SIGMA_REMARK_NOTE_SHAPE,
  SIGMA_TKE_SHAPE,
  TKE_GROUP_EMPTY_ICON,
  TRANSFORM_3D_MATRIX,
} from '../../util/constants';
import { getStylesVal, setPlaneShapeConnection, transform2DTo3D } from '../../util/tools';
import store from '../../store';
import { getResizePoints, formatRectPoints } from '../../affix/resize-element/utils';
import { RectangleHead } from './rectangle-head';
import { handleVnodeResizePoints } from './utils';
import ShapeManager from '../../shape/shapeManager';

const shapeManager = ShapeManager.getManager();

export const render = (core, vnode, mode) => {
  const {
    key,
    position: { x, y },
    width,
    height,
  } = vnode;

  const is3D = SIGMA_GRAPH_MODE_3D === mode;
  let tipsIcon = undefined;
  let tipsText = undefined;
  let groupBody = undefined;
  const tipsIconWidth = 80;
  const tipsIconHeight = 80;
  const component = new SVG().group()
    .attr({ key, id: key });

  const startPoint = { x: 0, y: 0 };

  const headInstance = new RectangleHead(vnode);
  component.on('style-changed', (event) => {
    const { styles, data, initial, pure, isExport } = event.detail;
    if (!data && !initial) return;

    const fill = getStylesVal(styles.fill);
    const stroke = getStylesVal(styles.stroke);
    const strokeWidth = getStylesVal(styles.strokeWidth);
    const strokeStyle = getStylesVal(styles.strokeStyle);
    const showTips = getStylesVal(styles.showTips);
    const tipsIconPoint = {
      x: (width - tipsIconWidth) / 2,
      y: (height - tipsIconHeight) / 2,
    };
    if (initial) {
      // 多边形的点
      vnode.data.points = getResizePoints(width, height, is3D);

      // 新建
      const svg = new SVG('<svg></svg>').attr({
        x,
        y,
        overflow: 'visible',
        class: 'position',
      });

      // body
      const body = svg.group().attr({ class: 'body' });
      body.polygon(formatRectPoints(vnode)).attr({ class: 'main' });
      groupBody = body;
      if (vnode?.type === SIGMA_TKE_SHAPE && showTips) {
        // 状态图标
        tipsIcon = new SVG(TKE_GROUP_EMPTY_ICON).attr({
          class: 'state-icon',
          width: tipsIconWidth,
          height: tipsIconHeight,
          x: is3D ? transform2DTo3D(tipsIconPoint, true).x : tipsIconPoint.x,
          y: is3D ? transform2DTo3D(tipsIconPoint, true).y : tipsIconPoint.y,
        });
        tipsIcon.findOne('.tke-group-empty-icon-g').attr({
          transform: is3D ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')})` : '',
        });
        tipsText =  body.text('未绑定集群').attr({
          color: '#6E829C',
          fontSize: '14px',
          fontWeight: 700,
          transform: is3D ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')})` : '',
        });
        // 获取文本的宽度和高度
        const textWidth = tipsText.bbox().width;
        const textHeight = tipsText.bbox().height;
        // 计算文本的水平和垂直偏移量
        const offsetX = (width - textWidth) / 2;
        const offsetY = (height - textHeight) / 2 + tipsIconHeight / 2 + 15;
        // 设置文本的位置
        tipsText.move(offsetX, offsetY);
        body.add(tipsIcon);
      }
      // head
      if (vnode.type === SIGMA_REMARK_NOTE_SHAPE) {
        headInstance.create(svg, { boxWidth: vnode.width - 12, boxHeight: vnode.height - 12, isBlock: true, isExport });
      } else {
        const iconSize = vnode?.styles?.iconSize?.value || vnode?.styles?.iconSize?.default;
        headInstance.create(svg, { boxWidth: vnode.width - iconSize - 8 - 8 - 6, boxHeight: iconSize, isBlock: false, isExport });
      }

      // 存放 movable point
      svg.group().attr({ class: 'rect-movable' });
      component.add(svg);
      // 只有初始化的时候需要调用 size-change，其他 style-change 的时候不需要
      component.fire('size-change', { initial: true, data, pure });
    } else {
      // style change 的时候更新 head
      if (vnode.type === SIGMA_REMARK_NOTE_SHAPE) {
        headInstance.update(data, { boxWidth: vnode.width - 12, boxHeight: vnode.height - 12, isBlock: true });
      } else {
        const iconSize = vnode?.styles?.iconSize?.value || vnode?.styles?.iconSize?.default;
        headInstance.update(data, { boxWidth: vnode.width - iconSize - 8 - 8 - 6, boxHeight: iconSize, isBlock: false });
      }
      if (vnode?.type === SIGMA_TKE_SHAPE) {
        if (showTips) {
          if (tipsIcon && tipsText) {
            return;
          }
          // 状态图标
          tipsIcon = new SVG(TKE_GROUP_EMPTY_ICON).attr({
            x: is3D ? transform2DTo3D(tipsIconPoint, true).x : tipsIconPoint.x,
            y: is3D ? transform2DTo3D(tipsIconPoint, true).y : tipsIconPoint.y,
            width: tipsIconWidth,
            height: tipsIconHeight,
            class: 'state-icon',
          });
          tipsIcon.findOne('.tke-group-empty-icon-g').attr({
            transform: is3D ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')})` : '',
          });
          tipsText = groupBody.text('未绑定集群').attr({
            color: '#6E829C',
            fontSize: '14px',
            fontWeight: 700,
            transform: is3D ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')})` : '',
          });
          // 获取文本的宽度和高度
          const textWidth = tipsText.bbox().width;
          const textHeight = tipsText.bbox().height;
          // 计算文本的水平和垂直偏移量
          const offsetX = (width - textWidth) / 2;
          const offsetY = (height - textHeight) / 2 + tipsIconHeight / 2 + 15;
          // 设置文本的位置
          tipsText.move(offsetX, offsetY);
          groupBody.add(tipsIcon);
        } else {
          tipsIcon?.remove();
          tipsText?.remove();
          tipsIcon = undefined;
          tipsText = undefined;
        }
      }
    }

    const main = component.findOne('.main');
    main.attr({
      fill,
      stroke,
      'stroke-width': strokeWidth,
      'stroke-dasharray': strokeStyle === 'solid' ? null : '4,4',
    });

    // if (data?.borderRadius) {
    //   main.attr({
    //     points: formatRectPoints(vnode),
    //   });
    // }
  });

  component.on('size-change', (event) => {
    const { mode } = store.getState();
    const is3D = SIGMA_GRAPH_MODE_3D === mode;
    const {
      initial = false,
      willUpdate = true,
      animating = false,
    } = event.detail || {};
    setPlaneShapeConnection(vnode, is3D, false, startPoint);
    core.lines.add(vnode, is3D, false, startPoint);
    if (!initial) {
      const main = component.findOne('.main');
      const { width, height } = vnode;

      vnode.data.points = getResizePoints(width, height, is3D);
      const el = animating ? main.animate(DEFAULT_ANIMATION_SETTING) : main;
      el.attr({
        points: formatRectPoints(vnode),
      });
      if (tipsIcon && tipsText) {
        const tipsIconPoint = {
          x: (width - tipsIconWidth) / 2,
          y: (height - tipsIconHeight) / 2,
        };
        tipsIcon.attr({
          x: is3D ? transform2DTo3D(tipsIconPoint, true).x : tipsIconPoint.x,
          y: is3D ? transform2DTo3D(tipsIconPoint, true).y : tipsIconPoint.y,
          width: tipsIconWidth,
          height: tipsIconHeight,
        });
        // 获取文本的宽度和高度
        const textWidth = tipsText.bbox().width;
        const textHeight = tipsText.bbox().height;
        // 计算文本的水平和垂直偏移量
        const offsetX = (width - textWidth) / 2;
        const offsetY = (height - textHeight) / 2 + tipsIconHeight / 2 + 15;
        // 设置文本的位置
        tipsText.move(offsetX, offsetY);
      }

      core.lines.draw(vnode.key);
      // 宽高有变化的时候更新 head
      if (vnode.type === SIGMA_REMARK_NOTE_SHAPE) {
        headInstance.update(undefined, { boxWidth: vnode.width - 12, boxHeight: vnode.height - 12, isBlock: true });
      } else {
        const iconSize = vnode?.styles?.iconSize?.value || vnode?.styles?.iconSize?.default;
        headInstance.update(undefined, { boxWidth: vnode.width - iconSize - 8 - 8 - 6, boxHeight: iconSize, isBlock: false });
      }
    }
    handleVnodeResizePoints(vnode, initial, core);
    !initial && willUpdate && core.update([vnode]);
  });

  component.on('editable-change', () => {
    handleVnodeResizePoints(vnode, undefined, core);
  });

  component.on('position-change', (event) => {
    // 基础组remove在这里
    shapeManager.remove([vnode.key]);
    const { animating = false } = event.detail || {};
    const { position } = vnode;
    const { x, y } = position;
    const { mode } = store.getState();
    const is3D = SIGMA_GRAPH_MODE_3D === mode;
    const pos = component.findOne('.position');
    const el = animating ? pos.animate(DEFAULT_ANIMATION_SETTING) : pos;
    el.attr({ x, y });
    core.lines.add(vnode, is3D, false, startPoint);
    core.lines.draw(vnode.key);
  });

  component.on('checked-change', () => {
    const { isChecked, data } = vnode;
    (isChecked && data.canResize)
      ? vnode.resizePoints?.show()
      : vnode.resizePoints?.hide();

    const main = component.findOne('.main');
    main.css({
      filter: isChecked ? 'drop-shadow( 0 0 6px hsla(0, 0%, 0%, 0.2))' : 'none',
    });
  });

  vnode.component = component;
  return component;
};
