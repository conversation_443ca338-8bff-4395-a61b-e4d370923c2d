import {
  SIGMA_BLOCK_SHAPE,
  SIGMA_TEXTLABEL_SHAPE,
  SIGMA_ICON_SHAPE,
  SIGMA_IMAGE_SHAPE,
  SIGMA_AREA_SHAPE,
  SIGMA_AUTOSCALING_SHAPE,
  SIGMA_AVAILABILITYZONE_SHAPE,
  SIGMA_PRODUCT_SHAPE,
  SIGMA_LINE_SHAPE,
  SIGMA_CIRCLE_SHAPE,
  SIGMA_SECURITY_GROUP_SHAPE,
  SIGMA_SUBNET_SHAPE,
  SIGMA_VPC_SHAPE,
  // SIGMA_GROUP_SHAPE,
  SIGMA_SECURITY_SHAPE,
  SIGMA_RECTANGLE_SHAPE,
  SIGMA_BASE_GROUP_SHAPE,
  SIGMA_GRAPH_MODE_3D,
  SIGMA_REMARK_NOTE_SHAPE,
  SIGMA_CCN_SHAPE,
  SIGMA_TKE_SHAPE,
} from '../util/constants';
import store from '../store';

import AreaShapeRenderer from './area';
import AutoScalingShapeRenderer from './auto-scaling';
import AvailabilityZoneShapeRenderer from './availability-zone';
import BlockShapeRenderer from './block';
import CircleShapeRenderer from './circle';
import ImageShapeRenderer from './image';
import IconShapeRenderer from './icon';
import LineShapeRenderer from './line';
import ProductShapeRenderer from './product';
import SecurityGroupShapeRenderer from './security-group';
import SubnetShapeRenderer from './subnet';
import SecurityShapeRenderer from './security';
import TextLabelShapeRenderer from './text-label';
import VirtualPrivateCloudShapeRenderer from './virtual-private-cloud';
import RectangleShapeRenderer from './rectangle';
import BaseGroupShapeRender from './base-group';
import RemarkNoteShapeRender from './remark-note';
import CcnShapeRender from './ccn';
import TkeShapeRender from './tke';

const Render = function () {
  this.instance = null;
  this.map = new Map();
  this._init();
};

Render.prototype._init = function () {
  this.register(SIGMA_RECTANGLE_SHAPE, RectangleShapeRenderer);
  this.register(SIGMA_AREA_SHAPE, AreaShapeRenderer);
  this.register(SIGMA_AUTOSCALING_SHAPE, AutoScalingShapeRenderer);
  this.register(SIGMA_AVAILABILITYZONE_SHAPE, AvailabilityZoneShapeRenderer);
  this.register(SIGMA_BLOCK_SHAPE, BlockShapeRenderer);
  this.register(SIGMA_CIRCLE_SHAPE, CircleShapeRenderer);
  // this.register(SIGMA_GROUP_SHAPE, GroupShapeRenderer);
  this.register(SIGMA_IMAGE_SHAPE, ImageShapeRenderer);
  this.register(SIGMA_ICON_SHAPE, IconShapeRenderer);
  this.register(SIGMA_LINE_SHAPE, LineShapeRenderer);
  this.register(SIGMA_PRODUCT_SHAPE, ProductShapeRenderer);
  this.register(SIGMA_SECURITY_GROUP_SHAPE, SecurityGroupShapeRenderer);
  this.register(SIGMA_SUBNET_SHAPE, SubnetShapeRenderer);
  this.register(SIGMA_SECURITY_SHAPE, SecurityShapeRenderer);
  this.register(SIGMA_TEXTLABEL_SHAPE, TextLabelShapeRenderer);
  this.register(SIGMA_VPC_SHAPE, VirtualPrivateCloudShapeRenderer);
  this.register(SIGMA_BASE_GROUP_SHAPE, BaseGroupShapeRender);
  this.register(SIGMA_REMARK_NOTE_SHAPE, RemarkNoteShapeRender);
  this.register(SIGMA_CCN_SHAPE, CcnShapeRender);
  this.register(SIGMA_TKE_SHAPE, TkeShapeRender);
};

Render.getRender = function () {
  if (this.instance) return this.instance;
  return (this.instance = new Render());
};

Render.prototype.register = function (type, renderer) {
  if (!this.map.has(type)) {
    this.map.set(type, renderer);
    return true;
  }
  throw new Error('已存在相同类型的基础元素, 检查是否重复或使用别的类型名称');
};

Render.prototype.deregister = function (type) {
  this.map.delete(type);
};

Render.prototype.render = function (core, vnode) {
  const { mode, container: _container } = store.getState();
  const { type } = vnode;
  const shape = this.map.get(type);
  if (shape) {
    let component = shape.render(core, vnode, mode);
    if ([SIGMA_IMAGE_SHAPE].includes(type)) component = vnode.component;
    // const gindex = gIndex === undefined ? this._getGIndex(vnode) : gIndex;
    let container;
    // 如果vnode是要绑定其他元素的，直接返回父元素
    if (vnode.attach) {
      const { shapes } = core.data;
      container = shapes[vnode.attach].component;
      if (container) {
        return { component, container };
      }
      delete vnode.attach;
    }

    const gindex = this._getGIndex(vnode);
    container = _container[gindex];

    return { component, container };
  }
  throw new Error(`render: 元素数据错误, 请检查程序。vnode: ${vnode}`);
};

Render.prototype.rerender = function (core, vnode) {
  const { mode } = store.getState();
  const shape = this.map.get(vnode.type);
  if (shape) {
    shape.rerender(core, vnode, mode);
    return;
  }
  throw new Error(`rerender: 元素数据错误, 请检查程序。vnode: ${vnode}`);
};

Render.prototype.transform = function (vnode) {
  const shape = this.map.get(vnode.type);
  if (shape) {
    return shape.transform(vnode);
  }
};

Render.prototype.reduction = function (vnode) {
  const shape = this.map.get(vnode.type);
  if (shape) {
    return shape.reduction(vnode);
  }
};

Render.prototype.exports = async function (core, vnode) {
  const { mode } = store.getState();
  const shape = this.map.get(vnode.type);
  if (shape) {
    // const gIndex = vnode.gIndex === undefined ? this._getGIndex(vnode) : vnode.gIndex;
    const gIndex = this._getGIndex(vnode);
    const component = await shape.exports(core, vnode, mode);

    return [component, gIndex];
  }
  throw new Error(`exports: 元素数据错误, 请检查程序。vnode: ${vnode}`);
};

Render.prototype.getflat = function (vnode) {
  if (!vnode) {
    console.warn('Function getFlat: vnode is undefind!');
    return;
  }
  const { connection, d2_connection, position: { x, y } } = vnode;
  const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
  const c = is3D ? connection : d2_connection;
  if (!c) {
    const { mode } = store.getState();
    const shape = this.map.get(vnode.type);
    if (shape) {
      return shape.getflat(vnode, mode);
    }
  }
  const output = [];
  const target = ['t0', 'r0', 'b0', 'l0'];
  for (const dir of target) {
    const p = c[dir] || {};
    output.push({
      x: x + p.x,
      y: y + p.y,
    });
  }
  return output;
};

Render.prototype._getGIndex = function (vnode) {
  switch (vnode.type) {
    case SIGMA_AUTOSCALING_SHAPE:
    case SIGMA_AREA_SHAPE:
    case SIGMA_AVAILABILITYZONE_SHAPE:
    case SIGMA_SECURITY_GROUP_SHAPE:
    case SIGMA_VPC_SHAPE:
    case SIGMA_SUBNET_SHAPE:
    case SIGMA_BASE_GROUP_SHAPE:
    case SIGMA_CCN_SHAPE:
    case SIGMA_TKE_SHAPE:
      return 4;
    case SIGMA_RECTANGLE_SHAPE:
      return 5;
    case SIGMA_LINE_SHAPE:
      return 6;
    case SIGMA_CIRCLE_SHAPE:
      return 7;
    case SIGMA_BLOCK_SHAPE:
    case SIGMA_PRODUCT_SHAPE:
    case SIGMA_SECURITY_SHAPE:
      return 8;
    case SIGMA_IMAGE_SHAPE:
      return 9;
    case SIGMA_ICON_SHAPE:
      return 10;
    case SIGMA_TEXTLABEL_SHAPE:
    case SIGMA_REMARK_NOTE_SHAPE:
      return 11;
    default:
      throw new Error(`数据错误: ${vnode}`);
  }
};

export default Render;
