import {
  getSigmaPoint,
  get2DSigmaPoint,
  transform2DTo3D,
  transform3DTo2D,
} from '../../util/tools';
import {
  SIGMA_GRAPH_MODE_3D,
  CONNECTION_CENTER,
} from '../../util/constants';
import store from '../../store';
import { cloneDeep } from 'lodash';

export const _pointMouseDown = (e, ele, core, vnode, index, mode) => {
  e.stopPropagation();
  const { doc, uneditable } = store.getState();
  if (uneditable) return;
  const startX = ele.attr('cx');
  const startY = ele.attr('cy');
  const start = doc.point(e.pageX, e.pageY);
  function mouseMove(ev) {
    const end = doc.point(ev.pageX, ev.pageY);
    const nextX = startX + end.x - start.x;
    const nextY = startY + end.y - start.y;
    const pos = mode === SIGMA_GRAPH_MODE_3D
      ? getSigmaPoint({ x: nextX, y: nextY })
      : get2DSigmaPoint({ x: nextX, y: nextY });
    vnode.data.main[index] = pos;
    core.update([vnode]);
  }
  function mouseUp() {
    doc.off('mousemove', mouseMove);
    doc.off('mouseup', mouseUp);
  }

  doc.on('mousemove', mouseMove);
  doc.on('mouseup', mouseUp);
};

export const _lineMouseDown = (e, ele, core, vnode, index, mode) => {
  const { uneditable } = store.getState();
  if (uneditable) return;
  e.stopPropagation();
  const newPoint = mode === SIGMA_GRAPH_MODE_3D
    ? getSigmaPoint(ele.point(e.pageX, e.pageY))
    : get2DSigmaPoint(ele.point(e.pageX, e.pageY));
  const nextMain = cloneDeep(vnode.data.main);
  nextMain.splice(index + 1, 0, newPoint);
  vnode.data.main = nextMain;
  core.update([vnode]);
};

export const _lineMouseDBClick = (core, vnode, index) => {
  const { uneditable } = store.getState();
  if (uneditable) return;
  vnode.data.main.length > 4 && vnode.data.main.splice(index, 1);
  core.update([vnode]);
};

const getMidpoints = (a1, a2) => ({
  x: (a1.x + a2.x) / 2,
  y: (a1.y + a2.y) / 2,
});

const get2dConnection = (points) => {
  const top = {
    value: Infinity,
    index: 0,
  };
  const left = {
    value: Infinity,
    index: 0,
  };
  const right = {
    value: 0,
    index: 0,
  };
  const bottom = {
    value: 0,
    index: 0,
  };

  points.reverse().forEach((item, index) => {
    if (left.value > item.x) {
      left.value = item.x;
      left.index = index;
    }

    if (bottom.value < item.y && ![left.index].includes(index)) {
      bottom.value = item.y;
      bottom.index = index;
    }

    if (right.value < item.x && ![left.index, bottom.index].includes(index)) {
      right.value = item.x;
      right.index = index;
    }

    if (top.value > item.y && ![left.index, bottom.index, right.index].includes(index)) {
      top.value = item.y;
      top.index = index;
    }
  });

  return [
    points[top.index],
    points[right.index],
    points[bottom.index],
    points[left.index],
  ];
};

/**
 * 给定一组多边形的点，取其中点，并通过比较坐标获取上下左右四个方向的连接点
 */
export const getAnyConnection = (points, is3D) => {
  if (is3D) {
    const middlePoints = points.map((item, index) => {
      const next = index >= points.length - 1 ? 0 : index + 1;
      return getMidpoints(transform3DTo2D(item), transform3DTo2D(points[next]));
    });

    const c = get2dConnection(middlePoints);
    const c3d = c.map(item => transform2DTo3D(item));
    return {
      [CONNECTION_CENTER[0]]: c3d[0],
      [CONNECTION_CENTER[1]]: c3d[1],
      [CONNECTION_CENTER[2]]: c3d[2],
      [CONNECTION_CENTER[3]]: c3d[3],
    };
  }

  const middlePoints = points.map((item, index) => {
    const next = index >= points.length - 1 ? 0 : index + 1;
    return getMidpoints(item, points[next]);
  });

  const c2d = get2dConnection(middlePoints);
  return {
    [CONNECTION_CENTER[0]]: c2d[0],
    [CONNECTION_CENTER[1]]: c2d[1],
    [CONNECTION_CENTER[2]]: c2d[2],
    [CONNECTION_CENTER[3]]: c2d[3],
  };
};
