import { SVG } from '@svgdotjs/svg.js';
import {
  SIGMA_GRAPH_MODE_2D,
  TRANSFORM_3D_SCALE,
} from '../../util/constants';
import {
  getFlatAndRouteTransform,
  getStylesVal,
  setPlaneShapeConnection,
} from '../../util/tools';
import { initImageSize, loadImage, setImageSizeChange } from './utils';
import { parseStyle } from '../icon/utils';
import ShapeManager from '../../shape/shapeManager';

const shapeManager = ShapeManager.getManager();

export const render = async (core, vnode, mode) => {
  const {
    key,
    position: { x, y },
    styles,
  } = vnode;
  const isFlat = getStylesVal(styles.isFlat);

  const component = new SVG().group()
    .attr({ key, id: key });
  const svg = new SVG('<svg></svg>').attr({
    x,
    y,
    overflow: 'visible',
    class: 'position',
  });

  svg.group().attr({
    class: 'g1',
  });

  component.add(svg);
  // 解决初始化渲染的问题
  initImageSize(vnode);
  vnode.component = component;
  await loadImage(vnode);

  // 监听样式变化
  component.on('style-changed', (event) => {
    const { initial, styles, data } = event.detail;

    if (initial) {
      // 解决初始化渲染的问题
      // if (!(vnode.width && vnode.height)) {
      //   vnode.width = DEFAULT_IMAGE_SHAPE_SIZE;
      //   vnode.height = DEFAULT_IMAGE_SHAPE_SIZE;
      // }
      setImageSizeChange(vnode);
      return;
    }

    if (data?.image) { // image 变化，需要监听 image onload 事件
      loadImage(vnode);
    } else { // 其他样式变化
      const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
      const isFlat = getStylesVal(styles.isFlat);
      const route = getStylesVal(styles.route);
      const scale = getStylesVal(styles.scale);

      const group = component.findOne('.g1');
      const image = component.findOne('.image');

      const w = image.attr('width');
      const h = image.attr('height');
      vnode.width = scale * TRANSFORM_3D_SCALE * w;
      vnode.height = scale * TRANSFORM_3D_SCALE * h;
      const transform = getFlatAndRouteTransform(
        isFlat,
        is3D,
        route,
        { x: 0, y: vnode.height / 2 },
      );
      group.attr({
        transform: `${transform} scale(${scale * TRANSFORM_3D_SCALE})`,
      });

      setImageSizeChange(vnode);
    }
  });

  component.on('position-change', () => {
    shapeManager.remove([vnode.key]);
    const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(vnode.styles.is3d);
    const { x, y } = vnode.position;
    const svg = component.findOne('.position');
    svg.attr({ x, y });
    // 设置延长线
    core.lines.add(vnode, is3D, isFlat);
    core.lines.draw(vnode.key);
  });

  component.on('checked-change', () => {
    const {
      component,
      styles,
      isChecked,
    } = vnode;

    const src = getStylesVal(styles.image);

    const group = component.findOne('.g1');
    const rect = component.findOne('.image-rect');
    const image = component.findOne('.image');
    if (src === image?.attr('href'))  {
      const w = image.attr('width');
      const h = image.attr('height');
      if (!isChecked && rect) {
        rect.remove();
      } else if (!rect && isChecked) {
        group.rect().attr({
          x: (-1 * w) / 2,
          y: (-1 * h) / 2,
          width: w,
          height: h,
          stroke: '#006eff',
          fill: 'none',
          'stroke-width': '4',
          class: 'image-rect',
        });
      }
    }
  });


  component.on('attach-change', () => {
    const { is3D, isFlat } = parseStyle(vnode);
    // connection
    setPlaneShapeConnection(vnode, is3D, isFlat);
  });
  return component;
};

export const exports = async (core, vnode, mode) => await render(core, vnode, mode);
