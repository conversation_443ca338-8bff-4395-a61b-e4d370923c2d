import DOMPurify from 'dompurify';

// 处理svg字符串，防止svg里面夹带恶意代码
export const sanitizedSvg = (svg) => {
  if (!svg) return '';
  // @ts-ignore
  const filterSvg = svg?.replaceAll('script', '')?.replaceAll('href', '')
    ?.replaceAll('iframe', '');
  return filterSvg;
};

// 处理用户输入内容，防止用户输入恶意代码
export const sanitizedHtml = (html) => {
  const sanitizedHTML = DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [], // 不允许任何 HTML 标签
    FORBID_ATTR: ['href'], // 禁止 href 属性
    FORBID_TAGS: ['script'], // 禁止 script 标签
  });
  return sanitizedHTML;
};
