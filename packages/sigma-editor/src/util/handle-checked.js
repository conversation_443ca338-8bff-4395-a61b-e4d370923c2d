import { SVG } from '@svgdotjs/svg.js';
import store from '../store';
import { NOT_RAISE_WHEN_CHECKED_TYPES } from './constants';

export function handleCheckedChange(event, vnode) {
  if (
    !vnode
    || vnode.attach
    || NOT_RAISE_WHEN_CHECKED_TYPES.includes(vnode.type)
  ) return;

  handleOtherChecked(event, vnode);
}

function handleOtherChecked(event, vnode) {
  const { initial = false } = event.detail || {};

  const { key, isChecked, component } = vnode;
  const className = 'checked-shapes';
  const { container: { gCache } } = store.getState();
  let wrapper = gCache.findOne(`.${className}`);
  if (!wrapper) {
    wrapper = gCache.group({ class: className });
  }

  if (isChecked) {
    const ghost = new SVG().group()
      .attr({ key });
    ghost.insertAfter(component);
    component.remember('ghost', ghost);
    component.toParent(wrapper);
  } else {
    if (initial) return;

    const ghost = component.remember('ghost');
    ghost?.replace(component);
    component.forget('ghost');
  }

  component.off('deleted.checked');
  component.on('deleted.checked', () => {
    component?.remember('ghost')?.remove();
  });
}
