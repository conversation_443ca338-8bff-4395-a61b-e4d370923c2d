import { SVG } from '@svgdotjs/svg.js';
import store, { ALIGNMENT_LINES_DELETE } from '../store';
import { NOT_RAISE_WHEN_CHECKED_TYPES } from './constants';
import { getChildGroups } from './get-nearest-group';
import { handleProductQueue } from './handle-queue';

export function bindEvents(core, vnode) {
  if (!vnode?.component) return;
  handleAttachEvent(core, vnode);
  handleGroupQueue(core, vnode);
  handleProductQueue(vnode);
}

function handleGroupQueue(core, vnode) {
  const { type, component } = vnode;
  if (!NOT_RAISE_WHEN_CHECKED_TYPES.includes(type)) return;
  component.on('start-move', () => {
    // console.log('start', vnode.key);
    const { shapes } = core.data;
    const children = getChildGroups(shapes, vnode);
    for (const key of [vnode.key].concat(children)) {
      const { component } = shapes[key];
      if (!component) continue;
      if (component.remember('ghost')) {
        component.front();
        continue;
      }

      const ghost = new SVG().group()
        .attr({ key });
      ghost.insertAfter(component);
      component.remember('ghost', ghost);
      component.front();

      component.off('deleted.group-queue');
      component.on('deleted.group-queue', () => {
        component.remember('ghost')?.remove();
      });
    }
  });

  component.on('end-move', () => {
    // console.log('end',  vnode.key);
    const { shapes } = core.data;
    const children = getChildGroups(shapes, vnode);
    for (const key of [vnode.key].concat(children)) {
      const { component } = shapes[key];
      if (!component) continue;
      const ghost = component.remember('ghost');
      ghost?.replace(component);
      component.forget('ghost');
    }
  });
}

function handleAttachEvent(core, vnode) {
  const { component } = vnode;
  component.on('style-changed', (e) => {
    const { initial } = e.detail || {};
    if (vnode.attach) {
      // 通知父组件
      const { shapes } = core.data;
      const parent = shapes[vnode.attach];
      parent?.component?.fire('attach-change', {
        type: initial ? 'create' : 'changed',
      });
    }
  });

  component.on('deleted', () => {
    if (vnode.attach) {
      // 通知父组件
      const { shapes } = core.data;
      const parent = shapes[vnode.attach];
      parent?.component?.fire('attach-change', {
        type: 'deleted',
      });
    }

    store.dispatch({
      type: ALIGNMENT_LINES_DELETE,
      value: vnode.key,
    });
  });
};
