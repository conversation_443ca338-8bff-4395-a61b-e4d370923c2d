import chroma from 'chroma-js';

export const getTransitionColor = (hex, opacity = 1, light = false) => {
  hex = hex === 'transparent' ? '#ffffff' : hex;
  const base = light ? chroma('#ffffff') : chroma('#000000');

  return chroma.mix(base, hex, opacity, 'rgb').hex();
};

export const createShadesBSP = (hex) => {
  const baseLight = chroma('#ffffff');
  const baseDark = chroma('#000000');
  const interval = 0.08;

  return [
    chroma.mix(hex, baseLight, 8 * interval, 'rgb').hex(),
    chroma.mix(hex, baseLight, 6 * interval, 'rgb').hex(),
    chroma.mix(hex, baseLight, 4 * interval, 'rgb').hex(),
    chroma.mix(hex, baseLight, 2 * interval, 'rgb').hex(),
    hex,
    chroma.mix(hex, baseDark, 2 * interval, 'rgb').hex(),
    chroma.mix(hex, baseDark, 4 * interval, 'rgb').hex(),
    chroma.mix(hex, baseDark, 6 * interval, 'rgb').hex(),
    chroma.mix(hex, baseDark, 8 * interval, 'rgb').hex(),
  ];
};

export const createShadesRGB = (hex) => {
  const [r, g, b] = chroma(hex).rgb();

  return [
    chroma
      .rgb(r + (255 - r) * 0.9, g + (255 - g) * 0.9, b + (255 - b) * 0.9)
      .hex(),
    chroma
      .rgb(r + (255 - r) * 0.75, g + (255 - g) * 0.75, b + (255 - b) * 0.75)
      .hex(),
    chroma
      .rgb(r + (255 - r) * 0.6, g + (255 - g) * 0.6, b + (255 - b) * 0.6)
      .hex(),
    chroma
      .rgb(r + (255 - r) * 0.3, g + (255 - g) * 0.3, b + (255 - b) * 0.3)
      .hex(),
    hex,
    chroma.rgb(r * 0.85, g * 0.85, b * 0.85).hex(),
    chroma.rgb(r * 0.7, g * 0.7, b * 0.7).hex(),
    chroma.rgb(r * 0.6, g * 0.6, b * 0.6).hex(),
    chroma.rgb(r * 0.45, g * 0.45, b * 0.45).hex(),
    chroma.rgb(r * 0.3, g * 0.3, b * 0.3).hex(),
  ];
};
