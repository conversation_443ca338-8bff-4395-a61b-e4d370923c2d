import store from '../store';
import {
  BASE_SLOPE,
  BASE_GRID_X,
  BASE_GRID_Y,
  BASE_STEP_X,
  BASE_STEP_Y,
  BASE_HALF_GRID_Y,
  BASE_HALF_GRID_X,
  BASE_HALF_GRID_2D,
  BASE_GRID_2D,
  BASE_BEVEL,
  TRANSFORM_3D_MATRIX,
  TRANSFORM_3D_MATRIX_REVERSE,
  CONNECTION_BOTTOM_KEYS,
  NOT_GROUPABLE_TYPE,
  SIGMA_TEXTLABEL_SHAPE,
  SIGMA_LINE_SHAPE,
  ADSORPTION_POINT_WIDTH,
  ADSORPTION_POINT_HEIGHT,
  BASE_SIZE_2D,
} from './constants';
import { round } from 'lodash';

/**
 * 将 point 数组转换为 point 属性字符串
 * @param {*} arr point 数组 [{x, y}...]
 * @returns 多边形 point 字符串
 */
export function arrayPointsToStringPoints(arr = []) {
  let stringPoints = '';
  for (let i = 0; i < arr.length; i++) {
    const { x, y } = arr[i];
    if (i === arr.length - 1) {
      stringPoints += `${x} ${y}`;
      break;
    }
    stringPoints += `${x} ${y}, `;
  }
  return stringPoints;
}

/**
 * 获取偏移量坐标
 * @param {*} polygonArrow 多边形坐标数组
 * @param {*} offsetX X轴偏移量
 * @param {*} offsetY Y轴偏移量
 * @param {*} lock 锁定坐标（下标）不计算偏移量
 * @param {*} sign 参考坐标，图形坐标最小值
 * @returns 计算后的数组
 */
export function computePolygonArrow(
  polygonArrow,
  offsetX,
  offsetY,
  lock,
  _sign,
) {
  const nextPolygonArrow = [];
  for (let i = 0; i < polygonArrow.length; i++) {
    const polygonPoint = polygonArrow[i];
    const nextPolygonPoint = {};
    if (lock && lock.includes(i)) {
      nextPolygonPoint.x = polygonPoint.x;
      nextPolygonPoint.y = polygonPoint.y;
    } else {
      nextPolygonPoint.x = polygonPoint.x + offsetX;
      nextPolygonPoint.y = polygonPoint.y + offsetY;
    }
    nextPolygonArrow.push(nextPolygonPoint);
  }
  return nextPolygonArrow;
}

/**
 * 获取一个坐标的 SigmaPoint 规范坐标
 * @param {*} point 一个坐标点 {x, y}
 * @param {*} offsetX X 轴偏移量 （某些坐标会有初始值，需要在标准值的基础上做偏移）
 * @param {*} offsetY Y 轴偏移量
 * @returns SigmaPoint {x, y}
 */
export function getSigmaPoint(point, opts = {}) {
  const { offsetX = 0, offsetY = 0, step = 1 } = opts;

  const move = 3 + step;
  const quarter = BASE_STEP_Y * step;  // 9.25
  const one = quarter * 4; // 37

  const pos = { x: point.x, y: point.y };
  const kx = (point.x + 8) >> move;
  const o2 = point.y % one;
  const ko2 = Math.round(o2 / quarter);
  const km = (kx - ko2) % 2 === 0 ? 0 : -1;
  const ko2y = Math.floor((ko2 + km) * quarter);

  pos.x = (kx << move) + offsetX;
  pos.y = ~~(point.y / one) * one + ko2y + offsetY;

  return { ...point, ...pos };
}

// 获取2D模式下的规范坐标
export function get2DSigmaPoint(point, opts = {}) {
  const { offsetX = 0, offsetY = 0, step = 1 } = opts;

  const half = BASE_HALF_GRID_2D * step;
  const one = half * 2;

  const pos = { x: point.x, y: point.y };
  const kx = ~~(point.x / one);
  const ox = point.x % one;
  const kxm = Math.floor(Math.round(ox / half) * half);

  const ky = ~~(point.y / one);
  const oy = point.y % one;
  const kym = Math.floor(Math.round(oy / half) * half);

  pos.x = kx * one + kxm + offsetX;
  pos.y = ky * one + kym + offsetY;

  pos.x = Math.round(pos.x / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D;
  pos.y = Math.round(pos.y / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D;

  return { ...point, ...pos };
}

// 3D 坐标转 2D 坐标
export function transform3DTo2D(point, exact = false, offsetX = 0, offsetY = 0) {
  const { x, y } = point;
  const bx = x + y / BASE_SLOPE;
  const by = y - BASE_SLOPE * x;
  const _x = exact ? ((bx / BASE_HALF_GRID_X) * BASE_HALF_GRID_2D)
    :  Math.floor(Math.round(bx / BASE_HALF_GRID_X) * BASE_HALF_GRID_2D);
  const _y = exact ? ((by / BASE_HALF_GRID_Y) * BASE_HALF_GRID_2D)
    : Math.floor(Math.round(by / BASE_HALF_GRID_Y) * BASE_HALF_GRID_2D);
  const pos = { x: _x + offsetX, y: _y + offsetY };
  return pos;
}

// 2D 坐标转 3D 坐标
export function transform2DTo3D(point, exact = false, offsetX = 0, offsetY = 0) {
  const { x, y } = point;
  const kby = exact ?  y / BASE_HALF_GRID_2D : Math.round(y / BASE_HALF_GRID_2D);
  const kbx = exact ? x / BASE_HALF_GRID_2D :  Math.round(x / BASE_HALF_GRID_2D);
  const _x = (kbx - kby) * BASE_STEP_X;
  const _y = (kbx + kby) * BASE_STEP_Y;

  return { x: _x + offsetX, y: _y + offsetY };
}

/**
 * 获取在 3D 模式下
 * @param {*} p1 {x, y}
 * @param {*} p2 {x, y, direction: t / l（上 / 左）} 如果没有则是点到点
 * @param {*} w 宽度
 * @param {*} d 深度
 * @returns [stepX, stepY] x 轴的步长， y轴步长
 */
export function getPointToPointStep(p1, p2, w = 1, d = 1) {
  const offsetb1 = p2.y - p2.x * BASE_SLOPE - p1.y + p1.x * BASE_SLOPE;
  const offsetb2 = p2.y + p2.x * BASE_SLOPE - p1.y - p1.x * BASE_SLOPE;

  let stepX = round(offsetb2 / BASE_BEVEL, 2);
  let stepY = round(offsetb1 / BASE_BEVEL, 2);
  if (p2.direction === 't') {
    stepY += d / 2;
  } else if (p2.direction === 'l') {
    stepX += w / 2;
  }

  return [stepX, stepY];
}


/**
 * 兼容旧版
 *  3d，平面投影（flat），旋转（route）转换
 * 计算太过麻烦，直接通过拼接结果获取更简单。
 * @param {*} isFlat 正交投影
 * @param {*} is3D 是否为3D展示
 * @param {*} route 旋转角度
 * @returns 不同参数组合的 transfrom
 */
export function flatAnd3DAndRoutCrossProduct(
  isFlat = false,
  is3D = true,
  route = 'e',
) {
  if (is3D) {
    if (isFlat) {
      switch (route) {
        case 'e':
        case 'n':
          return 'matrix(0.707, 0.409, 0, 0.816, 0, 0) translate(26 0)';
        case 's':
        case 'w':
          return 'matrix(0.707, -0.409, 0, 0.816, 0, 0) translate(26 0)';
        default:
          console.error(`转换值错误，请检查设置的值: ${route}`);
      }
    } else {
      switch (route) {
        case 'e':
          return 'matrix(0.707 0.409 -0.707 0.409 0 0)';
        case 's':
          return 'matrix(0.707 -0.409 0.707 0.409 0 0)';
        case 'n':
          return 'matrix(-0.707 -0.409 0.707 -0.409 0 0)';
        case 'w':
          return 'matrix(-0.707 0.409 -0.707 -0.409 0 0)';
        default:
          console.error(`转换值错误，请检查设置的值: ${route}`);
      }
    }
  }
  switch (route) {
    case 'e':
      return '';
    case 's':
      return 'rotate(270)';
    case 'n':
      return 'rotate(180)';
    case 'w':
      return 'rotate(90)';
    default:
      console.error(`转换值错误，请检查设置的值: ${route}`);
  }
}

/**
 * 新版
 *  3d，平面投影（flat），旋转（route）转换
 * 计算太过麻烦，直接通过拼接结果获取更简单。
 * @param {*} isFlat 正交投影
 * @param {*} is3D 是否为3D展示
 * @param {*} route 旋转角度
 * @returns 不同参数组合的 transfrom
 */
export function getFlatAndRouteTransform(
  isFlat = false,
  is3D = true,
  route = 'e',
  transformOrigin = { x: 0, y: 0 },
) {
  const { x, y } = transformOrigin;
  const matrix = is3D
    ? (isFlat ? 'matrix(0.707, 0.409, 0, 0.816, 0, 0)' : 'matrix(0.707 0.409 -0.707 0.409 0 0)')
    : '';
  const transform = `${is3D ? matrix : ''} translate(${-x} ${-y})`;
  const routeList = {
    e: '',
    s: 'rotate(270)',
    n: 'rotate(180)',
    w: 'rotate(90)',
  };
  if (routeList[route] === undefined)console.error(`转换值错误，请检查设置的值: ${route}`);
  return `${transform} ${routeList[route]}`;
}

/**
 * 仅对有四个方向连接点的元素进行位置转换标记
 * 返回具有四个方向的对象，分别是 上： t， 下：b，左：l，右：r;
 * @param {*} connection 连接点
 * @param {*} is3D 是否为 3D 模式
 * @returns {t: {x, y}, b: {x, y}, l: {x, y}, r: {x, y}}
 */
export const connectionsToDirections = (connection, is3D) => {
  if (connection.length !== 4) return null;
  const directions = {
    t: connection[0],
    b: connection[0],
    l: connection[0],
    r: connection[0],
  };
  if (is3D) {
    // t => max_x, min_y
    // r => max_x, max_y
    // l => min_x, min_y
    // b => min_x, max_y
    const sortConnections = connection.sort((a, b) => a.y - b.y);
    if (sortConnections[0].x > sortConnections[1].x) {
      [directions.t, directions.l] = sortConnections;
    } else {
      [directions.l, directions.t] = sortConnections;
    }
    if (sortConnections[2].x > sortConnections[3].x) {
      [,, directions.r, directions.b] = sortConnections;
    } else {
      [,, directions.b, directions.r] = sortConnections;
    }
  } else {
    // t => min_y, b => max_y, l => min_x, r => max_x
    for (let i = 0; i < 4; i++) {
      const connector = connection[i];
      if (directions.t.y > connector.y) {
        directions.t = connector;
      }
      if (directions.b.y < connector.y) {
        directions.b = connector;
      }
      if (directions.l.x > connector.x) {
        directions.l = connector;
      }
      if (directions.r.x < connector.x) {
        directions.r = connector;
      }
    }
  }
  return directions;
};

/**
 * 获取两个坐标点与水平轴形成的角度，-180 ~ 180
 * @param {*} p1 坐标点1 {x, y}
 * @param {*} p2 坐标点2
 * @returns 角度 -180 ~ 180 deg
 */
export const getPointToPointDegree = (p1, p2) => {
  const radian = Math.atan2(p1.y - p2.y, p1.x - p2.x);
  const angle = (180 / Math.PI) * radian;
  return { radian, angle };
};

/**
 *
 * @param {*} poly 多边形坐标数组 [{x, y}, ...]
 * @param {*} pt 坐标 {x, y}
 * @returns 坐标 pt 是否在多边形数组是否包含的路径范围内 boolean
 */
export function isPointInPoly(poly, pt) {
  let c = false;
  for (let i = -1, l = poly.length, j = l - 1; ++i < l; j = i) ((poly[i].y <= pt.y && pt.y < poly[j].y)
      || (poly[j].y <= pt.y && pt.y < poly[i].y))
      && pt.x
        < ((poly[j].x - poly[i].x) * (pt.y - poly[i].y))
          / (poly[j].y - poly[i].y)
          + poly[i].x
      && (c = !c);
  return c;
}

/**
 * 获取属性自定义值
 * @param {*} style styles 的属性值，{name, default, value}
 * @returns 返回默认值或直接值
 */
export function getStylesVal(style = {}) {
  return style.value === null ? style.default : style.value;
}

/**
 * 获取 3D 模式下 BlockShape 的连接点
 * @param {*} vnode BlockShape VNode节点数据
 * @param {*} outline BlockShape 的 outline 数据，为一个长度为 6 的数组,根据基础的 outline 计算得到
 * @returns connections: [{x, y}]
 */
export function getBlockShapeConnection(vnode, outline) {
  const { styles } = vnode;
  const height = getStylesVal(styles.height);
  const depth = getStylesVal(styles.depth);
  const width = getStylesVal(styles.width);

  const conn1 = {
    x: (outline[2].x + outline[3].x) / 2,
    y: (outline[2].y + outline[3].y) / 2,
  };
  const conn2 = {
    x: (outline[3].x + outline[4].x) / 2,
    y: (outline[3].y + outline[4].y) / 2,
  };
  const conn3 = { x: null, y: null };
  const conn4 = { x: null, y: null };
  const h = 2 * height;
  const d = 2 * depth;
  let conn3Sign = h - depth;
  let conn4Sign = h - width;

  if (conn3Sign >= 0) {
    conn3.x = (conn3Sign / h) * outline[2].x + ((h - conn3Sign) / h) * outline[1].x;
    conn3.y = (conn3Sign / h) * outline[2].y + ((h - conn3Sign) / h) * outline[1].y;
  } else {
    conn3Sign = -1 * conn3Sign;
    conn3.x = (conn3Sign / d) * outline[0].x + ((d - conn3Sign) / d) * outline[1].x;
    conn3.y = (conn3Sign / d) * outline[0].y + ((d - conn3Sign) / d) * outline[1].y;
  }

  if (conn4Sign >= 0) {
    conn4.x = (conn4Sign / h) * outline[4].x + ((h - conn4Sign) / h) * outline[5].x;
    conn4.y = (conn4Sign / h) * outline[4].y + ((h - conn4Sign) / h) * outline[5].y;
  } else {
    conn4Sign = -1 * conn4Sign;
    conn4.x = (conn4Sign / d) * outline[0].x + ((d - conn4Sign) / d) * outline[5].x;
    conn4.y = (conn4Sign / d) * outline[0].y + ((d - conn4Sign) / d) * outline[5].y;
  }
  return [conn1, conn2, conn3, conn4];
}

/**
 * 获取 2D 模式下的可连接点
 * @param {*} pos 位置
 * @param {*} h 高
 * @param {*} w 宽
 */
export function get2DBlockShapeConnection(pos, h, w) {
  const width = w * BASE_GRID_2D;
  const height = h * BASE_GRID_2D;
  return [
    { x: width / 2, y: 0 },
    { x: 0, y: height / 2 },
    { x: width / 2, y: height },
    { x: width, y: height / 2 },
  ];
}

/**
 *
 * @param {*} p1 svg 坐标点1 {x, y}
 * @param {*} p2 svg 坐标点2 {x, y}
 * @returns [{x, y}, {x, y}, {x, y}, {x, y}] 3D 矩形(polygon)坐标点数组
 */
export function getPolyRectPoint(p1, p2) {
  const x = p2.x - p1.x;
  const y = p2.y - p1.y;
  const absX = Math.abs(x);
  const absY = Math.abs(y);
  const absZ = absY - absX * Math.tan((30 / 180) * Math.PI);
  const offsetY = absZ * Math.sin((30 / 180) * Math.PI);
  const offsetX = absZ * Math.cos((30 / 180) * Math.PI);

  const p3 = {};
  const p4 = {};
  if (x >= 0) {
    if (y >= 0) {
      p3.x = p2.x + offsetX;
      p3.y = p2.y - offsetY;
      p4.x = p1.x - offsetX;
      p4.y = p1.y + offsetY;
    } else {
      p3.x = p2.x + offsetX;
      p3.y = p2.y + offsetY;
      p4.x = p1.x - offsetX;
      p4.y = p1.y - offsetY;
    }
  } else {
    if (y >= 0) {
      p3.x = p1.x + offsetX;
      p3.y = p1.y + offsetY;
      p4.x = p2.x - offsetX;
      p4.y = p2.y - offsetY;
    } else {
      p3.x = p1.x + offsetX;
      p3.y = p1.y - offsetY;
      p4.x = p2.x - offsetX;
      p4.y = p2.y + offsetY;
    }
  }
  return [p1, p4, p2, p3];
}

/**
 * 获取以两点为对角顶点的矩形坐标列表
 * @param {*} p1
 * @param {*} p2
 * @returns [{x, y}, {x, y}, {x, y}, {x, y}]
 */
export function get2DPolyRectPoint(p1, p2) {
  return [p1, { x: p2.x, y: p1.y }, p2, { x: p1.x, y: p2.y }];
}

/**
 * 通过坐标，计算四个边的点，通过点线相交获取交点，计算出四个交点坐标，绘制矩形
 * @param {*} shapeFlatCoordinates 任意个投影坐标列表，[[{x, y}...]...]
 * @returns [{x,y}, {x,y}, {x,y}, {x,y}] 长度为 4 的矩形坐标。
 */
export function getIntersectionPolyCoordinates(
  shapeFlatCoordinates,
  padding = 0,
) {
  const tl = {}; // top-left
  const tr = {}; // top-right
  const bl = {}; // bottom-left
  const br = {}; // bottom-right
  let btl = -Infinity;
  let btr = Infinity;
  let bbl = -Infinity;
  let bbr = Infinity; // b-top-left, b-top-right，b-bottom-left, b-bottom-right
  const ktlbr = -1 * BASE_SLOPE; // k-top-left-bottom-right
  const ktrbl = BASE_SLOPE; // k-top-right-bottom-left
  // 计算出四个偏移量 b 值；
  // BASE_GRID_X
  // BASE_GRID_Y
  for (let i = 0; i < shapeFlatCoordinates.length; i++) {
    // 单个 vnode 的投影坐标
    const shapeFlatCoordinate = shapeFlatCoordinates[i];
    // 计算每个点的直线方程
    for (let j = 0; j < shapeFlatCoordinate.length; j++) {
      const { x, y } = shapeFlatCoordinate[j];
      // y = kx + b; k
      // b = y - kx
      const btlbr = y - ktlbr * x;
      const btrbl = y - ktrbl * x;
      btl = btl < btlbr ? btlbr : btl;
      btr = btr > btrbl ? btrbl : btr;
      bbl = bbl < btrbl ? btrbl : bbl;
      bbr = bbr > btlbr ? btlbr : bbr;
    }
  }
  const offsetx = padding * BASE_GRID_X * 2;
  const offsety = padding * BASE_GRID_Y * 2;
  // 通过斜截式获取方程式，两直线交点公式计算交点
  // x = (b2 - b1) / (k1 - k2)
  // y = (b2 - b1) / (k1 - k2) * k + b;
  tl.x = (btr - btl) / (ktlbr - ktrbl);
  tl.y = tl.x * ktlbr + btl;
  tr.x = (bbr - btr) / (ktrbl - ktlbr);
  tr.y = tr.x * ktrbl + btr;
  bl.x = (btl - bbl) / (ktrbl - ktlbr);
  bl.y = bl.x * ktrbl + bbl;
  br.x = (bbl - bbr) / (ktlbr - ktrbl);
  br.y = br.x * ktlbr + bbr;
  // 上 右 下 左
  const o = [
    { x: tr.x, y: tr.y - offsety },
    { x: tl.x + offsetx, y: tl.y },
    { x: bl.x, y: bl.y + offsety },
    { x: br.x - offsetx, y: br.y },
  ];
  return o.map(p => getSigmaPoint(p));
  // return o;
}

/**
 * 获取一组坐标的最小矩形轮廓坐标
 * @param {*} shapeFlatCoordinates
 * @param {*} padding
 * @returns [{x, y}, {x, y}, {x, y}, {x, y}]
 */
export function get2DIntersectionPolyCoordinates(
  shapeFlatCoordinates,
  padding = 0,
) {
  let minx = Infinity;
  let miny = Infinity;
  let maxx = -Infinity;
  let maxy = -Infinity;
  for (let i = 0; i < shapeFlatCoordinates.length; i++) {
    const shapeFlatCoordinate = shapeFlatCoordinates[i];
    for (let j = 0; j < shapeFlatCoordinate.length; j++) {
      const { x, y } = shapeFlatCoordinate[j];
      if (x < minx) minx = x;
      if (x > maxx) maxx = x;
      if (y < miny) miny = y;
      if (y > maxy) maxy = y;
    }
  }
  const offset = BASE_GRID_2D * padding * 2;
  minx -= offset;
  miny -= offset;
  maxx += offset;
  maxy += offset;
  if (
    !Number.isFinite(minx)
    || !Number.isFinite(miny)
    || !Number.isFinite(maxx)
    || !Number.isFinite(maxy)
  ) {
    return [];
  }
  // 上 右 下 左
  return [
    get2DSigmaPoint({ x: minx, y: miny }),
    get2DSigmaPoint({ x: maxx, y: miny }),
    get2DSigmaPoint({ x: maxx, y: maxy }),
    get2DSigmaPoint({ x: minx, y: maxy }),
  ];
}

/**
 * 判断两条线段是否不相交
 * @param {*} x1
 * @param {*} y1
 * @param {*} x2
 * @param {*} y2
 * @param {*} x3
 * @param {*} y3
 * @param {*} x4
 * @param {*} y4
 * @returns boolean 相交为 false, 不相交为 true
 */
export function judgeIntersect(p1, p2, p3, p4) {
  const { x: x1, y: y1 } = p1;
  const { x: x2, y: y2 } = p2;
  const { x: x3, y: y3 } = p3;
  const { x: x4, y: y4 } = p4;
  if (
    !(
      Math.min(x1, x2) <= Math.max(x3, x4)
      && Math.min(y3, y4) <= Math.max(y1, y2)
      && Math.min(x3, x4) <= Math.max(x1, x2)
      && Math.min(y1, y2) <= Math.max(y3, y4)
    )
  ) {
    return false;
  }
  const u = (x3 - x1) * (y2 - y1) - (x2 - x1) * (y3 - y1);
  const v = (x4 - x1) * (y2 - y1) - (x2 - x1) * (y4 - y1);
  const w = (x1 - x3) * (y4 - y3) - (x4 - x3) * (y1 - y3);
  const z = (x2 - x3) * (y4 - y3) - (x4 - x3) * (y2 - y3);
  return u * v <= 0.00000001 && w * z <= 0.00000001;
}

/**
 * 通过 image 对象 获取他的 base64
 * @param {*} img 图片对象
 * @returns base64 image/png
 */
export function image2Base64(img) {
  const canvas = document.createElement('canvas');
  canvas.width = img.width;
  canvas.height = img.height;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(img, 0, 0, img.width, img.height);
  const dataURL = canvas.toDataURL('image/png');
  return dataURL;
}
export function getImgBase64Info(url) {
  return new Promise((resolve, reject) => {
    let base64 = '';
    const img = new Image();
    img.src = `${url}?stamp=${Date.now()}`;
    img.setAttribute('crossOrigin', 'anonymous');
    img.onload = function () {
      base64 = image2Base64(img);
      resolve({ base64, width: img.width, height: img.height });
    };
    img.onerror = function () {
      reject(new Error(`failed load image ${url}`));
    };
  });
}

/**
 * 获取一个简单多边形外轮廓，距离为 padding (负值则向内收缩)
 * 相邻三点形成两个向量，使用向量与叉积与角度计算偏移坐标
 * 锐角时直接加入坐标，钝角时，补充一个转角坐标
 * @param {*} data 一组区域坐标数据 [...{x, y}]
 * @return 返回一组外轮廓区域坐标数据 [...{x, y}]
 */
export function getContourCoordinates(data, padding = 20) {
  const coordinates = [];
  const len = data.length;
  for (let i = 0; i < len; i++) {
    const prev = i - 1 < 0 ? len - 1 : i - 1;
    const next = (i + 1) % len;

    const x1 = data[i].x - data[prev].x;
    const y1 = data[i].y - data[prev].y;
    const x2 = data[next].x - data[i].x;
    const y2 = data[next].y - data[i].y;

    const d_A = (x1 ** 2 + y1 ** 2) ** 0.5;
    const d_B = (x2 ** 2 + y2 ** 2) ** 0.5;
    const Vec_Cross = x1 * y2 - x2 * y1;

    const sin_theta = Vec_Cross / (d_A * d_B);
    if (Math.abs(sin_theta) < 0.15) continue;

    const dv = padding / sin_theta;
    if (sin_theta < 0) {
      const Q1_x = data[i].x + (dv / d_A) * x1 - (dv / d_B) * x2;
      const Q1_y = data[i].y + (dv / d_A) * y1 - (dv / d_B) * y2;

      coordinates.push({ x: Q1_x, y: Q1_y });
    } else {
      const Q1_x = data[i].x + (dv / d_A / 2) * x1 - (dv / d_B) * x2;
      const Q1_y = data[i].y + (dv / d_A / 2) * y1 - (dv / d_B) * y2;

      const Q2_x = data[i].x + (dv / d_A) * x1 - (dv / d_B / 2) * x2;
      const Q2_y = data[i].y + (dv / d_A) * y1 - (dv / d_B / 2) * y2;

      coordinates.push({ x: Q1_x, y: Q1_y }, { x: Q2_x, y: Q2_y });
    }
  }
  return coordinates;
}

/**
 * 获取两个线段的交点
 * @param {*} a 线段1起点坐标
 * @param {*} b 线段1终点坐标
 * @param {*} c 线段2起点坐标
 * @param {*} d 线段2终点坐标
 * @returns false ｜ {x, y} 没有交点或者交点为 {x, y}
 */
export const segmentsIntersection = (a, b, c, d) => {
  const area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);
  const area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);
  if (area_abc * area_abd >= 0) {
    return false;
  }
  const area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);
  const area_cdb = area_cda + area_abc - area_abd;
  if (area_cda * area_cdb >= 0) {
    return false;
  }
  const t = area_cda / (area_abd - area_abc);
  const dx = t * (b.x - a.x);
  const dy = t * (b.y - a.y);
  return { x: a.x + dx, y: a.y + dy };
};

/**
 * 计算两个矩阵相乘
 * @param {*} matrix 转换矩阵
 * @returns 返回原矩阵与转换矩阵的乘积
 */
export const matrixTransform = (matrix) => {
  const [a1, b1, c1, d1] = TRANSFORM_3D_MATRIX;
  const [a2, b2, c2, d2] = matrix;
  return [
    a1 * a2 + c1 * b2,
    a1 * c2 + c1 * d2,
    b1 * a2 + d1 * b2,
    b1 * c2 + d1 * d2,
    0,
    0,
  ];
};

/**
 * 矩形
 * 实线矩形： -64 685, 336 453, -64 222, -464 453
 * 虚线矩形： -791.4375 -881.8875, -791.4375 -316.575, -226.125 -316.575, -226.125 -881.8875
 * matrix(-0.707 -0.409 0.707 -0.409 0 0) => （a, b, c, d, e, f）;
 * -64 ~= -791.4375 * -0.707 + -881.8875 * 0.707 （x = ax + cy）
 * 685 ~= -791.4375 * -0.409 + -881.8875 * -0.409 (y = bx + dy)
 * 将 2.5D 坐标系坐标转换为 2D 坐标，并通过 transform 表达相同视
 * @param {*} coordinates 2.5D坐标列表
 * @returns [{x, y}...] 坐标列表
 */
export const transformFlatCoordinatesReverse = (coordinates) => {
  const flatCoordinates = [];
  const [a, b] = TRANSFORM_3D_MATRIX_REVERSE;
  for (let i = 0; i < coordinates.length; i++) {
    const { x, y } = coordinates[i];
    const ox = x / a;
    const oy = y / b;

    const _x = (ox + oy) / 2;
    const _y = (ox - oy) / -2;

    flatCoordinates.push({ x: _x, y: _y });
  }
  return flatCoordinates;
};

export const transformFlatCoordinates = (coordinates) => {
  /**
   * x = ax1 + cy1
   * y = bx1 + dy1
   *
   * x = a(x1 + y1)
   * y = b(x1 - y1)
   *
   * x / 0.707 = x1 + y1
   * y / -0.409 = x1 - y1
   *
   * _x = (x / 0.707 + y / -0.409) / 2;
   * _y = (x / 0.707 - y / -0.409) / 2;
   */
  const flatCoordinates = [];
  const [a, b] = TRANSFORM_3D_MATRIX;
  for (let i = 0; i < coordinates.length; i++) {
    const { x, y } = coordinates[i];
    const ox = x / a;
    const oy = y / b;

    const _x = (ox + oy) / 2;
    const _y = (ox - oy) / 2;

    flatCoordinates.push({ x: _x, y: _y });
  }
  return flatCoordinates;
};

// 解决 JSON.stringify 报错 Uncaught (in promise) TypeError: Converting circular structure to JSON
export const stringify = (obj) => {
  let cache = [];
  const str = JSON.stringify(obj, (_, value) => {
    if (typeof value === 'object' && value !== null) {
      if (cache.indexOf(value) !== -1) {
        // Circular reference found, discard key
        return;
      }
      // Store value in our collection
      cache.push(value);
    }
    return value;
  });
  cache = null; // reset the cache
  return str;
};

export function getPlaneShape2dConnection(startX, startY, width, height) {
  const connection = {
    t0: {
      x: startX,
      y: startY,
      w: ADSORPTION_POINT_HEIGHT,
      h: ADSORPTION_POINT_HEIGHT,
    },
    t2: {
      x: startX + width / 4 * 2,
      y: startY,
      isMid: true,
      w: ADSORPTION_POINT_WIDTH,
      h: ADSORPTION_POINT_HEIGHT,
    },
    r0: {
      x: startX + width,
      y: startY,
      w: ADSORPTION_POINT_HEIGHT,
      h: ADSORPTION_POINT_HEIGHT,
    },
    r2: {
      x: startX + width,
      y: startY + height / 4 * 2,
      isMid: true,
      w: ADSORPTION_POINT_HEIGHT,
      h: ADSORPTION_POINT_WIDTH,
    },
    b0: {
      x: startX + width,
      y: startY + height,
      w: ADSORPTION_POINT_HEIGHT,
      h: ADSORPTION_POINT_HEIGHT,
    },
    b2: {
      x: startX + width / 4 * 2,
      y: startY + height,
      isMid: true,
      w: ADSORPTION_POINT_WIDTH,
      h: ADSORPTION_POINT_HEIGHT,
    },
    l0: {
      x: startX,
      y: startY + height,
      w: ADSORPTION_POINT_HEIGHT,
      h: ADSORPTION_POINT_HEIGHT,
    },
    l2: {
      x: startX,
      y: startY + height / 4 * 2,
      isMid: true,
      w: ADSORPTION_POINT_HEIGHT,
      h: ADSORPTION_POINT_WIDTH,
    },
  };
  const halfWidth = width / 2;
  const halfHeight = height / 2;
  const xIntervalCount = Math.floor(halfWidth / ADSORPTION_POINT_WIDTH) - 1; // 起点和中点当中还可以放入几个点
  const yIntervalCount = Math.floor(halfHeight / ADSORPTION_POINT_WIDTH) - 1; // 起点和中点当中还可以放入几个点
  // 补齐上边线左半边坐标
  for (let i = 1; i <= xIntervalCount; i++) {
    const x = i * ADSORPTION_POINT_WIDTH + startX;
    const point = { x, y: startY, w: ADSORPTION_POINT_WIDTH, h: ADSORPTION_POINT_HEIGHT };
    connection[`t0-${i}-t2`] = point;
  }
  // 补齐上边线右半边坐标
  for (let i = 1; i <= xIntervalCount; i++) {
    const x = i * ADSORPTION_POINT_WIDTH + startX;
    const point = { x: width + startX - x, y: startY, w: ADSORPTION_POINT_WIDTH, h: ADSORPTION_POINT_HEIGHT };
    connection[`r0-${i}-t2`] = point;
  }
  // 补齐下边线左半边坐标
  for (let i = 1; i <= xIntervalCount; i++) {
    const x = i * ADSORPTION_POINT_WIDTH + startX;
    const point = { x, y: startY + height, w: ADSORPTION_POINT_WIDTH, h: ADSORPTION_POINT_HEIGHT };
    connection[`l0-${i}-b2`] = point;
  }
  // 补齐下边线右半边坐标
  for (let i = 1; i <= xIntervalCount; i++) {
    const x = i * ADSORPTION_POINT_WIDTH + startX;
    const point = { x: width + startX - x, y: startY + height, w: ADSORPTION_POINT_WIDTH, h: ADSORPTION_POINT_HEIGHT };
    connection[`b0-${i}-b2`] = point;
  }

  // 补齐左边线上半边坐标
  for (let i = 1; i <= yIntervalCount; i++) {
    const y = i * ADSORPTION_POINT_WIDTH + startY;
    const point = { x: startX, y, w: ADSORPTION_POINT_HEIGHT, h: ADSORPTION_POINT_WIDTH };
    connection[`t0-${i}-l2`] = point;
  }
  // 补齐左边线下半边坐标
  for (let i = 1; i <= yIntervalCount; i++) {
    const y = i * ADSORPTION_POINT_WIDTH + startY;
    const point = { x: startX, y: height + startY - y, w: ADSORPTION_POINT_HEIGHT, h: ADSORPTION_POINT_WIDTH };
    connection[`l0-${i}-l2`] = point;
  }

  // 补齐右边线上半边坐标
  for (let i = 1; i <= yIntervalCount; i++) {
    const y = i * ADSORPTION_POINT_WIDTH + startY;
    const point = { x: width + startX, y, w: ADSORPTION_POINT_HEIGHT, h: ADSORPTION_POINT_WIDTH };
    connection[`r0-${i}-r2`] = point;
  }
  // 补齐右边线下半边坐标
  for (let i = 1; i <= yIntervalCount; i++) {
    const y = i * ADSORPTION_POINT_WIDTH + startY;
    const point = { x: width + startX, y: height + startY - y, w: ADSORPTION_POINT_HEIGHT, h: ADSORPTION_POINT_WIDTH };
    connection[`b0-${i}-r2`] = point;
  }

  return connection;
}

// 自动折线的吸附点产品节点
export function getPlaneShape2dPolylineConnection(startX, startY, width, height) {
  const connection = {
    t0: {
      x: startX,
      y: startY,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: - BASE_HALF_GRID_2D + startX,
      y3d: - BASE_HALF_GRID_2D + startY,
    },
    t2: {
      x: startX + width / 4 * 2,
      y: startY,
      isMid: true,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: BASE_HALF_GRID_2D,
      y3d: - BASE_HALF_GRID_2D,
    },
    r0: {
      x: startX + width,
      y: startY,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: BASE_HALF_GRID_2D * 3,
      y3d: - BASE_HALF_GRID_2D,
    },
    r2: {
      x: startX + width,
      y: startY + height / 4 * 2,
      isMid: true,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: BASE_HALF_GRID_2D * 3,
      y3d: BASE_HALF_GRID_2D,
    },
    b0: {
      x: startX + width,
      y: startY + height,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: BASE_HALF_GRID_2D * 3,
      y3d: BASE_HALF_GRID_2D * 3,
    },
    b2: {
      x: startX + width / 4 * 2,
      y: startY + height,
      isMid: true,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: BASE_HALF_GRID_2D,
      y3d: BASE_HALF_GRID_2D * 3,
    },
    l0: {
      x: startX,
      y: startY + height,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: -BASE_HALF_GRID_2D,
      y3d: BASE_HALF_GRID_2D * 3,
    },
    l2: {
      x: startX,
      y: startY + height / 4 * 2,
      isMid: true,
      w: BASE_HALF_GRID_2D * 2,
      h: BASE_HALF_GRID_2D * 2,
      x3d: -BASE_HALF_GRID_2D,
      y3d: BASE_HALF_GRID_2D,
    },
  };
  return connection;
}

// 自动折线的吸附点组类型节点
export function getPlaneShape2dGroupPolylineConnection(startX, startY, width, height) {
  let widthUnit = undefined;
  let heightUnit = undefined;
  let vertexUnit = undefined;
  if (width > BASE_SIZE_2D && height > BASE_SIZE_2D) {
    // 如果组的宽高都大于了90
    widthUnit = width - BASE_HALF_GRID_2D * 2;
    heightUnit = height - BASE_HALF_GRID_2D * 2;
    vertexUnit = BASE_HALF_GRID_2D * 2;
  } else {
    // 如果组被缩小到小于90宽度或者高度, 则按4/1模式补齐
    vertexUnit = Math.min(width, height) / 4 * 2; // 顶点要求是正方形所以取小的一边的1/4的两倍做顶点的边长
    widthUnit = width - vertexUnit;
    heightUnit = height - vertexUnit;
  }
  const connection = {
    t0: {
      x: startX,
      y: startY,
      w: vertexUnit,
      h: vertexUnit,
      x3d: - vertexUnit / 2,
      y3d: - vertexUnit / 2,
    },
    t2: {
      x: startX + width / 4 * 2,
      y: startY,
      isMid: true,
      w: widthUnit,
      h: vertexUnit,
      x3d: vertexUnit / 2,
      y3d: - vertexUnit / 2,
    },
    r0: {
      x: startX + width,
      y: startY,
      w: vertexUnit,
      h: vertexUnit,
      x3d: vertexUnit / 2 + widthUnit,
      y3d: - vertexUnit / 2,
    },
    r2: {
      x: startX + width,
      y: startY + height / 4 * 2,
      isMid: true,
      w: vertexUnit,
      h: heightUnit,
      x3d: vertexUnit / 2 + widthUnit,
      y3d: vertexUnit / 2,
    },
    b0: {
      x: startX + width,
      y: startY + height,
      w: vertexUnit,
      h: vertexUnit,
      x3d: vertexUnit / 2 + widthUnit,
      y3d: vertexUnit / 2 + heightUnit,
    },
    b2: {
      x: startX + width / 4 * 2,
      y: startY + height,
      isMid: true,
      w: widthUnit,
      h: vertexUnit,
      x3d: vertexUnit / 2,
      y3d: vertexUnit / 2 + heightUnit,
    },
    l0: {
      x: startX,
      y: startY + height,
      w: vertexUnit,
      h: vertexUnit,
      x3d: -vertexUnit / 2,
      y3d: vertexUnit / 2 + heightUnit,
    },
    l2: {
      x: startX,
      y: startY + height / 4 * 2,
      isMid: true,
      w: vertexUnit,
      h: heightUnit,
      x3d: -vertexUnit / 2,
      y3d: vertexUnit / 2,
    },
  };
  return connection;
}

export function setPlaneShapeConnection(
  vnode,
  is3D,
  isFlat,
  startPoint,
) {
  const { width, height } = vnode;
  if (!(width && height)) return;

  if (!startPoint) startPoint = { x: -width / 2, y: - height };
  const { x: startX, y: startY } = startPoint;

  // cloneDeep 可以解决 stringify 时的循环引用问题
  vnode.d2_connection = getPlaneShape2dConnection(startX, startY, width, height);
  // 组节点折线吸附点
  vnode.d2_polyline_connection = getPlaneShape2dGroupPolylineConnection(startX, startY, width, height);
  if (vnode.sticky) {
    const { shapes } = store.getState().core.data;
    const child = shapes[vnode.sticky];
    if (child?.height) {
      CONNECTION_BOTTOM_KEYS.forEach((key) => {
        vnode.d2_connection[key].y += child.height;
      });
    }
  }

  let connection = {};
  let d3_polyline_connection = {};
  const { d2_connection: d2, d2_polyline_connection } = vnode;
  if (!is3D) {
    connection = d2;
    d3_polyline_connection = d2_polyline_connection;
  } else if (isFlat) {
    const keys = Object.keys(d2);
    for (let i = 0; i < keys.length; i ++) {
      connection[keys[i]] = {
        ...transform2DTo3D(d2[`b${i % 4}`], true),
        w: d2[keys[i]]?.w,
        h: d2[keys[i]]?.h,
        isMid: d2[keys[i]]?.isMid,
      };
    }
    const polylineKeys = Object.keys(d2_polyline_connection);
    for (let i = 0; i < polylineKeys.length; i ++) {
      d3_polyline_connection[polylineKeys[i]] = {
        ...transform2DTo3D(d2_polyline_connection[`b${i % 4}`], true),
        w: d2_polyline_connection[polylineKeys[i]]?.w,
        h: d2_polyline_connection[polylineKeys[i]]?.h,
        isMid: d2_polyline_connection[polylineKeys[i]]?.isMid,
        x3d: d2_polyline_connection[polylineKeys[i]]?.x3d,
        y3d: d2_polyline_connection[polylineKeys[i]]?.y3d,
      };
    }
  } else {
    for (const k in d2) {
      connection[k] = {
        ...transform2DTo3D(d2[k], true),
        w: d2[k].w,
        h: d2[k]?.h,
        isMid: d2[k]?.isMid,
      };
    }
    for (const k in d2_polyline_connection) {
      d3_polyline_connection[k] = {
        ...transform2DTo3D(d2_polyline_connection[k], true),
        w: d2_polyline_connection[k].w,
        h: d2_polyline_connection[k]?.h,
        isMid: d2_polyline_connection[k]?.isMid,
        x3d: d2_polyline_connection[k]?.x3d,
        y3d: d2_polyline_connection[k]?.y3d,
      };
    }
  }
  vnode.connection = connection;
  vnode.d3_polyline_connection = d3_polyline_connection;
}

export function getValidIcon(icon) {
  if (!icon.default && !icon.value) {
    return '';
  }
  const str = getStylesVal(icon);
  return `<svg ${str.split('<svg ').pop()}`;
}
// 将{x: number, y: number}[] 转化为 [number,number][]
export function parseObjectPointsToArray(points) {
  return points.reduce((acc, { x, y }) => {
    acc.push([x, y]);
    return acc;
  }, []);
}

export function isVnodeGroupable(vnode) {
  return !(NOT_GROUPABLE_TYPE.includes(vnode.type) || vnode.attach);
}

// 返回文本节点是否是线条的标签
export function isLineLabel(vnode, core) {
  if (!vnode.attach || vnode.type !== SIGMA_TEXTLABEL_SHAPE) return false;

  const { shapes } = core.data;
  return shapes[vnode.attach]?.type === SIGMA_LINE_SHAPE;
}


// 判断是否在组内
export function isInner(groupInfo, innerInfo) {
  const { x: x1, y: y1, w: w1, h: h1 } = groupInfo;
  const { x: x2, y: y2, w: w2, h: h2 } = innerInfo;

  // 计算小矩形的右下角坐标
  const x3 = x2 + w2;
  const y3 = y2 + h2;

  // 计算大矩形的右下角坐标
  const xMax = x1 + w1;
  const yMax = y1 + h1;

  const isInside = x2 >= x1 && y2 >= y1 && x3 <= xMax && y3 <= yMax;
  return isInside;
}

// 使用连线中的data.points来绘制连线
export function setUseLinePoints(status = true) {
  sessionStorage.setItem('UseLinePoints', status);
};

// 判断是否使用连线中的data.points来绘制连线
export function getUseLinePoints() {
  return sessionStorage.getItem('UseLinePoints') === 'true';
}
