import store from '../store';
import {
  BASE_HALF_GRID_2D,
  BASE_SIZE_2D,
  BASE_STEP_X,
  BASE_STEP_Y,
  BASE_BEVEL,
  BASE_GRID_Y,
  SIGMA_GRAPH_MODE_3D,
  SIGMA_PRODUCT_SHAPE,
  ADSORPTION_POINT_WIDTH,
  ADSORPTION_POINT_HEIGHT,
  GROUP_SHAPE_TYPES,
  SIGMA_REMARK_NOTE_SHAPE,
  SIGMA_TEXTLABEL_SHAPE,
} from './constants';
import { cloneDeep } from 'lodash';

export const connection = {
  t0: {
    x: 0,
    y: BASE_GRID_Y,
  },
  t1: {
    x: BASE_STEP_X,
    y: BASE_STEP_Y + BASE_GRID_Y,
  },
  t2: {
    x: BASE_STEP_X * 2,
    y: BASE_STEP_Y * 2 + BASE_GRID_Y,
  },
  t3: {
    x: BASE_STEP_X * 3,
    y: BASE_STEP_Y * 3 + BASE_GRID_Y,
  },

  l0: {
    x: - BASE_STEP_X * 4,
    y: BASE_STEP_Y * 4 + BASE_GRID_Y,
  },
  l1: {
    x: - BASE_STEP_X * 3,
    y: BASE_STEP_Y * 3 + BASE_GRID_Y,
  },
  l2: {
    x: - BASE_STEP_X * 2,
    y: BASE_STEP_Y * 2 + BASE_GRID_Y,
  },
  l3: {
    x: - BASE_STEP_X,
    y: BASE_STEP_Y + BASE_GRID_Y,
  },

  b0: {
    x: 0,
    y: BASE_BEVEL + BASE_STEP_Y * 4,
  },
  b1: {
    x: - BASE_STEP_X,
    y: BASE_BEVEL + BASE_STEP_Y * 3,
  },
  b2: {
    x: - BASE_STEP_X * 2,
    y: BASE_BEVEL + BASE_STEP_Y * 2,
  },
  b3: {
    x: - BASE_STEP_X * 3,
    y: BASE_BEVEL + BASE_STEP_Y,
  },

  r0: {
    x: BASE_STEP_X * 4,
    y: BASE_BEVEL,
  },
  r1: {
    x: BASE_STEP_X * 3,
    y: BASE_BEVEL + BASE_STEP_Y,
  },
  r2: {
    x: BASE_STEP_X * 2,
    y: BASE_BEVEL + BASE_STEP_Y * 2,
  },
  r3: {
    x: BASE_STEP_X,
    y: BASE_BEVEL + BASE_STEP_Y * 3,
  },
};

export const d2_connection = {
  t0: {
    x: 0,
    y: 0,
  },
  t1: {
    x: BASE_HALF_GRID_2D,
    y: 0,
  },
  t2: {
    x: BASE_HALF_GRID_2D * 2,
    y: 0,
  },
  t3: {
    x: BASE_HALF_GRID_2D * 3,
    y: 0,
  },

  l0: {
    x: 0,
    y: BASE_HALF_GRID_2D * 4,
  },
  l1: {
    x: 0,
    y: BASE_HALF_GRID_2D * 3,
  },
  l2: {
    x: 0,
    y: BASE_HALF_GRID_2D * 2,
  },
  l3: {
    x: 0,
    y: BASE_HALF_GRID_2D,
  },

  b0: {
    x: BASE_HALF_GRID_2D * 4,
    y: BASE_SIZE_2D,
  },
  b1: {
    x: BASE_HALF_GRID_2D * 3,
    y: BASE_SIZE_2D,
  },
  b2: {
    x: BASE_HALF_GRID_2D * 2,
    y: BASE_SIZE_2D,
  },
  b3: {
    x: BASE_HALF_GRID_2D,
    y: BASE_SIZE_2D,
  },

  r0: {
    x: BASE_SIZE_2D,
    y: 0,
  },
  r1: {
    x: BASE_SIZE_2D,
    y: BASE_HALF_GRID_2D,
  },
  r2: {
    x: BASE_SIZE_2D,
    y: BASE_HALF_GRID_2D * 2,
  },
  r3: {
    x: BASE_SIZE_2D,
    y: BASE_HALF_GRID_2D * 3,
  },
};

export const product_d2_connection = {
  t0: {
    x: 0,
    y: 0,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t0-1-t2']: {
    x: ADSORPTION_POINT_WIDTH,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t0-2-t2']: {
    x: ADSORPTION_POINT_WIDTH * 2,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t0-3-t2']: {
    x: ADSORPTION_POINT_WIDTH * 3,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t0-4-t2']: {
    x: ADSORPTION_POINT_WIDTH * 4,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  t2: {
    x: BASE_HALF_GRID_2D * 2,
    y: 0,
    isMid: true,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t2-1-r0']: {
    x: ADSORPTION_POINT_WIDTH * 6,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t2-2-r0']: {
    x: ADSORPTION_POINT_WIDTH * 7,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t2-3-r0']: {
    x: ADSORPTION_POINT_WIDTH * 8,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['t2-4-r0']: {
    x: ADSORPTION_POINT_WIDTH * 9,
    y: 0,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  l0: {
    x: 0,
    y: BASE_HALF_GRID_2D * 4,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['l0-1-l2']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 9,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l0-2-l2']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 8,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l0-3-l2']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 7,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l0-4-l2']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 6,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  l2: {
    x: 0,
    y: BASE_HALF_GRID_2D * 2,
    isMid: true,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l2-1-t0']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 4,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l2-2-t0']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 3,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l2-3-t0']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH * 2,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['l2-4-t0']: {
    x: 0,
    y: ADSORPTION_POINT_WIDTH,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  b0: {
    x: BASE_HALF_GRID_2D * 4,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b0-1-b2']: {
    x: ADSORPTION_POINT_WIDTH * 9,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b0-2-b2']: {
    x: ADSORPTION_POINT_WIDTH * 8,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b0-3-b2']: {
    x: ADSORPTION_POINT_WIDTH * 7,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b0-3-b2']: {
    x: ADSORPTION_POINT_WIDTH * 6,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  b2: {
    x: BASE_HALF_GRID_2D * 2,
    y: BASE_SIZE_2D,
    isMid: true,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b2-1-l0']: {
    x: ADSORPTION_POINT_WIDTH * 4,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b2-2-l0']: {
    x: ADSORPTION_POINT_WIDTH * 3,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b2-3-l0']: {
    x: ADSORPTION_POINT_WIDTH * 2,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['b2-4-l0']: {
    x: ADSORPTION_POINT_WIDTH,
    y: BASE_SIZE_2D,
    w: ADSORPTION_POINT_WIDTH,
    h: ADSORPTION_POINT_HEIGHT,
  },
  r0: {
    x: BASE_SIZE_2D,
    y: 0,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_HEIGHT,
  },
  ['r0-1-b2']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r0-2-b2']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 2,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r0-3-b2']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 3,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r0-4-b2']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 4,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  r2: {
    x: BASE_SIZE_2D,
    y: BASE_HALF_GRID_2D * 2,
    isMid: true,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r2-1-b0']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 6,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r2-2-b0']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 7,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r2-3-b0']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 8,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
  ['r2-4-b0']: {
    x: BASE_SIZE_2D,
    y: ADSORPTION_POINT_WIDTH * 9,
    w: ADSORPTION_POINT_HEIGHT,
    h: ADSORPTION_POINT_WIDTH,
  },
};

export function getValidConnection(vnode, lineType) {
  const { mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const { d2_connection, connection, d3_polyline_connection, d2_polyline_connection, type, attach } = vnode;
  const c = is3D ? connection : d2_connection;
  const polylineC = is3D ? d3_polyline_connection : d2_polyline_connection;
  if (lineType === 'polyline') {
    // 如果是折线，则只返回端点和中点 折线不管产品节点还是组节点都一样
    const keys = ['t0', 't2', 'r0', 'r2', 'b0', 'b2', 'l0', 'l2'];
    const newConnection = {};
    keys.forEach((key) => {
      newConnection[key] = polylineC[key];
    });
    return newConnection;
  }
  // 直线
  if (type === SIGMA_PRODUCT_SHAPE) {
    const removeKey = ['l0-1-b2', 'l0-2-b2', 'l0-3-b2', 'l0-4-b2', 'l0-5-b2', 'b0-1-b2', 'b0-2-b2', 'b0-3-b2', 'b0-4-b2', 'b0-5-b2'];
    const copy = cloneDeep(c);
    removeKey.forEach((key) => {
      delete copy[key];
    });
    return copy;
  }

  if ([...GROUP_SHAPE_TYPES, SIGMA_REMARK_NOTE_SHAPE].includes(type) || (type === SIGMA_TEXTLABEL_SHAPE && !attach)) {
    return c;
  }
}

export function getPointRadius() {
  const { connectionNumber: number } = store.getState();
  if (number === 8) return 10;
  if (number === 4) return 20;
  return 10;
}
