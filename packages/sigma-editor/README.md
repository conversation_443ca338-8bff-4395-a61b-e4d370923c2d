# <a href="/"><img src='https://api.icon.woa.com/tcloud_brand/dabc3cf0-7717-43da-aefb-367feef9afbc.svg' height='72' /></a>

SigmaEditor 是一个同时支持 2.5D 与 2D 两种视图模式的架构图编辑器，它内置了多个基础元素，提供了丰富的功能与 API 接口可供你迅速搭建应用。

### 安装

使用 npm 安装

```bash
npm install @tencent/sigma-editor  --registry=http://r.tnpm.oa.com --proxy=http://r.tnpm.oa.com:80 --verbose
```

使用 tnpm 安装

```bash
tnpm install @tencent/sigma-editor
```

### 文档

API 文档：[https://3d.diagram.woa.com/u/dev](https://3d.diagram.woa.com/u/dev)

### 在线示例

在线平台：[https://3d.diagram.woa.com/](https://3d.diagram.woa.com/)

### 基本示例

[代码示例](https://git.woa.com/tc-x/sigma-editor-test)

```js
// React 示例
import React, { useState, useEffect } from 'react';
import Sigma from 'sigma.samekh';
const Application = () => {
  const [sigma, setSigma] = useState(null);

  useEffect(() => {
    const container = document.getElementById('sigma-container');
    const instance = new Sigma(container);
    setSigma(instance);
  }, []);

  return <div id="sigma-container"></div>;
};
```

### 更新日志

日志：[https://git.woa.com/wildemou/samekh/tree/master/sigma.samekh/CHANGELOG.md](https://git.woa.com/wildemou/samekh/tree/master/sigma.samekh/CHANGELOG.md)

### 帮助与讨论

欢迎联系项目相关人员咨询或获取帮助：wildemou / loomisli / chaoluo / wenjuli
欢迎加入讨论群 / 开发群

```js
/**
 * 欢迎联系项目相关人员咨询或获取帮助：wildemou / loomisli / chaoluo / wenjuli
 * 欢迎加入讨论群 / 开发群
 *     へ　　　　　／|
 *     /＼7　　∠＿/
 *     /　│　　 ／　／
 *     │　Z ＿,＜　／　　 /`ヽ
 *     │　　　　　ヽ　　 /　　〉
 *     Y　　　　　`　 /　　/
 *     ｲ● ､　●　　⊂⊃〈　　/
 *     ()　 へ　　　　|　＼〈
 *     >ｰ ､_　 ィ　 │ ／／
 *     / へ　　 /　ﾉ＜| ＼＼
 *     ヽ_ﾉ　　(_／　 │／／
 *     7　　　　　　　|／
 *     ＞―r￣￣`ｰ―＿
 */
```

### 源码协议

[MIT](https://git.woa.com/wildemou/samekh/tree/master/sigma.samekh/LICENSE)

### 贡献者

- [wildemou](https://km.woa.com/user/wildemou)
