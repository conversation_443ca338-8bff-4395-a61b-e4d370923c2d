{"name": "@tencent/sigma-editor", "version": "6.6.6", "description": "如意云架构2.5D编辑器核心, 编辑器通过暴露了一系列的API来支持图形函数式操作, 支持回调函数来与业务逻辑进行交互。", "main": "src/index.js", "browser": "dist/index.cmd.js", "module": "dist/index.umd.js", "es": "dist/index.es.js", "types": "types/sigma-editor.d.ts", "scripts": {"dev": "rimraf dist && NODE_ENV=development rollup -c --watch", "build": "rimraf dist && NODE_ENV=production rollup -c", "lint": "eslint --fix --ext js,ts src test --max-warnings=-1", "format": "prettier --write \"{src,test}/**/*.{js,ts}\" \"**/*.md\"", "format:check": "prettier --list-different \"{src,test}/**/*.{js,ts}\" \"**/*.md\"", "prepare": "husky install"}, "repository": {"type": "git", "url": "https://git.woa.com/tc-x/sigma-editor.git"}, "keywords": ["2.5D", "如意", "架构图", "svg", "graph"], "files": ["dist", "es", "lib", "types", "test"], "dependencies": {"@svgdotjs/svg.js": "^3.1.1", "chroma-js": "^2.1.2", "localforage": "^1.10.0", "lodash": "^4.17.21", "lz-string": "^1.5.0", "overlap-area": "^1.1.0", "redux": "^4.1.1", "uuid": "^8.3.2"}, "author": "<PERSON><PERSON><PERSON> lo<PERSON>", "license": "ISC", "devDependencies": {"@babel/cli": "^7.17.6", "@babel/core": "^7.17.5", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.16.11", "@babel/runtime": "^7.20.0", "@rollup/plugin-babel": "^6.0.2", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.1", "@tencent/eslint-config-tencent": "^1.0.4", "babel-jest": "^29.2.2", "eslint": "^8.42.0", "husky": "^8.0.3", "jest": "^29.2.2", "rimraf": "^5.0.1", "rollup": "^3.2.3", "rollup-plugin-typescript2": "^0.34.1", "rollup-terser": "^1.0.3"}}