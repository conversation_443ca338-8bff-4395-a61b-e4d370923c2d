# WORKLIST

近期项目计划以及待修复的 bug 记录

## PLANLIST

- Features

  - [ ✅ ] 架构调整
  - [ ✅ ] 元素创建时支持自定义数据
  - [ ✅ ] 创建时支持自定义数
  - [ ✅ ] 优化工具箱创建位置
  - [ ✅ ] 图片与 Icon 位置优化
  - [ ✅ ] 创建时支持自动创建 Label
  - [ ✅ ] 元素替换功能 replaceNode
  - [ ✅ ] 区域算法更新
    - 轮廓算法
    - 阴影
  - [ ✅ ] 支持关联元素的复制粘贴
  - [ ✅ ] 支持端点双击删除
  - [ ✅ ] 元素创建排列优化
  - [ ✅ ] 图片导出 bug 修复
  - [ ✅ ] 连线算法更新
    - 箭头旋转角度算法
    - 连线距离算法
  - [ ✅ ] 多个元素对齐
  - [ ✅ ] 多个元素排列
  - [ ✅ ] 使用 TS 重构，已支持 ts 类型签名
  - [ ✅ ] 改造为 TNPM 包
  - [ ✅ ] 接口补充，支持自定义位置
  - [ 🏃 ] 虚线区域矩形算法优化
  - [ 🚀 ] 子网与安全组，私有网络的嵌套特征
    - 子网与子网，私有网络与私有网络不能互相嵌套
    - 安全组与安全组可以互相嵌套
    - 私有网络 > 子网 > 安全组
    - 私有网络可以包含多个子网
    - 子网可以包含多个安全组
  - [ 🚀 ] 自动连线/自动折线算法实现
  - [ 🚀 ] 全局错误捕获，获取错误数据并存储到本地
  - [ 🚀 ] 设置节点属性值
  - [ 🚀 ] 获取节点属性值
  - [ 🚀 ] 视觉效果补充
    - 选中动画
    - 连线动画
  - [ 🚀 ] 测试计划中...
    - 单元测试
    - 集成测试
    - 功能测试

- Bugs

  - [ ✅ ] 复制粘贴，剪切有问题
  - [ ✅ ] 历史存储到前进后退有时候不准确
  - [ ✅ ] 修复 Block 元素在不同颜色下选中状态表现一致的问题
  - [ ✅ ] 修复鼠标连线被移除时事件响应未移除的 bug；
  - [ ✅ ] 当图像元素全部在坐标系第二象限时会出现居中计算 bug
  - [ ✅ ] linkTextLabel 不应该参与计算。
  - [ 🐛 ] 使用基础元素时，偏移量需要使用 width 与 deep 进行计算（基础元素是非固定宽高），固定值会出现误差
  - [ 🐛 ] 设置背景大小后再进行转换后元素的拖动事件失效，转换不准确
  - [ 🐛 ] 文本的投影坐标有时候不准确
  - [ 🐛 ] 有时候框选后的元素无法被移除选中状态，需要点击后才能移除，暂无必现操作，之后有发现后再处理。

- Optimizes
  - [ ✅ ] 优化图片 2D / 2.5D 导出, 元素居中问题
  - [ 🌸 ] 历史记录与前进，后退实现不够优雅，最好是使用数据差异来做。
  - [ 🌸 ] 属性编辑面板调整到右侧，组合排列工具移动至顶部
  - [ 🌸 ] 默认缩放使用画布中心缩放
  - [ 🌸 ] 补充快捷键操作
