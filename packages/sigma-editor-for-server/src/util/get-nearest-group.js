import render from '../renders';
import { getAreaSize, getOverlapSize } from 'overlap-area';
import { GROUP_SHAPE_TYPES } from './constants';
import { isVnodeGroupable, parseObjectPointsToArray } from './tools';
import store from '../store';

function sortGroups(groups) {
  return groups.sort((a, b) => b.component.position() - a.component.position());
}

export function getChildGroups(shapes, group) {
  const keys = Object.keys(group?.relations || {});
  if (!keys.length) return [];
  const output = [];
  for (const key of keys) {
    const vnode = shapes[key];
    if (vnode && GROUP_SHAPE_TYPES.includes(vnode.type)) {
      output.push(key);
      output.push(...getChildGroups(shapes, vnode));
    }
  }
  return output;
}

function getVnodeNearestGroup(gs, vnode, children) {
  if (!vnode) return;
  const vp = parseObjectPointsToArray(render.getflat(vnode));
  const vSize = getAreaSize(vp);
  let target;

  for (const group of gs) {
    if (group.key === vnode.key) continue;
    // group 不能是 vnode 的子组
    if (children.includes(group.key)) continue;

    const gp = parseObjectPointsToArray(render.getflat(group));
    const area = getOverlapSize(gp, vp);

    if (area >= vSize / 2 || area >= getAreaSize(gp) / 2) {
      target = group;
      break;
    }
  }

  return target;
}

export function getNearestGroup(core, vnodes) {
  const groups = core._getTypesShapes(GROUP_SHAPE_TYPES);
  if (!groups.length) return;
  const gs = sortGroups(groups);
  const { shapes } = core.data;

  let max = -1; let target;
  for (const vnode of vnodes) {
    const children = getChildGroups(shapes, vnode);
    const t = getVnodeNearestGroup(gs, vnode, children);
    if (!t) continue;
    const position = t.component.position();
    if (position > max) {
      target = t;
      max = position;
    }
  }

  return target;
}

// 如果是一组 vnodes，要将其折叠，类似于逆向 get relations
export function foldVnodes(vnodes) {
  const { core } = store.getState();
  const map = {};
  const output = [];

  for (const vnode of vnodes) {
    if (GROUP_SHAPE_TYPES.includes(vnode.type)) {
      const rvs = core._getRelationShapes(vnode);
      for (const { key } of rvs) {
        map[key] = true;
      }
    }
  }

  for (const vnode of vnodes) {
    if (map[vnode.key]) continue;
    if (!isVnodeGroupable(vnode)) continue;
    output.push(vnode);
  }

  return output;
}
