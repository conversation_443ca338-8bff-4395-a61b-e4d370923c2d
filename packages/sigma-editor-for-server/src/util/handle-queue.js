import store from '../store';
import { SIGMA_ALL_PRODUCT_SHAPES, SIGMA_GRAPH_MODE_3D } from './constants';
import { transform3DTo2D } from './tools';

export function getPlanePosition(vnode) {
  const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
  const { position: p } = vnode;
  return is3D ? transform3DTo2D(p) : p;
}

export function handleProductQueue(vnode) {
  const { mode, core } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  if (!is3D) return;
  const { component } = vnode;
  component.on(['initial', 'end-move'], () => {
    if (!SIGMA_ALL_PRODUCT_SHAPES.includes(vnode.type)) return;
    const { shapes } = core.data;
    const p1 = getPlanePosition(vnode);
    const ghost = component.remember('ghost');
    const current = ghost || component;

    // 向前递归查找
    let previous = current.prev();
    while (previous) {
      const key = previous.attr('key');
      const p2 = getPlanePosition(shapes[key]);
      if (p1.y < p2.y || (p1.y === p2.y && p1.x < p2.x)) {
        current.insertBefore(previous);
        previous = current.prev();
      } else {
        previous = null;
      }
    }

    // 向后递归查找
    let next = current.next();
    while (next) {
      const key = next.attr('key');
      const p2 = getPlanePosition(shapes[key]);
      if (p1.y > p2.y || (p1.y === p2.y && p1.x > p2.x)) {
        current.insertAfter(next);
        next = current.next();
      } else {
        next = null;
      }
    }
  });
};
