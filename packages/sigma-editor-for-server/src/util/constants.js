// 组件分类标记
// 基础类 -----------------------------------------------------------------------
export const SHAPE_CATEGORY_COMMON = 'COMMON';
export const SHAPE_CATEGORY_GROUP = 'GROUP';
export const SHAPE_CATEGORY_COMPUTE = 'COMPUTE';
export const SHAPE_CATEGORY_NETWORK = 'NETWORK';
export const SHAPE_CATEGORY_STORAGE = 'STORAGE';
export const SHAPE_CATEGORY_DATABASE = 'DATABASE';
export const SHAPE_CATEGORY_ANALYTICS = 'ANALYTICS';
export const SHAPE_CATEGORY_APP_SERVICE = 'APP_SERVICE';
export const SHAPE_CATEGORY_MISC = 'MISC';
export const SHAPE_CATEGORY_PRODUCT = 'PRODUCT';
// 安全类 -----------------------------------------------------------------------
export const SHAPE_CATEGORY_SECURITY = 'SECURITY';

// 组件类型标记
// 基础类 -----------------------------------------------------------------------
export const SIGMA_RECTANGLE_SHAPE = 'SIGMA_RECTANGLE_SHAPE';
export const SIGMA_BLOCK_SHAPE = 'SIGMA_BLOCK_SHAPE';
export const SIGMA_TEXTLABEL_SHAPE = 'SIGMA_TEXTLABEL_SHAPE';
export const SIGMA_ICON_SHAPE = 'SIGMA_ICON_SHAPE';
export const SIGMA_IMAGE_SHAPE = 'SIGMA_IMAGE_SHAPE';
export const SIGMA_AREA_SHAPE = 'SIGMA_AREA_SHAPE';
export const SIGMA_AUTOSCALING_SHAPE = 'SIGMA_AUTOSCALING_SHAPE';
export const SIGMA_AVAILABILITYZONE_SHAPE = 'SIGMA_AVAILABILITYZONE_SHAPE';
export const SIGMA_PRODUCT_SHAPE = 'SIGMA_PRODUCT_SHAPE';
export const SIGMA_LINE_SHAPE = 'SIGMA_LINE_SHAPE';
export const SIGMA_CIRCLE_SHAPE = 'SIGMA_CIRCLE_SHAPE';
export const SIGMA_SECURITY_GROUP_SHAPE = 'SIGMA_SECURITY_GROUP_SHAPE';
export const SIGMA_SUBNET_SHAPE = 'SIGMA_SUBNET_SHAPE';
export const SIGMA_VPC_SHAPE = 'SIGMA_VPC_SHAPE';
// export const SIGMA_GROUP_SHAPE = 'SIGMA_GROUP_SHAPE';
export const SIGMA_SECURITY_SHAPE = 'SIGMA_SECURITY_SHAPE';
export const SIGMA_BASE_GROUP_SHAPE = 'SIGMA_BASE_GROUP_SHAPE';
export const SIGMA_REMARK_NOTE_SHAPE = 'SIGMA_REMARK_NOTE_SHAPE'; // 新增一个Note类型shape，同样继承于RectangleShape
export const SIGMA_CCN_SHAPE = 'SIGMA_CCN_SHAPE'; // 新增云联网CCN组类型
export const SIGMA_TKE_SHAPE = 'SIGMA_TKE_SHAPE'; // 新增TKE组类型

// 组件初始状态配置
export const SHAPE_CREATE_START_POSITION_X = 384; // 初始位置 x 坐标
export const SHAPE_CREATE_START_POSITION_Y = 222; // 初始位置 y 坐标

// 基础信息
// 128 * 74
export const BASE_STEP_X = 16; // 2.5D X轴映射 1/8 单位
export const BASE_STEP_Y = 9.25; // 2.5D Y轴映射 1/8 单位
export const BASE_HALF_GRID_X = 32; // 2.5D X轴映射 1/4 单位
export const BASE_HALF_GRID_Y = 18.5; // 2.5D Y轴映射 1/4 单位
export const BASE_GRID_X = 64; // 2.5D X轴映射 1/2 单位
export const BASE_GRID_Y = 37; // 2.5D Y轴映射 1/2 单位
export const BASE_SLOPE = 0.578125; // 比值
export const BASE_BEVEL = 73.92564;

// 90 * 90
export const BASE_STEP_2D = 11.25; // 2D X轴映射 1/8 单位
export const BASE_HALF_GRID_2D = 22.5; // 2D Y轴映射 1/4 单位
export const BASE_GRID_2D = 45; // 2.5D X轴映射 1/2 单位
export const BASE_SLOPE_2D = 1; // 比值
export const BASE_SIZE_2D = 90; // 2D 尺寸

// 渲染行为
export const RENDER_INIT = 'RENDER_INIT'; // 初始化
export const RENDER_CREATE = 'RENDER_CREATE'; // 新增元素
export const RENDER_UPDATE = 'RENDER_UPDATE'; // 更新元素
export const RENDER_REMOVE = 'RENDER_REMOVE'; // 移除元素

// 渲染参数
export const TRANSFORM_3D_SCALE = 1.415;
export const TRANSFORM_3D_MATRIX = [0.707, 0.409, -0.707, 0.409, 0, 0];
export const TRANSFORM_3D_MATRIX_FLAT = [0.707, 0.409, 0, 0.816, 0, 0];
export const TRANSFORM_3D_MATRIX_REVERSE = [
  -0.707, -0.409, 0.707, -0.409, 0, 0,
];
export const STATIC_CHECKED_FILL_COLOR = '#006eff';
export const STATIC_CHECKED_STROKE_COLOR = '#006eff';

export const STATIC_DEFAULT_2D_FILL_COLOR = '#0052D9';
export const STATIC_DEFAULT_2D_STROKE_COLOR = '#0052D9';

// 组件默认透明度
export const STATIC_COMPONENT_OPACITY = 1;

export const SIGMA_GRAPH_MODE_2D = 'SIGMA_GRAPH_MODE_2D';
export const SIGMA_GRAPH_MODE_3D = 'SIGMA_GRAPH_MODE_3D';

// 动画参数
export const ANIMATE_MOVE_DURATION = 128;
export const ANIMATE_MOVE_DELAY = 0;
export const ANIMATE_MOVE_WHEN = 'now';

// movablepoints
export const CIRCLE_CLASS_PREFIX = 'c-';
export const MOVABLE_POINTS_CLASS = 'movable-points';
export const MOVABLE_TIP_CLASS = 'movable-tip';

export const SELECT_CURSOR = 'url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/5080ef5b-8d3a-4bc9-a8a8-6e22a1018447.svg), auto';
export const EXCLUDE_PROPERTYS = [
  'component',
  'alignmentLines',
  // 'movablePoints',
  'resizePoints',
  'controlPoints',
  'renderComponent',
  'stickyUnits',
];

// connection 中的中心点，主要用于连线自动连接时的情况，自动连接只连接中点
export const CONNECTION_CENTER = ['t2', 'r2', 'b2', 'l2'];
export const CONNECTION_CENTER_MAP = { t2: 'b2', b2: 't2', l2: 'r2', r2: 'l2' };
export const ALL_CONNECTION_KEYS = [
  't0', 't1', 't2', 't3',
  'r0', 'r1', 'r2', 'r3',
  'b0', 'b1', 'b2', 'b3',
  'l0', 'l1', 'l2', 'l3',
];
export function getEmptyConnections() {
  return ALL_CONNECTION_KEYS.reduce((acc, dir) => {
    acc[dir] = { x: 0, y: 0 };
    return acc;
  }, {});
}
// 箭头和线段重叠的部分不够尖，箭头要缩进一点
export const LINE_ARROW_OFFSET = 8;

export const NETWORK_SHAPE_TYPES = [
  SIGMA_SUBNET_SHAPE,
  SIGMA_VPC_SHAPE,
  SIGMA_SECURITY_GROUP_SHAPE,
  SIGMA_BASE_GROUP_SHAPE,
];

// 组 type
export const GROUP_SHAPE_TYPES = [
  ...NETWORK_SHAPE_TYPES,
  SIGMA_AREA_SHAPE,
  SIGMA_AUTOSCALING_SHAPE,
  SIGMA_AVAILABILITYZONE_SHAPE,
  SIGMA_CCN_SHAPE,
  SIGMA_TKE_SHAPE,
];

// 不能成组的元素
export const NOT_GROUPABLE_TYPE = [
  SIGMA_CIRCLE_SHAPE,
  SIGMA_LINE_SHAPE,
  // SIGMA_AUTOSCALING_SHAPE,
  // SIGMA_TEXTLABEL_SHAPE,
];

export const NOT_SUBSTANCE_SHAPE_TYPES = [
  // ...GROUP_SHAPE_TYPES,
  ...NOT_GROUPABLE_TYPE,
  SIGMA_RECTANGLE_SHAPE,
];

export const NO_POSITION_MANAGER_SHAPE_TYPES = [
  ...NOT_GROUPABLE_TYPE,
  SIGMA_RECTANGLE_SHAPE,
];

export const NOT_RAISE_WHEN_CHECKED_TYPES = [
  ...GROUP_SHAPE_TYPES,
];

export const DEFAULT_BASE_ICON = `<svg  width="48" height="48" viewBox="0 0 48 48">
<g fill="#0052D9" fill-rule="evenodd">
  <path d="M36 28v2h-2v4h-4v2h-2v-2h-3v2h-2v-2h-3v2h-2l.2-2H14v-4h-2v-2h2v-3.033L12 25v-2l2 .033V20h-2v-2h2v-4h4v-2h2v2h3.033L23 12h2l-.033 2H28v-2h2v2h4v4h2v2h-2v3.033L36 23v2l-2-.033V28h2zm-4-12H16v16h16V16zm-3 13H19V19h10v10zm-2-8h-6v6h6v-6z"/>
  <path fill-rule="nonzero" d="M24.202 45C12.572 45 3.11 35.579 3.11 24S12.572 3 24.202 3c10.604 0 19.382 7.84 20.851 18H47.1C45.624 9.716 35.938 1 24.202 1 11.443 1 1.1 11.297 1.1 24s10.342 23 23.1 23c11.737 0 21.423-8.716 22.9-20h-2.047c-1.471 10.16-10.248 18-20.852 18z"/>
</g>
</svg>`;

export const DEFAULT_AREA_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 8C2 7.686 2.032 7.379 2.078 7.078L6 11V12H8V14C4.691 14 2 11.309 2 8ZM14 8C14 9.093 13.701 10.116 13.188 11H12L10 9H5V7H8V4H10V2.35C12.327 3.176 14 5.394 14 8ZM8 0C3.582 0 0 3.582 0 8C0 12.418 3.582 16 8 16C12.418 16 16 12.418 16 8C16 3.582 12.418 0 8 0Z" fill="#18609C"/>
</svg>
`;

export const DEFAULT_AVAILABILITY_ZONE_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.333 0L1 5.5L8.333 11L15.666 5.5L8.333 0ZM4.333 5.5L8.333 2.5L12.333 5.5L8.333 8.501L4.333 5.5ZM8.333 13.5L2.667 9.25L1 10.5L8.333 16L15.666 10.5L13.999 9.25L8.333 13.5Z" fill="#3178AF"/>
</svg>
`;

export const DEFAULT_AUTO_SCALING_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="server">
<path id="stroke3" d="M2.66602 9.33333H7.33268L8.66602 10.6667H13.3327L11.9993 7L13.3327 3.33333H8.66602L7.33268 2H2.66602V5.66667V9.33333ZM2.66602 9.33333V14.3333" stroke="#2A86FF" stroke-width="2" stroke-linecap="square"/>
</g>
</svg>`;


export const DEFAULT_BASE_GROUP_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_863_1748)">
<mask id="mask0_863_1748" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<path d="M16 0H0V16H16V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_863_1748)">
<path d="M8 0.842285L12 3.17562V6.42529L15 8.17562V12.8244L11 15.1577L8 13.4073L5 15.1577L1 12.8244V8.17562L4 6.42529V3.17562L8 0.842285ZM5 8.17562L3 9.32299V11.676L5 12.842L7 11.675V10.8673V10V9.5V9.32299L5 8.17562ZM11 8.17562L9 9.32299V11.676L11 12.842L13 11.675V10.8673V10V9.32299L12.312 8.92229L11 8.17562ZM8 3.15699L6 4.32299V6.42529L8 7.59128L10 6.42529V5.86728V5.5V4.32299L9.312 3.92229L8 3.15699Z" fill="#334966"/>
</g>
</g>
<defs>
<clipPath id="clip0_863_1748">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>`;


export const DEFAULT_VPC_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 12H9V9H7V12ZM3 13H13V8H3V13ZM4 6C4 3.794 5.794 2 8 2C10.206 2 12 3.794 12 6H4ZM14 6C14 2.691 11.309 0 8 0C4.691 0 2 2.691 2 6H1V15H15V6H14Z" fill="#354D7F"/>
</svg>
`;

export const DEFAULT_SUBNET_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_863_1775)">
<mask id="mask0_863_1775" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<path d="M16 0H0V16H16V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_863_1775)">
<path d="M8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16ZM2.08296 9C2.55904 11.8377 5.027 14 8 14C10.973 14 13.441 11.8377 13.917 9H2.08296ZM2.08296 7H13.917C13.441 4.16229 10.973 2 8 2C5.027 2 2.55904 4.16229 2.08296 7ZM8.64256 12.8552C9.17713 11.6333 9.5 9.88855 9.5 8C9.5 6.11145 9.17713 4.36666 8.64256 3.14478C8.43061 2.66034 8.20314 2.30218 8 2.09422C7.79686 2.30218 7.56939 2.66034 7.35744 3.14478C6.82287 4.36666 6.5 6.11145 6.5 8C6.5 9.88855 6.82287 11.6333 7.35744 12.8552C7.56939 13.3397 7.79686 13.6978 8 13.9058C8.20314 13.6978 8.43061 13.3397 8.64256 12.8552ZM8 16C6.067 16 4.5 12.4183 4.5 8C4.5 3.58172 6.067 0 8 0C9.933 0 11.5 3.58172 11.5 8C11.5 12.4183 9.933 16 8 16Z" fill="#5865B4"/>
</g>
</g>
<defs>
<clipPath id="clip0_863_1775">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`;

export const DEFAULT_CCN_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.999 6.02149C13.999 6.01449 14 6.00749 14 6.00049C14 2.68649 11.313 0.000488281 8 0.000488281C4.686 0.000488281 2 2.68649 2 6.00049C2 6.00749 2.001 6.01449 2.001 6.02149C0.793 6.93449 0 8.36949 0 10.0005C0 12.7615 2.238 15.0005 5 15.0005V13.0005C3.346 13.0005 2 11.6545 2 10.0005C2 9.06449 2.439 8.19649 3.206 7.61749L4.001 7.01749V6.02149V5.93449C4.035 3.75949 5.816 2.00049 8 2.00049C10.185 2.00049 11.966 3.76049 12 5.93749L11.999 5.94549V6.02149V7.01749L12.794 7.61749C13.561 8.19649 14 9.06449 14 10.0005C14 11.6545 12.654 13.0005 11 13.0005H9V10.0005H10V6.00049H6V10.0005H7V15.0005H11C13.762 15.0005 16 12.7615 16 10.0005C16 8.36949 15.207 6.93449 13.999 6.02149Z" fill="#354D7F"/>
</svg>`;

export const DEFAULT_TKE_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 9V16L1 12.8933V9H7ZM15 9V12.8933L9 16V9H15ZM5 11H3V11.9878L5 13V11ZM13 11H11V13L13 11.9878V11ZM7 0V7H1V3.10668L7 0ZM9 0L15 3.10668V7H9V0ZM5 3L3 4.01219V5H5V3ZM11 3V5H13V4.01219L11 3Z" fill="#006EFF"/>
</svg>
`;

export const DEFAULT_SECURITY_GROUP_ICON = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.49963 9.46438L11.9497 5.01431L10.5355 3.6001L7.49975 6.63583L5.70731 4.84306L4.29297 6.25715L7.49963 9.46438Z" fill="#06774A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15 0H1V7.4C1 7.4 1.054 13.359 8 15C14.946 13.359 15 7.4 15 7.4V0ZM13 7.362C12.987 7.802 12.732 11.589 8 12.934C3.268 11.589 3.013 7.803 3 7.4V2H13V7.362Z" fill="#06774A"/>
</svg>`;

export const DEFAULT_ANIMATION_SETTING = {
  duration: 150,
  when: 'now',
  swing: true,
};

export const TOOLBOX_ADD_LINE_ICON = `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="3" cy="3" r="3" transform="matrix(0.707107 0.707107 0.707107 -0.707107 9.27148 30.4853)" fill="white"/>
<path d="M31.1927 12.8076L13.7676 30.2327" stroke="white"/>
<path d="M32.6072 11.3934L29.0717 19.1716L28.1625 15.8381L24.829 14.9289L32.6072 11.3934Z" fill="white"/>
</svg>`;

export const TOOLBOX_ADD_POLYLINE_ICON = `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30.8947 14.1026L23.5 24.117L20.579 24.117L14.5 32" stroke="white"/>
<circle cx="3" cy="3" r="3" transform="matrix(0.819152 0.573576 0.573576 -0.819152 10.5352 32.5349)" fill="white"/>
<path d="M32.3533 11.7324L30.1746 19.9939L28.7184 16.8605L25.2795 16.5243L32.3533 11.7324Z" fill="white"/>
</svg>`;

export const TKE_GROUP_EMPTY_ICON = `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g class="tke-group-empty-icon-g">
<circle cx="40" cy="40" r="40" fill="#EBEFF5"/>
<rect opacity="0.01" x="16" y="16" width="48" height="48" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40 36.8774L60.999 49.0024V30.0024L40 17.8774L18.999 30.0024V49.0024L40 36.8774Z" fill="#C1CCDD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40 42.1226L60.999 29.9976V49.9976L40 62.1226L18.999 49.9976V29.9976L40 42.1226Z" fill="white"/>
<rect opacity="0.01" x="48.3335" y="44" width="20.2222" height="20" fill="white"/>
<ellipse cx="58.4445" cy="54" rx="7.07777" ry="7" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M58.4448 46C53.9778 46 50.356 49.582 50.356 54C50.356 58.418 53.9778 62 58.4448 62C62.9119 62 66.5337 58.418 66.5337 54C66.5337 49.582 62.9119 46 58.4448 46ZM57.4337 58H59.4559V56H57.4337V58ZM59.4559 55H57.4337V50H59.4559V55Z" fill="#006EFF"/>
</g>
</svg>
`;

export const CONNECTION_BOTTOM_KEYS = ['b2'];

export const SIGMA_ALL_PRODUCT_SHAPES = [SIGMA_BLOCK_SHAPE, SIGMA_PRODUCT_SHAPE];

export const GLOBAL_EVENT_SUFFIX = '.sigma';

export const ADSORPTION_POINT_WIDTH = 7.5; // 产品节点吸附点间隔宽度
export const ADSORPTION_POINT_HEIGHT = 30; // 产品节点吸附点间隔高度
