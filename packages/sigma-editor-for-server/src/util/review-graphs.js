// import { isEmpty } from 'lodash';
// import { GROUP_SHAPE_TYPES } from './constants';

function reviewGraphs(core, vnode) {
  // const { shapes } = core.data;
  // initCircleGroup(shapes, vnode, vnode);
  initVnodeStyle(core, vnode);
}

// function initDependGroup(vnode) {
//   const { type, groups, relations } = vnode;
//   if (GROUP_SHAPE_TYPES.includes(type)) {
//     const gs = Object.keys(groups);
//     const keys = Object.keys(relations);
//     for (const key of gs) {
//       if (keys.includes(key)) {
//         delete vnode.groups[key];
//         delete vnode.relations[key];
//       }
//     }
//   }
// }

// function initCircleGroup(shapes, vnode, target) {
//   const { type, groups, relations } = vnode;
//   if (GROUP_SHAPE_TYPES.includes(type) && !isEmpty(groups)) {
//     for (const key in relations) {
//       if (!GROUP_SHAPE_TYPES.includes(relations[key])) continue;
//       // 存在循环引用
//       if (target.groups[key]) {
//         delete target.groups[key];
//         delete vnode.relations[key];
//         return;
//       }
//       initCircleGroup(shapes, shapes[key], target);
//     }
//   }
// }
function initVnodeStyle(core, vnode) {
  const { type, styles = {} } = vnode;
  const target = core.allStyles[type] || {};
  // 兼容诡异旧图
  if (styles.borderRadius) styles.borderRadius.type = 'number';
  vnode.styles = { ...target, ...styles };
};

export {
  reviewGraphs,
};
