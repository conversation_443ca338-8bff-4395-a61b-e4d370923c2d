import store from '../store';
import {
  SigmaRectangleShape,
  SigmaBlockShape,
  SigmaTextLabelShape,
  SigmaIconShape,
  SigmaImageShape,
  SigmaAreaShape,
  SigmaAutoScalingShape,
  SigmaAvailabilityZoneShape,
  SigmaProductShape,
  SigmaSecurityShape,
  SigmaBaseGroupShape,
  SigmaVPCShape,
  SigmaSubnetShape,
  SigmaSecurityGroupShape,
  SigmaLineShape,
} from '../shape';
import { EXCLUDE_PROPERTYS } from './constants';
import { cloneDeep } from 'lodash';

export function setLayersOpacity(opacity) {
  const {
    container: {
      gText,
      gImage,
      gIcon,
      gProduct,
      gRect,
      gLine,
      gCircle,
      gNetwork,
      gCache,
    },
  } = store.getState();

  gText.attr({ opacity });
  gImage.attr({ opacity });
  gIcon.attr({ opacity });
  gProduct.attr({ opacity });
  gRect.attr({ opacity });
  gLine.attr({ opacity });
  gCircle.attr({ opacity });
  gNetwork.attr({ opacity });
  gCache.attr({ opacity });
}

export function getAllShapeStyles() {
  const instance = [
    new SigmaRectangleShape(),
    new SigmaBlockShape(),
    new SigmaTextLabelShape(),
    new SigmaImageShape(),
    new SigmaIconShape(),
    new SigmaBaseGroupShape(),
    new SigmaAreaShape(),
    new SigmaAutoScalingShape(),
    new SigmaAvailabilityZoneShape(),
    new SigmaSecurityGroupShape(),
    new SigmaVPCShape(),
    new SigmaSubnetShape(),
    new SigmaProductShape(),
    new SigmaSecurityShape(),
    new SigmaLineShape(),
  ];
  return instance.reduce((acc, { type, styles }) => {
    acc[type] = styles;
    return acc;
  }, {});
}

export function pureClone(vs, isMap = false) {
  if (isMap) {
    return Object.keys(vs).reduce((acc, v) => {
      acc[v] = pureClone(vs[v]);
      return acc;
    }, {});
  }
  if (Array.isArray(vs)) {
    return vs.map(v => pureClone(v));
  }

  const v = {};
  for (const key in vs) {
    if (EXCLUDE_PROPERTYS.includes(key)) continue;
    v[key] = vs[key];
  }
  return cloneDeep(v);
}
