import { SigmaLineShape as LineShape } from '../shape/other/LineShape';
// import { SigmaTextLabelShape as TextLabelShape } from '../shape/common/TextLabelShape';
import store from '../store';
import { SIGMA_GRAPH_MODE_3D, TOOLBOX_ADD_LINE_ICON, TOOLBOX_ADD_POLYLINE_ICON, GROUP_SHAPE_TYPES, SIGMA_GRAPH_MODE_2D } from './constants';

export let lastToolBoxVnode = null;
function isFirefox() {
  return navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
}
export const createConnectLineToolBox = (core, vnode) => {
  const { root: doc, uneditable, scale, root, viewBox: { x, y }, mode } = store.getState();
  const isFox = isFirefox();
  // 不可编辑
  let contain = document.getElementById('sigma-c-box');
  if (!contain) {
    contain = document.createElement('div');
    contain.id = 'sigma-c-box';
    contain.style = 'height: 0px; position: fixed; top: 0px; left: 0px; width: 100%; z-index: 901;';
    doc.appendChild(contain);
  }
  contain.innerHTML = '';
  const rectDom = document.getElementById(vnode.key);
  if (!rectDom) {
    return;
  }
  const rect = rectDom.getBoundingClientRect();
  const lineShapeInstance = new LineShape();
  // const textLabelInstance = new TextLabelShape();
  const connContainer = document.createElement('div');
  connContainer.id = 'sigma-c-container';
  connContainer.style.position = 'absolute';
  connContainer.style.marginLeft = '36px';
  if (isFox) {
    const { x: rootX, y: rootY } = root.getBoundingClientRect();
    connContainer.style.left = mode === SIGMA_GRAPH_MODE_3D
      ? `${rootX + (vnode.position.x - x) / scale + rect.width}px`
      : `${rootX + (vnode.position.x - x + vnode.width + 30) / scale}px`;
    connContainer.style.top = mode === SIGMA_GRAPH_MODE_3D
      ? `${rootY +  (vnode.position.y - y + 60) / scale}px`
      : `${rootY +  (vnode.position.y - y + (vnode.height / 2)) / scale}px`;
    connContainer.style.transform = 'translate(-50%, -50%)';
    // 如果是组，特殊处理
    if (GROUP_SHAPE_TYPES.includes(vnode.type) &&  mode === SIGMA_GRAPH_MODE_3D) {
      connContainer.style.left = `${rootX + (vnode.position.x - x) / scale}px`;
    }
  } else {
    connContainer.style.left = mode === SIGMA_GRAPH_MODE_2D ? `${rect.x + rect.width}px` : `${rect.x + rect.width - 54 / scale}px`;
    if (uneditable) {
      connContainer.style.top = `${rect.y + rect.height / 2 - 30}px`;
    } else {
      connContainer.style.top = `${rect.y + rect.height / 2}px`;
    }
  }


  // 直线箭头
  const arrow = document.createElement('span');
  arrow.style = 'display: flex; align-items: center; transform: scale(1.2); justify-content: center; width: 36px; height:36px; cursor: pointer; background-color: #0052d9; border-radius: 100%; box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;';
  arrow.innerHTML = TOOLBOX_ADD_LINE_ICON;
  arrow.classList.add('sigma-c-arrow');
  arrow.onclick = function (e) {
    const { callbacks = {} } = core.options;
    if (callbacks.onCreateArrowLine) {
      const isContinue = callbacks.onCreateArrowLine(e, vnode);
      clearConnectLineToolBox();
      if (!isContinue) return;
    }

    e.stopPropagation();
    const node = lineShapeInstance.create(
      vnode,
      vnode.component.point(e.pageX, e.pageY),
    );
    // 初始化起点的坐标
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { position: { x, y }, connection, d2_connection } = vnode;
    const offset = is3D ? connection.r0 : d2_connection.r2;
    node.data.start.x =  x + offset.x;
    node.data.start.y = y + offset.y;
    core.add(node, { _historical: false });
    contain.innerHTML = '';
    core._setShapeChecked(node, true, false);
  };

  // 曲线
  const polyline = document.createElement('span');
  polyline.style = 'margin-top: 20px; display: flex; align-items: center; transform: scale(1.2); justify-content: center; width: 36px; height:36px; cursor: pointer; background-color: #0052d9; border-radius: 100%; box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;';
  polyline.innerHTML = TOOLBOX_ADD_POLYLINE_ICON;
  polyline.classList.add('sigma-c-polyline');
  polyline.onclick = function (e) {
    const { callbacks = {} } = core.options;
    if (callbacks.onCreatePolyline) {
      const isContinue = callbacks.onCreatePolyline(e, vnode);
      clearConnectLineToolBox();
      if (!isContinue) return;
    }

    e.stopPropagation();
    const node = lineShapeInstance.create(
      vnode,
      vnode.component.point(e.pageX, e.pageY),
    );

    node.styles.lineType.value = 'polyline';
    // 初始化起点的坐标
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { position: { x, y }, connection, d2_connection } = vnode;
    const offset = is3D ? connection.r0 : d2_connection.r2;
    node.data.start.x =  x + offset.x;
    node.data.start.y = y + offset.y;
    core.add(node, { _historical: false });
    contain.innerHTML = '';
    core._setShapeChecked(node, true, false);
  };
  let doms = [];
  if (!uneditable) {
    doms = [arrow, polyline];
  }

  if (vnode.linkable) {
    const label = document.createElement('span');
    label.style = 'display: flex; align-items: center; transform: scale(1.2); justify-content: center; width: 36px; height: 36px; cursor: pointer;margin-top: 20px; background-color: #0052d9; border-radius: 100%; box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;';
    label.innerHTML = `<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 1025 1024">
      <path fill="#ffffff" d="M984.38 219.267l-95.092 95.091-183.414-177.794 97.902-97.901a127.704 127.704 0 1 1 180.603 180.604zM833.6 369.982L248.936 954.645 0 1024l68.27-249.895 582.49-582.491L833.6 369.982z" />
    </svg>
    `;
    label.classList.add('sigma-c-label');
    label.onclick = function (e) {
      const { callbacks = {} } = core.options;
      // 传入回调会阻止默认行为
      if (callbacks.onCreateLabel) {
        const isContinue = callbacks.onCreateLabel(e, vnode);
        clearConnectLineToolBox();
        if (!isContinue) return;
      }

      e.stopPropagation();

      if (!vnode.sticky) {
        core._createNodeLabel(vnode);
        contain.innerHTML = '';
      } else {
        const node = core.data.shapes[vnode.sticky];
        if (node) {
          core.onlyShapeChecked(node);
          contain.innerHTML = '';
        }
      }
    };
    doms.push(label);
  }

  contain.appendChild(connContainer);
  connContainer.append(...doms);
  // 记录值
  lastToolBoxVnode = vnode;
};
/**
 * 清空连线工具箱
 */
export const clearConnectLineToolBox = () => {
  const t = document.getElementById('sigma-c-box');
  if (t && t.innerHTML !== '') t.innerHTML = '';
  // 记录值
  lastToolBoxVnode = null;
};
