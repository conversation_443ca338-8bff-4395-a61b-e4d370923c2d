import {
  judgeIntersect,
  getPolyRectPoint,
  getSigmaPoint,
  get2DSigmaPoint,
} from './tools';
import {
  BASE_SLOPE,
  BASE_GRID_Y,
  BASE_GRID_X,
  BASE_HALF_GRID_X,
  BASE_HALF_GRID_Y,
  BASE_HALF_GRID_2D,
  BASE_GRID_2D,
} from './constants';

const MIN_DISTANCE = (BASE_GRID_X ** 2 + BASE_GRID_Y ** 2) ** 0.5;
const HALF_MIN_DISTANCE = MIN_DISTANCE / 2; // 最小间距
const HALF_2D_MIN_DISTANCE = Math.floor(BASE_GRID_2D / 2);
const OFFSET_DEVIATION = 3; // 最大误差值

/**
 * 合并多个轮廓坐标为一个大的新轮廓坐标
 * @param  shapeFlatCoordinates 任意个轮廓坐标列表，[[{x, y}...]...]
 * @returns 返回由多个轮廓坐标列表合并成的多边形新轮廓坐标 [{x, y} ...]
 */
export function getAdaptivePolyCoordinates(shapeFlatCoordinates) {
  const points = [];
  const ktlbr = -1 * BASE_SLOPE;
  const ktrbl = BASE_SLOPE;
  for (let i = 0; i < shapeFlatCoordinates.length; i++) {
    const shapeFlatCoordinate = shapeFlatCoordinates[i];
    for (let j = 0; j < shapeFlatCoordinate.length; j++) {
      const { x, y } = getSigmaPoint(shapeFlatCoordinate[j]);
      const bsmall = Math.floor(-1 * y - ktrbl * x);
      const bbig = Math.floor(-1 * y - ktlbr * x);
      points.push(getSigmaPoint({ x, y, bsmall, bbig }));
    }
  }

  const starts = getAdaptivePolyStart(points);
  // 获取 TopRight RightBottom BottomLeft LeftTop 四个数组;
  const topLeftPoints = getAdaptivePolyTLPoints(points, starts[0]);
  const leftBottomPoints = getAdaptivePolyLBPoints(points, starts[1]);
  const bottomRightPoints = getAdaptivePolyBRPoints(points, starts[2]);
  const rightTopPoints = getAdaptivePolyRTPoints(points, starts[3]);
  const coordinates = [
    ...topLeftPoints,
    ...leftBottomPoints,
    ...bottomRightPoints,
    ...rightTopPoints,
  ];
  return adaptivePolyPointsMerge(coordinates);
}

// 获取起始坐标
function getAdaptivePolyStart(points) {
  // X
  // 最大 bbig 值 最小y值 的点是 start1
  // 最小 bsmall 值 最大y值 的点是 start2
  // 最小 bbig 值 最大y值 的点是 start3
  // 最大 bsmall 值 最小y值 的点是 start4
  let start1 = null;
  let start2 = null;
  let start3 = null;
  let start4 = null;
  let bb1 = -Infinity;
  let bb2 = Infinity;
  let bb3 = Infinity;
  let bb4 = -Infinity;
  for (let i = 0; i < points.length; i++) {
    const p = points[i];
    const { x, y, bsmall, bbig } = p;
    // start1
    if (bb1 < bbig && bbig - bb1 > OFFSET_DEVIATION) {
      bb1 = bbig;
      start1 = p;
    } else if (Math.abs(bbig - bb1) < OFFSET_DEVIATION && x < start1.x) {
      start1 = p;
    }
    // start2
    if (bb2 > bsmall && bb2 - bsmall > OFFSET_DEVIATION) {
      bb2 = bsmall;
      start2 = p;
    } else if (Math.abs(bb2 - bsmall) < OFFSET_DEVIATION && y < start2.y) {
      start2 = p;
    }
    // start3
    if (bb3 > bbig && bb3 - bbig > OFFSET_DEVIATION) {
      bb3 = bbig;
      start3 = p;
    } else if (Math.abs(bb3 - bbig) < OFFSET_DEVIATION && y > start3.y) {
      start3 = p;
    }
    // start4
    if (bb4 < bsmall && bsmall - bb4 > OFFSET_DEVIATION) {
      bb4 = bsmall;
      start4 = p;
    } else if (Math.abs(bb4 - bsmall) < OFFSET_DEVIATION && y > start4.y) {
      start4 = p;
    }
  }
  return [start1, start4, start3, start2];
}

// 获取四边坐标
function getAdaptivePolyTLPoints(points, start) {
  // Top - Right -> b 值从大到小 相同 b 值 取 y 最小的， x < point.x
  // 找到下一个点 P
  // 获取过这个点的线与 过 prev 点 线的交点，M
  // prev.bbig < bbig && prev.bsmall < bsmall
  // 相同 bbig 则 x 越小优先
  start.type = 'vertex';
  start.direction = 'tl';
  const result = [start];
  let next = true;
  let prev = start;
  while (next) {
    next = null;
    // let offsetBSmall = Infinity;
    let offsetBBig = Infinity;
    // 计算 point
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const { x, bbig, bsmall } = point;
      // const _offsetBSmall = prev.bsmall - bsmall;
      const _offsetBBig = prev.bbig - bbig;
      if (_offsetBBig < 0) continue;

      if (Math.abs(offsetBBig - _offsetBBig) < OFFSET_DEVIATION && x < next.x) {
        next = point;
      }
      if (
        prev.bbig - bbig > OFFSET_DEVIATION
        && bsmall - prev.bsmall > OFFSET_DEVIATION
        && offsetBBig - _offsetBBig > OFFSET_DEVIATION
        && x < prev.x
      ) {
        next = point;
        offsetBBig = _offsetBBig;
      }
    }
    if (next) {
      const ins = getPolyRectPoint(prev, next);
      ins[1].direction = 'tl';
      ins[1].type = 'intersection';
      ins[3].direction = 'tl';
      ins[3].type = 'intersection';
      const intersection = ins[1].y < ins[3].y ? [ins[3], ins[1]] : [ins[1], ins[3]];
      intersection[0].bsmall = prev.bsmall;
      intersection[0].bbig = next.bbig;
      intersection[1].bsmall = next.bsmall;
      intersection[1].bbig = prev.bbig;
      intersection[1].type = 'vertex';

      next.type = 'vertex';
      next.direction = 'tl';
      result.push(intersection, next);
      prev = next;
    }
  }
  return result;
}
function getAdaptivePolyLBPoints(points, start) {
  start.type = 'vertex';
  start.direction = 'lb';
  const result = [start];
  let next = true;
  let prev = start;
  while (next) {
    next = null;
    let offsetBSmall = Infinity;
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const { x, y, bbig, bsmall } = point;
      const _offsetBSmall = prev.bsmall - bsmall;
      if (_offsetBSmall < 0) continue;
      if (
        Math.abs(offsetBSmall - _offsetBSmall) < OFFSET_DEVIATION
        && x < next.x
      ) {
        next = point;
      }
      if (
        prev.bbig - bbig > OFFSET_DEVIATION
        && prev.bsmall - bsmall > OFFSET_DEVIATION
        && offsetBSmall - _offsetBSmall > OFFSET_DEVIATION
        && y > prev.y
      ) {
        next = point;
        offsetBSmall = _offsetBSmall;
      }
    }
    if (next) {
      const ins = getPolyRectPoint(prev, next);
      ins[1].direction = 'lb';
      ins[1].type = 'intersection';
      ins[3].direction = 'lb';
      ins[3].type = 'intersection';
      const intersection =        ins[1].x < ins[3].x ? [ins[3], ins[1]] : [ins[1], ins[3]];
      intersection[0].bsmall = next.bsmall;
      intersection[0].bbig = prev.bbig;
      intersection[1].bsmall = prev.bsmall;
      intersection[1].bbig = next.bbig;
      intersection[1].type = 'vertex';
      next.type = 'vertex';
      next.direction = 'lb';
      result.push(intersection, next);
      prev = next;
    }
  }
  return result;
}
function getAdaptivePolyBRPoints(points, start) {
  start.type = 'vertex';
  start.direction = 'br';
  const result = [start];
  let next = true;
  let prev = start;
  while (next) {
    next = null;
    // let offsetBSmall = -Infinity;
    let offsetBBig = -Infinity;
    // 计算 point
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const { x, bbig, bsmall } = point;
      // const _offsetBSmall = prev.bsmall - bsmall;
      const _offsetBBig = prev.bbig - bbig;
      if (_offsetBBig > 0) continue;

      if (Math.abs(offsetBBig - _offsetBBig) < OFFSET_DEVIATION && x > next.x) {
        next = point;
      }
      if (
        bbig - prev.bbig > OFFSET_DEVIATION
        && prev.bsmall - bsmall > OFFSET_DEVIATION
        && _offsetBBig - offsetBBig > OFFSET_DEVIATION
        && x > prev.x
      ) {
        next = point;
        offsetBBig = _offsetBBig;
      }
    }
    if (next) {
      const ins = getPolyRectPoint(prev, next);
      ins[1].direction = 'br';
      ins[1].type = 'intersection';
      ins[3].direction = 'br';
      ins[3].type = 'intersection';
      const intersection =        ins[1].y < ins[3].y ? [ins[1], ins[3]] : [ins[3], ins[1]];
      intersection[0].bsmall = prev.bsmall;
      intersection[0].bbig = next.bbig;
      intersection[1].bsmall = next.bsmall;
      intersection[1].bbig = prev.bbig;
      intersection[1].type = 'vertex';
      next.type = 'vertex';
      next.direction = 'br';
      result.push(intersection, next);
      prev = next;
    }
  }
  return result;
}
function getAdaptivePolyRTPoints(points, start) {
  start.type = 'vertex';
  start.direction = 'rt';
  const result = [start];
  let next = true;
  let prev = start;
  while (next) {
    next = null;
    let offsetBSmall = -Infinity;
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const { x, y, bbig, bsmall } = point;
      const _offsetBSmall = prev.bsmall - bsmall;
      if (_offsetBSmall > 0) continue;

      if (
        Math.abs(offsetBSmall - _offsetBSmall) < OFFSET_DEVIATION
        && x > next.x
      ) {
        next = point;
      }
      if (
        bbig - prev.bbig > OFFSET_DEVIATION
        && bsmall - prev.bsmall > OFFSET_DEVIATION
        && _offsetBSmall - offsetBSmall > OFFSET_DEVIATION
        && y < prev.y
      ) {
        next = point;
        offsetBSmall = _offsetBSmall;
      }
    }
    if (next) {
      const ins = getPolyRectPoint(prev, next);
      ins[1].direction = 'rt';
      ins[1].type = 'intersection';
      ins[3].direction = 'rt';
      ins[3].type = 'intersection';
      const intersection =        ins[1].x < ins[3].x ? [ins[1], ins[3]] : [ins[3], ins[1]];
      intersection[0].bsmall = next.bsmall;
      intersection[0].bbig = prev.bbig;
      intersection[1].bsmall = prev.bsmall;
      intersection[1].bbig = next.bbig;
      intersection[1].type = 'vertex';
      next.type = 'vertex';
      next.direction = next.direction ? next.direction : 'rt';
      result.push(intersection, next);
      prev = next;
    }
  }
  return result;
}

// 对四边扫描结果进行合并
function adaptivePolyPointsMerge(points) {
  const results = [];
  let headIndex = 0;
  let maxDistance = -Infinity;
  for (let i = 0; i < points.length; i++) {
    const p = points[i];
    const { x, y, direction, bsmall } = results[results.length - 1] || {};
    let next;
    if (Array.isArray(p)) {
      const [_p, remove] = getNextPoint(results, p);
      if (remove) {
        i++;
        results.pop();
      }
      next = getSigmaPoint(_p);
    } else {
      next = getSigmaPoint(p);
    }
    if (next.x !== x && next.y !== y) {
      if (
        results.length > 0
        && direction === 'tl'
        && Math.abs(bsmall - next.bsmall) < OFFSET_DEVIATION
      ) {
        const _dis = ((x - next.x) ** 2 + (y - next.y) ** 2) ** 0.5;
        if (_dis > maxDistance) {
          maxDistance = _dis;
          headIndex = i - 1;
        }
      }
      results.push(next);
    }
  }
  const step = Math.round(maxDistance / MIN_DISTANCE);

  return computeCornerCoordinates(results, headIndex, step);
}

// 判断，如果出现交叉情况，那么使用另一个顶点，并且移除上一个点
function getNextPoint(results, p) {
  const [p1, p2] = p;
  if (results.length < 4) return [p1, false];
  for (let i = 0; i < results.length - 2; i++) {
    if (
      judgeIntersect(
        results[i],
        results[i + 1],
        results[results.length - 1],
        p1,
      )
    ) {
      return [p2, true];
    }
  }
  return [p1, false];
}

// 对合并结果进行转角坐标计算
function computeCornerCoordinates(points, headIndex, step) {
  const results = [];
  for (let i = 0; i < points.length; i++) {
    const prev = results[results.length - 1] || points[points.length - 1];
    const curr = points[i];
    const next = points[(i + 1) % points.length];
    if (curr.x === prev.x && curr.x === prev.y) {
      continue;
    }
    if (i === headIndex) {
      results.push(curr);
      continue;
    }
    const prevDistance = ((curr.x - prev.x) ** 2 + (curr.y - prev.y) ** 2) ** 0.5;
    const nextDistance = ((next.x - curr.x) ** 2 + (next.y - curr.y) ** 2) ** 0.5;
    switch (curr.direction) {
      case 'tl':
        const tl = computeTLRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...tl.ps.map(p => getSigmaPoint(p)));
        tl.next && i++;
        break;
      case 'lb':
        const lb = computeLBRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...lb.ps.map(p => getSigmaPoint(p)));
        lb.next && i++;
        break;
      case 'br':
        const br = computeBRRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...br.ps.map(p => getSigmaPoint(p)));
        br.next && i++;
        break;
      case 'rt':
        const rt = computeRTRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...rt.ps.map(p => getSigmaPoint(p)));
        rt.next && i++;
        break;
      default:
        console.error('错误数据');
    }
  }
  const headPoints = getAdaptiveHeadCoordinates(points[headIndex], step);
  return [results, headPoints];
}

function computeTLRounds(prev, curr, next, prevdis, nextdis) {
  const { x, y, type } = curr;
  if (prevdis >= HALF_MIN_DISTANCE && nextdis >= HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x: x + BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
          { x: x - BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
        ],
        next: false,
      };
    }
    return {
      ps: [
        { x: x + BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
        { x: x - BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
      ],
      next: false,
    };
  }
  if (prevdis >= HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          {
            x: x * 2 - next.x,
            y: next.y,
          },
          next,
        ],
        next: true,
      };
    }
    return {
      ps: [{ x: x * 2 - next.x, y: next.y }, next],
      next: true,
    };
  }
  if (nextdis >= HALF_MIN_DISTANCE && prevdis < HALF_MIN_DISTANCE) {
    return {
      ps: [{ x: x * 2 - prev.x, y: prev.y }],
      next: false,
    };
  }
  if (prevdis < HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (prevdis <= nextdis) {
      return {
        ps: [{ x: x * 2 - prev.x, y: prev.y }],
        next: false,
      };
    }
    return {
      ps: [curr, { x: next.x * 2 - x, y }],
      next: true,
    };
  }
  return {
    ps: [curr],
    next: false,
  };
}

function computeLBRounds(prev, curr, next, prevdis, nextdis) {
  const { x, y, type } = curr;
  if (prevdis >= HALF_MIN_DISTANCE && nextdis >= HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x: x + BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
          { x: x + BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
        ],
        next: false,
      };
    }
    return {
      ps: [
        { x: x - BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
        { x: x - BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
      ],
      next: false,
    };
  }
  if (prevdis >= HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [{ x: next.x, y: y * 2 - next.y }, next],
        next: true,
      };
    }
  }
  if (nextdis >= HALF_MIN_DISTANCE && prevdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [{ x: prev.x, y: y * 2 - prev.y }],
        next: false,
      };
    }
    return {
      ps: [{ x: prev.x, y: y * 2 - prev.y }],
      next: false,
    };
  }
  if (prevdis < HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (prevdis <= nextdis) {
      return {
        ps: [{ x: prev.x, y: y * 2 - prev.y }],
        next: false,
      };
    }
    return {
      ps: [{ x: next.x, y: y * 2 - next.y }, next],
      next: true,
    };
  }
  return {
    ps: [curr],
    next: false,
  };
}

function computeBRRounds(prev, curr, next, prevdis, nextdis) {
  const { x, y, type } = curr;
  if (prevdis >= HALF_MIN_DISTANCE && nextdis >= HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x: x - BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
          { x: x + BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
        ],
        next: false,
      };
    }
    return {
      ps: [
        { x: x - BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
        { x: x + BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
      ],
      next: false,
    };
  }
  if (prevdis >= HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [{ x: x * 2 - next.x, y: next.y }, next],
        next: true,
      };
    }
  }
  if (nextdis >= HALF_MIN_DISTANCE && prevdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [{ x: x * 2 - prev.x, y: prev.y }],
        next: false,
      };
    }
    return {
      ps: [{ x: x * 2 - prev.x, y: prev.y }],
      next: false,
    };
  }
  if (prevdis < HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (prevdis <= nextdis) {
      return {
        ps: [
          {
            x: x * 2 - prev.x,
            y: prev.y,
          },
        ],
        next: false,
      };
    }
    return {
      ps: [
        {
          x: x * 2 - next.x,
          y: next.y,
        },
        next,
      ],
      next: true,
    };
  }
  return {
    ps: [curr],
    next: false,
  };
}

function computeRTRounds(prev, curr, next, prevdis, nextdis) {
  const { x, y, type } = curr;
  if (prevdis >= HALF_MIN_DISTANCE && nextdis >= HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x: x - BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
          { x: x - BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
        ],
        next: false,
      };
    }
    return {
      ps: [
        { x: x + BASE_HALF_GRID_X, y: y + BASE_HALF_GRID_Y },
        { x: x + BASE_HALF_GRID_X, y: y - BASE_HALF_GRID_Y },
      ],
      next: false,
    };
  }
  if (prevdis >= HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [{ x: next.x, y: y * 2 - next.y }, next],
        next: true,
      };
    }
    return {
      ps: [{ x: next.x, y: y * 2 - next.y }, next],
      next: true,
    };
  }
  if (nextdis >= HALF_MIN_DISTANCE && prevdis < HALF_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [{ x: prev.x, y: y * 2 - prev.y }],
        next: false,
      };
    }
    return {
      ps: [{ x: prev.x, y: y * 2 - prev.y }],
      next: false,
    };
  }
  if (prevdis < HALF_MIN_DISTANCE && nextdis < HALF_MIN_DISTANCE) {
    if (prevdis <= nextdis) {
      return {
        ps: [{ x: prev.x, y: y * 2 - prev.y }],
        next: false,
      };
    }
    return {
      ps: [
        {
          x: next.x,
          y: y * 2 - next.y,
        },
        next,
      ],
      next: true,
    };
  }
  return {
    ps: [curr],
    next: false,
  };
}

export function getAdaptiveHeadCoordinates(start, step) {
  const { x, y } = start;

  const _step = step > 4 ? 4 : step;
  return [
    getSigmaPoint({ x, y }),
    getSigmaPoint({
      x: x - BASE_HALF_GRID_X,
      y: y - BASE_HALF_GRID_Y,
    }),
    getSigmaPoint({
      x: x - BASE_HALF_GRID_X * (_step + 2),
      y: y + BASE_HALF_GRID_Y * _step,
    }),
    getSigmaPoint({
      x: x - BASE_HALF_GRID_X * (_step + 2),
      y: y + BASE_HALF_GRID_Y * (_step + 2),
    }),
  ];
}

export function getRectHeadCoordinates(startPoint, endPoint) {
  const dis = ((startPoint.x - endPoint.x) ** 2 + (startPoint.y - endPoint.y) ** 2)
    ** 0.5;
  return getAdaptiveHeadCoordinates(startPoint, Math.round(dis / MIN_DISTANCE));
}

export function get2DAdaptivePolyCoordinates(shapeFlatCoordinates) {
  const points = [];
  const start = { x: Infinity, y: Infinity };
  for (let i = 0; i < shapeFlatCoordinates.length; i++) {
    const shapeFlatCoordinate = shapeFlatCoordinates[i];
    for (let j = 0; j < shapeFlatCoordinate.length; j++) {
      const coor = shapeFlatCoordinate[j];
      const { x, y } = coor;
      points.push(coor);
      if (
        (Math.abs(start.y - y) < OFFSET_DEVIATION && x < start.x)
        || (y < start.y && Math.abs(start.y - y) > OFFSET_DEVIATION)
      ) {
        start.x = x;
        start.y = y;
      }
    }
  }
  const tls = get2DAdaptivePolyTLPoints(points, start);
  const headIndex = adaptive2DPolyHeadindex(tls);
  const heads = get2DRectHeadCoordinates(tls[headIndex], tls[headIndex + 1]);
  const lbs = get2DAdaptivePolyLBPoints(points, tls.pop());
  const brs = get2DAdaptivePolyBRPoints(points, lbs.pop());
  const rts = get2DAdaptivePolyRTPoints(points, brs.pop());
  const coordinates = [...tls, ...lbs, ...brs, ...rts];
  const results = compute2DCornerCoordinates(coordinates, headIndex);
  return [results, heads];
}
function get2DAdaptivePolyTLPoints(points, start) {
  /**
   * 第一次查找：找到 x 比自己小的，y 比自己大的， y轴差值小的优先
   *           如果 y 值差值一样，则优先选择 x 差值大的
   * 第二次查找：如果第一次查找失败那么查找 x 与自己相等的，然后查找 y 值越大的优先。
   */
  start.direction = 'tl';
  start.type = 'vertex';
  const results = [start];
  let next = start;
  while (next) {
    let offset1 = Infinity;
    let offset2 = -Infinity;
    let next1 = null;
    let next2 = null;
    next = null;
    const prev = results[results.length - 1];
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const _offset = point.y - prev.y;
      if (_offset <= 0) continue;
      if (offset1 === _offset && point.x < next1.x) {
        next1 = point;
      }
      if (_offset < offset1 && point.x < prev.x) {
        next1 = point;
        offset1 = _offset;
      }
      if (_offset > 0 && prev.x === point.x && _offset > offset2) {
        next2 = point;
        offset2 = _offset;
      }
    }
    next = next1 !== null ? next1 : next2;
    if (next1) {
      results.push(
        { x: prev.x, y: next.y, type: 'intersection', direction: 'tl' },
        { ...next, direction: 'tl', type: 'vertex' },
      );
      continue;
    }
    if (next2) {
      results.push({ ...next, direction: 'tl', type: 'vertex' });
    }
  }
  return results;
}
function get2DAdaptivePolyLBPoints(points, start) {
  /**
   * 从L-B
   * 查找 x 大于的，优先查找 x 值小的。相同 x 则查找 y值更大的。
   */
  start.direction = 'lb';
  start.type = 'vertex';
  const results = [start];
  let next = start;
  while (next) {
    let offset1 = Infinity;
    let offset2 = -Infinity;
    let next1 = null;
    let next2 = null;
    next = null;
    const prev = results[results.length - 1];
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const _offset = point.x - prev.x;
      if (_offset <= 0) continue;
      if (offset1 === _offset && point.y > next1.y) {
        next1 = point;
      }
      if (_offset < offset1 && point.y > prev.y) {
        next1 = point;
        offset1 = _offset;
      }
      if (_offset > 0 && prev.y === point.y && _offset > offset2) {
        next2 = point;
        offset2 = _offset;
      }
    }
    next = next1 !== null ? next1 : next2;
    if (next1) {
      results.push(
        { x: next.x, y: prev.y, direction: 'lb', type: 'intersection' },
        { ...next, direction: 'lb', type: 'vertex' },
      );
      continue;
    }
    if (next2) {
      results.push({ ...next, direction: 'lb', type: 'vertex' });
    }
  }
  return results;
}
function get2DAdaptivePolyBRPoints(points, start) {
  start.direction = 'br';
  start.type = 'vertex';
  const results = [start];
  let next = start;
  while (next) {
    let offset1 = Infinity;
    let offset2 = -Infinity;
    let next1 = null;
    let next2 = null;
    next = null;
    const prev = results[results.length - 1];
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const _offset = prev.y - point.y;
      if (_offset <= 0) continue;
      if (offset1 === _offset && point.x > next1.x) {
        next1 = point;
      }
      if (_offset < offset1 && point.x > prev.x) {
        next1 = point;
        offset1 = _offset;
      }
      if (_offset > 0 && prev.x === point.x && _offset > offset2) {
        next2 = point;
        offset2 = _offset;
      }
    }
    next = next1 !== null ? next1 : next2;
    if (next1) {
      results.push(
        { x: prev.x, y: next.y, direction: 'br', type: 'intersection' },
        { ...next, direction: 'br', type: 'vertex' },
      );
      continue;
    }
    if (next2) {
      results.push({ ...next, direction: 'br', type: 'vertex' });
    }
  }
  return results;
}
function get2DAdaptivePolyRTPoints(points, start) {
  start.direction = 'rt';
  start.type = 'vertex';
  const results = [start];
  let next = start;
  while (next) {
    let offset = Infinity;
    next = null;
    const prev = results[results.length - 1];
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const _offset = prev.x - point.x;
      if (_offset <= 0) continue;
      if (offset === _offset && point.y < next.y) {
        next = point;
      }
      if (_offset < offset && point.y < prev.y) {
        next = point;
        offset = _offset;
      }
    }
    if (next) {
      results.push(
        { x: next.x, y: prev.y, direction: 'rt', type: 'intersection' },
        { ...next, direction: 'rt', type: 'vertex' },
      );
      continue;
    }
  }
  return results;
}

function adaptive2DPolyHeadindex(points) {
  let maxDis = -Infinity;
  let index = 0;
  for (let i = 1; i < points.length; i++) {
    const { x: x1, y: y1 } = points[i - 1];
    const { x: x2, y: y2 } = points[i];
    if (x1 === x2 && y2 - y1 > maxDis) {
      maxDis = y2 - y1;
      index = i - 1;
    }
  }
  return index;
}

function compute2DCornerCoordinates(points, headIndex) {
  const results = [];
  for (let i = 0; i < points.length; i++) {
    const curr = points[i];
    const prev = results[results.length - 1] || points[points.length - 1];
    const next = points[i + 1] || points[0];
    if (i === headIndex) {
      results.push(curr);
      continue;
    }
    const prevDistance =      ((curr.x - prev.x) ** 2 + (curr.y - prev.y) ** 2) ** 0.5;
    const nextDistance =      ((next.x - curr.x) ** 2 + (next.y - curr.y) ** 2) ** 0.5;
    switch (curr.direction) {
      case 'tl':
        const tl = computeTL2DRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...tl.ps);
        tl.next && i++;
        break;
      case 'lb':
        const lb = computeLB2DRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...lb.ps);
        lb.next && i++;
        break;
      case 'br':
        const br = computeBR2DRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...br.ps);
        br.next && i++;
        break;
      case 'rt':
        const rt = computeRT2DRounds(
          prev,
          curr,
          next,
          prevDistance,
          nextDistance,
        );
        results.push(...rt.ps);
        rt.next && i++;
        break;
      default:
        console.error('错误数据');
    }
  }
  return results.map(p => get2DSigmaPoint(p));
}

function computeTL2DRounds(prev, curr, next, prevDis, nextDis) {
  const { x, y, type } = curr;
  if (prevDis >= HALF_2D_MIN_DISTANCE && nextDis >= HALF_2D_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x: x + HALF_2D_MIN_DISTANCE, y },
          { x, y: y + HALF_2D_MIN_DISTANCE },
        ],
      };
    }
    return {
      ps: [
        { x, y: y - HALF_2D_MIN_DISTANCE },
        { x: x - HALF_2D_MIN_DISTANCE, y },
      ],
    };
  }
  if (prevDis >= HALF_2D_MIN_DISTANCE && nextDis < HALF_2D_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x, y: y - HALF_2D_MIN_DISTANCE },
          { x: x + HALF_2D_MIN_DISTANCE, y },
        ],
        next: true,
      };
    }
  }
  if (nextDis >= HALF_2D_MIN_DISTANCE && prevDis < HALF_2D_MIN_DISTANCE) {
  }
  return {
    ps: [curr],
    next: false,
  };
}
function computeLB2DRounds(prev, curr, next, prevDis, nextDis) {
  const { x, y, type } = curr;
  if (prevDis >= HALF_2D_MIN_DISTANCE && nextDis >= HALF_2D_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x, y: y - HALF_2D_MIN_DISTANCE },
          { x: x + HALF_2D_MIN_DISTANCE, y },
        ],
      };
    }
    return {
      ps: [
        { x: x - HALF_2D_MIN_DISTANCE, y },
        { x, y: y + HALF_2D_MIN_DISTANCE },
      ],
    };
  }
  return {
    ps: [curr],
    next: false,
  };
}

function computeBR2DRounds(prev, curr, next, prevDis, nextDis) {
  const { x, y, type } = curr;
  if (prevDis >= HALF_2D_MIN_DISTANCE && nextDis >= HALF_2D_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x: x - HALF_2D_MIN_DISTANCE, y },
          { x, y: y - HALF_2D_MIN_DISTANCE },
        ],
      };
    }
    return {
      ps: [
        { x, y: y + HALF_2D_MIN_DISTANCE },
        { x: x + HALF_2D_MIN_DISTANCE, y },
      ],
    };
  }
  return { ps: [curr] };
}
function computeRT2DRounds(prev, curr, next, prevDis, nextDis) {
  const { x, y, type } = curr;
  if (prevDis >= HALF_2D_MIN_DISTANCE && nextDis >= HALF_2D_MIN_DISTANCE) {
    if (type === 'vertex') {
      return {
        ps: [
          { x, y: y + HALF_2D_MIN_DISTANCE },
          { x: x - HALF_2D_MIN_DISTANCE, y },
        ],
      };
    }
    return {
      ps: [
        { x: x + HALF_2D_MIN_DISTANCE, y },
        { x, y: y - HALF_2D_MIN_DISTANCE },
      ],
    };
  }

  return { ps: [curr] };
}

export function get2DAdaptiveHeadCoordinates(start, step) {
  const { x, y } = start;
  const _step = step > 12 ? 12 : step;
  return [
    get2DSigmaPoint({ x, y }),
    get2DSigmaPoint({
      x: x - BASE_HALF_GRID_2D * 2,
      y,
    }),
    get2DSigmaPoint({
      x: x - BASE_HALF_GRID_2D * 2,
      y: y + BASE_HALF_GRID_2D * (_step - 3),
    }),
    get2DSigmaPoint({
      x,
      y: y + BASE_HALF_GRID_2D * (_step - 1),
    }),
  ];
}

export function get2DRectHeadCoordinates(startPoint, endPoint) {
  return get2DAdaptiveHeadCoordinates(
    startPoint,
    Math.round((endPoint.y - startPoint.y) / BASE_HALF_GRID_2D),
  );
}
