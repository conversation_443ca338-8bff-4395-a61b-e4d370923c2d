import render from '../renders';

// 射线法判断点是否在另一个多边形中
function rayMethod(point, vs) {
  const { x, y } = point;
  let inside = false;
  for (let i = 0, j = vs.length - 1; i < vs.length; j = i++) {
    const { x: xi, y: yi } = vs[i];
    const { x: xj, y: yj } = vs[j];

    const intersect = ((yi >= y) !== (yj >= y))
            && (x <= ((xj - xi) * (y - yi) / (yj - yi) + xi));
    if (intersect) inside = !inside;
  }
  return inside;
};

export function getIsInclusion(parent, child) {
  if (!parent || !child) return;
  const cp = render.getflat(parent);
  const cc = render.getflat(child);
  let b1 = true; let b2  = false;

  for (const c of cc) {
    if (rayMethod(c, cp)) {
      b2 = true;
    } else {
      b1 = false;
    }
  }


  // 0 为三不沾 1 为有重叠 2 为包含
  if (b1) {
    return 2;
  }

  if (b2) {
    return 1;
  }

  return 0;

  // return cc.some(c => rayMethod(c, cp));
}
