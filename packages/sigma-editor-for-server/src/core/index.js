import { v4 as uuid } from 'uuid';
import _, { cloneDeep, isArray } from 'lodash';
import { SVG, on, off } from '@svgdotjs/svg.js';
import { isInside } from 'overlap-area';
import {
  SigmaSecurityGroupShape,
  SigmaSubnetShape,
  SigmaVPCShape,
  SigmaBaseGroupShape,
  SigmaAreaShape,
  SigmaAvailabilityZoneShape,
  SigmaAutoScalingShape,
  SigmaTextLabelShape,
  SigmaCcnShape,
  SigmaTkeShape,
} from '../shape';
import store, { ALIGNMENT_LINES_CLEAR, SET_PREV_SELECTED_GROUP, SET_ROOT_ACTION } from '../store';
import renders from '../renders';
import ShapeManager from '../shape/shapeManager';
import {
  getSigmaPoint,
  get2DSigmaPoint,
  getPolyRectPoint,
  get2DPolyRectPoint,
  arrayPointsToStringPoints,
  stringify,
  parseObjectPointsToArray,
  isVnodeGroupable,
  transform2DTo3D,
  transform3DTo2D,
  is<PERSON><PERSON><PERSON>abel,
  isInner,
} from '../util/tools';
import {
  RENDER_CREATE,
  RENDER_UPDATE,
  RENDER_REMOVE,
  SIGMA_LINE_SHAPE,
  SIGMA_CIRCLE_SHAPE,
  SIGMA_GRAPH_MODE_2D,
  SIGMA_GRAPH_MODE_3D,
  SELECT_CURSOR,
  EXCLUDE_PROPERTYS,
  GROUP_SHAPE_TYPES,
  NOT_SUBSTANCE_SHAPE_TYPES,
  NETWORK_SHAPE_TYPES,
  BASE_HALF_GRID_2D,
  BASE_STEP_Y,
  BASE_STEP_X,
  BASE_SIZE_2D,
  GLOBAL_EVENT_SUFFIX,
  BASE_HALF_GRID_X,
  BASE_HALF_GRID_Y,
  SIGMA_TKE_SHAPE,
} from '../util/constants';
import {
  createConnectLineToolBox,
  clearConnectLineToolBox,
  lastToolBoxVnode,
} from '../util/toolbox';
import { AlignmentLines } from '../affix/alignment-line';
import cmdStack from './cmd';
import { foldVnodes, getNearestGroup } from '../util/get-nearest-group';
import { isLineMoving } from '../renders/line/utils';
import { pureClone, getAllShapeStyles } from '../util/nodes';
import { reviewGraphs } from '../util/review-graphs';
import { setMovablePoints } from '../affix/render-movable-point';
import { handleCheckedChange } from '../util/handle-checked';
import { bindEvents } from '../util/bind-events';
import { getPlanePosition } from '../util/handle-queue';

let _autoLocalSaveTimer = null;
let _rangSelectionTimer = null;
const touchPos = {};
// let _updateMoveTimer = null;
const shapeManager = ShapeManager.getManager();

function SigmaCore(options = {}) {
  this.instance = null;
  this.data = {
    id: uuid(),
    shapes: {}, // 渲染数据 key -> shapeNode
    cached: [], // 缓存的数据，比如复制的数据， 存储 pureNode => 移除 key， component 属性
  };
  this.options = options;
  this.lines = new AlignmentLines();
  this.cmdStack = cmdStack;
  this.allStyles = getAllShapeStyles();
  this.isDoubleTouch = false;
  this._init();
}

/*
 * 初始化函数
 * 初始数据一定是3d，如果是 2d 模式下渲染，那么需要初始转换后再渲染。
 */
SigmaCore.prototype._init = function () {
  const { mode } = store.getState();
  document.oncontextmenu = function () {
    return false;
  };
  if (mode === SIGMA_GRAPH_MODE_2D) {
    this.data.shapes = this.transform(this.data.shapes);
    clearConnectLineToolBox();
  }
  this._initRender();
  this._initWindowFunction();
  this._initAssistFunction();
  this.cmdStack.clearCmd();
};

SigmaCore.prototype._toggleMode = function () {
  const { mode } = store.getState();
  if (mode === SIGMA_GRAPH_MODE_2D) {
    this.data.shapes = this.transform(this.data.shapes);
    clearConnectLineToolBox();
  } else if (mode === SIGMA_GRAPH_MODE_3D) {
    this.data.shapes = this.reduction(this.data.shapes);
  }
  console.log('_toggleMode');
  this.forceRender();
};

/**
 * 新增节点
 * @param {*} vnodes 新增的节点数据
 * @param {*} opts 参数（预留）_historical 是否需要存储到历史
 * @returns
 */
SigmaCore.prototype.add = function (vnodes, opts = { _historical: true }) {
  clearConnectLineToolBox();
  const { shapes = {} } = this.data;
  const _oVnodes = _.isArray(vnodes) ? vnodes : [vnodes];
  const _vnodes = this._vnodeVerify(_oVnodes);
  if (!_vnodes.length) return;
  for (let i = 0; i < _vnodes.length; i++) {
    const vnode = _vnodes[i];
    shapes[vnode.key] = vnode;
  }
  this._setData({ vnodes: _vnodes, action: RENDER_CREATE }, opts, 'add');
  if (opts._historical) {
    cmdStack.saveAction({ type: 'add', data: _vnodes });
  }
};

/**
 * 更新节点
 * @param {*} vnodes 更新的节点数据
 * @param {*} opts 参数（预留）_historical 是否需要存储到历史
 */
SigmaCore.prototype.update = function (vnodes, opts = {}) {
  const { shapes } = this.data;
  for (let i = 0; i < vnodes.length; i++) {
    const { key } = vnodes[i];
    if (shapes[key]) {
      const shape = shapes[key];
      const vnode = vnodes[i];
      for (const k in vnode) {
        if (!EXCLUDE_PROPERTYS.includes(k)) {
          shape[k] = cloneDeep(vnode[k]);
        }
      }
    }
  }
  this._setData({ vnodes, action: RENDER_UPDATE });
  this._relateUpdate(vnodes, opts);
};

/**
 * 移除节点
 * @param {*} vnodes 移除的节点数据
 * @param {*} opts 参数（预留）_historical 是否需要存储到历史
 */
SigmaCore.prototype.remove = function (_v, opts = {}) {
  const { canRemoveLocked = false, _historical = true, isSub = false, force = false } = opts;
  const vnodes  = _.isArray(_v) ? _v : [_v];
  const vnodesPrevSnapshots = pureClone(vnodes);
  const { uneditable } = store.getState();
  if (uneditable && !force) return;
  const { shapes } = this.data;
  const v = canRemoveLocked ? vnodes : vnodes.filter(({ lock }) => !lock);

  if (_historical && !isSub) {
    cmdStack.saveAction({ type: 'delete', data: [] });
  }
  cmdStack.tbVnode = lastToolBoxVnode;

  for (let i = 0; i < v.length; i++) {
    const vnode = v[i];
    const { type, key: vkey, groups = {}, component } = vnode;

    // 全选时，组内元素可能已经删除过了，后面就不用再删除了
    if (!shapes[vkey]) {
      continue;
    }
    delete shapes[vnode.key];
    // 触发删除事件
    component.fire('deleted', { ...opts, isSub: true, _historical });

    if (_historical) {
      cmdStack.saveAction({ type: 'delete', data: [vnodesPrevSnapshots[i]], isSub: true });
    }
    // 如果是组
    if (GROUP_SHAPE_TYPES.includes(type)) {
      const { relations = {} } = vnode;
      for (const key in relations) {
        const child = shapes[key];
        const childSnapshots = pureClone(child);
        child &&  delete child.groups?.[vkey];
        if (child && _historical) {
          cmdStack.saveAction({ data: [child], oldData: [childSnapshots], isSub: true });
        }
      }
      // 如果该删除的组还属于另外的组，则将该组下的图元groups属性设置为他的父组，且将父组的relations属性中添加上该删除组下图元的key
      for (const key in groups) {
        const groupItem = shapes[key];
        for (const subKey in relations) {
          const child = shapes[subKey];
          if (child?.groups) {
            child.groups[key] = groupItem?.type;
          }
        }
        if (groupItem?.relations) {
          groupItem.relations = { ...groupItem.relations, ...relations };
        }
      }
    }
    // 如果加入了组
    for (const key in groups) {
      const g = shapes[key];
      if (g) {
        const gSnapshots = pureClone(g);
        delete g.relations?.[vkey];
        g.component?.fire('relations-change', { vnodesPrevSnapshots: [gSnapshots], isEnd: _historical });
      }
    }
  }
  this._setData({ vnodes: v, action: RENDER_REMOVE }, { ...opts, _historical }, 'delete');
  clearConnectLineToolBox();
  shapeManager.remove(v.map(vn => vn.key));

  this._relateRemove(v, opts);
  if (_historical && !isSub) {
    cmdStack.processDeleteCmd(this);
  }
};

/**
 * 关联更新，查找更新节点关联更新的点
 * @param {*} vnodes
 */
SigmaCore.prototype._relateUpdate = function (vnodes) {
  this.__whenShapeUpdate(vnodes);
};
/**
 * 关联删除，查找更新节点关联删除的节点
 * @param {*} vnodes
 */
SigmaCore.prototype._relateRemove = function (vnodes, opts) {
  const { callbacks = {} } = this.options;
  this.__whenShapeDelete(vnodes, opts);
  callbacks.onShapeDelete && callbacks.onShapeDelete(vnodes);
};

/**
 * 收归函数，在图操作后统一执行渲染，历史记录，本地存储，回调函数等
 * @param {*} param0 数据和行为 {action， vnodes}
 * @param {*} param1 预留参数，目前仅包含 _historical 属性判断是否需要存储到历史。
 * @param {*} nodeOperationType add update delete, 节点操作类型
 */
SigmaCore.prototype._setData = function ({ vnodes, action }, opts) {
  this.render(vnodes, action, opts);
  // this._autoSetCheckedShapes(vnodes, action);
  if (action === RENDER_CREATE) {
    this.onlyShapeChecked(vnodes.filter(({ type, attach }) => ![SIGMA_LINE_SHAPE].includes(type) && !attach));
  }
  this._autoLocalSave();
  this.__onGraphChange();
};

// 数据渲染核心函数
SigmaCore.prototype.render = function (vnodes, action, opts) {
  for (let i = 0; i < vnodes.length; i++) {
    const vnode = vnodes[i];
    switch (action) {
      case RENDER_CREATE:
        this._addComponent(vnode, opts);
        this.__checkShapeLocation(vnode, RENDER_CREATE);
        // 这是一个危险操作，会导致一些层级问题
        // this.__setShapeLocation(vnode);
        break;
      case RENDER_UPDATE:
        this._updateComponent(vnode);
        this.__checkShapeLocation(vnode, RENDER_UPDATE);
        break;
      case RENDER_REMOVE:
        this._removeComponent(vnode);
        break;
      default:
        throw new Error(`错误的渲染模式，检查渲染行为 ${action}, ${vnodes}`);
    }
  }
};
SigmaCore.prototype.forceRender = function () {
  const {
    container: {
      gText,
      gImage,
      gIcon,
      gProduct,
      gRect,
      gLine,
      gCircle,
      gNetwork,
      gCache,
    },
  } = store.getState();
  gText.clear();
  gImage.clear();
  gIcon.clear();
  gProduct.clear();
  gRect.clear();
  gLine.clear();
  gCircle.clear();
  gNetwork.clear();
  gCache.clear();
  console.time('force render');
  const { shapes } = this.data;
  for (const key in shapes) {
    reviewGraphs(this, shapes[key]);
  }
  const sortedShapes = this._getSortedShapes(Object.values(shapes));
  console.log('sortedShapes', sortedShapes);
  for (const vnode of sortedShapes) {
    this._addComponent(vnode);
    this.__checkShapeLocation(vnode, RENDER_CREATE);
  }
  console.timeEnd('force render');
};
SigmaCore.prototype.rerender = function () {
  const { shapes } = this.data;
  const vnodes = [];
  for (const key in shapes) {
    vnodes.push(shapes[key]);
  }
  this.update(vnodes);
};

// group 的渲染依赖于其他基础元素的渲染，所以需要先排序
SigmaCore.prototype._getSortedShapes = function (vnodes, dataSource = 'data') {
  const { shapes } = this.data;
  const gs = [];
  const ags = [];
  const lines = [];
  const attached = [];
  const others = [];
  // 递归组，并按照层级放到数组里
  function traversal(vnode, index) {
    if (!vnode) return;
    if (!GROUP_SHAPE_TYPES.includes(vnode.type)) return;
    if (!gs[index]) gs[index] = [];
    gs[index].push(vnode);
    const { key: k, relations = {} } = vnode;
    for (const key in relations) {
      const v = shapes[key];
      // 兼容旧的图，防止自作多情的组
      if (!(v && v.groups[k])) {
        delete vnode.relations[key];
        continue;
      }
      traversal(shapes[key], index + 1);
    }
  }

  for (const vnode of vnodes) {
    // reviewGraphs(this, vnode);
    if (GROUP_SHAPE_TYPES.includes(vnode.type)) {
      if (dataSource === 'history') {
        gs.push(vnode);
      } else {
        const { groups = {} } = vnode;
        if (Object.keys(groups).length === 0) {
          traversal(vnode, 0);
        };
      }
      ags.push(vnode);
      continue;
    }
    if (vnode.attach) {
      attached.push(vnode);
      continue;
    }
    if (vnode.type === SIGMA_LINE_SHAPE) {
      lines.push(vnode);
      continue;
    }
    others.push(vnode);
  }

  const dgs = [...new Set(gs.flat())];
  // 兼容旧的容器，这些都是没有源头节点的
  for (const v of ags) {
    if (!dgs.find(({ key }) => key === v.key)) {
      v.groups = {};
      v.relations = {};
      others.push(v);
    }
  }

  // 去重合并
  return others.sort((a, b) => {
    const p1 = getPlanePosition(a);
    const p2 = getPlanePosition(b);
    const is = p1.y < p2.y || (p1.y === p2.y && p1.x < p2.x);
    return is ? -1 : 1;
  })
    .concat(dgs)
    .concat(lines)
    .concat(attached);
};

SigmaCore.prototype._initRender = function () {
  const { shapes } = this.data;
  const sortedShapes = this._getSortedShapes(Object.values(shapes));

  for (const vnode of sortedShapes) {
    this.__checkShapeLocation(vnode, RENDER_CREATE);
  }
};
SigmaCore.prototype._initRenderWithStringData = function (data) {
  const { mode } = store.getState();
  // const formateData = JSON.parse(dataString);
  this.data.shapes = data;
  this.data.cached = [];
  store.dispatch({
    type: ALIGNMENT_LINES_CLEAR,
  });
  if (mode === SIGMA_GRAPH_MODE_2D) {
    this.data.shapes = this.transform(this.data.shapes);
    clearConnectLineToolBox();
  }

  this.forceRender();
  this._autoLocalSave();
};
SigmaCore.prototype._pureRender = async function (
  container,
  { onlySelection },
) {
  const { shapes } = this.data;
  const sortedShapes = pureClone(this._getSortedShapes(Object.values(shapes)));
  for (const vnode of sortedShapes) {
    vnode.component = null;

    if (onlySelection && vnode.isChecked) {
      this._setShapeChecked(vnode, false, false);
      await this._renderPureComponent(container, vnode);
    }
    if (!onlySelection) {
      this._setShapeChecked(vnode, false, false);
      await this._renderPureComponent(container, vnode);
    }
    if (vnode.component) {
      this.__checkShapeLocation(vnode, RENDER_CREATE);
    }
  }
};

SigmaCore.prototype.transform = function (data) {
  if (_.isArray(data)) {
    const shapes = cloneDeep(data);
    for (const shape of shapes) {
      shape.component = null;
      renders.transform(shape);
    }
    return shapes;
  }

  const shapes = cloneDeep(data, true);
  for (const key in shapes) {
    const shape = shapes[key];
    shape.component = null;
    renders.transform(shape);
  }
  return shapes;
};

SigmaCore.prototype.reduction = function (data) {
  if (_.isArray(data)) {
    const shapes = cloneDeep(data);
    for (const shape of shapes) {
      shape.component = null;
      renders.reduction(shape);
    }

    return shapes;
  }
  const shapes = cloneDeep(data, true);
  for (const key in shapes) {
    const shape = shapes[key];
    shape.component = null;
    renders.reduction(shape);
  }
  return shapes;
};

// 创建、更新、移除dom操作
SigmaCore.prototype._addComponent = function (vnode, opts = {}) {
  const { component, container } = renders.render(this, vnode);
  let nextX;
  let nextY;
  let dragable = false;
  const that = this;
  setTimeout(() => {
    component.on('mousedown', e => this.__onmousedown(e, vnode));
    component.on('mouseup', e => this.__onmouseup(e, vnode));
    component.on('mouseover', e => this.__onmouseover(e, vnode));
    component.on('mouseout', e => this.__onmouseout(e, vnode));
    component.on('dblclick', e => this.__ondoubleclick(e, vnode));
    component.on('contextmenu', e => this.__onrightclick(e, vnode));
    component.on('checked-change', e => handleCheckedChange(e, vnode));
    component.on('touchstart', (e) => {
      setTimeout(() => {
        if (that.isDoubleTouch) {
          return;
        }
        const event = e.targetTouches[0];
        touchPos.x = event.clientX;
        touchPos.y = event.clientY;
        const { root, viewBox, scale, dropzone, doc } = store.getState();
        const isRotated = root.classList.contains('isRotated'); // 移动端sigma是否存在旋转画布的场景
        const { callbacks } = this.options;
        component.on('touchmove', (e) => {
          if (that.isDoubleTouch) {
            return;
          }
          const moveEvent = e.targetTouches[0];
          e.preventDefault();
          const { x: viewboxX, y: viewboxY, w, h } = viewBox;
          let offsetX;
          let offsetY;
          if (!isRotated) {
            offsetX = touchPos.x - moveEvent.clientX;
            offsetY = touchPos.y - moveEvent.clientY;
          } else {
            offsetY = moveEvent.clientX - touchPos.x;
            offsetX = touchPos.y - moveEvent.clientY;
          }
          const dropzoneWidth = dropzone.node.width.baseVal.value;
          const dropzoneHeight = dropzone.node.height.baseVal.value;
          const dropzoneX = dropzone.node.x.baseVal.value;
          const dropzoneY = dropzone.node.y.baseVal.value;
          nextX = _.round(viewboxX + offsetX * scale, 0);
          nextY = _.round(viewboxY + offsetY * scale, 0);
          dragable = false;
          if (dropzoneY >= 0 && nextY >= 0) {
            if (nextY <= dropzoneY) {
              nextY = dropzoneY;
              return;
            }
            if (nextY - dropzoneY + h >= dropzoneHeight) {
              nextY = dropzoneHeight - h + dropzoneY;
              return;
            }
          }
          if (dropzoneY < 0 && nextY >= 0) {
            const totalHeight = Math.abs(dropzoneY) + nextY + h;
            if (totalHeight >= dropzoneHeight) {
              nextY = dropzoneHeight -  Math.abs(dropzoneY) - h;
              return;
            }
          }
          if (dropzoneY < 0 && nextY < 0) {
            if (nextY <= dropzoneY) {
              nextY = dropzoneY;
              return;
            }
            if (Math.abs(dropzoneY) - Math.abs(nextY)  + h >= dropzoneHeight) {
              nextY = -(dropzoneHeight - Math.abs(dropzoneY) - h);
              return;
            }
          }

          if (dropzoneX >= 0 && nextX >= 0) {
            if (nextX <= dropzoneX) {
              nextX = dropzoneX;
              return;
            }
            if (nextX - dropzoneX + w >= dropzoneWidth) {
              nextX = dropzoneWidth - w + dropzoneX;
              return;
            }
          }
          if (dropzoneX < 0 && nextX >= 0) {
            const totalWidth = Math.abs(dropzoneX) + nextX + w;
            if (totalWidth >= dropzoneWidth) {
              nextX = dropzoneWidth -  Math.abs(dropzoneX) - w;
              return;
            }
          }
          if (dropzoneX < 0 && nextX < 0) {
            if (nextX <= dropzoneX) {
              nextX = dropzoneX;
              return;
            }
            if (Math.abs(dropzoneX) - Math.abs(nextX) + w >= dropzoneWidth) {
              nextX = -(dropzoneWidth - Math.abs(dropzoneX) - w);
              return;
            }
          }
          dragable = true;
          const nextViewBox = `${nextX} ${nextY} ${viewBox.w} ${viewBox.h}`;
          doc.attr({ viewBox: nextViewBox });
          // 触发 doc move 事件
          callbacks.onDocMove && callbacks.onDocMove();
        });
      }, 0);
    });
    component.on('touchend', () => {
      setTimeout(() => {
        const { viewBox } = store.getState();
        if (nextX !== undefined && nextY !== undefined && dragable) {
          store.dispatch({
            type: SET_ROOT_ACTION,
            value: {
              viewBox: { x: nextX, y: nextY, w: viewBox.w, h: viewBox.h },
            },
          });
          nextX = undefined;
          nextY = undefined;
        }
      }, 0);
    });
  }, 0);

  bindEvents(this, vnode);

  container.add(component);
  // if (GROUP_SHAPE_TYPES.includes(vnode.type)) component.back();

  component.fire('style-changed', { styles: vnode.styles, initial: true, pure: !opts._historical });
  component.fire('checked-change', { initial: true });
  // if (GROUP_SHAPE_TYPES.includes(vnode.type)) {
  //   setTimeout(() => component.fire('rank-change'));
  // }
  this.__onShapeInit(vnode);
};
SigmaCore.prototype._updateComponent = function (vnode) {
  renders.rerender(this, vnode);
};
SigmaCore.prototype._removeComponent = function (vnode) {
  vnode.component?.remove();
};
SigmaCore.prototype._renderPureComponent = async function (container, vnode) {
  const [component, gIndex] = await renders.exports(this, vnode);
  // 样式初始化
  const params = { initial: true, styles: vnode.styles, isExport: true };
  component.fire('style-changed', params);

  if (_.isNumber(gIndex)) {
    container[gIndex].add(component);
    // if (GROUP_SHAPE_TYPES.includes(vnode.type)) component.back();
    return;
  }
  throw new Error(`容器不存在，请检查数据: ${vnode}`);
};

SigmaCore.prototype.__onShapeInit = function (vnode) {
  const { callbacks = {} } = this.options;
  callbacks.onShapeInit && callbacks.onShapeInit(vnode);

  // vnode.component.fire('initial');
};

// SigmaCore.prototype._handleCheckedChange = function (event, vnode) {
//   const { initial = false } = event.detail || {};
//   if (!vnode || NOT_RAISE_WHEN_CHECKED_TYPES.includes(vnode.type) || vnode.attach) return;

//   const { key, isChecked, component } = vnode;
//   const className = 'checked-shapes';
//   const { container: { gCache } } = store.getState();
//   let wrapper = gCache.findOne(`.${className}`);
//   if (!wrapper) {
//     wrapper = gCache.group({ class: className });
//   }

//   if (isChecked) {
//     const ghost = new SVG().group()
//       .attr({ key });
//     ghost.insertAfter(component);
//     component.remember('ghost', ghost);
//     component.toParent(wrapper);
//   } else {
//     if (initial) return;

//     const ghost = component.remember('ghost');
//     ghost?.replace(component);
//     component.forget('ghost');
//   }

//   component.off('deleted.checked');
//   component.on('deleted.checked', () => {
//     component?.remember('ghost')?.remove();
//   });
// };

// 初始化公共事件、键盘事件
SigmaCore.prototype._initWindowFunction = function () {
  // delete 只响应在画布上
  const { root, doc } = store.getState();
  on(root, `keydown${GLOBAL_EVENT_SUFFIX}`, (e) => {
    const keyCode = e.key;
    const ctrlKey = e.ctrlKey || e.metaKey;
    const { shiftKey } = e;
    switch (true) {
      case ctrlKey && (keyCode === 'c' || keyCode === 'C'):
        this._keyPressCtrlAndC();
        break;
      case ctrlKey && (keyCode === 'v' || keyCode === 'V'):
        this._keyPressCtrlAndV();
        break;
      case ctrlKey && (keyCode === 'd' || keyCode === 'D'):
        e.preventDefault();
        this._keyPressCtrlAndD();
        break;
      case ctrlKey && (keyCode === 'y' || keyCode === 'Y'):
      case ctrlKey && shiftKey && (keyCode === 'z' || keyCode === 'Z'):
        e.preventDefault();
        this._keyPressCtrlAndY();
        break;
      case ctrlKey && (keyCode === 'z' || keyCode === 'Z'):
        e.preventDefault();
        this._keyPressCtrlAndZ();
        break;
      case ctrlKey && (keyCode === 'x' || keyCode === 'X'):
        this._keyPressCtrlAndX();
        break;
      case ctrlKey && (keyCode === 's' || keyCode === 'S'):
        this._keyPressCtrlAndS();
        break;
      case ctrlKey && (keyCode === 'l' || keyCode === 'L'):
        this._keyPressCtrlAndL();
        break;
      case ctrlKey && (keyCode === 'g' || keyCode === 'G'):
        e.preventDefault();
        this._keyPressCtrlAndG();
        break;
      case (keyCode === 'ArrowUp' || keyCode === 'W' || keyCode === 'w')
        && !ctrlKey:
        clearConnectLineToolBox();
        this._keyPressUp();
        break;
      case (keyCode === 'ArrowDown' || keyCode === 'S' || keyCode === 's')
        && !ctrlKey:
        clearConnectLineToolBox();
        this._keyPressDown();
        break;
      case (keyCode === 'ArrowRight' || keyCode === 'D' || keyCode === 'd')
        && !ctrlKey:
        clearConnectLineToolBox();
        this._keyPressRight();
        break;
      case (keyCode === 'ArrowLeft' || keyCode === 'A' || keyCode === 'a')
        && !ctrlKey:
        clearConnectLineToolBox();
        this._keyPressLeft();
        break;
      case ctrlKey && keyCode === 'ArrowLeft':
        e.preventDefault();
        this._keyPressCtrlAndLeft();
        break;
      case ctrlKey && keyCode === 'ArrowRight':
        e.preventDefault();
        this._keyPressCtrlAndRight();
        break;
      case ctrlKey && keyCode === 'ArrowUp':
        e.preventDefault();
        this._keyPressCtrlAndUp();
        break;
      case ctrlKey && keyCode === 'ArrowDown':
        e.preventDefault();
        this._keyPressCtrlAndDown();
        break;

      default:
      // ...
    }
  });

  const { callbacks = {} } = this.options;

  root.addEventListener('keydown', (e) => {
    const keyCode = e.key;
    const ctrlKey = e.ctrlKey || e.metaKey;
    if (keyCode === 'Backspace' || keyCode === 'Delete') {
      this._keyPressDelete();
    }
    // 全选也只响应在画布上
    if (ctrlKey && (keyCode === 'a' || keyCode === 'A')) {
      clearConnectLineToolBox();
      this._keyPressCtrlAndA();
    }
  });

  function onMouseWheel(e) {
    e.preventDefault();
    const { ctrlKey, metaKey, deltaX, deltaY } = e;
    const isCtrl = ctrlKey || metaKey;
    if (isCtrl) return;
    const { viewBox, scale, doc, dropzone } = store.getState();
    const dropzoneWidth = dropzone.node.width.baseVal.value;
    const dropzoneHeight = dropzone.node.height.baseVal.value;
    const dropzoneX = dropzone.node.x.baseVal.value;
    const dropzoneY = dropzone.node.y.baseVal.value;
    const { x: viewboxX, y: viewboxY, w, h } = viewBox;
    const nextX = viewboxX + scale * deltaX;
    const nextY = viewboxY + scale * deltaY;
    if (dropzoneY >= 0 && nextY >= 0) {
      if (nextY <= dropzoneY) {
        return;
      }
      if (nextY - dropzoneY + h >= dropzoneHeight) {
        return;
      }
    }
    if (dropzoneY < 0 && nextY >= 0) {
      const totalHeight = Math.abs(dropzoneY) + nextY + h;
      if (totalHeight >= dropzoneHeight) {
        return;
      }
    }
    if (dropzoneY < 0 && nextY < 0) {
      if (nextY <= dropzoneY) {
        return;
      }
      if (Math.abs(dropzoneY) - Math.abs(nextY)  + h >= dropzoneHeight) {
        return;
      }
    }

    if (dropzoneX >= 0 && nextX >= 0) {
      if (nextX <= dropzoneX) {
        return;
      }
      if (nextX - dropzoneX + w >= dropzoneWidth) {
        return;
      }
    }
    if (dropzoneX < 0 && nextX >= 0) {
      const totalWidth = Math.abs(dropzoneX) + nextX + w;
      if (totalWidth >= dropzoneWidth) {
        return;
      }
    }
    if (dropzoneX < 0 && nextX < 0) {
      if (nextX <= dropzoneX) {
        return;
      }
      if (Math.abs(dropzoneX) - Math.abs(nextX) + w >= dropzoneWidth) {
        return;
      }
    }

    const nextViewBox = `${nextX} ${nextY} ${w} ${h}`;
    store.dispatch({
      type: SET_ROOT_ACTION,
      value: {
        viewBox: { x: nextX, y: nextY, w, h },
      },
    });
    doc.attr({ viewBox: nextViewBox });

    clearConnectLineToolBox();
    // 触发 doc move 事件
    callbacks.onDocMove && callbacks.onDocMove();
  }

  // doc.on('wheel', throttle(onMouseWheel, 20));
  doc.on('wheel', onMouseWheel);
};
SigmaCore.prototype._initAssistFunction = function () {
  const {
    dropzone,
    container: { gCache },
  } = store.getState();
  const that = this;
  let startPos;
  const poly = new SVG().polygon()
    .attr({ class: 'selection' });
  // window.addEventListener('keydown', (e) => {
  //   if (e.code === 'Space') {
  //     dropzone.off('mousedown', dropzoneMousedown);
  //   }
  // });
  // window.addEventListener('keyup', (e) => {
  //   if (e.code === 'Space') {
  //     dropzone.on('mousedown', dropzoneMousedown);
  //   }
  // });
  on(window, `keydown${GLOBAL_EVENT_SUFFIX}`, (e) => {
    if (e.code === 'Space') {
      dropzone.off('mousedown', dropzoneMousedown);
    }
  });
  on(window, `keyup${GLOBAL_EVENT_SUFFIX}`, (e) => {
    if (e.code === 'Space') {
      dropzone.on('mousedown', dropzoneMousedown);
    }
  });

  dropzone.on('mousedown', dropzoneMousedown);

  function dropzoneMousedown(e) {
    if (!(e.ctrlKey || e.metaKey)) {
      startPos = dropzone.point(e.clientX, e.clientY);
      window.addEventListener('mousemove', frameSelection);
      window.addEventListener('mouseup', shakeOff);
      // on(window, 'mousemove.frame', frameSelection);
      // on(window, 'mouseup.frame', shakeOff);
    }
    that.allShapeNotChecked();
    clearConnectLineToolBox();
  }

  const frameSelection = (e) => {
    const { mode } = store.getState();
    const endPos = dropzone.point(e.clientX, e.clientY);
    const polyPoint = mode === SIGMA_GRAPH_MODE_3D
      ? getPolyRectPoint(startPos, endPos)
      : get2DPolyRectPoint(startPos, endPos);
    poly.attr({
      points: arrayPointsToStringPoints(polyPoint),
      fill: '#4080FF',
      'fill-opacity': 0.15,
      stroke: '#4080FF',
      'stroke-width': 2,
      cursor: SELECT_CURSOR,
    });
    if (!gCache.findOne('.selection')) {
      gCache.add(poly);
    }
    that.__rangSelectionNodes(polyPoint);
  };
  const shakeOff = () => {
    // gCache.clear();
    this.__clearSelection();

    window.removeEventListener('mouseup', shakeOff);
    window.removeEventListener('mousemove', frameSelection);
    // off(window, '.frame');
  };

  // 清除 movablePoints
  // dropzone.on('mouseover', () => {
  //   clearMovablePoints();
  // });
};

SigmaCore.prototype.__clearSelection = function () {
  const {
    container: { gCache },
  } = store.getState();
  const wrapper = gCache.findOne('.selection');
  wrapper?.remove();
};

SigmaCore.prototype._setShapeChecked = function (v, is, shouldUpdate = true) {
  const vns = Array.isArray(v) ? v : [v];
  let vnodes = vns.filter(item => item?.isChecked !== is);
  vnodes = vnodes.filter(item => !!item);
  if (vnodes.length <= 0) return;
  for (let i = 0; i < vnodes.length; i++) {
    const vnode = vnodes[i];
    if (vnode) {
      vnode.isChecked = is;

      // 触发 checked-change
      vnode.component?.fire('checked-change');
    }
  }
  shouldUpdate && this.update(vnodes, { cachemanager: true });

  this.__onCheckedChange();
};

// 节点操作
SigmaCore.prototype.onlyShapeChecked = function (v) {
  const vnodes = Array.isArray(v) ? v : [v];
  if (!vnodes.length) return;

  const { shapes } = this.data;
  const map = vnodes.map(_vn => _vn.key);
  const willTrue = [];
  const willFalse = [];

  for (const key in shapes) {
    const vnode = shapes[key];
    if (map.includes(key)) {
      if (!vnode.isChecked) {
        willTrue.push(vnode);
      }
    } else if (vnode.isChecked) {
      willFalse.push(vnode);
    }
  }

  this._setShapeChecked(willTrue, true);
  this._setShapeChecked(willFalse, false);
};
SigmaCore.prototype._toggleShapeChecked = function (v) {
  const vnodes = Array.isArray(v) ? v : [v];
  const is = vnodes.every(({ isChecked }) => isChecked === true);
  this._setShapeChecked(vnodes, !is);
};

SigmaCore.prototype.allShapeChecked = function () {
  const { shapes } = this.data;
  this._setShapeChecked(Object.values(shapes), true);
};
SigmaCore.prototype.allShapeNotChecked = function () {
  const { shapes } = this.data;
  this._setShapeChecked(Object.values(shapes), false);
};
SigmaCore.prototype.allShapeTypeChecked = function (vonode) {
  const { type: t } = vonode;
  const { shapes } = this.data;
  this._setShapeChecked(
    Object.values(shapes).filter(({ type }) => type === t),
    true,
  );
};

// 属性设置
SigmaCore.prototype.setStyles = function (data = {}, keys, opt = {}) {
  let checkedShapes = [];
  if (keys) {
    const _keys = Array.isArray(keys) ? keys : [keys];
    checkedShapes = this._getShapesByKeys(_keys);
  } else {
    checkedShapes = this._getCheckedShapes();
  }
  const nodesPrevSnapshots = pureClone(checkedShapes);
  const { _historical = true } = opt;
  const oldData = {};
  for (let i = 0; i < checkedShapes.length; i++) {
    const vnode = checkedShapes[i];
    Object.keys(data).forEach((key) => {
      oldData[key] = vnode.styles[key];
    });
    for (const key in data) {
      vnode.styles[key] = Object.assign({}, vnode.styles[key], cloneDeep(data[key]));
    }
  }
  if (_historical) {
    cmdStack.saveAction({
      data: checkedShapes,
      oldData: nodesPrevSnapshots,
      groupBegin: true,
    });
  }
  this._changeNodesStyles(checkedShapes, cloneDeep(data));

  if (_historical) {
    const targetKeys = checkedShapes.map(it => it.key);
    cmdStack.saveAction({
      type: 'setStyles',
      data: [{ keys: targetKeys, style: data }],
      oldData: [{ keys: targetKeys, style: oldData }],
      groupEnd: true,
    });
  }
};
// 属性设置
SigmaCore.prototype._changeNodesStyles = function (nodes, data) {
  this.update(nodes);

  nodes.forEach((vnode) => {
    vnode.component?.fire('style-changed', { styles: vnode.styles, data });
  });
};

// 安全组、私有网络、子网相关 VPC > Subnet > SecurityGroup
SigmaCore.prototype.setBaseGroup = function (key = null) {
  return this.setCommonGroup(SigmaBaseGroupShape, key);
};
SigmaCore.prototype.setSecurityGroup = function (key = null) {
  return this.setCommonGroup(SigmaSecurityGroupShape, key);
};
SigmaCore.prototype.setSubnetGroup = function (key = null) {
  return this.setCommonGroup(SigmaSubnetShape, key);
};
SigmaCore.prototype.setVPCGroup = function (key = null) {
  return this.setCommonGroup(SigmaVPCShape, key);
};
SigmaCore.prototype.setAreaGroup = function (key = null) {
  return this.setCommonGroup(SigmaAreaShape, key);
};
SigmaCore.prototype.setAvailabilityZoneGroup = function (key = null) {
  return this.setCommonGroup(SigmaAvailabilityZoneShape, key);
};
SigmaCore.prototype.setCcnGroup = function (key = null) {
  return this.setCommonGroup(SigmaCcnShape, key);
};
SigmaCore.prototype.setTkeGroup = function (key = null) {
  return this.setCommonGroup(SigmaTkeShape, key);
};
SigmaCore.prototype.setAutoScalingGroup = function (key = null) {
  this.setCommonGroup(SigmaAutoScalingShape, key);
};


SigmaCore.prototype.setCommonGroup = function (Shape, key = null) {
  let checkeds = this._getCheckedShapes().filter(v => isVnodeGroupable(v));
  checkeds = foldVnodes(checkeds);
  const { shapes } = this.data;
  if (!checkeds.length) return;

  let flag = false;
  let vnode;

  cmdStack.saveAction({
    groupBegin: true,
  });
  if (key) {
    const { shapes } = this.data;
    const shape = shapes[key];
    if (shape) {
      flag = true;
      vnode = shape;
    } else {
      console.error('设置了错误的基础组');
      return;
    }
  } else {
    const s = new Shape();
    vnode = s.create();
    this.add([vnode]);
  }
  const firstShape = checkeds?.[0] || {};
  const firstShapeGroups = firstShape?.groups || {};
  const firstShapeGroupKey = Object.keys(firstShapeGroups)?.[0];
  const vnodesPrevSnapshots = pureClone(checkeds);
  const groupPrevSnapshots = pureClone([vnode]);

  for (let j = 0; j < checkeds.length; j++) {
    const target = checkeds[j];
    // 注意： base group 的 key 为 groups
    // 选中图元解除与原来组的关系
    if (target?.groups) {
      const targetGroupKeys = Object.keys(target.groups);
      targetGroupKeys.forEach((groupKey) => {
        const groupItem = shapes[groupKey];
        if (groupItem?.relations) {
          delete groupItem.relations?.[target?.key];
        }
      });
      target.groups = {};
    }
    // 再把所选图元的所属组设置为新的组
    target.groups[vnode.key] = vnode.type;
    vnode.relations[target.key] = target.type;
  }
  // 如果checkeds数组中第一个图元存在所属组, 则将新成的组加入该组中
  if (firstShapeGroupKey) {
    const firstCheckGroup = shapes[firstShapeGroupKey];
    if (firstCheckGroup) {
      if (!(firstCheckGroup?.relations)) {
        firstCheckGroup.relations = {};
      }
      firstCheckGroup.relations = { ...firstCheckGroup.relations, [vnode.key]: vnode.type };
      vnode.groups = { [firstCheckGroup.key]: firstCheckGroup.type };
    }
  }

  cmdStack.saveAction({
    data: pureClone(checkeds),
    oldData: vnodesPrevSnapshots,
  });
  cmdStack.saveAction({
    groupEnd: true,
    data: pureClone([vnode]),
    oldData: groupPrevSnapshots,
  });

  this.update([vnode]);

  // 触发relations-change事件
  vnode.component.fire('relations-change', {
    type: flag ? 'add' : 'initial',
  });

  const parentGroupKeys = vnode?.groups || {};
  const keys = Object.keys(parentGroupKeys);
  keys.forEach((item) => {
    const shape = shapes[item];
    if (shape) {
      shape.component.fire('rank-change');
    }
  });

  return key || vnode.key;
};

// 常见操作
SigmaCore.prototype.clone = function () {
  this.copy();
  this.paste();
};
SigmaCore.prototype.cut = function () {
  const checkedShapes = this._getCheckedShapes();
  if (!checkedShapes.length) return;
  this.copy();
  this.remove(checkedShapes, { canRemoveLocked: true });
  this.__onCheckedChange();
};

SigmaCore.prototype.copy = function () {
  const { uneditable } = store.getState();
  if (uneditable) {
    return;
  }
  const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
  const checkedShapes = this._getCheckedShapes({
    excludeType: SIGMA_LINE_SHAPE,
    checkedable: true,
  });
  if (!checkedShapes.length) {
    this.data.cached = {};
    return;
  };
  let nextCached = [...checkedShapes];
  nextCached.push(...this._getRelationShapes(checkedShapes));
  nextCached.push(...this._getRelationLines(nextCached));
  nextCached = pureClone(this._deDuplicateShapes(nextCached));

  this.data.cached = is3D ? this.transform(nextCached) : nextCached;
};

SigmaCore.prototype.paste = function () {
  const { uneditable } = store.getState();
  if (uneditable) {
    return;
  }
  const { mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const { cached, shapes } = this.data;
  if (!cached.length) return;

  const nextCached = pureClone(cached);
  const willUpdateGroups = [];
  const parentGroupKeys = [];
  const map = {};

  for (let k = 0; k < nextCached.length; k++) {
    const nextShape = nextCached[k];
    const newKey = uuid();
    map[nextShape.key] = newKey;
    nextShape.component = null;
    nextShape.key = newKey;
    // if (nextShape.type === SIGMA_TKE_SHAPE) {
    //   nextShape.relations = {};
    //   nextShape.styles.showTips.value = true;
    //   nextShape.position.x += nextShape.width;
    //   nextShape.height = 180;
    //   nextShape.width = 180;
    // }
  }
  for (let l = 0; l < nextCached.length; l++) {
    const nextShape = nextCached[l];
    // const offset =  is3D ? { x: 0, y: BASE_GRID_Y * 2 } : { x: BASE_SIZE_2D, y: BASE_SIZE_2D };
    const offset =  { x: BASE_SIZE_2D, y: 0 };
    nextShape.position.x += offset.x;
    nextShape.position.y += offset.y;
    // 更新label
    nextShape.sticky = map[nextShape.sticky] || null;
    nextShape.attach = map[nextShape.attach] || null;
    for (const key in nextShape.groups) {
      if (map[key]) {
        nextShape.groups[map[key]] = nextShape.groups[key];
        parentGroupKeys.push(map[key]);
        delete nextShape.groups[key];
      } else { // 此时并没有一同复制组
        const parent = shapes[key];
        if (parent) {
          parent.relations[nextShape.key] = nextShape.type;
          willUpdateGroups.push(parent);
          parentGroupKeys.push(key);
        } else {
          delete nextShape.groups[key];
        }
      }
    }

    if (nextShape.type === SIGMA_LINE_SHAPE) {
      const { data } = nextShape;
      const { start, end, points } = data;
      data.start.vkey = map[start.vkey];
      data.end.vkey = map[end.vkey];
      for (let i = 0; i < points.length; i ++) {
        const { x, y } = points[i];
        data.points[i].x = x + offset.x;
        data.points[i].y = y + offset.y;
      }
    }
    if (GROUP_SHAPE_TYPES.includes(nextShape.type)) {
      const { relations = {} } = nextShape;
      for (const key in relations) {
        if (map[key]) {
          relations[map[key]] = relations[key];
          delete relations[key];
        }
      }
    }
  }

  this.allShapeNotChecked();
  this.add(is3D ? this.reduction(nextCached) : nextCached);
  willUpdateGroups.forEach((g) => {
    g.component?.fire('relations-change');
  });
  parentGroupKeys.forEach((key) => {
    const g = shapes[key];
    if (g) {
      g.component?.fire('rank-change');
    }
  });
  const { callbacks = {} } = this.options;
  callbacks.onShapesPaste && callbacks.onShapesPaste(nextCached);
};

SigmaCore.prototype.__setLock = function (is) {
  const checkedShapes = this._getCheckedShapes();
  const vnodesPrevSnapshots = pureClone(checkedShapes);
  for (let i = 0; i < checkedShapes.length; i++) {
    checkedShapes[i].lock = is;
  }
  cmdStack.saveAction({
    data: checkedShapes,
    oldData: vnodesPrevSnapshots,
  });
  this.__onCheckedChange();
};
SigmaCore.prototype.lock = function () {
  this.__setLock(true);
};
SigmaCore.prototype.unlock = function () {
  this.__setLock(false);
};
SigmaCore.prototype.toggleLock = function () {
  const checkedShapes = this._getCheckedShapes();
  const is = checkedShapes.every(({ lock }) => lock === true);
  this.__setLock(!is);
};
SigmaCore.prototype.delete = function () {
  const checkedShapes = this._getCheckedShapes();
  if (!checkedShapes.length) return;
  const c = checkedShapes.filter(({ forever }) => !forever)?.filter(({ isTkeSubVode }) => !isTkeSubVode); // 这里在加一个标志位isTkeSubVode 如过是tkegourp下的节点不让删
  this.remove(c);
  this.__onCheckedChange();
};
SigmaCore.prototype.descript = function (description) {
  const checkedShapes = this._getCheckedShapes();
  if (!checkedShapes.length) return;
  const vnodesPrevSnapshots = pureClone(checkedShapes);
  for (let i = 0; i < checkedShapes.length; i++) {
    checkedShapes[i].descript = description;
  }
  cmdStack.saveAction({
    data: checkedShapes,
    oldData: vnodesPrevSnapshots,
  });
};
SigmaCore.prototype.raise = function () {
  const checkedShapes = this._getCheckedShapes();
  if (checkedShapes.length !== 1) return;
  const vnode = checkedShapes[0];
  const { max } = this.__getShapesMaxAndMinZIndex();
  const vnodesPrevSnapshots = pureClone(checkedShapes);
  vnode.zIndex = max + 1;
  cmdStack.saveAction({
    data: checkedShapes,
    oldData: vnodesPrevSnapshots,
  });
};
SigmaCore.prototype.lower = function () {
  const checkedShapes = this._getCheckedShapes();
  if (checkedShapes.length !== 1) return;
  const vnode = checkedShapes[0];
  const { min } = this.__getShapesMaxAndMinZIndex();
  const vnodesPrevSnapshots = pureClone(checkedShapes);
  vnode.zIndex = min - 1;
  cmdStack.saveAction({
    data: checkedShapes,
    oldData: vnodesPrevSnapshots,
  });
};
SigmaCore.prototype.replace = function (node, opt = {}) {
  const { _historical = true, needCheckType = true } = opt;
  let typeCheckeds = [];
  if (needCheckType) {
    const checkeds = this._getCheckedShapes();
    typeCheckeds = checkeds.filter(vn => vn.type === node.type);
  } else {
    typeCheckeds = [this.data.shapes[node.key]];
  }
  if (typeCheckeds.length !== 1) return;
  const vnodesPrevSnapshots = pureClone(typeCheckeds);
  const { key, position } = typeCheckeds[0];
  Object.assign(node, { key, position });
  typeCheckeds[0].component.remove();
  const option = _historical ? { _historical: true, vnodesPrevSnapshots } : { _historical: false };
  this.add([node], option);
};
SigmaCore.prototype.move = function (stepX = 0, stepY = 0) {
  const { uneditable } = store.getState();
  if (uneditable) {
    return;
  }
  const willUpdateNodes = [];
  const _checkedShapes = this._getCheckedShapes();
  // const checkedShapes = _checkedShapes.filter(s => !s.lock);
  const checkedShapes = this._deDuplicateShapes(_checkedShapes.concat(this._getRelationShapes(_checkedShapes)).filter(v => !v.attach && !v.lock));
  if (!checkedShapes?.length) return;
  const vnodesPrevSnapshots = checkedShapes.map(it => pureClone(it));
  for (let i = 0; i < checkedShapes.length; i++) {
    const vnode = checkedShapes[i];
    if (vnode.attach) continue;

    vnodesPrevSnapshots.push(pureClone(vnode));
    const { x, y } = vnode.position;
    vnode.position = { x: x + stepX, y: y + stepY };
    willUpdateNodes.push(vnode);
  }
  // 找到
  const polyLineKeys = willUpdateNodes.filter(item => item.styles?.lineType?.value === 'polyline').map(item => item.key);
  const willUpdateLines = this._getRelationLines(willUpdateNodes);
  willUpdateLines.forEach((line) => {
    line.component.fire('position-change', {
      diff: { x: stepX, y: stepY },
    });
  });
  willUpdateNodes.forEach((vnode) => {
    const isLine = vnode?.type === SIGMA_LINE_SHAPE;
    // 折线需要传入diff参数
    if (!isLine) {
      vnode.component.fire('position-change', {
        position: vnode.position,
        type: 'move',
        movingLineKeys: polyLineKeys,
      });
    }
  });
  cmdStack.saveAction({
    updateType: 'position-change',
    data: willUpdateNodes,
    oldData: vnodesPrevSnapshots,
  });
  this.lines.clear();
  this.update(willUpdateNodes);
  this._updateVnodeGroups(willUpdateNodes);
};

SigmaCore.prototype._updateVnodeGroups = function (v) {
  const vnodes = Array.isArray(v) ? v : [v];
  const { shapes } = this.data;
  const willUpdateGroups = {};
  for (const vnode of vnodes) {
    const { groups = {} } = vnode;
    for (const key in groups) {
      if (!willUpdateGroups[key]) {
        const group = shapes[key];
        group && (willUpdateGroups[key] = group);
      }
    }
  }
  Object.values(willUpdateGroups).forEach((group) => {
    group.component?.fire('relations-change');
  });
};

// 元属性设置
SigmaCore.prototype.setPosition = function (position, key) {
  const shapes = this._getShapesByKeys([key]);
  if (shapes.length > 0) {
    const standardPosition = this.__getStandardPosition({
      ...shapes[0].position,
      ...position,
    });
    shapes[0].position = standardPosition;
    this.update(shapes);
  }
};

// 对齐与排列
// SigmaCore.prototype.oldAlign = function (dir, keys) {
//   const { mode } = store.getState();
//   const willUpdateNodes = [];
//   const vnodesPrevSnapshots = [];
//   const substanceCheckedShapes = this._getCheckedSubstanceShape(keys);
//   if (substanceCheckedShapes.length < 2) return;
//   const positions = substanceCheckedShapes.map(vnode => vnode.position);

//   const index = {
//     top: 0,
//     right: 1,
//     bottom: 2,
//     left: 3,
//   }[dir];

//   if (mode === SIGMA_GRAPH_MODE_3D) {
//     const rect = getIntersectionPolyCoordinates([positions]);
//     const align = rect[index];
//     for (let i = 0; i < substanceCheckedShapes.length; i++) {
//       const vnode = substanceCheckedShapes[i];
//       const { position } = vnode;
//       vnodesPrevSnapshots.push(pureClone(vnode));
//       let b1; let b2; let x; let y;
//       if (['left', 'right'].includes(dir)) {
//         b1 = position.y - position.x * BASE_SLOPE;
//         b2 = align.y + align.x * BASE_SLOPE;

//         x = (b1 - b2) / (-2 * BASE_SLOPE);
//         y = BASE_SLOPE * x + b1;
//       }
//       if (['top', 'bottom'].includes(dir)) {
//         b1 = position.y + position.x * BASE_SLOPE;
//         b2 = align.y - align.x * BASE_SLOPE;

//         x = (b1 - b2) / (2 * BASE_SLOPE);
//         y = -BASE_SLOPE * x + b1;
//       }

//       const nextPosition = getSigmaPoint({ x, y });
//       vnode.position = nextPosition;
//       willUpdateNodes.push(vnode);
//     }
//   } else {
//     const rect = get2DIntersectionPolyCoordinates([positions]);
//     const align = rect[index];
//     for (let i = 0; i < substanceCheckedShapes.length; i++) {
//       const vnode = substanceCheckedShapes[i];
//       const { position } = vnode;
//       vnodesPrevSnapshots.push(pureClone(vnode));
//       if (['left', 'right'].includes(dir)) {
//         vnode.position = { x: align.x, y: position.y };
//       }
//       if (['top', 'bottom'].includes(dir)) {
//         vnode.position = { y: align.y, x: position.x };
//       }
//       willUpdateNodes.push(vnode);
//     }
//   }

//   willUpdateNodes.forEach((vnode) => {
//     vnode.component.fire('position-change', {
//       position: vnode.position,
//     });
//   });
//   // 存储这次移动操作
//   cmdStack.saveAction({
//     updateType: 'position-change',
//     data: willUpdateNodes,
//     oldData: vnodesPrevSnapshots,
//   });
//   this.lines.clear();

//   this.update(willUpdateNodes);
//   this._updateVnodeGroups(willUpdateNodes);
// };
/**
 * @description 子节点同步父节点的偏移
 * @param {*} offset 父节点偏移量
 * @param {*} vnode 父节点
 * @returns 发生变化的节点
 */
SigmaCore.prototype.setChildrenPosition = function (offset, vnode) {
  const { relations = {} } = vnode;
  const childrenKeys = Object.keys(relations);
  const changeNodes = [];
  if (childrenKeys.length > 0) {
    const vnodes = this._getShapesByKeys(childrenKeys);
    vnodes?.forEach((node) => {
      this.setChildrenPosition(offset, node);
      const { position } = node;
      const nextPosition = {
        x: position.x + offset.x,
        y: position.y + offset.y,
      };
      node.position = nextPosition;
      node.component.fire('position-change', {
        position: vnode.position,
      });
      changeNodes.push(node);
    });
  }
  return changeNodes;
};
// 对齐操作
SigmaCore.prototype.align = function (dir, keys) {
  const { mode } = store.getState();
  const { shapes } = this.data;
  const data = Object.keys(shapes).map(key => shapes[key]);
  const willUpdateNodes = [];
  const substanceCheckedShapes = this._getCheckedSubstanceShape(keys);
  // 记录当前需要修改未知的节点快照
  const vnodesPrevSnapshots = data.map(vnode => cloneDeep(vnode));
  if (substanceCheckedShapes.length < 2) return;

  // 如果是3d，则先把position转化为2d下的坐标，计算好再转化为3d下的坐标
  if (mode === SIGMA_GRAPH_MODE_3D) {
    substanceCheckedShapes.forEach((shape) => {
      shape.position = transform3DTo2D(shape.position);
    });
  }

  if (dir === 'top') {
    const topPosition = Math.min(...substanceCheckedShapes.map(vnode => vnode.position.y));
    substanceCheckedShapes.forEach((vnode) => {
      const offset = {
        x: 0,
        y: topPosition - vnode.position.y,
      };
      const changeNodes = this.setChildrenPosition(offset, vnode);
      willUpdateNodes.push(...changeNodes);
      vnode.position.y = topPosition;
      if (mode === SIGMA_GRAPH_MODE_3D) {
        vnode.position = transform2DTo3D(vnode.position);
      }
      willUpdateNodes.push(vnode);
    });
  }
  if (dir === 'bottom') {
    const bottomPosition = Math.max(...substanceCheckedShapes.map(vnode => vnode.position.y + vnode.height));
    substanceCheckedShapes.forEach((vnode) => {
      const offset = {
        x: 0,
        y: bottomPosition - vnode.position.y - vnode.height,
      };
      const changeNodes = this.setChildrenPosition(offset, vnode);
      willUpdateNodes.push(...changeNodes);
      vnode.position.y = bottomPosition - vnode.height;
      if (mode === SIGMA_GRAPH_MODE_3D) {
        vnode.position = transform2DTo3D(vnode.position);
      }
      willUpdateNodes.push(vnode);
    });
  }
  if (dir === 'left') {
    const leftPosition = Math.min(...substanceCheckedShapes.map(vnode => vnode.position.x));
    substanceCheckedShapes.forEach((vnode) => {
      const offset = {
        x: leftPosition - vnode.position.x,
        y: 0,
      };
      const changeNodes = this.setChildrenPosition(offset, vnode);
      willUpdateNodes.push(...changeNodes);
      vnode.position.x = leftPosition;
      if (mode === SIGMA_GRAPH_MODE_3D) {
        vnode.position = transform2DTo3D(vnode.position);
      }
      willUpdateNodes.push(vnode);
    });
  }
  if (dir === 'right') {
    const rightPosition = Math.max(...substanceCheckedShapes.map(vnode => vnode.position.x + vnode.width));
    substanceCheckedShapes.forEach((vnode) => {
      const offset = {
        x: rightPosition - vnode.position.x - vnode.width,
        y: 0,
      };
      const changeNodes = this.setChildrenPosition(offset, vnode);
      willUpdateNodes.push(...changeNodes);
      vnode.position.x = rightPosition - vnode.width;
      if (mode === SIGMA_GRAPH_MODE_3D) {
        vnode.position = transform2DTo3D(vnode.position);
      }
      willUpdateNodes.push(vnode);
    });
  }

  willUpdateNodes.forEach((vnode) => {
    vnode.component.fire('position-change', {
      position: vnode.position,
    });
  });
  // 存储这次移动操作
  cmdStack.saveAction({
    updateType: 'position-change',
    data: willUpdateNodes,
    oldData: vnodesPrevSnapshots,
  });
  this.lines.clear();
  this.update(willUpdateNodes);
  this._updateVnodeGroups(willUpdateNodes);
};
// 排列
// SigmaCore.prototype.oldArrange = function (type, step = 2, keys) {
//   const { mode } = store.getState();
//   const willUpdateNodes = [];
//   const vnodesPrevSnapshots = [];
//   const substanceCheckedShapes = this._getCheckedSubstanceShape(keys);
//   if (substanceCheckedShapes.length < 2) return;
//   const positions = substanceCheckedShapes.map(vnode => vnode.position);
//   const { length } = substanceCheckedShapes;
//   const square = Math.ceil(length ** 0.5);

//   if (mode === SIGMA_GRAPH_MODE_3D) {
//     const rect = getIntersectionPolyCoordinates([positions]);
//     const align = rect[0];
//     for (let i = 0; i < length; i++) {
//       const vnode = substanceCheckedShapes[i];
//       const { width, height } = vnode;
//       vnodesPrevSnapshots.push(pureClone(vnode));

//       const stepX = step + (width / BASE_SIZE_2D);
//       const stepY = step + (height / BASE_SIZE_2D);

//       let nextPosition;

//       if (type === 'row') {
//         nextPosition = getSigmaPoint({
//           x: align.x + BASE_GRID_X * i * stepX,
//           y: align.y + BASE_GRID_Y * i * stepY,
//         });
//       }

//       if (type === 'col') {
//         nextPosition = getSigmaPoint({
//           x: align.x - BASE_GRID_X * i * stepX,
//           y: align.y + BASE_GRID_Y * i * stepY,
//         });
//       }

//       if (type === 'square') {
//         const row = ~~(i / square);
//         const col = i % square;
//         nextPosition = getSigmaPoint({
//           x: align.x + BASE_GRID_X * (col - row) * stepX,
//           y: align.y + BASE_GRID_Y * (row + col) * stepY,
//         });
//       }

//       vnode.position = nextPosition;
//       willUpdateNodes.push(vnode);
//     }
//   } else {
//     const rect = get2DIntersectionPolyCoordinates([positions]);
//     const align = rect[0];
//     for (let i = 0; i < length; i++) {
//       const vnode = substanceCheckedShapes[i];
//       const { width, height } = vnode;
//       vnodesPrevSnapshots.push(pureClone(vnode));

//       const stepX = step + (width / BASE_SIZE_2D);
//       const stepY = step + (height / BASE_SIZE_2D);

//       let nextPosition;

//       if (type === 'row') {
//         nextPosition = {
//           x: align.x + BASE_SIZE_2D * i * stepX,
//           y: align.y,
//         };
//       }
//       if (type === 'col') {
//         nextPosition = {
//           x: align.x,
//           y: align.y + BASE_SIZE_2D * i * stepY,
//         };
//       }

//       if (type === 'square') {
//         const row = ~~(i / square);
//         const col = i % square;
//         nextPosition = {
//           x: align.x + BASE_SIZE_2D * col * stepX,
//           y: align.y + BASE_SIZE_2D * row * stepY,
//         };
//       }

//       vnode.position = nextPosition;
//       willUpdateNodes.push(vnode);
//     }
//   }

//   willUpdateNodes.forEach((vnode) => {
//     vnode.component.fire('position-change', {
//       position: vnode.position,
//     });
//   });
//   this.lines.clear();
//   this.update(willUpdateNodes);
//   cmdStack.saveAction({
//     updateType: 'position-change',
//     data: willUpdateNodes,
//     oldData: vnodesPrevSnapshots,
//   });
//   this._updateVnodeGroups(willUpdateNodes);
// };
// 排列 row col square new
SigmaCore.prototype.arrange = function (type, step = 1, keys) {
  const { mode } = store.getState();
  const willUpdateNodes = [];
  const substanceCheckedShapes = this._getCheckedSubstanceShape(keys);
  // 记录当前需要修改未知的节点快照
  const vnodesPrevSnapshots = substanceCheckedShapes.map(vnode => cloneDeep(vnode));
  if (substanceCheckedShapes.length < 2) return;
  const { length } = substanceCheckedShapes;

  // 如果是3d，则先把position转化为2d下的坐标，计算好再转化为3d下的坐标
  if (mode === SIGMA_GRAPH_MODE_3D) {
    substanceCheckedShapes.forEach((shape) => {
      shape.position = transform3DTo2D(shape.position);
    });
  }

  if (type === 'row') {
    // 根据type和节点position排列筛选出来的节点
    const sortedShapes = substanceCheckedShapes.sort((a, b) => a.position.x - b.position.x);
    let leftPosition = sortedShapes[0].position.x;
    const topPosition = sortedShapes[0].position.y;
    sortedShapes.forEach((shape, ind) => {
      if (ind > 0) {
        shape.position.x = leftPosition + sortedShapes[ind - 1].width + BASE_SIZE_2D * step;
        shape.position.y = topPosition;
        leftPosition = shape.position.x;
        if (mode === SIGMA_GRAPH_MODE_3D) {
          shape.position = transform2DTo3D(shape.position);
        }
        willUpdateNodes.push(shape);
      }
    });
  }

  if (type === 'col') {
    // 根据type和节点position排列筛选出来的节点
    const sortedShapes = substanceCheckedShapes.sort((a, b) => a.position.x - b.position.x);
    const leftPosition = sortedShapes[0].position.x;
    let topPosition = sortedShapes[0].position.y;
    sortedShapes.forEach((shape, ind) => {
      if (ind > 0) {
        shape.position.x = leftPosition;
        shape.position.y = topPosition + sortedShapes[ind - 1].height + BASE_SIZE_2D * step;
        topPosition = shape.position.y;
        if (mode === SIGMA_GRAPH_MODE_3D) {
          shape.position = transform2DTo3D(shape.position);
        }
        willUpdateNodes.push(shape);
      }
    });
  }

  if (type === 'square') {
    // 有几行
    const square = Math.ceil(length ** 0.5);
    // 根据type和节点position排列筛选出来的节点
    const sortedShapes = substanceCheckedShapes.sort((a, b) => (a.position.x + a.position.y) - (b.position.x + b.position.y));
    const leftPosition = sortedShapes[0].position.x;
    const topPosition = sortedShapes[0].position.y;
    sortedShapes.forEach((shape, ind) => {
      // 当前在几行几列
      const row = ~~(ind / square);
      const col = ind % square;
      shape.position.x = leftPosition + (BASE_SIZE_2D * step + BASE_SIZE_2D) * (col);
      shape.position.y = topPosition + (BASE_SIZE_2D * step + BASE_SIZE_2D) * (row);
      if (mode === SIGMA_GRAPH_MODE_3D) {
        shape.position = transform2DTo3D(shape.position);
      }
      willUpdateNodes.push(shape);
    });
  }

  willUpdateNodes.forEach((vnode) => {
    vnode.component.fire('position-change', {
      position: vnode.position,
    });
  });
  this.lines.clear();
  this.update(willUpdateNodes);
  cmdStack.saveAction({
    updateType: 'position-change',
    data: willUpdateNodes,
    oldData: vnodesPrevSnapshots,
  });
  this._updateVnodeGroups(willUpdateNodes);
};
// 分散
// SigmaCore.prototype.dispersion = function (type = 'row') {
//   const { mode } = store.getState();
//   const willUpdateNodes = [];
//   const positionMap = {
//     row: 'x',
//     col: 'y',
//   };
//   // 获取能被排列的元素列表
//   const substanceCheckedShapes = this._getCheckedSubstanceShape();
//   const { length } = substanceCheckedShapes;
//   if (length < 2) return;

//   // 记录当前需要修改未知的节点快照
//   const vnodesPrevSnapshots = substanceCheckedShapes.map(vnode => cloneDeep(vnode));

//   // 如果是3d，则先把position转化为2d下的坐标，计算好再转化为3d下的坐标
//   if (mode === SIGMA_GRAPH_MODE_3D) {
//     substanceCheckedShapes.forEach((shape) => {
//       shape.position = transform3DTo2D(shape.position);
//     });
//   }
//   const param = positionMap[type];
//   // 根据type和节点position排列筛选出来的节点
//   const sortedShapes = substanceCheckedShapes.sort((a, b) => a.position[param] - b.position[param]);

//   // 计算最远间隔
//   const distance = sortedShapes[length - 1].position[param] - sortedShapes[0].position[param];

//   // 计算相邻节点距离
//   const step = distance / (length - 1);

//   // 获取基础位置（第一个节点的位置）
//   const basicPosition = sortedShapes[0].position[param];
//   // 根据step间隔，更新节点position
//   sortedShapes.forEach((vnode, i) => {
//     vnode.position[param] = basicPosition + step * i;
//     if (mode === SIGMA_GRAPH_MODE_3D) {
//       vnode.position = transform2DTo3D(vnode.position);
//     }
//     willUpdateNodes.push(vnode);
//   });

//   willUpdateNodes.forEach((vnode) => {
//     vnode.component.fire('position-change', {
//       position: vnode.position,
//     });
//   });
//   this.lines.clear();
//   this.update(willUpdateNodes);
//   cmdStack.saveAction({
//     updateType: 'position-change',
//     data: willUpdateNodes,
//     oldData: vnodesPrevSnapshots,
//   });
//   this._updateVnodeGroups(willUpdateNodes);
// };

// 类似ppt的横向分散和纵向分散，type为row/col
SigmaCore.prototype.dispersion = function (type = 'row') {
  const { mode } = store.getState();
  const willUpdateNodes = [];
  const positionMap = {
    row: 'x',
    col: 'y',
  };
  const dirMap = {
    row: 'width',
    col: 'height',
  };
  // 获取能被排列的元素列表
  const substanceCheckedShapes = this._getCheckedSubstanceShape();
  const { length } = substanceCheckedShapes;
  if (length < 3) return;
  const { shapes } = this.data;
  const data = Object.keys(shapes).map(key => shapes[key]);
  // 记录当前需要修改未知的节点快照
  const vnodesPrevSnapshots = data.map(vnode => cloneDeep(vnode));

  // 如果是3d，则先把position转化为2d下的坐标，计算好再转化为3d下的坐标
  if (mode === SIGMA_GRAPH_MODE_3D) {
    substanceCheckedShapes.forEach((shape) => {
      shape.position = transform3DTo2D(shape.position);
    });
  }
  // x/y
  const param = positionMap[type];
  // width/height
  const attr = dirMap[type];
  // 根据type和节点position排列筛选出来的节点
  const sortedShapes = substanceCheckedShapes.sort((a, b) => a.position[param] - b.position[param]);

  // 计算最远间隔
  let distance = 0;
  // 最右边节点的左边 - 最左边节点的右边 - 所有中间节点的宽度
  const midWidth = sortedShapes.slice(1, length - 1).reduce((acc, cur) => acc + cur.width, 0);
  distance = sortedShapes[length - 1].position[param] - sortedShapes[0].position[param] - sortedShapes[0][attr] - midWidth;
  // 计算相邻节点距离
  const step = distance / (length - 1);
  // 获取基础位置（第一个节点的位置）
  let basicPosition = sortedShapes[0].position[param];
  // 根据step间隔，更新节点position
  sortedShapes.forEach((vnode, i) => {
    if (i !== 0 && i !== length - 1) {
      const prePosition = vnode.position[param];
      vnode.position[param] = basicPosition + sortedShapes[i - 1][dirMap[type]] + step;
      const changeDistance = vnode.position[param] - prePosition;
      const offset = {
        x: 0,
        y: 0,
      };
      offset[param] = changeDistance;
      this.setChildrenPosition(offset, vnode);
      basicPosition = vnode.position[param];
      if (mode === SIGMA_GRAPH_MODE_3D) {
        vnode.position = transform2DTo3D(vnode.position);
      }
      willUpdateNodes.push(vnode);
    }
  });

  willUpdateNodes.forEach((vnode) => {
    vnode.component.fire('position-change', {
      position: vnode.position,
    });
  });
  this.lines.clear();
  this.update(willUpdateNodes);
  cmdStack.saveAction({
    updateType: 'position-change',
    data: willUpdateNodes,
    oldData: vnodesPrevSnapshots,
  });
  this._updateVnodeGroups(willUpdateNodes);
};

// 获取数据
SigmaCore.prototype._getTypesShapes = function (type) {
  const { shapes } = this.data;
  const _types = _.isArray(type) ? type : [type];
  const vnodes = [];
  for (const key in shapes) {
    const vnode = shapes[key];
    if (_types.includes(vnode.type)) {
      vnodes.push(vnode);
    }
  }
  return vnodes;
};
SigmaCore.prototype._getCheckedShapes = function (options = {}) {
  const { shapes } = this.data;
  const { excludeKeys = [], excludeType = '', checkedable } = options;
  const checkedShapes = [];
  for (const key in shapes) {
    const shape = shapes[key];
    if (shape) {
      const flag1 = shape.isChecked
        && (shape.checkedable || shape.checkedable === undefined || checkedable);
      let flag2 = true;
      let flag3 = true;
      if (excludeKeys.length) {
        flag2 = !excludeKeys.includes(shape.key);
      }
      if (excludeType) {
        flag3 = shape.type !== excludeType;
      }

      if (flag1 && flag2 && flag3) {
        checkedShapes.push(shapes[key]);
      }
    }
  }
  return checkedShapes;
};
SigmaCore.prototype._getShapesByKeys = function (keys) {
  const { shapes } = this.data;
  const shapeVNodes = [];
  for (let i = 0; i < keys.length; i++) {
    const vnode = shapes[keys[i]];
    if (vnode) {
      shapeVNodes.push(vnode);
    }
  }
  return shapeVNodes;
};
// SigmaCore.prototype._getPureVnode = function (vnodes, attrs) {
//   if (!_.isArray(vnodes)) {
//     const vnode = pureClone(vnodes);
//     for (let j = 0; j < attrs.length; j++) {
//       const attrName = attrs[j];
//       delete vnode[attrName];
//     }
//     return vnode;
//   }
//   const _vnodes = [];
//   for (let i = 0; i < vnodes.length; i++) {
//     const vnode = pureClone(vnodes[i]);
//     for (let j = 0; j < attrs.length; j++) {
//       const attrName = attrs[j];
//       delete vnode[attrName];
//     }
//     _vnodes.push(vnode);
//   }
//   return _vnodes;
// };
SigmaCore.prototype._getMovingLineShape = function () {
  const { shapes } = this.data;
  for (const key in shapes) {
    if (isLineMoving(shapes[key])) {
      return shapes[key];
    }
  }
  return null;
};
SigmaCore.prototype._getDraggingLineShape = function () {
  const { shapes } = this.data;
  for (const key in shapes) {
    const vnode = shapes[key];
    const { type, data } = vnode;
    if (
      type === SIGMA_LINE_SHAPE
      && [data.start.type, data.end.type].includes('dragging')
    ) {
      return vnode;
    }
  }
  return null;
};

SigmaCore.prototype._getMouseLineShape = function () {
  const { shapes } = this.data;
  for (const key in shapes) {
    const vnode = shapes[key];
    const { type, data } = vnode;
    if (
      type === SIGMA_LINE_SHAPE
      && data.end.type === 'mouse'
    ) {
      return vnode;
    }
  }
  return null;
};

SigmaCore.prototype._getNetworkShapes = function () {
  return this._getTypesShapes(NETWORK_SHAPE_TYPES);
};
SigmaCore.prototype._deDuplicateShapes = function (vnodes) {
  if (!(Array.isArray(vnodes) && vnodes.length)) return;
  const output = {};
  for (const vnode of vnodes) {
    output[vnode.key] = vnode;
  }
  return Object.values(output);
};
// 如果是组，包含组内的所有元素，主要是text
SigmaCore.prototype._getRelationShapes = function (v) {
  const vnodes = Array.isArray(v) ? v : [v];
  const output = {};

  const { shapes } = this.data;
  for (const vnode of vnodes) {
    if (GROUP_SHAPE_TYPES.includes(vnode.type)) {
      for (const key in vnode.relations) {
        const el = shapes[key];
        if (el) {
          output[key] = el;
          const others = this._getRelationShapes(el);
          others.forEach((item) => {
            output[item.key] = item;
          });
        }
      }
    }

    if (vnode.sticky) {
      const text = shapes[vnode.sticky];
      text && (output[text.key] = text);
    }
  }
  const checkedKeys = Object.keys(output);
  const checkedPolyLineKeys = Object.keys(shapes).filter(key => shapes[key]?.styles?.lineType?.value === 'polyline' && checkedKeys?.includes(shapes[key]?.data?.start?.vkey) && checkedKeys?.includes(shapes[key]?.data?.end?.vkey));
  if (checkedPolyLineKeys?.length) {
    checkedPolyLineKeys.forEach((key) => {
      output[key] = shapes[key];
    });
  }
  return Object.values(output);
};

SigmaCore.prototype._getRelationShapesWithoutTke = function (v) {
  const vnodes = Array.isArray(v) ? v : [v];
  const output = {};

  const { shapes } = this.data;
  for (const vnode of vnodes) {
    if (GROUP_SHAPE_TYPES.includes(vnode.type)) {
      for (const key in vnode.relations) {
        const el = shapes[key];
        if (el) {
          output[key] = el;
          const others = this._getRelationShapesWithoutTke(el);
          others.forEach((item) => {
            output[item.key] = item;
          });
        }
      }
    }

    if (vnode.sticky) {
      const text = shapes[vnode.sticky];
      text && (output[text.key] = text);
    }
  }
  const checkedKeys = Object.keys(output);
  const checkedPolyLineKeys = Object.keys(shapes).filter(key => shapes[key]?.styles?.lineType?.value === 'polyline' && checkedKeys?.includes(shapes[key]?.data?.start?.vkey) && checkedKeys?.includes(shapes[key]?.data?.end?.vkey));
  if (checkedPolyLineKeys?.length) {
    checkedPolyLineKeys.forEach((key) => {
      output[key] = shapes[key];
    });
  }
  return Object.values(output);
};
SigmaCore.prototype._getCheckedSubstanceShape = function (keys) {
  let checkeds;
  const { shapes } = this.data;
  if (!keys || !Array.isArray(keys)) {
    checkeds = this._getCheckedShapes();
  } else {
    checkeds = keys.reduce((acc, key) => {
      if (key && shapes[key]) acc.push(shapes[key]);
      return acc;
    }, []);
  }

  const groupKeys = checkeds.map(check => Object.keys(check?.groups ?? [])[0]);
  const isCommonParent = groupKeys.every((groupKey, index, array) => groupKey && (index === 0 || groupKey === array[0]));

  if (isCommonParent) return checkeds;
  const substanceCheckedShapes = checkeds.filter(vnode => !NOT_SUBSTANCE_SHAPE_TYPES.includes(vnode.type)
      && !vnode.attach && Object.keys(vnode.groups).length === 0);
  return substanceCheckedShapes;
};

// 键盘操作
SigmaCore.prototype._keyPressUp = function () {
  const { mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const params = is3D ? [BASE_STEP_X, -BASE_STEP_Y] : [0, -BASE_HALF_GRID_2D];
  this.move(...params);
};
SigmaCore.prototype._keyPressDown = function () {
  const { mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const params = is3D ? [-BASE_STEP_X, BASE_STEP_Y] : [0, BASE_HALF_GRID_2D];
  this.move(...params);
};
SigmaCore.prototype._keyPressLeft = function () {
  const { mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const params = is3D ? [-BASE_STEP_X, -BASE_STEP_Y] : [-BASE_HALF_GRID_2D, 0];
  this.move(...params);
};
SigmaCore.prototype._keyPressRight = function () {
  const { mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const params = is3D ? [BASE_STEP_X, BASE_STEP_Y] : [BASE_HALF_GRID_2D, 0];
  this.move(...params);
};
SigmaCore.prototype._keyPressCtrlAndC = function () {
  this.copy();
};
SigmaCore.prototype._keyPressCtrlAndV = function () {
  this.paste();
};
SigmaCore.prototype._keyPressCtrlAndD = function () {
  this.clone();
};
SigmaCore.prototype._keyPressCtrlAndZ = function () {
  this.undo();
};
SigmaCore.prototype._keyPressCtrlAndY = function () {
  this.redo();
};
SigmaCore.prototype._keyPressCtrlAndX = function () {
  this.cut();
};
SigmaCore.prototype._keyPressCtrlAndA = function () {
  this.allShapeChecked();
};
SigmaCore.prototype._keyPressDelete = function () {
  this.delete();
};
SigmaCore.prototype._keyPressCtrlAndS = function () {
  const { callbacks = {} } = this.options;
  callbacks.customizeSave && callbacks.customizeSave();
};
SigmaCore.prototype._keyPressCtrlAndL = function () {
  this.toggleLock();
};
SigmaCore.prototype._keyPressCtrlAndG = function () {
  // this.setGroup(null);
};
SigmaCore.prototype._keyPressCtrlAndLeft = function () {
  this.align('left');
};
SigmaCore.prototype._keyPressCtrlAndRight = function () {
  this.align('right');
};
SigmaCore.prototype._keyPressCtrlAndUp = function () {
  this.align('top');
};
SigmaCore.prototype._keyPressCtrlAndDown = function () {
  this.align('bottom');
};

// 前进、回退，如果是 2.5D 则将当前数据进行转换后渲染。
SigmaCore.prototype.redo = function () {
  cmdStack.redo(this);
};
SigmaCore.prototype.undo = function () {
  cmdStack.undo(this);
};
/**
 * 把撤销回退中存储的节点数据联系到当前数据中，当前数据中才有component等属性，!!其它地方慎用
 * @param {*} nodes 撤销回退中存储的节点的数据数组，数据中必须包含key
 */
SigmaCore.prototype._linkUndoNodesInData = function (nodes) {
  const { mode } = store.getState();
  const shapesNeedUpdate = [];
  const fixShapes = mode === SIGMA_GRAPH_MODE_3D ? nodes : this.transform(nodes);
  fixShapes.forEach((vnode) => {
    const existShape = this.data.shapes[vnode.key];
    if (existShape) {
      // 其它地方可能会引入不需要的属性，这里再删一下，避免下一步把component等属性覆盖没了
      EXCLUDE_PROPERTYS.forEach((item) => {
        delete vnode[item];
      });
      Object.assign(existShape, vnode);
      shapesNeedUpdate.push(existShape);
    }
  });
  return shapesNeedUpdate;
};

/**
 * 根据事件类型，触发节点更新操作
 * @param {*} nodes 撤销回退中存储的节点的数据数组，数据中必须包含key
 */
SigmaCore.prototype._updateNodes = function (nodes, eventType) {
  if (!nodes || nodes.length === 0) return;
  const { mode } = store.getState();
  const eventNames = Array.isArray(eventType) ? eventType : [eventType];
  const fixShapes = mode === SIGMA_GRAPH_MODE_3D ? nodes : this.transform(nodes);
  const existShapes = [];
  fixShapes.forEach((vnode) => {
    const existShape = this.data.shapes[vnode.key];
    if (existShape) {
      // 其它地方可能会引入不需要的属性，这里再删一下，避免下一步把component等属性覆盖没了
      EXCLUDE_PROPERTYS.forEach((item) => {
        delete vnode[item];
      });
      Object.assign(existShape, vnode);
      eventNames.forEach(eventName => existShape.component.fire(eventName, { pure: true }));
      existShapes.push(existShape);
    }
  });
  if (existShapes.length === 1) {
    this._setShapeChecked(existShapes, true);
  }
};

// 本地存储,本地存储的数据是原数据，仅存储 2.5D 模式数据，那么需要转换后存储，
SigmaCore.prototype._autoLocalSave = function () {
  const { mode } = store.getState();
  if (_autoLocalSaveTimer) {
    clearTimeout(_autoLocalSaveTimer);
  }
  const that = this;
  _autoLocalSaveTimer = setTimeout(() => {
    let localData = {};
    const { shapes } = that.data;
    for (const key in shapes) {
      const vnode = shapes[key];
      if (
        // vnode.type === SIGMA_LINE_SHAPE
        // && (vnode.data.end.type === 'mouse'
        //   || vnode.data.start.type === 'dragging'
        //   || vnode.data.end.type === 'dragging')
        isLineMoving(vnode)
      ) {
        continue;
      }
      // const node = that._getPureVnode(vnode, EXCLUDE_PROPERTYS);
      const node = pureClone(vnode);
      localData[key] = node;
    }
    if (mode === SIGMA_GRAPH_MODE_2D) {
      localData = that.reduction(localData);
    }

    try {
    } catch (e) {
      console.warn('Local Storage is full, Please empty data');
    }
  }, 1000);
};

// 回调暴露方法
SigmaCore.prototype.__onCheckedChange = function () {
  const that = this;
  setTimeout(() => {
    const checkedShapes = that._getCheckedShapes();
    const { callbacks = {} } = that.options;
    callbacks.onCheckedChange && callbacks.onCheckedChange(pureClone(checkedShapes));
  }, 0);
};
SigmaCore.prototype.__onGraphChange = function () {
  const { callbacks = {} } = this.options;
  const { shapes } = this.data;
  callbacks.onGraphChange && callbacks.onGraphChange(shapes);
};

SigmaCore.prototype.__onmousedown = function (e, vnode) {
  if (e.button === 0) {
    this.__onmouseleftdown(e, vnode);
    return;
  }
  if (e.button === 2) {
    this.__onmouserightdown(e, vnode);
  }
};
SigmaCore.prototype.__onmouseleftdown = function (e, vnode) {
  const { mode, uneditable, dropzone } = store.getState();
  const lineMouseShape = this._getMovingLineShape();

  if (lineMouseShape && vnode.type === SIGMA_LINE_SHAPE) return;

  e.stopPropagation();

  const that = this;
  let clearConnectLineToolBoxFlag = true;
  const { component, key, connectable, lock } = vnode;
  const checkedSign = vnode.isChecked;

  if (!checkedSign) {
    if (e.ctrlKey || e.metaKey) {
      that._toggleShapeChecked(vnode);
    } else {
      that.onlyShapeChecked(vnode);
    }
  }

  // 触发点击回调
  that.__onclick(e, vnode);
  if (
    lineMouseShape
    && vnode.key !== lineMouseShape.key
    // && lineMouseShape.data.start.vkey !== key
  ) {
    let k;
    if (connectable) k = key;
    if (!connectable && vnode.attach) k = vnode.attach;
    k && lineMouseShape.component?.fire('line-complete', {
      key: k,
    });
    return;
  }

  const doc = component.root();
  const checkedNodes = this._getCheckedShapes();
  const isOnlyLineLabel = checkedNodes.length === 1 && isLineLabel(checkedNodes[0], this);
  const willUpdateNodes = isOnlyLineLabel ? checkedNodes : this._getAllUpdateNodes(checkedNodes);
  // const willUpdateNodes = this._getAllUpdateNodes(checkedNodes);
  const cloneWillUpdateNodes = cloneDeep(willUpdateNodes);
  const { callbacks = {} } = this.options;
  // 对参考线进行去重排序
  this.lines.sort(willUpdateNodes);
  const willUpdateLines = this._getRelationLines(willUpdateNodes);
  const cloneWillUpdateLines = pureClone(willUpdateLines);
  const is3D = mode === SIGMA_GRAPH_MODE_3D;

  const getPoint = is3D ? getSigmaPoint : get2DSigmaPoint;
  const pStart = doc.point(e.clientX, e.clientY);
  let _start = { x: 0, y: 0 };
  let isUpdateSign = false;
  let _updateMoveTimer = false;
  let isMoving = false;
  let rafId = null;

  const dropzoneWidth = dropzone.node.width.baseVal.value;
  const dropzoneHeight = dropzone.node.height.baseVal.value;
  const dropzoneX = dropzone.node.x.baseVal.value;
  const dropzoneY = dropzone.node.y.baseVal.value;
  const mouseMove = (ev) => {
    if (_updateMoveTimer) return;
    if (clearConnectLineToolBoxFlag) {
      clearConnectLineToolBox();
    }

    if (uneditable) return;
    _updateMoveTimer = true;
    // _updateMoveTimer = setTimeout(() => {
    const { doc } = store.getState();
    const pEnd = doc.point(ev.clientX, ev.clientY);

    // const diff = getPoint({ x: pEnd.x - pStart.x, y: pEnd.y - pStart.y });
    const diff = this._getMouseMoveDiff(pStart, pEnd);
    if (diff.x === 0 && diff.y === 0) {
      _updateMoveTimer = false;
      return;
    };
    // 更新可能存在的线的 points
    willUpdateLines.forEach((line) => {
      line.component.fire('position-change', {
        diff: {
          x: diff.x - _start.x,
          y: diff.y - _start.y,
        },
      });
    });

    _start = diff;
    const movingLineKeys = willUpdateLines?.map(line => line?.key) ?? [];
    for (let i = 0; i < willUpdateNodes.length; i++) {
      // 触发开始运动的事件
      if (!isUpdateSign) {
        willUpdateNodes[i].component?.fire('start-move');
      }

      const { position } = cloneWillUpdateNodes[i];
      const dp = {
        x: position.x + diff.x,
        y: position.y + diff.y,
      };

      const dropzoneInfo = {
        x: dropzoneX,
        y: dropzoneY,
        w: dropzoneWidth,
        h: dropzoneHeight,
      };

      const vNodePosition = getPoint(dp);

      const { width, height } = willUpdateNodes[i];

      // 拖拽过程中要判断图元是否超出了拖拽画布区域
      const flag = isInner(
        dropzoneInfo,
        {
          x: vNodePosition.x,
          y: vNodePosition.y,
          w: width,
          h: height,
        },
      );
      if (!flag) {
        break;
      }

      if (vnode?.isTkeSubVode) {
        // 如果移动的是tke组下的的元素，则需要判断是否移除了tke 的边界，因为tke 是不允许移到外面的
        let positionInfo = is3D ? transform3DTo2D(getPoint(dp)) : getPoint(dp);
        if (is3D) {
          // 3d坐标转换成2d坐标时会有这边一个偏移量差距
          positionInfo.x = positionInfo.x + 112.5;
          positionInfo.y = positionInfo.y + 22.5;
          positionInfo = get2DSigmaPoint(positionInfo);
        }
        const groups = vnode?.groups;
        if (groups) {
          const groupKeys = Object.keys(groups);
          if (groupKeys?.length) {
            const groupInfo = this.data.shapes[groupKeys[0]];
            const { width: width1, height: height1, position: position1 } = groupInfo;
            const { width: width2, height: height2 } = willUpdateNodes[i];
            const groupPoint = is3D ? transform3DTo2D(position1) : position1;
            const flag = isInner({ ...groupPoint, w: width1, h: height1 }, { ...positionInfo, w: width2, h: height2 });
            if (!flag) {
              break;
            }
          }
        }
      }

      willUpdateNodes[i].position = vNodePosition;

      // 触发位置变动事件
      willUpdateNodes[i].component?.fire('position-change', {
        position: willUpdateNodes[i].position,
        type: 'mousemove',
        mousePosition: pEnd,
        movingLineKeys,
      });
    }

    isUpdateSign = true;
    that.update(willUpdateNodes);
    _updateMoveTimer = false;

    this.__handleRelationsWhenMove(checkedNodes);

    callbacks.onShapeMove && callbacks.onShapeMove(willUpdateNodes);
    clearConnectLineToolBoxFlag = false;
  };
  const mouseUp = () => {
    if (e.ctrlKey || e.metaKey) {
      if (checkedSign) {
        that._setShapeChecked(vnode, false);
      } else {
        that._setShapeChecked(vnode, true);
      }
    } else if (e.shiftKey) {
      that.allShapeTypeChecked(vnode);
    }

    if (connectable) {
      const newCheckedShapes = this._getCheckedShapes();
      if (newCheckedShapes.length === 1) {
        createConnectLineToolBox(this, newCheckedShapes[0]);
      } else {
        clearConnectLineToolBox();
      }
    } else {
      clearConnectLineToolBox();
    }

    doc.off('mousemove.component', optMove);
    doc.off('mouseup.component', mouseUp);
    off(window, `mouseup${GLOBAL_EVENT_SUFFIX}`, mouseUp);
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }

    if (isUpdateSign) {
      that.update(willUpdateNodes);
      // 存储这次移动操作
      cmdStack.saveAction({
        updateType: 'position-change',
        data: willUpdateNodes.concat(willUpdateLines),
        oldData: cloneWillUpdateNodes.concat(cloneWillUpdateLines),
      });

      willUpdateNodes.forEach(({ component }) => {
        component?.fire('end-move');
      });

      this.__handleRelationsWhenMove(checkedNodes, true);

      callbacks.onShapeMoveEnd && callbacks.onShapeMoveEnd(willUpdateNodes);
    }


    // 清除延长线
    this.lines.clear();
  };
  const optMove = (ev) => {
    if (isMoving) return;

    isMoving = true;

    rafId = requestAnimationFrame(() => {
      mouseMove(ev);
      isMoving = false;
    });
  };

  if (!lock) {
    callbacks.onShapeMoveStart && callbacks.onShapeMoveStart(willUpdateNodes);
    doc.on('mousemove.component', optMove);
  }
  doc.on('mouseup.component', mouseUp);
  on(window, `mouseup${GLOBAL_EVENT_SUFFIX}`, mouseUp);
};

SigmaCore.prototype._getAllUpdateNodes = function (checkedNodes) {
  return this._deDuplicateShapes(checkedNodes.concat(this._getRelationShapes(checkedNodes))).filter(({ type, attach }) => (!attach && type !== SIGMA_LINE_SHAPE));
};
// 解决diff计算时的精度问题
SigmaCore.prototype._getMouseMoveDiff = function (pStart, pEnd, is3D) {
  const getPoint = is3D ? getSigmaPoint : get2DSigmaPoint;
  const minX = is3D ? BASE_HALF_GRID_X : BASE_HALF_GRID_2D;
  const minY = is3D ? BASE_HALF_GRID_Y : BASE_HALF_GRID_2D;
  const diff = { x: pEnd.x - pStart.x, y: pEnd.y - pStart.y };
  if (Math.abs(diff.x) < minX && Math.abs(diff.y) < minY) {
    const offsetX = (diff.x >= 0 ? minX : - minX) * 4;
    const offsetY = (diff.y >= 0 ? minY : - minY) * 4;
    const d = getPoint({
      x: offsetX + diff.x,
      y: offsetY + diff.y,
    });
    return {
      x: d.x - offsetX,
      y: d.y - offsetY,
    };
  }

  return getPoint(diff);
};

// 元素拖拽时判断是否需要加入或者离开容器，isEnd为拖拽是否结束
SigmaCore.prototype.__handleRelationsWhenMove = function (_vnode, isEnd = false) {
  const v = isArray(_vnode) ? _vnode : [_vnode];
  const vnodes = foldVnodes(v);
  const { shapes } = this.data;
  const target = getNearestGroup(this, vnodes);
  if (target?.type === SIGMA_TKE_SHAPE) {
    // tke组特殊不能拖入成组
    return;
  }
  const willUpdate = {};
  const groupsPrevSnapshots = {};
  const vnodesPrevSnapshots = {};
  for (const vnode of vnodes) {
    // if (target?.key === vnode.key) continue;
    const { prevSelectdGroup } = store.getState();

    if (isEnd) {
      const map = {};

      if (target) {
        map[target.key] = {
          group: target,
          status: 2,  // 0 为删除  1为原本就有  2为新增
        };
      }

      // 遍历当前 vnode 的 groups
      for (const key in vnode.groups) {
        if (!map[key]) {
          const group = shapes[key];
          if (!group) continue;
          map[key] = {
            group,
            status: 0, // 代表移出了组
          };
        } else {
          map[key].status = 1; // 原本就有了
        }
      }
      // 遍历 map，处理新增和删除
      for (const key in map) {
        const { group, status } = map[key];
        // 避免多次更新同一个元素时，丢失前面的变化
        if (!groupsPrevSnapshots[group.key]) {
          groupsPrevSnapshots[group.key] = pureClone(group);
        }

        if ([2, 0].includes(status) && !vnodesPrevSnapshots[group.key]) {
          vnodesPrevSnapshots[vnode.key] = pureClone(vnode);
        }

        if (status === 2) {
          if (group.key !== vnode.key) {
            // 通知新增
            group.relations[vnode.key] = vnode.type;
            vnode.groups[group.key] = group.type;

            group.component.fire('rank-change');
          }
        } else if (status === 0) {
        // 通知删除
          delete group.relations[vnode.key];
          delete vnode.groups[group.key];
        }

        willUpdate[group.key] = group;
        group.component.fire('pre-select', {
          key: null,
        });
      }

      // prevSelectdGroup 在结束时设为null
      prevSelectdGroup?.component.fire('pre-select', {
        key: null,
      });
      store.dispatch({
        type: SET_PREV_SELECTED_GROUP,
        value: null,
      });
    } else if (prevSelectdGroup?.key !== target?.key) {
      // 更新 prevSelectdGroup
      prevSelectdGroup && prevSelectdGroup.component.fire('pre-select', {
        key: target?.key,
      });
      target && target.component.fire('pre-select', {
        key: target.key,
      });

      store.dispatch({
        type: SET_PREV_SELECTED_GROUP,
        value: target,
      });
    }
  }


  if (isEnd) {
    const vnodesKeysHasUpdated = Object.keys(vnodesPrevSnapshots);
    if (vnodesKeysHasUpdated.length > 0) {
      // 存储上面修改过group属性的vnode
      cmdStack.saveAction({
        data: vnodesKeysHasUpdated.map(key => shapes[key]),
        oldData: vnodesKeysHasUpdated.map(key => vnodesPrevSnapshots[key]),
        isSub: true,
      });
    }
    Object.values(willUpdate).forEach((group) => {
      group.component.fire('relations-change', { vnodesPrevSnapshots: [groupsPrevSnapshots[group.key]] });
    });
  }
};
SigmaCore.prototype._getRelationLines = function (vnodes) {
  const allLines = this._getTypesShapes(SIGMA_LINE_SHAPE);
  const output = [];
  for (const line of allLines) {
    const is = isLineMoving(line);
    if (is) continue;
    const { start, end } = line.data;
    let hasStart = false;
    let hasEnd = false;
    for (const { key } of vnodes) {
      if (!hasStart) {
        hasStart = key === start.vkey;
      }
      if (!hasEnd) {
        hasEnd = key === end.vkey;
      }
      if (hasStart && hasEnd) {
        output.push(line);
        break;
      }
    }
  }
  return output;
};

SigmaCore.prototype.__onmouserightdown = function (e, vnode) {
  e.stopPropagation();
  const lineMouseShape = this._getMovingLineShape();
  if (lineMouseShape) return;
  const {
    options: { callbacks = {} },
  } = this;
  if (!vnode.isChecked) {
    this.onlyShapeChecked(vnode);
  }
  clearConnectLineToolBox();
  callbacks.onShapeMouseRightDown && callbacks.onShapeMouseRightDown(e, vnode);
};
SigmaCore.prototype.__onmouseup = function (e, vnode) {
  const { callbacks = {} } = this.options;
  callbacks.onShapeMouseUp && callbacks.onShapeMouseUp(e, vnode);
};
SigmaCore.prototype.__onmouseover = function (e, vnode) {
  const shapeBody = vnode.component?.findOne('.shape-body');
  shapeBody?.attr({
    opacity: 0.6,
  });

  // const { uneditable } = store.getState();
  // if (uneditable) return;
  const { callbacks = {} } = this.options;
  callbacks.onShapeMouseOver && callbacks.onShapeMouseOver(e, vnode);
  const { mode } = store.getState();
  setMovablePoints(this, vnode, true, mode === SIGMA_GRAPH_MODE_3D);
};
SigmaCore.prototype.__onmouseout = function (e, vnode) {
  const shapeBody = vnode.component?.findOne('.shape-body');
  shapeBody?.attr({
    opacity: 1,
  });

  // const { uneditable } = store.getState();
  // if (uneditable) return;
  const { callbacks = {} } = this.options;
  callbacks.onShapeMouseOut && callbacks.onShapeMouseOut(e, vnode);
};
SigmaCore.prototype.__onclick = function (e, vnode) {
  const { callbacks = {} } = this.options;
  callbacks.onShapeClick && callbacks.onShapeClick(e, vnode);
};
SigmaCore.prototype.__ondoubleclick = function (e, vnode) {
  const { callbacks = {} } = this.options;
  callbacks.onShapeDBLClick && callbacks.onShapeDBLClick(e, vnode);
};
SigmaCore.prototype.__onrightclick = function (e, vnode) {
  const { callbacks = {} } = this.options;
  callbacks.onShapeMouseRightClick
    && callbacks.onShapeMouseRightClick(e, vnode);
};

// 其它辅助函数
SigmaCore.prototype._vnodeVerify = function (vnodes) {
  const lineShapes = this._getTypesShapes(SIGMA_LINE_SHAPE);
  let result = vnodes;
  for (let i = 0; i < vnodes.length; i++) {
    const vnode = vnodes[i];
    if (vnode.type === SIGMA_LINE_SHAPE) {
      const { start: s1, end: e1 } = vnode.data;
      for (let j = 0; j < lineShapes.length; j++) {
        const { start, end } = lineShapes[j].data;
        if (s1 && e1 && start.vkey === s1.vkey && end.vkey === e1.vkey) {
          result = vnodes.filter(n => n.key !== vnode.key);
        }
      }
    }
  }
  return result;
};
SigmaCore.prototype.__whenShapeUpdate = function (vnodes) {
  const { shapes } = this.data;
  const relateShapes = [];
  for (const skey in shapes) {
    // 找到相关联的连线 LineShape 同步更新
    const s = shapes[skey];
    if (s.type === SIGMA_LINE_SHAPE) {
      if (s.data.start.type === 'vnode') {
        if (vnodes.filter(n => n.key === s.data.start.vkey).length > 0) {
          relateShapes.push(s);
        }
      }
      if (s.data.end.type === 'vnode') {
        if (vnodes.filter(n => n.key === s.data.end.vkey).length > 0) {
          relateShapes.push(s);
        }
      }
    }
    // 找到关联的 NetworkShape 同步更新
    if (GROUP_SHAPE_TYPES.includes(s.type)) {
      if (vnodes.filter(n => s.relations?.[n.key]).length > 0) {
        relateShapes.push(s);
      }
    }
  }
  if (relateShapes.length) {
    this.update(relateShapes);
  }
};
SigmaCore.prototype.__whenShapeDelete = function (vnodes, opts = {}) {
  const { _historical = true } = opts;
  let deleteShapes = [];
  const updateShapes = [];
  const updateShapesPrevSnapshots = [];
  const lineShapes = [];
  const { shapes } = this.data;

  for (const skey in shapes) {
    const s = shapes[skey];
    const p = !vnodes.filter(k => k.key === skey).length;
    if (s.type === SIGMA_LINE_SHAPE && p) {
      lineShapes.push(s);
    }
  }

  for (let i = 0; i < vnodes.length; i++) {
    const vnode = vnodes[i];

    if (vnode.type === SIGMA_LINE_SHAPE) {
      const startKey = vnode.data.start.vkey;
      const endKey = vnode.data.end.vkey;
      if (!endKey) {
        window.clearMouseLineEvents();
      }
      const startKeyRelateVnodes = lineShapes.filter(l => l.data.start.vkey === startKey || l.data.end.vkey === startKey);
      const endKeyRelateVnodes = lineShapes.filter(l => l.data.start.vkey === endKey || l.data.end.vkey === endKey);
      if (
        !startKeyRelateVnodes.length
        && shapes[startKey]
        && shapes[startKey].type === SIGMA_CIRCLE_SHAPE
      ) {
        deleteShapes.push(shapes[startKey]);
      }
      if (
        !endKeyRelateVnodes.length
        && shapes[endKey]
        && shapes[endKey].type === SIGMA_CIRCLE_SHAPE
      ) {
        deleteShapes.push(shapes[endKey]);
      }

      vnode.component?.fire('pre-delete');
    } else {
      const startAndEndPointLines = lineShapes.filter(l => l.data.start.vkey === vnode.key || l.data.end.vkey === vnode.key);

      if (startAndEndPointLines.length === 2 && vnode.type === SIGMA_CIRCLE_SHAPE) {
        deleteShapes.push(startAndEndPointLines[0]);
        const updateShape = startAndEndPointLines[1];
        const startVkey0 = startAndEndPointLines[0].data.start.vkey;
        const startVkey1 = startAndEndPointLines[1].data.start.vkey;
        const endVkey0 = startAndEndPointLines[0].data.end.vkey;
        const endVkey1 = startAndEndPointLines[1].data.end.vkey;
        updateShapesPrevSnapshots.push(pureClone(updateShape));
        updateShape.data.start.vkey = startVkey0 === vnode.key
          ? startVkey1 === vnode.key
            ? endVkey1
            : startVkey1
          : startVkey0;
        updateShape.data.end.vkey = endVkey0 === vnode.key
          ? endVkey1 === vnode.key
            ? startVkey1
            : endVkey1
          : endVkey0;

        updateShapes.push(updateShape);
      } else {
        deleteShapes = deleteShapes.concat(startAndEndPointLines);
      }
    }
  }

  if (deleteShapes.length) {
    this.remove(deleteShapes, { _historical, isSub: true });
  }
  if (updateShapes.length) {
    this.update(updateShapes);
    cmdStack.saveAction({ data: updateShapes, oldData: updateShapesPrevSnapshots });
  }
};

SigmaCore.prototype.__checkShapeLocation = function (vnode, type) {
  // 判断新增或者更新
  // 新增，都是找到比自己大的第一个，移动到它之前，
  // 如果是更新，如果不是最大或者最小，那么就不动
  const { shapes } = this.data;
  const { key, component, zIndex } = vnode;
  if (!component?.parent()) return;
  const siblings = component.siblings();
  if (siblings.length <= 1) return;
  let targetDom = null;
  const zIndexs = [];
  for (let i = 0; i < siblings.length; i++) {
    const vkey = siblings[i].attr('key');
    const target = shapes[vkey];
    if (vkey === key || !target) continue;
    const targetZIndex = target.zIndex;
    zIndexs.push(targetZIndex);
    if (!targetDom && targetZIndex > zIndex) {
      targetDom = siblings[i];
    }
  }

  if (type === RENDER_CREATE && targetDom) {
    component.insertBefore(targetDom);
  }
  if (zIndexs.filter(i => i >= zIndex).length === 0) {
    component.front();
  } else if (zIndexs.filter(i => i <= zIndex).length === 0) {
    component.back();
  }
};
SigmaCore.prototype.__setShapeLocation = function (vnode) {
  if (vnode.__index) {
    const targetDom = document.getElementById(`${vnode.__index}`);
    if (targetDom) {
      vnode.component.insertBefore(targetDom);
    }
  }
};

// 其它工具函数
SigmaCore.prototype.__getShapesMaxAndMinZIndex = function () {
  const { shapes } = this.data;
  let min = 0;
  let max = 0;
  for (const key in shapes) {
    const { zIndex } = shapes[key];
    max = max < zIndex ? zIndex : max;
    min = min > zIndex ? zIndex : min;
  }
  return { min, max };
};
SigmaCore.prototype.__rangSelectionNodes = function (rang) {
  _rangSelectionTimer && clearTimeout(_rangSelectionTimer);
  _rangSelectionTimer = setTimeout(() => {
    const { shapes } = this.data;
    const points = parseObjectPointsToArray(rang);
    const vnodes = [];
    for (const key in shapes) {
      const vnode = shapes[key];
      if (vnode.type === SIGMA_LINE_SHAPE) continue;
      const flat = parseObjectPointsToArray(renders.getflat(vnode));
      const is = flat.some(p => isInside(p, points));
      if (is) {
        vnodes.push(vnode);
      }
    }
    vnodes.push(...this._getRelationLines(vnodes));
    this.onlyShapeChecked(vnodes);
  }, 0);
};

SigmaCore.prototype.__getStandardPosition = function (position) {
  const { mode } = store.getState();
  if (
    _.isObject(position)
    && _.isNumber(position.x)
    && _.isNumber(position.y)
  ) {
    const _pos = mode === SIGMA_GRAPH_MODE_2D
      ? get2DSigmaPoint(position)
      : getSigmaPoint(position);
    if (_pos.x !== position.x || _pos.y !== position.y) {
      console.warn('所设坐标不属于规范坐标，已自动修正。');
    }
    return _pos;
  }
  return null;
};

/**
 * 获取设置自定义属性的 vnode 节点
 * @param {*} key vnode key, string 类型
 * @returns vnode ｜ null
 */
SigmaCore.prototype._getCustomizeShape = function (key) {
  const { shapes } = this.data;
  const checkedShapes = this._getCheckedShapes();
  const vnode =    key && shapes[key]
    ? shapes[key]
    : checkedShapes.length === 1
      ? checkedShapes[0]
      : null;
  return vnode;
};
/**
 * 为节点设置自定义属性
 * @param {*} customize 自定义属性数据 any
 * @param {*} targetVnodeKey 目标节点
 * @returns
 */
SigmaCore.prototype.setCustomize = function (customize = {}, targetVnodeKey) {
  if (!_.isObject(customize)) {
    console.error('自定义属性值必须是一个 Object');
    return false;
  }
  const vnode = this._getCustomizeShape(targetVnodeKey);
  if (vnode) {
    vnode.customize = cloneDeep(customize);
    return true;
  }
  return false;
};
/**
 * 重置 vnode 自定义数据
 * @param {*} targetVnodeKey  目标节点或者选中节点
 * @returns 返回操作结果
 */
SigmaCore.prototype.resetCustomize = function (targetVnodeKey) {
  const vnode = this._getCustomizeShape(targetVnodeKey);
  if (vnode) {
    vnode.customize = undefined;
    return true;
  }
  return false;
};
/**
 * 获取节点自定义数据
 * @param {*} targetVnodeKey  目标节点或者选中节点
 * @returns 返回数据
 */
SigmaCore.prototype.getCustomize = function (targetVnodeKey) {
  const vnode = this._getCustomizeShape(targetVnodeKey);
  if (vnode) {
    return vnode.customize;
  }
  return {};
};

SigmaCore.prototype.getGraphData = function () {
  const { mode } = store.getState();
  const shapes = pureClone(this.data.shapes, true);
  // for (const key in shapes) {
  //   EXCLUDE_PROPERTYS.forEach((item) => {
  //     delete shapes[key][item];
  //   });
  // }
  const _shapes = mode === SIGMA_GRAPH_MODE_3D ? shapes : this.reduction(shapes);
  Object.keys(_shapes).forEach((k) => {
    if (_shapes[k].dragPoints) {
      delete _shapes[k].dragPoints;
    }
  });
  try {
    return JSON.stringify(_shapes);
  } catch (err) {
    console.error('getGraphData execution error！The shapes is:', _shapes);
  }
};

SigmaCore.prototype.clearHistory = function () {
  this.cmdStack.clearCmd();
};

SigmaCore.prototype.__handleEditable = function (is) {
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      uneditable: is,
    },
  });

  const { shapes } = this.data;
  for (const key in shapes) {
    const { component } = shapes[key];
    component.fire('editable-change', {
      uneditable: is,
    });
  }

  return is;
};

SigmaCore.prototype._createNodeLabel = function (vnode, name, opt = {}) {
  if (!vnode.sticky) {
    const nodesPrevSnapshots = [pureClone(vnode)];
    const textLabelInstance = new SigmaTextLabelShape();
    const isLineLabel = vnode.type === SIGMA_LINE_SHAPE;
    if (isLineLabel) {
      const { position, positionPercent } = opt;
      textLabelInstance.position = position || { ...vnode.data.points[0] };
      textLabelInstance.positionPercent = positionPercent;
      cmdStack.saveAction({
        maxGroupBegin: true,
      });
    } else {
      cmdStack.saveAction({
        groupBegin: true,
      });
    }
    const node = textLabelInstance.createLinkNode({ key: vnode.key, name: name ?? vnode.name });
    node.connectable = false;
    // node.isChecked = vnode.isChecked;
    vnode.sticky = node.key; // 绑定关系
    this.add(node, {
      _historical: true,
    });
    if (isLineLabel) {
      this.allShapeNotChecked();
    }
    this._setShapeChecked(node, true, false);
    this.update([vnode], { cachemanager: true });
    cmdStack.saveAction({
      data: [vnode],
      oldData: nodesPrevSnapshots,
    });
    if (isLineLabel) {
      node.component?.fire('start-edit');
      node.component.on('end-edit', () => {
        cmdStack.saveAction({
          maxGroupEnd: true,
        });
      });
      this.setStyles({
        fontSize: {
          name: '字号',
          type: 'number',
          default: 24,
          value: 14,
        },
      }, [node.key], { _historical: false });
    } else {
      cmdStack.saveAction({
        groupEnd: true,
      });
    }
    this.lines.clear();
  }
};

SigmaCore.prototype.setIsDoubleTouch = function (isDoubleTouch) {
  this.isDoubleTouch = isDoubleTouch;
};

export default SigmaCore;
