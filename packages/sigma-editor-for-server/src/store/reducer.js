import { SET_ROOT_ACTION, ALIGNMENT_LINES_ADD, ALIGNMENT_LINES_DELETE, ALIGNMENT_LINES_SORT, ALIGNMENT_LINES_CLEAR, SET_PREV_SELECTED_GROUP } from './actions';
import { SIGMA_GRAPH_MODE_3D } from '../util/constants';

const initialRoot = {
  root: null,
  mode: SIGMA_GRAPH_MODE_3D, // 模式 ;
  scale: 1, // 初始化缩放解决方案  1.2677
  minScale: 0.1, // 缩放最小倍数
  maxScale: 5, // 缩放最大倍数
  step: 0.1, // 缩放步长
  w: 0, // 容器宽度
  h: 0, // 容器高度
  doc: null, // svg 文档
  g: null, // 基础容器 g 标签
  defs: null, // defs
  viewBox: {
    x: 0,
    y: 0,
    w: 0,
    h: 0,
  },
  container: {}, // 各类元素的基础容器
  core: null, // Sigmacore
  alignments: {
    // 参考线
    lines: new Map(),
    sortX: [],
    sortY: [],
  },
  uneditable: false, // 不可编辑态
  prevSelectdGroup: null, // 元素在移动的时候需要保存上一个选中的组
  willClearWhenGroupDeleted: false,
  connectionNumber: 8, // 连线连接点的个数，默认16个，可配置 8个 4个
};

export function reducer(state = initialRoot, action) {
  switch (action.type) {
    case SET_ROOT_ACTION: {
      return { ...state, ...action.value };
    }

    case ALIGNMENT_LINES_ADD: {
      const { value } = action;
      const { alignments, ...restState } = state;
      alignments.lines.set(value[0], value[1]);
      return {
        ...restState,
        alignments,
      };
    }
    case ALIGNMENT_LINES_DELETE: {
      const { alignments, ...restState } = state;
      alignments.lines.delete(action.value);
      return {
        ...restState,
        alignments,
      };
    }
    case ALIGNMENT_LINES_SORT: {
      const { alignments, ...restState } = state;
      const { x, y } = action.value;
      alignments.sortX = x;
      alignments.sortY = y;
      return {
        ...restState,
        alignments,
      };
    }
    case ALIGNMENT_LINES_CLEAR: {
      const { alignments, ...restState } = state;
      alignments.lines.clear();
      alignments.sortX = [];
      alignments.sortY = [];
      return {
        ...restState,
        alignments,
      };
    }

    case SET_PREV_SELECTED_GROUP: {
      const { value } = action;
      const { prevSelectdGroup, ...restState } = state;
      return {
        ...restState,
        prevSelectdGroup: value,
      };
    }
    default:
      return state;
  }
}

export default reducer;
