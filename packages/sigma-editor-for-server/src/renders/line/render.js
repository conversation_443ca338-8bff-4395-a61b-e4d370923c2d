import { SVG, off, on } from '@svgdotjs/svg.js';
import store from '../../store';
import { SIGMA_GRAPH_MODE_3D } from '../../util/constants';
import {
  isLineMoving,
  setCheckedStyles,
  setLineMovingStatus,
  bindSideVnodeEvent,
  updateLineDataWhenMove,
  getSideVnodeEvents,
} from './utils';
import { clearConnectLineToolBox } from '../../util/toolbox';
import { getPointPercentInPath } from '../text-label/utils';
import { RenderComponent } from './render-component';
import cmdStack from '../../core/cmd';
import { clearMovablePoints } from '../../affix/render-movable-point';

export const render = (core, vnode) => {
  const { key } = vnode;
  const component = new SVG().group()
    .attr({
      key,
      id: key,
    });

  vnode.component = component;

  // 样式变化
  component.on('style-changed', (e) => {
    const { data = {}, initial } = e.detail;
    const { component } = vnode;
    // 新建或 update l1 l2
    if (initial) {
      vnode.renderComponent = new RenderComponent({ vnode });
      setLineMovingStatus(vnode);
      setCheckedStyles(vnode);
    }
    if (!initial) {
      // 切换直线折线，需要重置 data.points
      if (data.lineType) {
        vnode.data.points = null;
        vnode.renderComponent.updateType();
      }
      // 颜色等样式变化
      if (data.line || data.lineWidth || data.lineColor) {
        vnode.renderComponent.updateStyle();
      }

      // 箭头变化
      if (data.lineStart || data.lineEnd) {
        vnode.renderComponent.updatePath({ justArrow: true });
      }
    }

    if (initial) {
      component.fire('update-position', e.detail);
    }
  });

  // start 或者 end 变化，更新 l1 l2 箭头
  component.on('update-position', (e) => {
    const { initial = false, side, isExport = false } = e.detail || {};
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;

    if (!initial) {
      updateLineDataWhenMove(vnode, side, is3D);
      vnode.renderComponent.updatePath();
    }
    if (isExport) return;
    bindSideVnodeEvent(core, vnode, 'start', initial);
    bindSideVnodeEvent(core, vnode, 'end', initial);
    vnode.isChecked && vnode.renderComponent.resetDragPoint();
  });

  // 拖拽时涉及到端点变化
  component.on('side-change', (e) => {
    const { side, point, isEnd } = e.detail || {};
    const { data } = vnode;
    vnode.hasDragged = false;
    if (isEnd && data[side].type === 'vnode') return;

    if (isEnd) {
      const shape = core.data.shapes[data[side].vkey];
      data[side].x = shape.position.x;
      data[side].y = shape.position.y;
    } else {
      data[side].x = point.x;
      data[side].y = point.y;
    }
    setLineMovingStatus(vnode, side, isEnd ? 'vnode' : 'dragging');

    component.fire('update-position', { side });
  });

  // 起点终点一起移动或者组移动的情况
  component.on('position-change', (e) => {
    const { diff, pure } = e.detail || {};

    if (pure) {
      vnode.renderComponent.updatePath();
      return;
    }
    if (!diff) return;
    const { x, y } = diff;
    const { data } = vnode;
    for (let i = 1; i < data.points.length - 1; i++) {
      data.points[i].x += x;
      data.points[i].y += y;
    }
    vnode.renderComponent.updatePath();
    vnode.isChecked && vnode.renderComponent.resetDragPoint();
  });

  // 监听 checked 变化
  component.on('checked-change', () => {
    setCheckedStyles(vnode);
  });

  // 监听是否锁定的变化
  component.on('editable-change', () => {
    setCheckedStyles(vnode);
  });

  // 监听连线完成时间
  component.on('line-complete', (e) => {
    const { key } = e.detail || {};
    const { data } = vnode;
    if (!(
      key
      && key !== vnode.key
      && key !== data.start.vkey
    )) {
      deleteMouseLine();
      return;
    }

    window.clearMouseLineEvents();
    data.end.vkey = key;
    data.end.isMove = false;
    setLineMovingStatus(vnode, 'end', 'vnode');
    component.fire('update-position', { side: 'end' });
    // 存储新增连线记录
    cmdStack.saveAction({ type: 'add', data: [vnode] });

    // 连线结束后选中连线，但不进入历史记录中
    // core._setShapeChecked(vnode, true, false);
    clearConnectLineToolBox();
  });

  // 删除连线时，解除起止元素的事件
  component.on('deleted', () => {
    const { key } = vnode;
    const { shapes } = core.data;
    const { start, end } = vnode.data;
    const startVnode = shapes[start.vkey];
    const endVnode = shapes[end.vkey];
    const events = getSideVnodeEvents(key);
    startVnode?.component?.off(events);
    endVnode?.component?.off(events);
    vnode.renderComponent?.removeDragPoint();
  });

  // 使用当前数据刷新线条,用于撤销重做时直接渲染目标数据
  component.on('rerender-line', () => {
    vnode.renderComponent.updateType();
  });

  // 双击添加label
  component.on('dblclick', (e) => {
    const position = component.point(e.pageX, e.pageY);
    const { points } = vnode.data;
    const positionPercent = getPointPercentInPath(position, points);
    // position: { x, y }, positionPercent 双击位置距离起点路径占比百分比
    core._createNodeLabel(vnode, '双击编辑', { position, positionPercent });
  });

  if (vnode.data.end.type === 'mouse') {
    on(window, 'mousemove.line', getMouseCoordinate);
    on(window, 'contextmenu.line', deleteMouseLine);
    on(window, 'keydown.line', keydownAndDelete);
    on(window, 'mousedown.line', createNextLineAndPoint);
  }

  function getMouseCoordinate(e) {
    e.stopPropagation();
    const { dropzone } = store.getState();
    const { x, y } = dropzone.point(e.pageX, e.pageY);
    vnode.data.end.x = x;
    vnode.data.end.y = y;
    vnode.data.end.isMove = true; // 添加节点是否正在移动标识
    setCheckedStyles(vnode);
    vnode.component.fire('update-position', { side: 'end' });
  }

  function createNextLineAndPoint(e) {
    if (e.which === 3) return;
    deleteMouseLine();
  }

  function keydownAndDelete(e) {
    if (e.keyCode === 27) {
      deleteMouseLine();
    }
  }

  function deleteMouseLine() {
    window.clearMouseLineEvents();
    const linesMouse = [];
    for (const s in core.data.shapes) {
      const vnode = core.data.shapes[s];
      if (isLineMoving(vnode)) {
        linesMouse.push(vnode);
      }
    }
    if (linesMouse.length) {
      core.remove(linesMouse);
    }

    clearMovablePoints();
  }

  window.clearMouseLineEvents = function () {
    off(window, '.line');
  };

  return component;
};
