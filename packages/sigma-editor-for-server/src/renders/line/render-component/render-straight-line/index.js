import { DrawLine } from '../common/draw-line';
import { ChildPoint } from './point';
import { POINT_WRAPPER_CLASSNAME } from './utils';
import { DrawArrow } from '../common/arrow';

class RenderStraightLine {
  constructor({
    vnode,
  }) {
    this.vnode = vnode;

    this._init();
  }

  _points = [];
  _pointWrapper = null;
  _staightLine = null; // 直线实例
  _arrow = null;

  _init() {
    const { vnode } = this;
    this._points = [];
    // 1.18更新 前面已经初始化过了，不用再初始化了
    // 初始化点，先绘制线，后绘制点
    // this._initPoints();
    this._arrow = new DrawArrow({ vnode });
    this._staightLine = new DrawLine({ vnode, arrow: this._arrow });
    this._initPointWrapper();

    const { points } = vnode.data;

    for (let i = 0; i < points.length; i++) {
      const point = points[i];

      const { x, y, status } = point;
      const childPoint = new ChildPoint({
        vnode,
        parent: this,
        position: { x, y },
        status,
        index: i,
        isStart: i === 0,
        isEnd: i === points.length - 1,
      });

      this._points.push(childPoint);
      this._pointWrapper.add(childPoint.element);
    }
  }

  addRawPoints(index) {
    const { vnode } = this;
    const { data } = vnode;
    const current = data.points[index];
    const previous = data.points[index - 1];
    const next = data.points[index + 1];
    let acc = 0;
    const status = 'raw';

    if (previous) {
      const position = {
        x: (previous.x + current.x) / 2,
        y: (previous.y + current.y) / 2,
      };
      data.points.splice(index, 0, { ...position, status });

      const childPoint = new ChildPoint({
        vnode,
        parent: this,
        position,
        status,
      });
      this._points.splice(index, 0, childPoint);
      this._pointWrapper.add(childPoint.element);

      acc ++;
    }

    if (next) {
      const position = {
        x: (next.x + current.x) / 2,
        y: (next.y + current.y) / 2,
      };
      const newIndex = index + 1 + acc;
      data.points.splice(newIndex, 0, { ...position, status });

      const childPoint = new ChildPoint({
        vnode,
        parent: this,
        position,
        status,
      });
      this._points.splice(newIndex, 0, childPoint);
      this._pointWrapper.add(childPoint.element);
    }

    // 更新 index
    for (let i = index; i < data.points.length; i++) {
      this._points[i].index = i;
    }

    this._staightLine.updatePath();
  }

  pointChange(index, point, isEnd) {
    const { vnode } = this;
    const { data } = vnode;

    // 起点和终点
    if (index === 0) {
      vnode.component.fire('side-change', { side: 'start', point, isEnd });
      return;
    }
    if (index === data.points.length - 1) {
      vnode.component.fire('side-change', { side: 'end', point, isEnd });
      return;
    }

    const prevMature = index - 2;
    const prevRaw = index - 1;
    const nextRaw = index + 1;
    const nextMature = index + 2;

    // 当前
    data.points[index].x = point.x;
    data.points[index].y = point.y;
    // 前一个
    data.points[prevRaw].x = (data.points[prevMature].x + data.points[index].x) / 2;
    data.points[prevRaw].y = (data.points[prevMature].y + data.points[index].y) / 2;
    // 后一个
    data.points[nextRaw].x = (data.points[nextMature].x + data.points[index].x) / 2;
    data.points[nextRaw].y = (data.points[nextMature].y + data.points[index].y) / 2;

    // 更新 points
    for (let i = prevRaw; i <= nextRaw; i++) {
      this._points[i].update(data.points[i]);
    }

    // 检查是否会波及端点
    const is = this._checkSideEvent(index);

    !is && this._staightLine.updatePath();
  }

  _initPointWrapper() {
    const { component } = this.vnode;

    this._pointWrapper = component.findOne(`.${POINT_WRAPPER_CLASSNAME}`);
    if (!this._pointWrapper) {
      this._pointWrapper = component.group().attr({
        class: POINT_WRAPPER_CLASSNAME,
      });
    } else {
      this._pointWrapper.clear();
    }
  }

  _initPoints() {
    const { data } = this.vnode;
    if (!Array.isArray(data.points) || data.points.length <= 2) {
      const { start: { x: sx, y: sy }, end: { x: ex, y: ey } } = data;

      data.points = [
        { x: sx, y: sy, status: 'mature' },
        {
          x: (sx + ex) / 2,
          y: (sy + ey) / 2,
          start: 'raw',
        },
        { x: ex, y: ey, status: 'mature' },
      ];
    }
  }

  updateChecked(is) {
    for (const child of this._points) {
      child.element[is ? 'show' : 'hide']();
    }
  }

  updatePath(opts = {}) {
    if (!opts.justArrow) {
      const { points } = this.vnode.data;
      for (let i = 0; i < points.length; i++) {
        this._points[i].update(points[i]);
      }
    }
    this._arrow.render();
    this._staightLine.updatePath();
  }

  delete(index) {
    const { data } = this.vnode;
    if (index === 0 || index === data.points.length - 1) return;

    const point = this._points[index];
    if (point.status === 'raw') return;
    // 更新前面一个，删除后面俩
    const prev = {
      x: (data.points[index - 2].x + data.points[index + 2].x) / 2,
      y: (data.points[index - 2].y + data.points[index + 2].y) / 2,
      status: 'raw',
    };
    data.points[index - 1] = prev;
    this._points[index - 1].update(prev);

    [index, index + 1].forEach((i) => {
      this._points[i].element.remove();
    });
    data.points.splice(index, 2);
    this._points.splice(index, 2);

    // 更新index
    for (let i = index; i <= data.points.length - 1; i++) {
      this._points[i].index = i;
    }

    const is = this._checkSideEvent(index - 1);

    !is && this._staightLine.updatePath({ animating: true });
  }

  _checkSideEvent(index) {
    const { data, component } = this.vnode;
    const { length } = data.points;
    let is = false;
    if (index <= 2) {
      component.fire('update-position', { side: 'start' });
      is = true;
    }

    if (index >= length - 3) {
      component.fire('update-position', { side: 'end' });
      is = true;
    }

    return is;
  }

  updateStyle() {
    this._staightLine.updateStyle();
  }
}

export { RenderStraightLine };
