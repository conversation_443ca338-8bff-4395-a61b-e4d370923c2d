import { SVG } from '@svgdotjs/svg.js';
import { arrayPointsToStringPoints, getStylesVal } from '../../../../util/tools';
import store from '../../../../store';
import { SIGMA_GRAPH_MODE_3D, TRANSFORM_3D_MATRIX } from '../../../../util/constants';
import { getAmplitudeDegrees, ARROW_WRAPPER_CLASSNAME } from '../../utils';

class DrawArrow {
  constructor({ vnode }) {
    this.vnode = vnode;
    this.isStartArrow = false;
    this.isEndArrow = false;
    this.start = null;
    this.end = null;
    this.startDegree = null;
    this.endDegree = null;
    this.is3D = false;

    this._initWrapper();
    this.render();
  }
  _wrapper;
  _attrs = {
    overflow: 'visible',
    viewBox: '0 0 24 24',
    width: 14,
    height: 14,
  };

  render() {
    const { styles } = this.vnode;
    this.isStartArrow = getStylesVal(styles.lineStart) === 'arrow';
    this.isEndArrow = getStylesVal(styles.lineEnd) === 'arrow';
    this.is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;

    this._calcDegree();
    this._renderStart();
    this._renderEnd();
  }

  _renderStart() {
    const { data, styles } = this.vnode;
    const lineColor = getStylesVal(styles.lineColor);
    const path = arrayPointsToStringPoints(data.arrow);
    this.start = this._wrapper.findOne('.start');

    if (this.isStartArrow) {
      const { x, y } = data.points[0];

      if (!this.start) {
        // 初始化
        this.start = new SVG().attr({
          x,
          y,
          class: 'start',
          fill: lineColor,
          ...this._attrs,
        });
        this._wrapper.add(this.start);
        const main = this.start.group().attr({ class: 'main' });
        main.polygon(path);
      } else {
        // points 有变动
        this.start.attr({ x, y });
      }

      const main = this.start.findOne('.main');
      main.attr({
        transform: this.is3D
          ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')}) rotate(${this.startDegree + 180})`
          : `rotate(${this.startDegree + 180})`,
      });
    } else {
      this.start?.remove();
      this.start = null;
    }
  }

  _renderEnd() {
    const { data, styles } = this.vnode;
    const lineColor = getStylesVal(styles.lineColor);
    const path = arrayPointsToStringPoints(data.arrow);
    this.end = this._wrapper.findOne('.end');
    if (this.isEndArrow) {
      const { length } = data.points;
      const { x, y } = data.points[length - 1];

      if (!this.end) {
        this.end = new SVG().attr({
          x,
          y,
          class: 'end',
          fill: lineColor,
          ...this._attrs,
        });
        this._wrapper.add(this.end);
        const main = this.end.group().attr({ class: 'main' });
        main.polygon(path);
      } else {
        this.end.attr({ x, y });
      }

      const main = this.end.findOne('.main');
      main.attr({
        transform: this.is3D
          ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')}) rotate(${this.endDegree})`
          : `rotate(${this.endDegree})`,
      });
    } else {
      this.end?.remove();
      this.end = null;
    }
  }

  _calcDegree() {
    const { mode } = store.getState();
    const { points } = this.vnode.data;

    if (this.isStartArrow) {
      this.startDegree = getAmplitudeDegrees(points[0], points[1], mode);
    }

    if (this.isEndArrow) {
      const { length } = points;
      this.endDegree = getAmplitudeDegrees(
        points[length - 2],
        points[length - 1],
        mode,
      );
    }
  }

  _initWrapper() {
    const { component } = this.vnode;
    this._wrapper = component.findOne(`.${ARROW_WRAPPER_CLASSNAME}`);
    if (!this._wrapper) {
      this._wrapper = component.group().attr({ class: ARROW_WRAPPER_CLASSNAME });
    } else {
      this._wrapper.clear();
    }
  }
}

export {
  DrawArrow,
};
