import { DEFAULT_ANIMATION_SETTING } from '../../../../util/constants';
import { getStylesVal } from '../../../../util/tools';
import { getLineOffset, LINE_STROKE_DASHARRAY, LINE_WRAPPER_CLASSNAME } from '../../utils';

class DrawLine {
  constructor({
    vnode,
    arrow,
  }) {
    this.vnode = vnode;
    this.arrow = arrow;
    this._initWrapper();
    this._init();
  }

  _init() {
    const { vnode } = this;
    const { styles } = vnode;
    const line = getStylesVal(styles.line);
    const lineWidth = getStylesVal(styles.lineWidth);
    const lineColor = getStylesVal(styles.lineColor);

    const points = this._formatPoints();
    this.l1 = this.element.polyline(points).attr({
      'stroke-width': lineWidth ? lineWidth : 2,
      'stroke-linecap': line === 'dashed' ? 'butt' : null,
      'stroke-dasharray': line === 'dashed' ? LINE_STROKE_DASHARRAY : null,
      stroke: lineColor,
      fill: 'none',
      class: 'l1',
    });

    this.l2 = this.element.polyline(points)
      .attr({
        'stroke-width': 20,
        stroke: 'transparent',
        fill: 'none',
        class: 'l2',
      });
  }

  _initWrapper() {
    const { component } = this.vnode;
    this.element = component.findOne(`.${LINE_WRAPPER_CLASSNAME}`);
    if (!this.element) {
      this.element = component.group().attr({
        class: LINE_WRAPPER_CLASSNAME,
      });
    } else {
      this.element.clear();
    }
  }

  _formatPoints() {
    const {
      isStartArrow,
      isEndArrow,
      startDegree,
      endDegree,
      is3D,
    } = this.arrow;
    const { points } = this.vnode.data;

    return points.reduce((acc, { x, y }, index) => {
      if (index === 0 && isStartArrow) {
        const { x: offsetX, y: offsetY } = getLineOffset(startDegree + 180, is3D);
        acc.push([x + offsetX, y + offsetY]);
        return acc;
      }
      if ((index === points.length - 1) && isEndArrow) {
        const { x: offsetX, y: offsetY } = getLineOffset(endDegree, is3D);
        acc.push([x + offsetX, y + offsetY]);
        return acc;
      }

      acc.push([x, y]);
      return acc;
    }, []);
  }

  updatePath({ animating = false } = {}) {
    const points = this._formatPoints();
    const el1 = animating ? this.l1.animate(DEFAULT_ANIMATION_SETTING) : this.l1;
    const el2 = animating ? this.l2.animate(DEFAULT_ANIMATION_SETTING) : this.l2;
    el1.plot(points);
    el2.plot(points);
  }

  updateStyle() {
    const { vnode } = this;
    const { styles } = vnode;
    const line = getStylesVal(styles.line);
    const lineWidth = getStylesVal(styles.lineWidth);
    const lineColor = getStylesVal(styles.lineColor);

    this.l1.attr({
      'stroke-width': lineWidth ? lineWidth : 2,
      'stroke-linecap': line === 'dashed' ? 'butt' : null,
      'stroke-dasharray': line === 'dashed' ? LINE_STROKE_DASHARRAY : null,
      stroke: lineColor,
    });

    this.arrow.start?.attr({
      fill: lineColor,
    });
    this.arrow.end?.attr({
      fill: lineColor,
    });
  }
}

export { DrawLine };
