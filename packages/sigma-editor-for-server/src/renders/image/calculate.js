import {
  transform3DTo2D,
  transform2DTo3D,
  getSigmaPoint,
  get2DSigmaPoint,
} from '../../util/tools';
import {
  TRANSFORM_3D_MATRIX,
  TRANSFORM_3D_SCALE,
  SIGMA_GRAPH_MODE_2D,
} from '../../util/constants';

export const transform = (vnode) => {
  vnode.position = transform3DTo2D(vnode.position);
  return vnode;
};

export const reduction = (vnode) => {
  vnode.position = transform2DTo3D(vnode.position);
  return vnode;
};

export const getflat = (vnode, mode) => {
  const {
    position: { x, y },
    data: { w, h },
  } = vnode;

  if (mode === SIGMA_GRAPH_MODE_2D) {
    const offsetx = -w / 2;
    const offsety = -h / 2;
    return [
      { x: x + offsetx, y: y + offsety },
      { x: x + offsetx, y: y + offsety + h },
      { x: x + offsetx + w, y: y + offsety + h },
      { x: x + offsetx + w, y: y + offsety },
    ].map(p => get2DSigmaPoint(p));
  }

  let [a, b, c, d] = TRANSFORM_3D_MATRIX;
  a = a * TRANSFORM_3D_SCALE;
  b = b * TRANSFORM_3D_SCALE;
  c = c * TRANSFORM_3D_SCALE;
  d = d * TRANSFORM_3D_SCALE;

  const half_w = w / 2;
  const half_h = h / 2;

  const p1x = a * half_w + c * half_h;
  const p1y = b * half_w + d * half_h;

  return [
    { x: x - p1x, y: y - p1y },
    { x: x + a * w - p1x, y: y + b * w - p1y },
    { x: x + p1x, y: y + p1y },
    { x: x + c * h - p1x, y: y + d * h - p1y },
  ].map(p => getSigmaPoint(p));
};
