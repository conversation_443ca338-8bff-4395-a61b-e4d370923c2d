import { SVG } from '@svgdotjs/svg.js';
import {
  TRANSFORM_3D_SCALE,
  SIGMA_GRAPH_MODE_2D,
} from '../../util/constants';
import {
  getFlatAndRouteTransform,
  getStylesVal,
} from '../../util/tools';

export const exports = async (_, vnode, mode) => {
  const {
    key,
    position: { x, y },
    styles,
    data,
  } = vnode;
  const { cache, w, h } = data;
  const is3d = getStylesVal(styles.is3d);
  const isFlat = getStylesVal(styles.isFlat);
  const route = getStylesVal(styles.route);
  const scale = getStylesVal(styles.scale);
  const component = new SVG().group()
    .attr({ key, id: key });
  const svg = new SVG('<svg></svg>').attr({
    x,
    y,
    overflow: 'visible',
    class: 'position',
  });

  const shapeHeight = scale * h;

  const transform = getFlatAndRouteTransform(
    isFlat,
    mode === SIGMA_GRAPH_MODE_2D ? false : is3d,
    route,
    { x: 0, y: shapeHeight / 2 },
  );
  const group = svg.group().attr({
    transform: `${transform} scale(${scale * TRANSFORM_3D_SCALE})`,
    class: 'g1',
  });

  component.add(svg);

  const main = new SVG('<svg></svg>').attr({
    x: (-1 * w) / 2,
    y: (-1 * h) / 2,
    overflow: 'visible',
    class: 'main',
  });
  group.add(main);
  main.image().attr({ class: 'image', width: w, height: h, href: cache });
  return component;
};
