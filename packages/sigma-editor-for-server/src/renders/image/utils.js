import { SVG } from '@svgdotjs/svg.js';
import store from '../../store';
import { SIGMA_GRAPH_MODE_2D, TRANSFORM_3D_SCALE } from '../../util/constants';
import { getFlatAndRouteTransform, getStylesVal, image2Base64, setPlaneShapeConnection } from '../../util/tools';
import { clearConnectLineToolBox } from '../../util/toolbox';

export function getImageShapeStyles(vnode) {
  const { mode } = store.getState();
  const { styles } = vnode;
  // const { w = 0, h = 0 } = vnode.data;
  const { width, height } = vnode.data;
  const is3d = getStylesVal(styles.is3d);
  const route = getStylesVal(styles.route);
  const scale = getStylesVal(styles.scale);
  const src = getStylesVal(styles.image);
  const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : is3d;
  const isFlat = getStylesVal(styles.isFlat);
  return {
    is3d,
    is3D,
    route,
    scale,
    src,
    isFlat,
    width,
    height,
    // width: scale * TRANSFORM_3D_SCALE * w,
    // height: scale * TRANSFORM_3D_SCALE * h,
  };
}

export const DEFAULT_IMAGE_SHAPE_SIZE = 271.68;

export function initImageSize(vnode) {
  if (!(vnode.width && vnode.height)) {
    vnode.width = DEFAULT_IMAGE_SHAPE_SIZE;
    vnode.height = DEFAULT_IMAGE_SHAPE_SIZE;
  }
  setImageSizeChange(vnode, true);
}

export function setImageSizeChange(vnode, initial = false) {
  const { core, mode } = store.getState();
  const { styles } = vnode;
  const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
  const isFlat = getStylesVal(styles.isFlat);
  // 设置 connection
  setPlaneShapeConnection(vnode, is3D, isFlat);
  if (initial) return;
  // 设置参考线
  core.lines.add(vnode, is3D, isFlat);
}

export function loadImage(vnode) {
  const { core, mode } = store.getState();
  const { styles, component } = vnode;
  const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
  const isFlat = getStylesVal(styles.isFlat);
  const route = getStylesVal(styles.route);
  const src = getStylesVal(styles.image);
  const scale = getStylesVal(styles.scale);

  const group = component.findOne('.g1');
  let main = component.findOne('.main');
  const rect = component.findOne('.image-rect');
  const image = component.findOne('.image');

  const _image = new Image();
  _image.src = src;
  _image.setAttribute('crossOrigin', 'anonymous');

  return new Promise((resolve, reject) => {
    _image.onload = function () {
      const w = _image.width;
      const h = _image.height;

      // 渲染相关
      if (!main) {
        main = new SVG('<svg></svg>').attr({
          x: (-1 * w) / 2,
          y: (-1 * h) / 2,
          overflow: 'visible',
          class: 'main',
        });
        group.add(main);
      } else {
        main.attr({
          x: (-1 * w) / 2,
          y: (-1 * h) / 2,
        });
      }

      if (!image) {
        main.image(src).attr({ class: 'image', width: w, height: h });
      } else {
        image.attr({ href: src, width: w, height: h });
      }

      vnode.data = { w, h, cache: image2Base64(_image) };

      if (vnode.isChecked) {
        if (rect) {
          rect.attr({
            x: (-1 * w) / 2,
            y: (-1 * h) / 2,
            width: w,
            height: h,
          });
        } else {
          group.rect().attr({
            x: (-1 * w) / 2,
            y: (-1 * h) / 2,
            width: w,
            height: h,
            stroke: '#006eff',
            fill: 'none',
            'stroke-width': '4',
            class: 'image-rect',
          });
        }
      }
      // 设置缩放
      vnode.width = scale * TRANSFORM_3D_SCALE * w;
      vnode.height = scale * TRANSFORM_3D_SCALE * h;

      const transform = getFlatAndRouteTransform(
        isFlat,
        is3D,
        route,
        { x: 0, y: vnode.height / 2 },
      );
      group.attr({
        transform: `${transform} scale(${scale * TRANSFORM_3D_SCALE})`,
      });

      setImageSizeChange(vnode);

      resolve();
    };

    _image.onerror = function () {
      console.error('上传的图片加载失败');
      core.remove([vnode]);
      clearConnectLineToolBox();
      reject();
    };
  });
}
