import { SVG } from '@svgdotjs/svg.js';
import { getImgBase64Info } from '../../util/tools';
import {
  ANIMATE_MOVE_DURATION,
  ANIMATE_MOVE_DELAY,
  ANIMATE_MOVE_WHEN,
  SIGMA_GRAPH_MODE_3D,
  BASE_GRID_2D,
  STATIC_DEFAULT_2D_STROKE_COLOR,
} from '../../util/constants';

export const render = (_, vnode, mode) => {
  const component = mode === SIGMA_GRAPH_MODE_3D ? _3dRender(vnode) : _2dRender(vnode);

  vnode.component = component;
  return component;
};

const _3dRender = (vnode) => {
  const {
    key,
    position: { x, y },
    data,
    isChecked,
  } = vnode;

  const component = new SVG().group()
    .attr({ key, id: key });
  const main = new SVG(data.d3).attr('overflow', 'visible');
  component.add(main);
  main.attr({ x, y, class: 'main' });

  if (isChecked) {
    main.attr({ filter: 'url(#bright75)' });
  }
  return component;
};

const _2dRender = (vnode) => {
  const {
    key,
    position: { x, y },
    data,
    isChecked,
  } = vnode;

  const component = new SVG().group()
    .attr({ key, id: key });
  const svg = new SVG('<svg></svg>').attr({
    x,
    y,
    overflow: 'visible',
    class: 'position',
    width: BASE_GRID_2D * 2,
    height: BASE_GRID_2D * 2,
  });
  const g = svg.group().attr({
    x: -0.5,
    y: -0.5,
  });
  const main = new SVG(data.d2).attr({
    width: 60,
    height: 60,
    x: 15,
    y: 15,
  });
  const rect = g.rect(91, 91).attr({
    fill: '#E2E6EC',
    class: 'rect',
  });

  component.add(svg);
  g.add(rect);
  g.add(main);
  vnode.d2_connection = [
    { x: BASE_GRID_2D, y: 0 },
    { x: 0, y: BASE_GRID_2D },
    { x: BASE_GRID_2D, y: BASE_GRID_2D * 2 },
    { x: BASE_GRID_2D * 2, y: BASE_GRID_2D },
  ];

  if (isChecked) {
    rect.attr({
      'stroke-width': 4,
      stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
    });
  }

  return component;
};

export const rerender = (_, vnode, mode) => {
  if (mode === SIGMA_GRAPH_MODE_3D) {
    const {
      position: { x, y },
      isChecked,
      component,
    } = vnode;
    const main = component.findOne('.main');

    if (isChecked) {
      main.attr({ filter: 'url(#bright75)' });
    } else {
      main.attr('filter', null);
    }

    main
      .animate(ANIMATE_MOVE_DURATION, ANIMATE_MOVE_DELAY, ANIMATE_MOVE_WHEN)
      .attr({ x, y });
  } else {
    _2dReRender(vnode);
  }
};

const _2dReRender = (vnode) => {
  const {
    component,
    isChecked,
    position: { x, y },
  } = vnode;
  const pos = component.findOne('.position');
  const rect = component.findOne('.rect');

  if (rect) {
    rect.fill('#E2E6EC');
    if (isChecked) {
      rect.attr({
        'stroke-width': 2,
        stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
      });
    } else {
      rect.attr({
        'stroke-width': 2,
        stroke: 'none',
      });
    }
  } else {
  }
  pos
    .animate(ANIMATE_MOVE_DURATION, ANIMATE_MOVE_DELAY, ANIMATE_MOVE_WHEN)
    .attr({ x, y });
};

export const exports = async (_core, vnode, mode) => {
  const {
    key,
    position: { x, y },
    data,
  } = vnode;
  let component;
  if (mode === SIGMA_GRAPH_MODE_3D) {
    component = new SVG().group()
      .attr({ key, id: key });
    const main = new SVG(data.d3);
    component.add(main);
    main.attr({ x, y, class: 'main' });
    const img = main.findOne('image');
    const url = img.attr('href');
    const { base64 } = await getImgBase64Info(url);
    img.attr('href', base64);
  } else {
    component = _2dRender(vnode);
  }
  return component;
};
