import { transform3DTo2D, transform2DTo3D } from '../../util/tools';
import {
  SIGMA_GRAPH_MODE_3D,
  BASE_GRID_Y,
  BASE_GRID_X,
  BASE_STEP_X,
  BASE_GRID_2D,
  BASE_HALF_GRID_2D,
} from '../../util/constants';

export const transform = (vnode) => {
  vnode.position = transform3DTo2D(
    vnode.position,
    BASE_GRID_2D * 2 + BASE_HALF_GRID_2D,
    BASE_HALF_GRID_2D,
  );
  return vnode;
};

export const reduction = (vnode) => {
  vnode.position = transform2DTo3D({
    x: vnode.position.x - (BASE_GRID_2D * 2 + BASE_HALF_GRID_2D),
    y: vnode.position.y - BASE_HALF_GRID_2D,
  });
  return vnode;
};

export const getflat = (vnode, mode) => {
  const {
    position: { x },
  } = vnode;
  let {
    position: { y },
  } = vnode;
  if (mode === SIGMA_GRAPH_MODE_3D) {
    y = y + 2 * BASE_GRID_Y + BASE_STEP_X;
    return [
      { x, y },
      { x: x + BASE_GRID_X, y: y - BASE_GRID_Y },
      { x: x + 2 * BASE_GRID_X, y },
      { x: x + BASE_GRID_X, y: y + BASE_GRID_Y },
    ];
  }
  const offset = BASE_GRID_2D * 2;
  return [
    { x, y },
    { x, y: y + offset },
    { x: x + offset, y: y + offset },
    { x: x + offset, y },
  ];
};
