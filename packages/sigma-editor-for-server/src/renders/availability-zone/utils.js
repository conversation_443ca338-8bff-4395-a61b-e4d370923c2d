import {
  computePolygonArrow,
  getSigmaPoint,
  get2DSigmaPoint,
  transform3DTo2D,
} from '../../util/tools';
import {
  BASE_STEP_X,
  BASE_STEP_Y,
  BASE_SLOPE,
  SIGMA_GRAPH_MODE_2D,
  BASE_GRID_X,
  BASE_GRID_Y,
  BASE_GRID_2D,
  BASE_HALF_GRID_2D,
} from '../../util/constants';
import store from '../../store';

export function setZoneSizeAndFireEvent(vnode, nextMain, is3D) {
  const list = is3D ? nextMain.map(p => transform3DTo2D(p)) : nextMain;
  const main = list.map(p => get2DSigmaPoint(p));
  vnode.width = Math.abs(main[1].x - main[0].x);
  vnode.height = Math.abs(main[2].y - main[1].y);
  vnode.component?.fire('arrow-changed');
}

export const _arrow12MouseDown = (e, core, vnode, relative = 1, mode) => {
  e.stopPropagation();
  const { doc, uneditable } = store.getState();
  if (uneditable) return;
  const is2D = mode === SIGMA_GRAPH_MODE_2D;
  const {
    position: { x, y },
    data: { main, polygonArrow2, polygonArrow3, polygonArrow4, polygonMove },
  } = vnode;
  vnode.isChecked = true;
  core.update([vnode]);
  const start = doc.point(e.pageX, e.pageY);
  const min = is2D ? BASE_GRID_2D * 2 : BASE_GRID_X;
  function mouseMove(ev) {
    const end = doc.point(ev.pageX, ev.pageY);
    let nextPointOffsetX; let nextPointOffsetY;

    if (is2D) {
      const offsetX = end.x - start.x;
      nextPointOffsetX = ~~(offsetX / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D * relative;
      nextPointOffsetY = 0;
    } else {
      const bstart = start.y - BASE_SLOPE * start.x;
      const bend = end.y + BASE_SLOPE * end.x;
      const insx = (bend - bstart) / (BASE_SLOPE + BASE_SLOPE);
      const insy = (bend - bstart) / 2 + bstart;
      const offsetX = insx - start.x;
      const offsetY = insy - start.y;
      nextPointOffsetX = ~~(offsetX / BASE_STEP_X) * BASE_STEP_X * relative;
      nextPointOffsetY = ~~(offsetY / BASE_STEP_Y) * BASE_STEP_Y * relative;
    }

    const nextPolygonArrow2 = computePolygonArrow(
      polygonArrow2,
      nextPointOffsetX,
      nextPointOffsetY,
      [],
    );
    const nextPolygonMove = computePolygonArrow(
      polygonMove,
      nextPointOffsetX,
      nextPointOffsetY,
      [0, 3],
    );
    const nextMain = computePolygonArrow(
      main,
      nextPointOffsetX,
      nextPointOffsetY,
      [0, 3],
    );
    const nextPolygonArrow3 = computePolygonArrow(
      polygonArrow3,
      nextPointOffsetX / 2,
      nextPointOffsetY / 2,
      [],
    );
    const nextPolygonArrow4 = computePolygonArrow(
      polygonArrow4,
      nextPointOffsetX / 2,
      nextPointOffsetY / 2,
      [],
    );

    if (Math.ceil(nextMain[1].x) < min) return;
    vnode.data.polygonArrow2 = nextPolygonArrow2;
    vnode.data.polygonArrow3 = nextPolygonArrow3;
    vnode.data.polygonArrow4 = nextPolygonArrow4;
    vnode.data.polygonMove = nextPolygonMove.map(p => (is2D ? get2DSigmaPoint(p) : getSigmaPoint(p)));
    vnode.data.main = nextMain.map(p => (is2D ? get2DSigmaPoint(p) : getSigmaPoint(p)));

    setZoneSizeAndFireEvent(vnode, nextMain, !is2D);
    if (relative === -1) {
      const pos = { x: x - nextPointOffsetX, y: y - nextPointOffsetY };
      vnode.position = is2D
        ? get2DSigmaPoint(pos)
        : getSigmaPoint(pos);
    }
    core.update([vnode]);
  }
  function mouseUp() {
    doc.off('mousemove', mouseMove);
    doc.off('mouseup', mouseUp);
  }

  doc.on('mousemove', mouseMove);
  doc.on('mouseup', mouseUp);
};

export const _arrow34MouseDown = (e, core, vnode, relative = 1, mode) => {
  e.stopPropagation();
  const { doc, uneditable } = store.getState();
  if (uneditable) return;
  const {
    position: { x, y },
    data: { main, polygonArrow1, polygonArrow2, polygonArrow4, polygonMove },
  } = vnode;
  const is2D = mode === SIGMA_GRAPH_MODE_2D;
  vnode.isChecked = true;
  core.update([vnode]);
  const start = doc.point(e.pageX, e.pageY);
  const min = is2D ? BASE_GRID_2D * 2 : BASE_GRID_Y;
  function mouseMove(ev) {
    const end = doc.point(ev.pageX, ev.pageY);
    let nextPointOffsetX; let nextPointOffsetY;

    if (is2D) {
      const offsetY = end.y - start.y;
      nextPointOffsetY = ~~(offsetY / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D * relative;
      nextPointOffsetX = 0;
    } else {
      const bstart = start.y + BASE_SLOPE * start.x;
      const bend = end.y - BASE_SLOPE * end.x;
      const insx = (bend - bstart) / (-2 * BASE_SLOPE);
      const insy = (bend - bstart) / 2 + bstart;
      const offsetX = insx - start.x;
      const offsetY = insy - start.y;
      nextPointOffsetX = ~~(offsetX / BASE_STEP_X) * BASE_STEP_X * relative;
      nextPointOffsetY = ~~(offsetY / BASE_STEP_Y) * BASE_STEP_Y * relative;
    }
    const nextPolygonArrow1 = computePolygonArrow(
      polygonArrow1,
      nextPointOffsetX / 2,
      nextPointOffsetY / 2,
      [],
    );
    const nextPolygonArrow2 = computePolygonArrow(
      polygonArrow2,
      nextPointOffsetX / 2,
      nextPointOffsetY / 2,
      [],
    );
    const nextPolygonArrow4 = computePolygonArrow(
      polygonArrow4,
      nextPointOffsetX,
      nextPointOffsetY,
      [],
    );
    const nextPolygonMove = computePolygonArrow(
      polygonMove,
      nextPointOffsetX,
      nextPointOffsetY,
      [0, 1],
    );
    const nextMain = computePolygonArrow(
      main,
      nextPointOffsetX,
      nextPointOffsetY,
      [0, 1],
    );
    if (Math.ceil(nextMain[3].y) < min) return;
    vnode.data.polygonArrow1 = nextPolygonArrow1;
    vnode.data.polygonArrow2 = nextPolygonArrow2;
    vnode.data.polygonArrow4 = nextPolygonArrow4;
    vnode.data.polygonMove = nextPolygonMove.map(p => (is2D ? get2DSigmaPoint(p) : getSigmaPoint(p)));
    vnode.data.main = nextMain.map(p => (is2D ? get2DSigmaPoint(p) : getSigmaPoint(p)));

    setZoneSizeAndFireEvent(vnode, nextMain, !is2D);
    if (relative === -1) {
      const pos = { x: x - nextPointOffsetX, y: y - nextPointOffsetY };
      vnode.position = is2D
        ? get2DSigmaPoint(pos)
        : getSigmaPoint(pos);
    }
    core.update([vnode]);
  }
  function mouseUp() {
    doc.off('mousemove', mouseMove);
    doc.off('mouseup', mouseUp);
  }

  doc.on('mousemove', mouseMove);
  doc.on('mouseup', mouseUp);
};
