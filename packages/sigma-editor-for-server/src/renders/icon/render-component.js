import { SVG } from '@svgdotjs/svg.js';
import store from '../../store';
import { SIGMA_GRAPH_MODE_2D, STATIC_DEFAULT_2D_STROKE_COLOR } from '../../util/constants';
import { getFlatAndRouteTransform, getStylesVal, getValidIcon } from '../../util/tools';
import { ICON_SHAPE_ORIGINAL_SIZE } from './utils';

class IconRenderComponent {
  constructor({ vnode }) {
    this.vnode = vnode;
    this._reset();
    this._init();
  }
  _is3D = null;
  _style = null;
  _element = {};

  _init() {
    const { vnode } = this;
    const { key, position: { x, y } } = vnode;
    const component = new SVG().group()
      .attr({ key, id: key });
    const svg = new SVG('<svg></svg>').attr({
      x,
      y,
      overflow: 'visible',
      class: 'position',
    });
    component.add(svg);

    this._element.svg = svg;

    vnode.component = component;

    this._render();
  }

  _render() {
    this._reset();
    const { vnode, _style, _element } = this;
    const { isChecked } = vnode;
    const { width, height, scale, transform } = _style;
    const { main, svg } = _element;

    main.attr({ x: -(height / 2), y: -(width / 2) });
    const g1 = svg
      .group()
      .attr({ transform: `${transform} scale(${scale})`, class: 'g1' });

    const g2 = g1.group().attr('transform', 'scale(0.8)');

    const circle = g2.circle(height).attr({
      cx: 0,
      cy: 0,
      fill: 'transparent',
      class: 'circle',
    });

    g2.add(main);

    const rect = g1.rect(width, height).attr({
      x: -(width / 2),
      y: -(height / 2),
      stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
      fill: 'none',
      class: 'rect',
      'stroke-with': 4,
      opacity: isChecked ? 1 : 0,
    });

    _element.g1 = g1;
    _element.circle = circle;
    _element.rect = rect;
  }

  styleChange(data = {}) {
    this._reset({ initial: false, resetIcon: !!data.icon });
    const { _element, _style, vnode } = this;
    const { isChecked } = vnode;
    const { width, height, scale, transform } = _style;
    const { main, g1, circle, rect } = _element;

    main.attr({ x: -(height / 2), y: -(width / 2) });
    circle.attr({ r: height / 2 });
    rect.attr({
      width,
      height,
      x: -(width / 2),
      y: -(height / 2),
      opacity: isChecked ? 1 : 0,
    });
    g1.attr({ transform: `${transform} scale(${scale})` });
  }

  checkedChange() {
    this._element.rect.attr({
      opacity: this.vnode.isChecked ? 1 : 0,
    });
  }

  _reset(opts = {}) {
    const { styles, component } = this.vnode;
    this._is3D = store.getState().mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
    const { initial = true, resetIcon = false } = opts;

    const isFlat = getStylesVal(styles.isFlat);
    const route = getStylesVal(styles.route);
    const _scale = getStylesVal(styles.scale);
    const icon = getValidIcon(styles.icon);

    const transform = getFlatAndRouteTransform(
      isFlat,
      this._is3D,
      route,
      { x: 0, y: _scale * ICON_SHAPE_ORIGINAL_SIZE / 2 },
    );

    let main;

    // 初始化或重置宽高
    if (initial) {
      main = new SVG(icon).attr({ class: 'main' });
    } else if (resetIcon) {
      const oldMain = component.findOne('.main');
      main = new SVG(icon).attr({ class: 'main', data: icon });
      main.insertAfter(oldMain);
      oldMain.remove();
    } else {
      main = component.findOne('.main');
    }

    const height = parseInt(main.attr('height') || 48, 10);
    const width = parseInt(main.attr('width') || 48, 10);

    const scale = ((ICON_SHAPE_ORIGINAL_SIZE + 1) / height) * _scale;

    this.vnode.width = width * scale;
    this.vnode.height = height * scale;

    this._element.main = main;

    this._style = {
      // isFlat,
      // route,
      // icon,
      width,
      height,
      scale,
      transform,
    };
  }
}

export {
  IconRenderComponent,
};
