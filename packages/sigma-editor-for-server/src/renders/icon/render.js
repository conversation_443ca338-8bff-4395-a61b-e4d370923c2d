import { setPlaneShapeConnection } from '../../util/tools';
import { parseStyle } from './utils';
import { IconRenderComponent } from './render-component';
import ShapeManager from '../../shape/shapeManager';

const shapeManager = ShapeManager.getManager();

export const render = (core, vnode) => {
  vnode.renderComponent = null;
  vnode.renderComponent = new IconRenderComponent({ vnode });
  const { component } = vnode;

  // 设置延长线
  component.on('style-changed', (e) => {
    const { initial = false, data } = e.detail || {};
    if (!initial) {
      vnode.renderComponent.styleChange(data);
    }
    const { is3D, isFlat } = parseStyle(vnode);
    // connection
    setPlaneShapeConnection(vnode, is3D, isFlat);
    // 设置延长线
    core.lines.add(vnode, is3D, isFlat);
  });

  component.on('position-change', () => {
    shapeManager.remove([vnode.key]);
    const { position: { x, y } } = vnode;
    const { is3D, isFlat } = parseStyle(vnode);
    const svg = component.findOne('.position');

    svg.attr({ x, y });

    // 设置延长线
    core.lines.add(vnode, is3D, isFlat);
    core.lines.draw(vnode.key);
  });

  component.on('checked-change', () => {
    vnode.renderComponent.checkedChange();
  });

  component.on('attach-change', () => {
    const { is3D, isFlat } = parseStyle(vnode);
    // connection
    setPlaneShapeConnection(vnode, is3D, isFlat);
  });

  return component;
};

export const exports = async (core, vnode, mode) => render(core, vnode, mode);
