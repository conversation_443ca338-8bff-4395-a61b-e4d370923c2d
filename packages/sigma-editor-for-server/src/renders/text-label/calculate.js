import {
  transform3DTo2D,
  transform2DTo3D,
} from '../../util/tools';
import { SIGMA_GRAPH_MODE_3D, TRANSFORM_3D_MATRIX } from '../../util/constants';

export const transform = (vnode) => {
  vnode.position = transform3DTo2D(vnode.position);
  return vnode;
};

export const reduction = (vnode) => {
  vnode.position = transform2DTo3D(vnode.position);
  return vnode;
};

export const getflat = (vnode, mode) => {
  const {
    position: { x, y },
    component,
    width: w,
    height: h,
  } = vnode;
  if (component) {
    if (mode === SIGMA_GRAPH_MODE_3D) {
      const [a, b, c, d] = TRANSFORM_3D_MATRIX;
      const dots = [
        { x, y },
        { x: x + a * w, y: y + b * w },
        { x: x + a * w + c * h, y: y + b * w + d * h },
        { x: x + c * h, y: y + d * h },
      ];
      return dots;
    }
    return [
      { x, y },
      { x, y: y + h },
      { x: x + w, y: y + h },
      { x: x + w, y },
    ];
  }
  return [
    { x: 0, y: 0 },
    { x: 0, y: 0 },
    { x: 0, y: 0 },
    { x: 0, y: 0 },
  ];
};
