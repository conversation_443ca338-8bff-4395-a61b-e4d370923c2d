import { SIGMA_GRAPH_MODE_3D, SIGMA_LINE_SHAPE } from '../../util/constants';
import store from '../../store';

export const STROKE_WIDTH = 4;
/**
 *  3d，平面投影（flat），旋转（route）转换
 * 计算太过麻烦，直接通过拼接结果获取更简单。
 * @param {*} isFlat 正交投影
 * @param {*} is3D 是否为3D展示
 * @param {*} route 旋转角度
 * @returns 不同参数组合的 transfrom
 */
export function flatAnd3DAndRoutCrossProduct(
  isFlat = false,
  is3D = true,
  route = 'e',
) {
  if (is3D) {
    if (isFlat) {
      switch (route) {
        case 'e':
        case 'n':
          return 'matrix(0.707, 0.409, 0, 0.816, 0, 0) translate(26 0)';
        case 's':
        case 'w':
          return 'matrix(0.707, -0.409, 0, 0.816, 0, 0) translate(26 0)';
        default:
          console.error(`转换值错误，请检查设置的值: ${route}`);
      }
    } else {
      switch (route) {
        case 'e':
          return 'matrix(0.707 0.409 -0.707 0.409 0 0)';
        case 's':
          return 'matrix(0.707 -0.409 0.707 0.409 0 0)';
        case 'n':
          return 'matrix(-0.707 -0.409 0.707 -0.409 0 0)';
        case 'w':
          return 'matrix(-0.707 0.409 -0.707 -0.409 0 0)';
        default:
          console.error(`转换值错误，请检查设置的值: ${route}`);
      }
    }
  }
  switch (route) {
    case 'e':
      return '';
    case 's':
      return 'rotate(270)';
    case 'n':
      return 'rotate(180)';
    case 'w':
      return 'rotate(90)';
    default:
      console.error(`转换值错误，请检查设置的值: ${route}`);
  }
}

export function getXAlignStyles(xAlign) {
  switch (xAlign) {
    case 'left':
      return {
        tspanX: '0',
        textAnchor: 'start',
      };
    case 'center':
      return {
        tspanX: '50%',
        textAnchor: 'middle',
      };
    case 'right':
      return {
        tspanX: '100%',
        textAnchor: 'end',
      };
    default:
      return {
        tspanX: '50%',
        textAnchor: 'middle',
      };
  }
}

export function bindTspan(labels, tspanX, add, isProductLabel = false) {
  let { length } = labels;
  if (isProductLabel && length > 2) {
    // 当是产品节点且有超过三行的文本时
    length = 2;
  }
  const unit = 1.1;
  const base = length === 0 ? 0 : (length - 1) * (- unit / 2);
  for (let i = 0; i < length; i++) {
    const line =  add.tspan(labels[i]).attr({
      x: tspanX,
      y: '50%',
      dy: `${base + unit * i}em`,
    });
    if (isProductLabel) {
      line.attr({
        textLength: '135px',
      });
    }
  }
}

export function getTextLabelCommonCss(fontSize, textAnchor) {
  return {
    'font-weight': 'bold',
    'font-family': '\'pingfang SC\', \'helvetica neue\', arial, \'hiragino sans gb\',  \'microsoft yahei ui\', \'microsoft yahei\', simsun, sans-serif',
    'dominant-baseline': 'central',
    'font-size': `${fontSize}px`,
    'text-anchor': textAnchor,
  };
}

// 初始化的时候调用，转换旧的数据
export function initTextLabelProperty(vnode) {
  const { core } = store.getState();
  const { key, linkNodeKey } = vnode;
  if (linkNodeKey) {
    const { shapes } = core.data;
    const parent = shapes[linkNodeKey];
    if (!(parent && parent.linkTextLabel && parent.linkTextLabel === key)) {
      delete vnode.linkNodeKey;
      return;
    }
    vnode.attach = parent.key;
    parent.sticky = key;
    delete vnode.linkNodeKey;
    delete parent.linkTextLabel;
  }
}

export function bindTextLabelEvent(vnode) {
  const { core } = store.getState();
  const { shapes } = core.data;
  const { key, attach } = vnode;
  const parent = shapes[attach];
  if (!parent) return;

  const suffix = `.text-${key}`;

  parent.component?.off(suffix);
  parent.component?.on(`style-changed${suffix}`, () => {
    setStickyTextPosition(vnode);
  });
  parent.component?.on(`position-change${suffix}`, () => {
    setStickyTextPosition(vnode);
  });
  parent.component?.on(`update-position${suffix}`, () => {
    setStickyTextPosition(vnode);
  });

  parent.component.on(`attach-change${suffix}`, (e) => {
    const { type } = e.detail || {};
    type !== 'deleted' && setStickyTextPosition(vnode);
  });

  parent.component?.on(`deleted${suffix}`, (e) => {
    core.remove(vnode, e.detail);
  });
}

export function setStickyTextPosition(vnode) {
  const { core, mode } = store.getState();
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const { shapes } = core.data;
  const { attach } = vnode;
  const parent = shapes[attach];
  if (!parent) return;
  const {
    position: { x, y },
    data: { points },
  } = parent;

  // 连线的label位置计算
  if (parent.type === SIGMA_LINE_SHAPE) {
    // 根据label的位置百分比和连线长度算出距离
    const [position] = getExactPointOnPathByPercentage(vnode.positionPercent || 0.5, points);
    vnode.position = position;
  } else {
    // 其它节点的label位置计算
    const pc = is3D ? parent.connection : parent.d2_connection;
    const vc = is3D ? vnode.connection : vnode.d2_connection;
    const diffX = vc.b2.x - vc.t0.x;
    const diffY = vc.b2.y - vc.t0.y;
    vnode.position = {
      x: x + pc.b2.x - diffX,
      y: y + pc.b2.y - diffY,
    };
  }

  const { component } = vnode;
  component?.fire('position-change', {
    position: vnode.position,
  });
}

export function splitTextLabel(divElement) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.style.display = 'none';
  document.body.appendChild(canvas);

  // 获取 <div> 元素

  // 获取 <div> 中的文本内容
  const { textContent } = divElement;

  // 设置 canvas 的字体和宽度
  const computedStyle = getComputedStyle(divElement);
  const { fontSize } = computedStyle;
  const { fontFamily } = computedStyle;
  ctx.font = `${fontSize} ${fontFamily}`;
  const divWidth = divElement.offsetWidth;

  // 按照样式中的换行位置拆分文本
  const textArray = [];
  let currentLine = '';
  for (let i = 0; i < textContent.length; i++) {
    const char = textContent[i];
    currentLine += char;
    const lineWidth = ctx.measureText(currentLine).width;
    if (lineWidth >= divWidth || char === '\n') {
      textArray.push(currentLine.trim());
      currentLine = '';
    }
  }

  // 移除 canvas 元素
  document.body.removeChild(canvas);

  return textArray;
}

export function formatText(text, maxWidth, font, fontSize) {
  const lines = [];
  let currentLine = '';

  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  context.font = `bold ${fontSize} ${font}`; // 设置字体样式，包括 font-weight

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    currentLine += char;

    const metrics = context.measureText(currentLine);
    const { width } = metrics;

    if (width > maxWidth) {
      lines.push(currentLine.trim());
      currentLine = char;
    }
  }

  lines.push(currentLine.trim());

  return lines;
}
/**
 * 获取路径上给定百分比位置的精确坐标
 * @param {1-100} percentage 百分百
 * @param {Array<{x,y}>} polyline 路径点集
 * @returns {{x,y}} 目标点坐标
 * 示意图
 *  |                 路径               |
 *  |   给定位置百分比   |
 *  -------------------×-----------------
 *                     ↑ 目标点
 */
export function getExactPointOnPathByPercentage(percentage, polyline) {
  const totalLength = polyline.reduce((acc, cur, index, arr) => {
    if (index === arr.length - 1) return acc;
    const next = arr[index + 1];
    const dx = next.x - cur.x;
    const dy = next.y - cur.y;
    return acc + Math.sqrt(dx * dx + dy * dy);
  }, 0);

  const targetLength = totalLength * percentage;
  let currentLength = 0;

  for (let i = 0; i < polyline.length - 1; i++) {
    const p1 = polyline[i];
    const p2 = polyline[i + 1];
    const { x: x1, y: y1 } = p1;
    const { x: x2, y: y2 } = p2;

    const dx = x2 - x1;
    const dy = y2 - y1;
    const segmentLength = Math.sqrt(dx * dx + dy * dy);
    const remainingLength = targetLength - currentLength;

    if (segmentLength >= remainingLength) {
      const t = remainingLength / segmentLength;
      const x = x1 + t * dx;
      const y = y1 + t * dy;
      return [{ x, y }, polyline[i]];
    }

    currentLength += segmentLength;
  }

  return [polyline[polyline.length - 1], polyline[polyline.length - 2]];
}

/**
 * 获取给定坐标点在路径上的垂点（距离最近的点）占路径长度的百分比
 * @param {{x,y}} point 点
 * @param {Array<{x,y}>} polyline 路径点集
 * @returns {number} 垂位长度占比
 * 示意图
 *  |              路径总长度          |
 *  |   垂点路径占比  |
 *  ----------------×-----------------
 *                  ↑ 垂点
 *                  · 点在这
 */

export function getPointPercentInPath(point, polyline) {
  const lineLength = getPolylineLength(polyline);

  // 获取point在PathPoints路径上的垂直位置分割后的前半段路径
  const [pointPartPath] = getSplitPathByPoint(point, polyline);
  const pointPartLength = getPolylineLength(pointPartPath);

  return pointPartLength / lineLength;
}

function getPolylineLength(points) {
  let totalLength = 0;

  for (let i = 0; i < points.length - 1; i++) {
    const dx = points[i + 1].x - points[i].x;
    const dy = points[i + 1].y - points[i].y;
    const segmentLength = Math.sqrt(dx * dx + dy * dy);

    totalLength += segmentLength;
  }

  return totalLength;
}

/**
 * 获取给定坐标点在路径上的垂直位置（距离最近的点）把路径分割后的2段路径
 * @param {{x,y}} point 点
 * @param {Array<{x,y}>} polyline 路径点集
 * @returns {[Array<{x,y}>, Array<{x,y}>]} 分割后的2段路径
 * 示意图
 *  |              原路径              |
 *  |     路径1     |     路径2        |
 *  ----------------×-----------------
 *                  ↑ 垂点
 *                  · 点在这
 */
export function getSplitPathByPoint(point, polyline) {
  const [splitPos, index] = findClosestPointOnPolyline(point, polyline);
  const path1 = [];
  const path2 = [{ ...splitPos }];
  for (let i = 0; i <= index; i++) {
    path1.push(polyline[i]);
  }
  path1.push(splitPos);
  for (let i = index; i <= polyline.length; i++) {
    path2.push(polyline[i + 1]);
  }

  return [path1, path2];
}

/**
 * 获取给定坐标点在路径上的垂点（即距离最短的点）坐标
 * @param {{x,y}} point 点
 * @param {Array<{x,y}>} polyline 路径点集
 * @returns {[{x,y}, index]} [ 垂点坐标, 前一个点的索引 ]
 * 示意图
 *  |              原路径              |
 *  ----------------×-----------------
 *                  ↑ 垂点在这，算出这个点的坐标
 *                  · 点在这
 */
export function findClosestPointOnPolyline(point, polyline) {
  const { x: px, y: py } = point;
  let minDistance = Infinity;
  let closestPoint = null;
  let index = 0;

  for (let i = 0; i < polyline.length - 1; i++) {
    const p1 = polyline[i];
    const p2 = polyline[i + 1];
    const { x: x1, y: y1 } = p1;
    const { x: x2, y: y2 } = p2;

    const dx = x2 - x1;
    const dy = y2 - y1;
    const lineLengthSquared = dx * dx + dy * dy;
    const t = ((px - x1) * dx + (py - y1) * dy) / lineLengthSquared;

    let x; let y;
    if (t < 0) {
      x = x1;
      y = y1;
    } else if (t > 1) {
      x = x2;
      y = y2;
    } else {
      x = x1 + t * dx;
      y = y1 + t * dy;
    }

    const distanceSquared = (px - x) * (px - x) + (py - y) * (py - y);
    if (distanceSquared < minDistance) {
      minDistance = distanceSquared;
      closestPoint = { x, y };
      index = i;
    }
  }

  if (index === undefined) {
    debugger;
  }

  return [closestPoint, index];
}

/**
 * 把给定坐标点修正到距离路径一定距离之内
 * @param {{x,y}} point 点
 * @param {Array<{x,y}>} polyline 路径点集
 * @returns {[{x,y}, {x,y}, index]} 修正后坐标, 路径上的垂点, 路径上垂点前一个端点的索引
 */
export function getClosestPointWithinDistance(pointA, polyline, maxDistance = 10) {
  const [pointB, i] = findClosestPointOnPolyline(pointA, polyline);
  const dx = pointA.x - pointB.x;
  const dy = pointA.y - pointB.y;
  const distance = Math.sqrt(dx * dx + dy * dy);

  if (distance < maxDistance) {
    return [pointB, pointB, i];
  }
  const t = maxDistance / distance;
  const x = pointB.x + t * dx;
  const y = pointB.y + t * dy;

  const result = [{ x, y }, pointB, i];
  return result;
}

/**
 * 拖动时通过鼠标点控制连线的label位置，只能在连线上或者距离连线一定距离处
 * @param {{x,y}} mousePoint 鼠标坐标
 * @param {{width, height}} labelNode 文本节点，有宽高属性即可
 * @param {Array<{x,y}>} polyline 路径点集
 * @param {boolean} is3D 是否是3d渲染模式
 * @returns {{x,y}, 'center' | 'left' | 'right'} label目标坐标位置
 */
export function getLabelPositionAttachLine({
  point,
  labelNode,
  polyline,
  is3D,
}) {
  // 鼠标直接拖动label，以鼠标位置为准修改位置数据
  if (point) {
    const [fixPosition, closestPoint, prevPointIndex] = getClosestPointWithinDistance(point, polyline);
    const percent = getPointPercentInPath(closestPoint, polyline);
    const prevPoint = polyline[prevPointIndex];
    const side = fixPosition === closestPoint ? 'center' : getPointSideOfPath(fixPosition, closestPoint, prevPoint);
    const position = movePositionByDirection(
      fixPosition,
      closestPoint,
      labelNode,
      is3D,
    );
    return [position, percent, side];
  }
  // label跟随线条变化，保持位置百分比和对线偏移
  const side = labelNode.positionSide;
  const [closestPoint, prevPoint] = getExactPointOnPathByPercentage(
    labelNode.positionPercent,
    polyline,
  );
  const fixPosition = side === 'center' ? closestPoint : getPointBySideOfPath(prevPoint, closestPoint, side);
  const position = movePositionByDirection(
    fixPosition,
    closestPoint,
    labelNode,
    is3D,
  );
  return [position];
}

/**
 * 根据修正后的位置和垂点关系，挪动label位置，保证side=center时label骑在线上，为left或者right时不和线条重合
 * @param {{x,y}} fixPosition 限制距离后的鼠标位置
 * @param {{x,y}} closestPoint 鼠标位置到连线的垂点
 * @param {{width, height}} labelNode 文本节点，有宽高属性即可
 * @param {boolean} is3D 是否是3d渲染模式
 * @returns {{x,y}} 算出的合适的label右上角坐标
 */
function movePositionByDirection(fixPosition, closestPoint, labelNode, is3D) {
  const position = { ...fixPosition };
  const { width, height } = labelNode;

  if (is3D) {
    // 3D变换后的坐标，左上角位置不动，不用记录；
    const offset = {
      center: getPointOffsetAfterTransform(width / 2, height / 2), // 中心坐标
      rightTop: getPointOffsetAfterTransform(width, 0), // 右上角坐标
      rightBottom: getPointOffsetAfterTransform(width, height), // 右下角坐标
      leftBottom: getPointOffsetAfterTransform(0, height), // 左下角坐标
    };
    // 通过位置和垂点的夹角，来决定如何移动节点
    const defaultAngle = Math.atan(0.409 / 0.707) * (180 / Math.PI);
    const dy = fixPosition.y - closestPoint.y;
    const dx = fixPosition.x - closestPoint.x;
    const angle = Math.atan2(dy, dx);
    const angleDegrees = angle * (180 / Math.PI) - defaultAngle;

    if (dx === 0 && dy === 0) {
      // 没有角度，说明点重合，鼠标压着线，把矩形的中心点挪到垂点上
      position.x -= labelNode.width / 2;
      position.y -= labelNode.height / 2;
      position.x -= offset.center.x;
      position.y -= offset.center.y;
    } else {
      if (angleDegrees < -150 || angleDegrees > 90) {
        position.x -= width;
        position.x -= offset.rightTop.x;
        position.y -= offset.rightTop.y;
      } else if (angleDegrees < -90) {
        position.x -= width;
        position.y -= height;
        position.x -= offset.rightBottom.x;
        position.y -= offset.rightBottom.y;
      } else if (angleDegrees > -90 && angleDegrees < 30) {
        position.y -= height;
        position.x -= offset.leftBottom.x;
        position.y -= offset.leftBottom.y;
      }
    }

    return position;
  }

  if (fixPosition.x > closestPoint.x) {
    if (fixPosition.y > closestPoint.y) {
      // 位置在垂点右下角，不用动
    } else if (fixPosition.y === closestPoint.y) {
      // 位置在垂点正右侧，上移一半，使得文本节点中新和鼠标位置水平对齐
      position.y -= labelNode.height / 2;
    } else {
      // 位置在垂点右上侧，上移节点高度，使文本节点左下角和垂点保持固定距离
      position.y -= labelNode.height;
    }
  } else if (fixPosition.x === closestPoint.x) {
    if (fixPosition.y > closestPoint.y) {
      // 位置在垂点正下侧，上移节点高度，使文本节点左下角和垂点保持固定距离
      position.x -= labelNode.width / 2;
    } else if (fixPosition.y === closestPoint.y) {
      // 垂点和位置一样，说明压着线，把矩形的中心点挪到垂点上
      position.x -= labelNode.width / 2;
      position.y -= labelNode.height / 2;
    } else {
      // 位置在垂点正上方，左移一半，
      position.x -= labelNode.width / 2;
      position.y -= labelNode.height;
    }
  } else {
    if (fixPosition.y > closestPoint.y) {
      position.x -= labelNode.width;
    } else if (fixPosition.y === closestPoint.y) {
      position.x -= labelNode.width;
      position.y -= labelNode.height / 2;
    } else {
      position.x -= labelNode.width;
      position.y -= labelNode.height;
    }
  }

  return position;
};

// 已知a\b坐标，cb垂直于ab, c在ab的左右，求c坐标，
function getPointBySideOfPath(a, b, leftOrRight) {
  // Calculate the vector AB
  const ab = { x: b.x - a.x, y: b.y - a.y };

  // Normalize the vector AB
  const abNorm = Math.sqrt(ab.x * ab.x + ab.y * ab.y);
  ab.x /= abNorm;
  ab.y /= abNorm;

  // Find a vector perpendicular to AB
  const perp = leftOrRight === 'left' ? { x: ab.y, y: -ab.x } : { x: -ab.y, y: ab.x };

  // Multiply the perpendicular vector by the distance
  perp.x *= 10;
  perp.y *= 10;

  // Add the perpendicular vector to B to find C
  const c = { x: b.x + perp.x, y: b.y + perp.y };

  return c;
};

// 已知a\b\c坐标，求c在ab的左还是右，
function getPointSideOfPath(a, b, c) {
  // Calculate vectors AB and AC
  const ab = { x: b.x - a.x, y: b.y - a.y };
  const ac = { x: c.x - a.x, y: c.y - a.y };

  // Calculate the cross product of AB and AC
  const cross = ab.x * ac.y - ab.y * ac.x;

  // Return 'left' if cross product is positive, 'right' if negative
  return cross > 0 ? 'left' : 'right';
}

// 求转换后的坐标值的变化量，matrix默认为文本节点的转换矩阵
function getPointOffsetAfterTransform(x, y, matrix = [0.707, 0.409, -0.707, 0.409, 0, 0]) {
  // 解构矩阵参数
  const [a, b, c, d, e, f] = matrix;

  // 应用矩阵变换
  const newX = a * x + c * y + e;
  const newY = b * x + d * y + f;

  // 返回转换后的坐标变化量
  return { x: newX - x, y: newY - y };
}
