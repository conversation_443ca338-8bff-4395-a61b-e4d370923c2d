import { SVG } from '@svgdotjs/svg.js';
import { getStylesVal, setPlaneShapeConnection, isLineLabel } from '../../util/tools';
import {
  BASE_GRID_2D,
  BASE_HALF_GRID_2D,
  SIGMA_GRAPH_MODE_2D,
} from '../../util/constants';
import {
  STROKE_WIDTH,
  flatAnd3DAndRoutCrossProduct,
  bindTspan,
  getXAlignStyles,
  getTextLabelCommonCss,
  initTextLabelProperty,
  bindTextLabelEvent,
  setStickyTextPosition,
  formatText,
  getLabelPositionAttachLine,
} from './utils';
import EditTextLabel from '../../affix/edit-text-label';
import ShapeManager from '../../shape/shapeManager';

const shapeManager = ShapeManager.getManager();

export const render = (core, vnode, mode) => {
  const {
    key,
    position: { x, y },
  } = vnode;

  initTextLabelProperty(vnode);

  const component = new SVG().group()
    .attr({ key, id: key });
  const svg = new SVG('<svg></svg>').attr({
    x,
    y,
    overflow: 'visible',
    class: 'position',
  });
  component.add(svg);

  const startPoint = { x: 0, y: 0 };
  let outlineTextSvg = null;
  let rectSvg = null;
  let editTextLabel = null;
  let resetText = '';

  component.on('style-changed', (event) => {
    const { styles, initial, isExport = false } = event.detail || {};
    if (initial && !isExport) bindTextLabelEvent(vnode);

    const { isChecked } = vnode;
    const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
    let label = getStylesVal(styles.label);
    const fill = getStylesVal(styles.fill);
    const fontSize = getStylesVal(styles.fontSize);
    const isFlat = getStylesVal(styles.isFlat);
    const route = getStylesVal(styles.route);
    const outline = getStylesVal(styles.outline);
    const xAlign = getStylesVal(styles.xAlign);
    const { tspanX, textAnchor } = getXAlignStyles(xAlign);
    const commonCss = getTextLabelCommonCss(fontSize, textAnchor);

    // 渲染 text
    const transform = flatAnd3DAndRoutCrossProduct(
      isFlat,
      is3D,
      route,
    );
    if (vnode?.attach) {
      // 处理产品节点中的文本，老数据把换行符改成空格
      label = label?.replace(/\n/g, ' ');
    }
    let labels = label.split(/[\n]+/);
    if (labels.length === 1 && vnode?.attach) {
      const rs = formatText(labels[0], 135, commonCss['font-family'], commonCss['font-size']);
      labels = rs;
    }
    let group = component.findOne('.g1');
    if (!group) {
      // render 新建
      group = svg.group().attr({ transform, class: 'g1' });
      const replaceLabels = labels?.map(item => item.replace(/\s+/g, '\u3000'));
      // text outline
      if (outline) {
        outlineTextSvg = group
          .text((add) => {
            bindTspan(isExport ? replaceLabels : labels, tspanX, add, !!(vnode?.attach));
          })
          .attr({
            stroke: '#fff',
            class: 'text-outline',
            'stroke-width': STROKE_WIDTH,
            ...commonCss,
          });
      }
      if (isExport && !(vnode?.attach)) {
        // text 仅导入时用text渲染文本，foreignObject在xml中不显示
        group
          .text((add) => {
            bindTspan(replaceLabels, tspanX, add, !!(vnode?.attach));
          })
          .attr({
            fill,
            class: 'text',
            ...commonCss,
          });
      }
      resetText = label;
    } else {
      // rerender 重绘
      const textOutline = component.findOne('.text-outline');
      const text = component.findOne('.text');
      group.attr({ transform });
      if (outline) {
        if (textOutline) {
          outlineTextSvg = textOutline
            .text((add) => {
              bindTspan(labels, tspanX, add, !!(vnode?.attach));
            })
            .attr({ fill, 'font-size': `${fontSize}px`, 'text-anchor': textAnchor });
        } else {
          const _outline = new SVG()
            .text((add) => {
              bindTspan(labels, tspanX, add, !!(vnode?.attach));
            })
            .attr({
              stroke: '#ffffff',
              class: 'text-outline',
              'stroke-width': STROKE_WIDTH,
              ...commonCss,
            });

          _outline.insertBefore(text);
          outlineTextSvg = _outline;
        }
      }

      if (isExport && !(vnode?.attach)) {
        text
          .text((add) => {
            bindTspan(labels, tspanX, add, !!(vnode?.attach));
          })
          .attr({ fill, 'font-size': `${fontSize}px`, 'text-anchor': textAnchor });
      }
      if (editTextLabel) {
        editTextLabel.setText(label);
      }
    }
    // 刷新宽度和高度
    const text = component.findOne('.text-outline');
    const bbox = text.bbox();
    let svgWidth = vnode?.attach ? Math.ceil(bbox.w) : Math.ceil(bbox.w / BASE_GRID_2D) * BASE_GRID_2D;
    const svgHeight = vnode?.attach ? Math.ceil(bbox.h) : Math.ceil(bbox.h / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D;
    if (isLineLabel(vnode, core)) {
      svgWidth = 135;
    }
    svg.width(svgWidth).height(svgHeight);
    // border
    let rect = component.findOne('.rect');
    if (!rect) {
      // 初始化
      rect = group.rect(svgWidth, svgHeight).attr({
        class: 'rect',
        'stroke-width': STROKE_WIDTH,
        stroke: isChecked ? '#006eff' : '',
        fill: 'none',
      });
      // 将rect挪到最前面，避免其有颜色时压住text
      group.put(rect, 0);
    } else {
      rect.attr({
        width: svgWidth,
        height: svgHeight,
      });
    }
    rectSvg = rect;
    // connection、lines、movablePoints
    vnode.width = svgWidth;
    vnode.height = svgHeight;

    setPlaneShapeConnection(vnode, is3D, isFlat, startPoint);
    setStickyTextPosition(vnode);

    if (!vnode.attach) {
      core.lines.add(vnode, is3D, isFlat, startPoint);
    }

    if (!editTextLabel) {
      if (isExport && !(vnode?.attach)) {
        return;
      }
      outlineTextSvg.node.style.visibility = 'hidden';
      const { w: rw, h: rh } = rectSvg.bbox();
      const productLabelStyle = {
        ...commonCss,
        display: 'block',
        width: '100%',
        height: 'max-content',
        lineHeight: commonCss['font-size'],
        color: fill,
        textAlign: xAlign,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        '-webkit-line-clamp': 2,
        '-webkit-box-orient': 'vertical',
        whiteSpace: 'pre-wrap',
        wordWrap: 'break-word',
        paddingTop: '3px',
      };
      const textLabelStyle = {
        ...commonCss,
        width: '100%',
        height: '100%',
        lineHeight: commonCss['font-size'],
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexWrap: 'wrap',
        color: fill,
        textAlign: xAlign,
      };
      editTextLabel = new EditTextLabel({
        wrapper: group,
        text: label,
        width: rw,
        height: rh,
        maxLength: 128,
        initStyles: vnode?.attach ? productLabelStyle : textLabelStyle,
        isEnterSubmit: !!vnode?.attach,
        initForeignStyles: {
          textAlign: 'center',
          textShadow: '-1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff',
        },
        dblClickDom: component,
        onblur: (textdiv) => {
          rectSvg.node.style.visibility = 'initial';
          if (vnode?.attach) {
            editTextLabel.setTextStyle({
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              ['-webkit-line-clamp']: 2,
              ['-webkit-box-orient']: 'vertical',
            });
          }
          if (textdiv.innerText.length < 1) {
            core.setStyles({
              label: {
                value: resetText,
              },
            }, vnode.key);
            textdiv.innerText = resetText;
          } else {
            core.setStyles({
              label: {
                value: textdiv.innerText,
              },
            }, vnode.key);
          }
        },
        ondblclick: (textdiv) => {
          rectSvg.node.style.visibility = 'hidden';
          if (vnode?.attach) {
            // 编辑模式下移除省略相关样式
            textdiv.style.removeProperty('overflow');
            textdiv.style.removeProperty('textOverflow');
            textdiv.style.removeProperty('display');
            textdiv.style.removeProperty('-webkit-line-clamp');
            textdiv.style.removeProperty('-webkit-box-orient');
          }
        },
        onSave: () => {
          core?.options?.callbacks?.onDbClickSaveText?.(vnode);
        },
      });
    }

    if (editTextLabel && !isExport) {
      const { w: rw, h: rh } = rectSvg.bbox();
      editTextLabel.setforeignAttr({
        width: rw,
        height: rh,
      });
      editTextLabel.setTextStyle({
        ...commonCss,
        lineHeight: commonCss['font-size'],
        textAlign: xAlign,
        color: fill,
      });
    }
  });

  component.on('position-change', (e) => {
    shapeManager.remove([vnode.key]);
    const { component, styles, attach, position: { x, y } } = vnode;
    const pos = component.findOne('.position');

    if (isLineLabel(vnode, core)) {
      const { mousePosition } = e.detail;
      const parent = core.data.shapes[vnode.attach];
      const { points: polyline } = parent.data;
      const labelNode = vnode;
      const is3D = mode !== SIGMA_GRAPH_MODE_2D;
      let position;
      // 有mousePosition说明是鼠标直接拖动label，以鼠标位置为准修改位置数据
      if (mousePosition) {
        const [p, percent, side] = getLabelPositionAttachLine({ point: mousePosition, labelNode, polyline, is3D });
        vnode.positionSide = side;
        vnode.positionPercent = percent;
        position = p;
      } else {
        // 没有mousePosition说明是attach元素变化导致label位置变化，保持位置百分比和对线偏移
        const [p] = getLabelPositionAttachLine({ labelNode, polyline, is3D });
        position = p;
      }
      vnode.position = position;
      pos.attr(position);
    } else {
      pos.attr({ x, y });
    }

    if (!attach) {
      const is3D = mode === SIGMA_GRAPH_MODE_2D ? false : getStylesVal(styles.is3d);
      const isFlat = getStylesVal(styles.isFlat);

      // 设置延长线
      core.lines.add(vnode, is3D, isFlat, startPoint);
      core.lines.draw(vnode.key);
    }
  });

  component.on('checked-change', () => {
    const { isChecked } = vnode;
    const rect = component.findOne('.rect');

    rect.attr({
      stroke: isChecked ? '#006eff' : '',
    });
  });

  component.on('deleted', () => {
    const { shapes } = core.data;
    const { attach } = vnode;
    const parent = shapes[attach];
    parent && delete parent.sticky;
  });

  component.on('start-edit', () => {
    editTextLabel?.startEdit();
  });

  vnode.component = component;
  return component;
};

export const exports = async (core, vnode, mode) => render(core, vnode, mode);
