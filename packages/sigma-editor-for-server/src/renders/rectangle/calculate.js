import { transform3DTo2D, transform2DTo3D } from '../../util/tools';

export const transform = (vnode) => {
  vnode.position = transform3DTo2D(vnode.position);
  return vnode;
};

export const reduction = (vnode) => {
  vnode.position = transform2DTo3D(vnode.position);
  return vnode;
};

export const getflat = (vnode) => {
  const { resizePoints, position: { x, y } } = vnode;
  const { map } = resizePoints;
  const output = [];
  for (const key in map) {
    if (!/-/.test(key)) continue;
    const { position: p } = map[key];
    output.push({
      x: x + p.x,
      y: y + p.y,
    });
  }
  return output;
};
