import { ResizePoints } from '../../affix/resize-element';
import store from '../../store';
import { TRANSFORM_3D_MATRIX } from '../../util/constants';
import { transform2DTo3D } from '../../util/tools';

export const RECT_DEFAULT_TITLE_FONT_SIZE = 14;
export const RECT_DEFAULT_ICON_SIZE = 32;
export const RECT_DEFAULT_HEAD_GAP = 6;

export function getNodeAlignStandard({
  boxW,
  boxH,
  childW,
  childH,
  xAlign = 'left',
  position = 'inside',
  location = 'top',
  is3D,
}) {
  let x;
  let y;
  let transform = '';

  switch (xAlign) {
    case 'left':
      x = RECT_DEFAULT_HEAD_GAP;
      break;
    case 'center':
      x = (boxW - childW) / 2;
      break;
    case 'right':
      x = boxW - RECT_DEFAULT_HEAD_GAP - childW;
      break;
    default:
      x = RECT_DEFAULT_HEAD_GAP;
  }

  switch (location) {
    case 'top': {
      y = RECT_DEFAULT_HEAD_GAP;
      switch (position) {
        case 'inside':
          y = RECT_DEFAULT_HEAD_GAP;
          break;
        case 'border':
          y = - childH / 2;
          break;
        case 'outside':
          y = - childH - RECT_DEFAULT_HEAD_GAP;
          break;
        case 'default':
          y = RECT_DEFAULT_HEAD_GAP;
      }
      break;
    }
    case 'bottom': {
      y = boxH - RECT_DEFAULT_HEAD_GAP - childH;
      switch (position) {
        case 'inside':
          y = boxH - RECT_DEFAULT_HEAD_GAP - childH;
          break;
        case 'border':
          y = boxH - childH / 2;
          break;
        case 'outside':
          y = boxH + RECT_DEFAULT_HEAD_GAP;
          break;
        case 'default':
          y = RECT_DEFAULT_HEAD_GAP;
      }
      break;
    }
    default:
      y = RECT_DEFAULT_HEAD_GAP;
  }

  if (is3D) {
    const start = transform2DTo3D({ x, y }, true);
    x = start.x;
    y = start.y;
    transform += `matrix(${TRANSFORM_3D_MATRIX.join(' ')}) `;
  }


  return { x, y, transform };
}

export function handleVnodeResizePoints(vnode, initial, core) {
  setTimeout(() => {
    const { uneditable } = store.getState();
    const { points, canResize } = vnode.data;
    if (canResize && !uneditable) {
      if (initial || !vnode.resizePoints) {
        vnode.resizePoints = null;
        vnode.resizePoints = new ResizePoints({ vnode, shapes: core?.data?.shapes });
      } else {
        vnode.resizePoints.update(points);
      }
      vnode.isChecked ? vnode.resizePoints.show() : vnode.resizePoints.hide();
    } else {
      vnode.resizePoints?.hide();
      vnode.resizePoints = null;
    }
  }, 0);
}


export function bindTspan(labels, tspanX, add) {
  const { length } = labels;
  const unit = 1.1;
  const base = length === 0 ? 0 : (length - 1) * (- unit / 2);
  for (let i = 0; i < length; i++) {
    add.tspan(labels[i]).attr({
      x: tspanX,
      y: length * 8,
      dy: `${base + unit * i}em`,
    });
  }
}
