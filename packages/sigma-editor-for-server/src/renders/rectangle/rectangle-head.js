import { Line, SVG } from '@svgdotjs/svg.js';
import { getStylesVal, getValidIcon } from '../../util/tools';
import {
  getNodeAlignStandard,
} from './utils';
import store from '../../store';
import { SIGMA_GRAPH_MODE_3D, TRANSFORM_3D_MATRIX } from '../../util/constants';
import EditTextLabel from '../../affix/edit-text-label';

class RectangleHead {
  constructor(vnode) {
    this.vnode = vnode;
    this.elements = {};
    this.editTextLabel = null;
    this.core = store.getState().core;
    this.resetText = '';
    this.textLabel = null;
    this._initStyles();
  }

  _initStyles() {
    const { styles } = this.vnode;
    const icon = getValidIcon(styles.icon);
    const iconSize = getStylesVal(styles.iconSize);
    const label = getStylesVal(styles.label);
    const xAlign = getStylesVal(styles.xAlign);
    const yAlign = getStylesVal(styles.yAlign);
    const location = getStylesVal(styles.location);
    const position = getStylesVal(styles.position);
    const fontSize = getStylesVal(styles.fontSize);
    const color = getStylesVal(styles.color);
    this._styles = {
      icon,
      iconSize,
      label,
      xAlign,
      yAlign,
      location,
      position,
      fontSize,
      color,
    };
  }

  create(svg, boxInfo) {
    const { elements } = this;
    this.elements.svg = svg;
    // head
    elements.head = new SVG().attr({
      class: 'head',
      style: 'overflow: auto;',
    });

    svg.add(elements.head);

    // 边框标题遮挡
    elements.divide = this._createDivide();
    elements.head.add(elements.divide);

    // wrapper
    elements.headWrapper = elements.head.group().attr({ class: 'head-wrapper' });


    // icon
    if (this._styles.icon) {
      elements.icon = this._createIcon();
      elements.headWrapper.add(elements.icon);
    }

    // label
    this._createLabel(boxInfo);
    this.resetText = this._styles.label.replace(/\n/g, '\n');;

    // 定位，由于这个方法很耗时，所以放到微任务里
    queueMicrotask(() => {
      this._setHeadPosition();
    });
  }

  _createIcon() {
    const { icon, iconSize } = this._styles;
    return new SVG(icon).attr({
      class: 'icon',
      width: iconSize,
      height: iconSize,
    });
  }

  _createLabel(boxInfo) {
    const { label, iconSize, icon, fontSize, color, xAlign, yAlign } = this._styles;
    const inlineStyle = {
      height: '100%',
      width: '100%',
      display: 'inline-block',
      fontSize: `${fontSize}px`,
      color,
      fontWeight: 'bold',
      lineHeight: `${iconSize}px`,
      textAlign: xAlign,
      verticalAlign: 'super',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      yAlign: yAlign || 'top',
    };
    const blockStyle = {
      display: 'block',
      height: 'max-content',
      width: '100%',
      fontSize: `${fontSize}px`,
      color,
      fontWeight: 'bold',
      lineHeight: 1.3,
      overflow: 'hidden',
      whiteSpace: 'pre-wrap',
      wordWrap: 'break-word',
      textAlign: xAlign,
      yAlign: yAlign || 'top',
    };
    if (!this.textLabel) {
      let replacedContent = label?.replace(/\n/g, '\n');
      if (boxInfo?.isExport) {
        replacedContent = label?.replace(/\n/g, '\n')?.replace(/\s+/g, ' ')
          ?.replace(/nbsp;+/g, ' ');
      }
      const textLabel = new EditTextLabel({
        wrapper: this.elements.headWrapper,
        width: boxInfo?.boxWidth,
        height: boxInfo?.boxHeight,
        innerHTML: replacedContent,
        initStyles: boxInfo.isBlock ? blockStyle : inlineStyle,
        maxLength: boxInfo.isBlock ? undefined : 128,
        isInline: !boxInfo.isBlock,
        isHidden: boxInfo.isBlock,
        onblur: (textdiv) => {
          if (textdiv.innerText.length < 1) {
            this.core.setStyles({
              label: {
                value: this.resetText,
              },
            }, this.vnode.key);
            textdiv.innerText = this.resetText;
          } else {
            this.core.setStyles({
              label: {
                value: textdiv.innerText?.replace(/\n/g, '\n'),
              },
            }, this.vnode.key);
          }
          if (!(boxInfo.isBlock)) {
            textdiv.style.width = '100%';
          }
        },
        onSave: () => {
          this.core?.options?.callbacks?.onDbClickSaveText?.(this.vnode);
        },
      });
      this.textLabel = textLabel;
      if (icon) {
        this.textLabel.setforeignAttr(this._filterWidthIcon(icon, {
          x: iconSize + 8,
        }));
      }
    }
  }

  _createDivide() {
    return new Line({
      stroke: '#fff',
      'stroke-width': 4,
      opacity: 0,
    });
  }

  _setHeadPosition() {
    const { mode } = store.getState();
    const is3D = mode === SIGMA_GRAPH_MODE_3D;
    const { elements, vnode, _styles } = this;
    const { iconSize, xAlign, location, position } = _styles;
    if (is3D) elements.headWrapper.attr({ transform: '' });
    const { w, h } = elements.headWrapper.bbox();

    // 遮挡住分割线
    if (position === 'border') {
      elements.divide.attr({
        x1: -4,
        y1: h / 2,
        x2: w + 4,
        y2: h / 2,
        opacity: 1,
        transform: is3D ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')})` : '',
      });
    } else {
      elements.divide.attr({
        opacity: 0,
      });
    }

    const { x, y, transform } = getNodeAlignStandard({
      boxW: vnode.width,
      boxH: vnode.height,
      childW: w,
      childH: h,
      iconSize,
      xAlign,
      position,
      location,
      is3D,
    });
    elements.head.x(x).y(y);
    elements.headWrapper.attr({ transform });
  }
  _filterWidthIcon(exist, data) {
    return exist ? data : {};
  }

  update(data = {}, boxInfo) {
    const { _styles, elements } = this;
    const { icon } = elements;
    if (data.iconSize) {
      _styles.iconSize = getStylesVal(data.iconSize);
      if (_styles.icon) {
        elements.icon.attr({
          width: _styles.iconSize,
          height: _styles.iconSize,
        });
        this.textLabel.setforeignAttr(this._filterWidthIcon(icon, {
          height: _styles.iconSize,
          x: _styles.iconSize + 8,
        }));
        this.textLabel.setTextStyle({
          lineHeight: `${_styles.iconSize}px`,
        });
      }
    }

    if (data.fontSize) {
      const fontSizeValue = getStylesVal(data.fontSize);
      this.textLabel.setTextStyle({
        fontSize: `${fontSizeValue}px`,
        lineHeight: `${fontSizeValue}px`,
      });
      if (icon) {
        elements.icon.attr({
          width: fontSizeValue,
          height: fontSizeValue,
        });
      }
      this.textLabel.setforeignAttr(this._filterWidthIcon(icon, {
        height: fontSizeValue,
        x: fontSizeValue + 8,
      }));
      this.vnode.styles.iconSize.value = fontSizeValue;
    }

    if (data.color) {
      this.textLabel.setTextStyle({
        color: getStylesVal(data.color),
      });
    }

    if (data.icon) {
      _styles.icon = getValidIcon(data.icon);
      elements.icon?.remove();

      if (!_styles.icon) {
        elements.icon = null;
        // 没有图标的时候更新 Label位置
        this.textLabel.setforeignAttr(this._filterWidthIcon(icon, {
          x: 8,
        }));
      } else {
        const newIcon = this._createIcon();
        newIcon.insertBefore(this.textLabel.myforeign);
        elements.icon = newIcon;

        // 图标的时候更新 Label位置
        this.textLabel.setforeignAttr(this._filterWidthIcon(icon, {
          x: _styles.iconSize + 8,
        }));
      }
    }

    if (data.label) {
      _styles.label = getStylesVal(data.label);
      this._createLabel(boxInfo);
      this.textLabel.setInnerHtml(_styles.label?.replace(/\n/g, '\n'));
    }

    if (data.xAlign) {
      _styles.xAlign = getStylesVal(data.xAlign);
      if (boxInfo) {
        this.textLabel.setTextStyle({ textAlign: _styles.xAlign });
      }
    }

    if (data?.yAlign) {
      // 垂直对齐方式
      _styles.yAlign = getStylesVal(data.yAlign);
      if (boxInfo) {
        this.textLabel.setTextYAlign(data.yAlign);
      }
    }

    if (data.location) _styles.location = getStylesVal(data.location);
    if (data.position) _styles.position = getStylesVal(data.position);
    if (boxInfo && !data.fontSize) {
      this.textLabel.setforeignAttr({ width: boxInfo.boxWidth, height: boxInfo.boxHeight });
    }
    this._setHeadPosition();
  }
}

export {
  RectangleHead,
};
