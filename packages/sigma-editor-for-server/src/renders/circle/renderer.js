import { SVG } from '@svgdotjs/svg.js';
import { getSigmaPoint, get2DSigmaPoint, getStylesVal } from '../../util/tools';
import {
  STATIC_CHECKED_FILL_COLOR,
  SIGMA_GRAPH_MODE_3D,
  getEmptyConnections,
} from '../../util/constants';

function initConnections(vnode) {
  const { connection, d2_connection } = vnode;
  const isValid = c => c && typeof c === 'object' && !Array.isArray(c) && Object.keys(c).length === 16;
  if (!(isValid(connection) && isValid(d2_connection))) {
    vnode.connection = getEmptyConnections();
    vnode.d2_connection = getEmptyConnections();
  }
}

export const render = (core, vnode, mode) => {
  initConnections(vnode);

  const { key, isChecked, position, styles = {} } = vnode;
  const color = getStylesVal(styles.color) || '#000000';
  const opacity = getStylesVal(styles.opacity) || 1;
  const radius = getStylesVal(styles.radius) || 4;
  const hide = getStylesVal(styles.hide) || false;
  const component = new SVG().group()
    .attr({ key, id: key });
  const g = component.group();
  const { x, y } = mode === SIGMA_GRAPH_MODE_3D
    ? getSigmaPoint(position)
    : get2DSigmaPoint(position);

  g.circle().attr({
    cx: x,
    cy: y,
    class: 'c1',
    r: radius,
    fill: isChecked ? STATIC_CHECKED_FILL_COLOR : color,
    opacity: hide ? 0 : opacity,
  });
  g.circle().attr({
    cx: x,
    cy: y,
    r: 18,
    class: 'c2',
    opacity: isChecked ? 0.1 : 0,
  });
  component.on('dblclick', () => pointDBClick(core, vnode));

  vnode.component = component;
  return component;
};

export const rerender = (_, vnode, mode) => {
  const { isChecked, position, component, styles = {} } = vnode;
  const color = getStylesVal(styles.color) || '#000000';
  const opacity = getStylesVal(styles.opacity) || 1;
  const radius = getStylesVal(styles.radius) || 4;
  const hide = getStylesVal(styles.hide) || false;

  const c1 = component.findOne('.c1');
  const c2 = component.findOne('.c2');
  const { x, y } = mode === SIGMA_GRAPH_MODE_3D
    ? getSigmaPoint(position)
    : get2DSigmaPoint(position);
  c1.attr({
    cx: x,
    cy: y,
    fill: isChecked ? STATIC_CHECKED_FILL_COLOR : color,
    r: radius,
    opacity: hide ? 0 : opacity,
  });
  const c2Attr = { cx: x, cy: y };
  c2Attr.opacity = isChecked ? 0.1 : 0;
  c2.attr(c2Attr);
};

export const exports = async (core, vnode, mode) => render(core, vnode, mode);

function pointDBClick(core, vnode) {
  core.remove([vnode]);
}
