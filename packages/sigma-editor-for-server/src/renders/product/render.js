import { SVG } from '@svgdotjs/svg.js';
import { getStylesVal } from '../../util/tools';
import {
  SIGMA_GRAPH_MODE_3D,
  STATIC_DEFAULT_2D_STROKE_COLOR,
  STATIC_COMPONENT_OPACITY,
} from '../../util/constants';
import {
  generate3dShape,
  render3dComponent,
  PRODUCT_SHAPE_SIZE,
  PRODUCT_SHAPE_OFFSET_X,
  PRODUCT_SHAPE_OFFSET_Y,
  replaceFillDarkColor,
  PRODUCT_MAIN_COMMON_STYLES,
  setProductConnection,
} from './utils';
import store from '../../store';
import ShapeManager from '../../shape/shapeManager';

const shapeManager = ShapeManager.getManager();

export const render = (core, vnode, mode) => {
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const component = is3D ? _3dRender(vnode) : _2dRender(vnode);
  // 设置延长线
  const startPoint = is3D ? { x: PRODUCT_SHAPE_OFFSET_X, y: PRODUCT_SHAPE_OFFSET_Y } : { x: 0, y: 0 };

  if (!(vnode.width && vnode.height)) {
    vnode.width = PRODUCT_SHAPE_SIZE;
    vnode.height = PRODUCT_SHAPE_SIZE;
  }

  component.on('style-changed', (event) => {
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { styles, isChecked, customize = {} } = vnode;
    const opacity = getStylesVal(styles.opacity) ?? STATIC_COMPONENT_OPACITY;
    const hasBind = customize?.hasResource;
    if (is3D) {
      if (!event?.detail?.initial) {
        render3dComponent(component, styles, isChecked, hasBind);
      }
    } else {
      const baseStrokeColor = getStylesVal(styles.baseStroke);
      const baseBorderStrokeColor = getStylesVal(styles.baseBorderStroke);
      const baseBgColor = getStylesVal(styles.baseBgColor);
      const rect = component.findOne('.rect');
      rect.attr({
        fill: baseBgColor || baseStrokeColor,
        stroke: baseBorderStrokeColor || baseStrokeColor,
      });
      // 如果是未定义，则使用filldark的值，否则根据是否绑定资源设置filldark
      const fillDarkColor = hasBind === undefined ? getStylesVal(styles.fillDark) : hasBind ? '#006EFF' : '#888888';
      const main = component.findOne('.main');
      const d2 = replaceFillDarkColor(vnode.data.d2, fillDarkColor);
      const newMain = new SVG(d2).attr(PRODUCT_MAIN_COMMON_STYLES);
      newMain.insertAfter(main);
      main.remove();
      if (isChecked) {
        rect.attr({
          'stroke-width': 2,
          stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
        });
      }
    }
    component.attr({
      opacity,
    });
    setProductConnection(vnode);
    core.lines.add(vnode, is3D, false, startPoint);
  });

  component.on('attach-change', () => {
    setProductConnection(vnode);
  });

  component.on('checked-change', () => {
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { isChecked, styles, customize = {} } = vnode;
    const hasBind = customize?.hasResource;
    if (is3D) {
      render3dComponent(component, styles, isChecked, hasBind);
    } else {
      const rect = component.findOne('.rect');
      if (!rect) return;
      if (isChecked) {
        rect.attr({
          'stroke-width': 2,
          stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
        });
      } else {
        const baseBorderStrokeColor = getStylesVal(styles.baseBorderStroke);
        rect.attr({
          'stroke-width': 2,
          stroke: baseBorderStrokeColor || '#C1C6C8',
        });
      }
    }
  });

  component.on('position-change', (e) => {
    shapeManager.remove([vnode.key]);
    const { shapes } = core.data;
    const { movingLineKeys = [] } = e.detail;
    // 移动节点时找到节点是否有连线，让其恢复自动寻路
    const lineShape = Object.keys(shapes)?.filter(k => shapes[k]?.type === 'SIGMA_LINE_SHAPE'
      && (shapes[k]?.data?.start?.vkey === vnode?.key || shapes[k]?.data?.end?.vkey === vnode?.key)
      && !shapes[k].isChecked
      && !movingLineKeys?.includes(shapes[k]?.key));
    if (lineShape?.length > 0) {
      lineShape?.forEach((lineKey) => {
        shapes[lineKey].hasDragged = false;
      });
    }

    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const pos = component.findOne('.position');
    const { position: { x, y }, positionOffsetY = 0 } = vnode;
    pos.attr(is3D ? {
      x,
      y: y + positionOffsetY,
    } : { x, y });

    // 设置延长线
    core.lines.add(vnode, is3D, false, startPoint);
    core.lines.draw(vnode.key);
  });

  vnode.component = component;
  return component;
};

const _3dRender = (vnode) => {
  const {
    key,
    position: { x, y },
    positionOffsetY = 0,
    data,
    isChecked,
    styles,
  } = vnode;
  styles.fillDark.default = '#E2E6EC';

  const component = new SVG().group()
    .attr({ key, id: key });
  const main = new SVG(generate3dShape(data.d3));
  component.add(main);
  main.attr({ x, y: y + positionOffsetY, class: 'position' });
  const hasBind = vnode?.customize?.hasResource;
  render3dComponent(component, styles, isChecked, hasBind);

  vnode.component = component;
  return component;
};

const _2dRender = (vnode) => {
  const {
    key,
    position: { x, y },
    data,
    isChecked,
    styles,
  } = vnode;

  styles.fillDark.default = STATIC_DEFAULT_2D_STROKE_COLOR;

  const fillDarkColor = getStylesVal(styles.fillDark);
  const baseStrokeColor = getStylesVal(styles.baseStroke);

  const component = new SVG().group()
    .attr({ key, id: key });
  const svg = new SVG('<svg></svg>').attr({
    x,
    y,
    overflow: 'visible',
    class: 'position',
    width: PRODUCT_SHAPE_SIZE,
    height: PRODUCT_SHAPE_SIZE,
  });
  const g = svg.group().attr({
    x: -0.5,
    y: -0.5,
  });

  const d2 = replaceFillDarkColor(data.d2, fillDarkColor);
  const main = new SVG(d2).attr(PRODUCT_MAIN_COMMON_STYLES);

  const rect = g.rect(90, 90).attr({
    class: 'rect',
    fill: baseStrokeColor,
    'stroke-width': 2,
    stroke: '#C1C6C8',
  });

  component.add(svg);
  g.add(rect);
  g.add(main);

  if (isChecked) {
    rect.attr({
      'stroke-width': 2,
      stroke: STATIC_DEFAULT_2D_STROKE_COLOR,
    });
  }

  return component;
};
