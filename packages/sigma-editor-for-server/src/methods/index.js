import { SVG } from '@svgdotjs/svg.js';
import {
  generate3dShape,
  render3dComponent,
} from '../renders/product/utils';
import { defaultProductStyle } from '../shape/compute/ProductShape';

/**
 * 设置画布中鼠标的行为
 * @param {*} action grab 默认鼠标会拖动画布 move 默认鼠标会框选画布
 */
export function setCursorBehavior(action) {
  if (action === 'grab') {
    window.dispatchEvent(new KeyboardEvent('keydown', { code: 'Space' }));
  }
  if (action === 'move') {
    window.dispatchEvent(new KeyboardEvent('keyup', { code: 'Space' }));
  }
}

/**
 * 渲染 3d 产品元素，传入一个 3d 产品图标的 svg，将根据类名自动填充其颜色，并返回一个 svg
 * @param {content} string 3d svg
 * @returns string
 */
export function render3DProductShape(content) {
  const component = new SVG(generate3dShape(content));
  render3dComponent(component, defaultProductStyle, false);
  return component.svg();
}
