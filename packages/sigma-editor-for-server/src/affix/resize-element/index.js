import store from '../../store';
import { BASE_HALF_GRID_2D, SIGMA_GRAPH_MODE_3D } from '../../util/constants';
import { ResizeChild } from './child';
import { RESIZE_CLASSNAME, getResizePoints } from './utils';

class ResizePoints {
  static shapes = {};
  constructor({
    vnode,
    shapes,
    container = '.position',
    minWidth = BASE_HALF_GRID_2D,
    minHeight = BASE_HALF_GRID_2D,
  }) {
    if (!(vnode && vnode.component)) return;
    this.vnode = vnode;
    if (shapes) {
      ResizePoints.shapes = shapes;
    }
    const { component } = vnode;
    this.container = component.findOne(container);

    this.minWidth = minWidth;
    this.minHeight = minHeight;
    this.map = {};

    this._init();
  }
  _wrapper = null;

  _init() {
    const { mode } = store.getState();
    const { vnode, minWidth, minHeight } = this;
    const { points } = vnode.data;
    this._initWrapper();

    for (const key in points) {
      const point = points[key];
      const { position, cursor } = point;
      const child = new ResizeChild({
        position,
        cursor: cursor[mode],
        direction: key,
        vnode,
        minWidth,
        minHeight,
        shapes: ResizePoints.shapes,
      });
      this._wrapper.add(child.element);
      this.map[key] = child;
    }
  }

  _initWrapper() {
    const { component } = this.vnode;
    this._wrapper = component.findOne(`.${RESIZE_CLASSNAME}`);

    if (!this._wrapper) {
      this._wrapper = this.container.group()
        .attr({
          class: RESIZE_CLASSNAME,
        });
    } else {
      this._wrapper.clear();
    }
  }

  update(p) {
    const is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { width, height } = this.vnode;
    const points = p || getResizePoints(width, height, is3D);

    for (const key in points) {
      const point = points[key];
      const child = this.map[key];
      child.update(point.position);
    }
  }

  show() {
    for (const child of Object.values(this.map)) {
      child.element.show();
    }
  }

  hide() {
    for (const child of Object.values(this.map)) {
      child.element.hide();
    }
  }
}

export {
  ResizePoints,
};
