import { SVG } from '@svgdotjs/svg.js';
import store from '../../store';
import { SIGMA_GRAPH_MODE_3D, SIGMA_TKE_SHAPE, BASE_HALF_GRID_2D } from '../../util/constants';
import { get2DSigmaPoint, getSigmaPoint, transform2DTo3D, transform3DTo2D } from '../../util/tools';
import { clearConnectLineToolBox } from '../../util/toolbox';
import cmdStack from '../../core/cmd';
import { pureClone } from '../../util/nodes';
import { checkGroupSize } from './utils';

class ResizeChild {
  static shapes = {};
  constructor({
    direction,
    position,
    cursor,
    vnode,
    minWidth,
    minHeight,
    shapes,
  }) {
    this.direction = direction;
    this.position = position;
    this.cursor = cursor;
    this.vnode = vnode;
    this.vnodeCopyBeforeUpdate = pureClone(vnode);
    this.minWidth = minWidth;
    this.minHeight = minHeight;
    if (shapes) {
      ResizeChild.shapes = shapes;
    }
    this.subVnodes = [];
    this.init();
    this.canDrag = true;
  }

  _start = { x: 0, y: 0 };
  _is3D = false;

  init() {
    const { x, y } = this.position;
    this.element = new SVG().group()
      .attr({
        class: this.direction,
      });

    this.c1 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 4,
      cursor: this.cursor,
      fill: '#98A3B7',
    });

    this.c2 = this.element.circle().attr({
      cx: x,
      cy: y,
      r: 10,
      cursor: this.cursor,
      fill: 'transparent',
    });

    this.element.draggable();

    this.element.on('dragstart', this._startDrag.bind(this));
    this.element.on('dragmove', this._drag.bind(this), 50);
    this.element.on('dragend', this._endDrag.bind(this));
  }

  update(position) {
    this.position = position;
    const { x, y } = position;
    this.c1.attr({
      cx: x,
      cy: y,
    });
    this.c2.attr({
      cx: x,
      cy: y,
    });
  }

  _startDrag(e) {
    // e.preventDefault();
    this._is3D = store.getState().mode === SIGMA_GRAPH_MODE_3D;
    const { box } = e.detail;
    const { cx, cy } = box;
    this._start.x = cx;
    this._start.y = cy;
    this.vnodeCopyBeforeUpdate = pureClone(this.vnode);
    if (this.vnode?.type === SIGMA_TKE_SHAPE) {
      const subKeys = Object.keys(this.vnode?.relations);
      subKeys.forEach((key) => {
        const subVnode = ResizeChild.shapes?.[key];
        if (subVnode) {
          const { position, width, height } = subVnode;
          let subPosition = this._is3D ? transform3DTo2D(position) : position;
          if (this._is3D) {
            // 3d坐标转换成2d坐标时会有这边一个偏移量差距
            subPosition.x = subPosition.x + 112.5;
            subPosition.y = subPosition.y + 22.5;
            subPosition = get2DSigmaPoint(subPosition);
          }
          this.subVnodes.push({
            ...subPosition,
            w: width,
            h: height,
          });
        }
      });
    }
    clearConnectLineToolBox();
  }

  _drag(e) {
    e.preventDefault();
    const { scale } = store.getState();
    const { box } = e.detail;
    const { cx, cy } = box;
    const diff = {
      x: (cx - this._start.x) * scale,
      y: (cy - this._start.y) * scale,
    };
    // if (this.canDrag) {
    //   console.log('c', cx, cy, box);
    //   this._start.x = cx;
    //   this._start.y = cy;
    // }
    const moveInfo = {
      cx,
      cy,
    };
    this._sizeChange(diff, 'draging', moveInfo);
  }

  _endDrag(e) {
    const { scale } = store.getState();
    const { box } = e.detail;
    const { cx, cy } = box;

    const diff = this.canDrag ? {
      x: (cx - this._start.x) * scale,
      y: (cy - this._start.y) * scale,
    } : {
      x: 0,
      y: 0,
    };

    this._sizeChange(diff, 'end');
    this.subVnodes = [];
    this.canDrag = true;
  }

  _sizeChange(diff, status = 'draging', moveInfo = {}) {
    const { vnode } = this;
    const isEnd = status === 'end';
    const dirs = this.direction.split('-');
    const { x: dx, y: dy } = this._is3D ? transform3DTo2D(diff, true) : diff;
    const params = { ...diff, dx, dy };
    const changed = this.direction === 'left-top'
      ? this._leftTopResize(params)
      : dirs.reduce((acc, dir) => {
        const size = this[`_${dir}Resize`](params);
        return { ...size, ...acc };
      }, {});
    // x y 成对出现，width  height 不一定
    const { width, height, x, y } = changed;
    let flag = true;
    if (vnode?.type === SIGMA_TKE_SHAPE) {
      // 如果是tke组在变换大小，则需要检测内部图元的位置。 不允许缩小至图元到组的外面
      const { position, width: width1, height: height1 } = vnode;

      const groupInfo = {
        ...position, w: width1, h: height1,
      };

      if (width) {
        groupInfo.w = width;
      }
      if (height) {
        groupInfo.h = height;
      }

      if (x) {
        groupInfo.x = x;
      }

      if (y) {
        groupInfo.y = y;
      }

      if (this._is3D) {
        let transformPoint = transform3DTo2D(groupInfo);
        transformPoint = get2DSigmaPoint(transformPoint);
        groupInfo.x = transformPoint.x;
        groupInfo.y = transformPoint.y;
      }

      flag = checkGroupSize(groupInfo, this.subVnodes);
      this.canDrag = flag;
      if (!isEnd && !flag && (width < vnode.width || height < vnode.height)) {
        return;
      }
    }
    const { cx, cy } = moveInfo;
    if (cx && cy) {
      this._start.x = cx;
      this._start.y = cy;
    }
    if (width) vnode.width = width;
    if (height) vnode.height = height;
    if (isEnd) {
      const { x, y } = get2DSigmaPoint(
        { x: vnode.width, y: vnode.height },
        { step: 1 },
      );
      vnode.width = Math.ceil(x / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D;
      vnode.height = Math.ceil(y / BASE_HALF_GRID_2D) * BASE_HALF_GRID_2D;
    }
    const has = x !== undefined && y !== undefined;
    if (has) {
      if (isEnd) {
        const handler = this._is3D ? getSigmaPoint : get2DSigmaPoint;
        const { x: nx, y: ny } = handler({ x, y }, { step: 1 });
        vnode.position.x = nx;
        vnode.position.y = ny;
      } else {
        vnode.position.x = x;
        vnode.position.y = y;
      }
    }
    if (isEnd) {
      cmdStack.saveAction({
        groupBegin: true,
      });
    }
    vnode.component.fire('size-change', { isEnd, vnodeCopyBeforeUpdate: this.vnodeCopyBeforeUpdate });
    // x y 更新时才触发这个函数
    has && vnode.component.fire('position-change', {
      position: vnode.position,
      animating: false,
    });

    if (isEnd) {
      cmdStack.saveAction({
        updateType: ['size-change', 'position-change'],
        data: [vnode],
        oldData: [this.vnodeCopyBeforeUpdate],
        groupEnd: true,
      });
    }

    // console.timeEnd(`${status} size-change`);
  }

  _leftTopResize(params) {
    const { x: cx, y: cy } = this.vnode.position;
    const { x, y } = params;
    return {
      x: cx + x,
      y: cy + y,
      width: this._leftResize(params).width,
      height: this._topResize(params).height,
    };
  }

  _topResize({ y, dy }) {
    const { height } = this.vnode;
    const { x: cx, y: cy } = this.vnode.position;
    const tp = this._is3D
      ? transform2DTo3D({ x: 0, y: dy }, true)
      : { x: 0, y };
    return {
      x: tp.x + cx,
      y: tp.y + cy,
      height: Math.max(height - dy, this.minHeight),
    };
  }
  _bottomResize({ dy }) {
    const { height } = this.vnode;
    return {
      height: Math.max(height + dy, this.minHeight),
    };
  }
  _leftResize({ x, dx }) {
    const { width } = this.vnode;
    const { x: cx, y: cy } = this.vnode.position;
    const tp = this._is3D ? transform2DTo3D({ x: dx, y: 0 }, true) : { x, y: 0 };
    return {
      x: tp.x + cx,
      y: tp.y + cy,
      width: Math.max(width - dx, this.minWidth),
    };
  }
  _rightResize({ dx }) {
    const { width } = this.vnode;
    return {
      width: Math.max(width + dx, this.minWidth),
    };
  }
}


export {
  ResizeChild,
};
