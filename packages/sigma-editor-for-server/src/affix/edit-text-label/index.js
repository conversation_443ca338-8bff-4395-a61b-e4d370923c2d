import store from '../../store';

// 可编辑的文本label
export default class EditTextLabel {
  constructor(option) {
    this.myforeign = null;
    this.textdiv = null;
    this.wrapper = null;
    this.startEditFlag = false;
    this.init(option);
  }

  init(option) {
    const {
      maxLength, // 是否有最大长度
      wrapper,
      text,
      innerHTML,
      width,
      height,
      initStyles = {},
      initForeignStyles = {},
      initAttrs = {},
      ondblclick,
      onblur,
      dblClickDom, // 注册双击事件的dom 传入则注册在这里不传入则注册到textdiv
      isInline = false,
      isHidden = false,
      isEnterSubmit = false, // 是否需要enter提交
      onSave, // 保存事件
    } = option;
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    this.isSafari = isSafari;
    this.myforeign = wrapper.foreignObject(width, height);
    this.textdiv = document.createElement('div');
    this.ondblclick = ondblclick;
    this.isInline = isInline;
    this.onblur = onblur;
    this.onSave = onSave;
    if (text) {
      this.textdiv.innerText = text;
      this.textdiv.title = text;
    }
    if (innerHTML) {
      this.textdiv.innerHTML = innerHTML;
      this.textdiv.title = innerHTML;
    }
    this.myforeign.node.appendChild(this.textdiv);
    wrapper.node.appendChild(this.myforeign.node);
    this.wrapper = wrapper;
    if (isHidden) {
      this.myforeign.node.style.overflow = 'hidden';
    } else {
      this.myforeign.node.style.overflow = 'visible';
    }
    this.myforeign.node.style.position = 'relative';
    this.textdiv.setAttribute('contentEditable', 'false');
    this.textdiv.setAttribute('xmlns', 'http://www.w3.org/1999/xhtml');
    this.textdiv.style.whiteSpace = 'nowrap';
    this.textdiv.style.outline = 'none';
    this.setTextStyle(initStyles);
    this.setforeignAttr(initAttrs);
    this.setforeignStyle({ yAlign: initStyles?.yAlign, ...initForeignStyles });
    this.dblClickDom = dblClickDom;
    this.textdiv.addEventListener('keydown', (event) => {
      event.stopPropagation();
      if (event.key === 'Enter' && isEnterSubmit) {
        onblur(this.textdiv);
        this.textdiv.setAttribute('contentEditable', 'false');
        this.textdiv.removeEventListener('mousemove', this.mouseHandle);
        this.textdiv.removeEventListener('mousedown', this.mouseHandle);
        this.textdiv.removeEventListener('mouseup', this.mouseHandle);
        this.textdiv.removeEventListener('copy', this.copyHandle);
        this.onSave();
        if (this.dblClickDom) {
          this.dblClickDom.fire('end-edit');
        }
      }
    });
    this.textdiv.onmouseenter = () => {
      if (store.getState().uneditable) {
        this.textdiv.setAttribute('contentEditable', 'false');
      }
    };
    if (this.dblClickDom) {
      this.dblClickDom.on('dblclick', this.dblHandle);
    } else {
      this.textdiv.ondblclick = this.dblHandle;
    }
    this.textdiv.oninput = () => {
      if (maxLength && this.textdiv.innerText.length >= maxLength) {
        onblur(this.textdiv);
        this.textdiv.setAttribute('contentEditable', 'false');
      }
    };
    this.textdiv.onblur = () => {
      if (this.startEditFlag) {
        // 防止线上双击编辑后触发离开焦点事件
        return;
      }
      onblur(this.textdiv);
      this.textdiv.title = this.textdiv.innerText;
      this.textdiv.setAttribute('contentEditable', 'false');
      this.textdiv.removeEventListener('mousemove', this.mouseHandle);
      this.textdiv.removeEventListener('mousedown', this.mouseHandle);
      this.textdiv.removeEventListener('mouseup', this.mouseHandle);
      this.textdiv.removeEventListener('copy', this.copyHandle);
      this.onSave();
      if (this.dblClickDom) {
        this.dblClickDom.fire('end-edit');
      }
    };
  }

  copyHandle = (event) =>  {
    const selectedText = window.getSelection().toString();
    const textOnly = this.textdiv.innerText || selectedText;
    event.preventDefault();
    event.clipboardData.setData('text/plain', textOnly);
  };

  handle = (event) => {
    if (!this.textdiv.contains(event.target)) {
      this.onblur(this.textdiv);
      document.removeEventListener('mousedown', this.handle);
      this.textdiv.setAttribute('contentEditable', 'false');
      this.textdiv.removeEventListener('mousemove', this.mouseHandle);
      this.textdiv.removeEventListener('mousedown', this.mouseHandle);
      this.textdiv.removeEventListener('mouseup', this.mouseHandle);
      this.textdiv.removeEventListener('copy', this.copyHandle);
    }
  };

  mouseHandle = (event) => {
    event.stopPropagation();
  };

  dblHandle = () => {
    if (store.getState().uneditable) {
      // 非编辑模式不触发双击事件
      return;
    }
    if (this.isSafari) {
      document.addEventListener('mousedown', this.handle);
    }
    if (!store.getState().uneditable) {
      this.textdiv.setAttribute('contentEditable', 'true');
      const range = document.createRange();
      range.selectNodeContents(this.textdiv);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    }
    if (this.ondblclick) {
      this.ondblclick(this.textdiv);
    }
    if (this.isInline) {
      this.textdiv.style.width = 'auto';
    }
    this.textdiv.addEventListener('mousemove', this.mouseHandle);
    this.textdiv.addEventListener('mousedown', this.mouseHandle);
    this.textdiv.addEventListener('mouseup', this.mouseHandle);
    this.textdiv.addEventListener('copy', this.copyHandle);
  };

  setTextStyle(styles) {
    const keys = Object.keys(styles);
    keys.forEach((styleKey) => {
      this.textdiv.style[styleKey] = styles[styleKey];
    });
  }

  setforeignAttr(attrs) {
    const keys = Object.keys(attrs);
    keys.forEach((key) => {
      this.myforeign.node.setAttribute(key, attrs[key]);
    });
  }

  setforeignStyle(styles) {
    const { yAlign, ...otherStyle } = styles;
    const keys = Object.keys(otherStyle);
    keys.forEach((styleKey) => {
      this.myforeign.node.style[styleKey] = otherStyle[styleKey];
    });
    if (yAlign && !this.isSafari) {
      this.setTextYAlign({
        value: yAlign,
      });
    }
  }

  getText() {
    return this.textdiv.innerText;
  }

  setText(text) {
    this.textdiv.innerText = text;
  }

  setInnerHtml(innerHTML) {
    this.textdiv.innerHTML = innerHTML;
  }

  setTextYAlign(val) {
    if (this.isSafari) {
      return;
    }
    const { value } = val;
    this.textdiv.style.position = 'absolute';
    this.textdiv.style.removeProperty('bottom');
    this.textdiv.style.removeProperty('top');
    this.textdiv.style.removeProperty('transform');
    if (value === 'top') {
      // 顶端对齐
      this.textdiv.style.height = 'auto';
      this.textdiv.style.top = '0%';
    }
    if (value === 'bottom') {
      this.textdiv.style.height = 'auto';
      this.textdiv.style.bottom = '0%';
    }

    if (value === 'center') {
      this.textdiv.style.height = 'auto';
      this.textdiv.style.top = '50%';
      this.textdiv.style.transform = 'translateY(-50%)';
    }
  }

  startEdit() {
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    if (isSafari) {
      document.addEventListener('mousedown', this.handle);
    }
    if (!store.getState().uneditable) {
      this.textdiv.setAttribute('contentEditable', 'true');
      const range = document.createRange();
      range.selectNodeContents(this.textdiv);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    }
    if (ondblclick) {
      ondblclick();
    }
    this.textdiv.addEventListener('mousemove', this.mouseHandle);
    this.textdiv.addEventListener('mousedown', this.mouseHandle);
    this.textdiv.addEventListener('mouseup', this.mouseHandle);
    this.textdiv.addEventListener('copy', this.copyHandle);
    this.textdiv.focus();
    this.startEditFlag = true;
    setTimeout(() => {
      this.startEditFlag = false;
      this.textdiv.focus();
      // 创建一个新的Range对象
      const range = document.createRange();
      range.selectNodeContents(this.textdiv);
      range.collapse(false); // 将光标定位到文本末尾

      // 获取Selection对象并将Range对象设置为当前选区
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    }, (500));
  }
}
