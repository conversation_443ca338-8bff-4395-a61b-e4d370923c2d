import { SVG } from '@svgdotjs/svg.js';
import store from '../../store';
import {
  setLineMovingStatus,
} from '../../renders/line/utils';
import { MOVABLE_TIP_CLASS, STATIC_CHECKED_FILL_COLOR, TRANSFORM_3D_MATRIX } from '../../util/constants';

class MovablePoint {
  constructor({
    key,
    position,
    className,
    direction,
    maxWidth,
    maxHeight,
    isPolyline,
    is3D,
    isProduct,
  }) {
    this.pkey = key;
    this.position = position;
    this.className = className;
    this.direction = direction;
    this.maxHeight = maxHeight;
    this.maxWidth = maxWidth;
    this.isPolyline = isPolyline;
    this.is3D = is3D;
    this.isProduct = isProduct;
    this._render();
    this.tkeLineInfo = {};
  }

  _render() {
    const { x, y, w, h, x3d, y3d } = this.position;
    const element = new SVG().group()
      .attr({
        class: this.className,
      });
    if (this.isPolyline) {
      element.circle().attr({
        cx: x,
        cy: y,
        r: 4.5,
        fill: STATIC_CHECKED_FILL_COLOR,
        opacity: 0.3,
      });
    }
    if (this.is3D && this.isPolyline && !this.isProduct) {
      this.p2 = element.rect(w, h).attr({
        x: x3d,
        y: y3d,
        opacity: 0,
        class: 'p2',
        transform: this.is3D ? `matrix(${TRANSFORM_3D_MATRIX.join(' ')})` : '',
      });
    } else {
      this.p2 = element.rect(w, h).attr({
        x: x - w / 2,
        y: y - h / 2,
        opacity: 0,
        class: 'p2',
      });
    }


    this.element = element;

    this._bindMouseEvent();
    this._bindDraggingEvent();
    this._bindMouseOutEvent();
  }

  rerender(position) {
    const { x, y, w, h } = position;
    this.position = position;

    this.p2.attr({
      x: x - w / 2,
      y: y - h / 2,
    });
  }

  _bindMouseEvent() {
    const { core } = store.getState();
    const { shapes } = core.data;

    // 处理正在连线
    this.p2.off('mouseover.mouse');
    this.p2.on('mouseover.mouse', () => {
      const mouseLine = core._getMouseLineShape();
      if (mouseLine) {
        this._initTip();

        this.p2.on('mousedown.mouse', (e) => {
          this.p2.off('mousedown.mouse');

          const lineVnode = core._getMouseLineShape();
          if (!lineVnode) return;

          // 这里要阻止冒泡，不然会响应目标节点的点击事件
          e.stopPropagation();
          this._removeTip();

          const { data } = lineVnode;
          const parent = shapes[this.pkey];
          const { position } = parent;

          data.start.isFixed = true;
          data.end.isFixed = true;
          data.end.dir = this.direction;
          data.end.x = position.x;
          data.end.y = position.y;

          lineVnode.component.fire('line-complete', { key: this.pkey });
        });
      }
    });
  }

  _bindDraggingEvent() {
    const { core } = store.getState();
    const { shapes } = core.data;

    // 处理拖拽
    this.p2.off('mouseover.movable');
    this.p2.on('mouseover.movable', () => {
      const lineVnode = core._getDraggingLineShape();
      // 记录下当前线条的起始key和终点key
      if (lineVnode?.isTkeSubVode) {
        this.tkeLineInfo = {
          start: lineVnode.data.start.vkey,
          end: lineVnode.data.end.vkey,
        };
      }
      if (lineVnode) {
        this._initTip();
        this.p2.on('mouseup.movable', () => {
          this.p2.off('mouseup.movable');
          this._removeTip();

          const { data } = lineVnode;
          let side;
          if (data.start.type === 'dragging') {
            side = 'start';
          }
          if (data.end.type === 'dragging') {
            side = 'end';
          }
          if (lineVnode?.isTkeSubVode) {
            // 如果是tke内部连线，如果练到了别的节点，阻止
            if (this.tkeLineInfo[side] !== this.pkey) {
              this.tkeLineInfo = {};
              return;
            }
          }
          // 这里不能阻止冒泡
          // e.stopPropagation();
          const parent = shapes[this.pkey];
          const { position } = parent;

          data[side].isFixed = true;
          data[side].dir = this.direction;
          data[side].vkey = this.pkey;
          data[side].x = position.x;
          data[side].y = position.y;

          setLineMovingStatus(lineVnode, side, 'vnode');
          lineVnode.component.fire('update-position', { side });
        });
      }
    });
  }

  _bindMouseOutEvent() {
    this.p2.off('mouseout.movable');
    this.p2.on('mouseout.movable', () => {
      this._removeTip();
    });
  }

  _initTip() {
    const { container: { gCache }, core } = store.getState();
    const parent = core.data.shapes[this.pkey];
    const x = parent.position.x + this.position.x;
    const y = parent.position.y + this.position.y;

    let tip = gCache.remember(MOVABLE_TIP_CLASS);
    if (!tip) {
      if (this.position.isMid && !this.isPolyline) {
        tip = new SVG().attr({
          x,
          y,
          'pointer-events': 'none',
          class: MOVABLE_TIP_CLASS,
        });
        tip.circle().attr({
          r: 8,
          fill: STATIC_CHECKED_FILL_COLOR,
          opacity: 1,
        });
        tip.circle().attr({
          r: 6,
          fill: '#fff',
          opacity: 1,
        });
        gCache.add(tip);
        gCache.remember(MOVABLE_TIP_CLASS, tip);
      } else {
        tip = new SVG().attr({
          x,
          y,
          'pointer-events': 'none',
          class: MOVABLE_TIP_CLASS,
        });
        tip.circle().attr({
          r: 4.5,
          fill: STATIC_CHECKED_FILL_COLOR,
        });
        gCache.add(tip);
        gCache.remember(MOVABLE_TIP_CLASS, tip);
      }
    } else {
      if (this.position.isMid) {
        tip.attr({
          x,
          y,
        });
      } else {
        tip.attr({
          x,
          y,
          opacity: 0.6,
        });
      }
    }
  }

  _removeTip() {
    const { container: { gCache } } = store.getState();
    const tip = gCache.remember(MOVABLE_TIP_CLASS);
    if (!tip) return;
    tip.remove();
    if (this.position.isMid) {
      if (this.isPolyline && !this.isProduct) {
        this.p2.attr({
          width: this.position.w,
          height: this.position.h,
          x: this.position.x3d,
          y: this.position.y3d,
          opacity: 0,
        });
      } else {
        this.p2.attr({
          width: this.position.w,
          height: this.position.h,
          x: this.position.x - this.position.w / 2,
          y: this.position.y - this.position.h / 2,
          opacity: 0,
        });
      }
    }
    gCache.forget(MOVABLE_TIP_CLASS);
  }
}

export {
  MovablePoint,
};

export * from './utils';
