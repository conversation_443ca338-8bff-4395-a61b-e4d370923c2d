import { SIGMA_REMARK_NOTE_SHAPE, SHAPE_CATEGORY_COMMON } from '../../util/constants';
import { SigmaRectangleShape } from './RectangleShape';

export class SigmaRemarkNoteShape extends SigmaRectangleShape {
  constructor() {
    super();
    Object.assign(this.styles, {
      geometry: {
        name: '边框形状',
        type: 'select',
        default: 'rectangular', // manual rectangular
        value: null,
      },
      padding: { name: '边距', type: 'number', default: 1, value: null },
    });
    this.styles.label.default = 'Double click to edit';
    this.styles.icon.default = null;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#FFF3A3';
    this.styles.fill.default = '#FFF3A3';
    this.styles.yAlign = {
      name: '标题垂直对齐方式',
      type: 'select',
      default: 'top',
      value: null,
    };
    this.editable.push('geometry', 'padding');
  }

  type = SIGMA_REMARK_NOTE_SHAPE;
  category = SHAPE_CATEGORY_COMMON;
  name = 'REMARK';
  cName = '备注';

  gIndex = 4;

  zIndex = 1;
  relations = {}; // key -> true ; 关联的组件key值对象，当关联的任意一个node出现更新时，重新计算 data， 渲染;

  create = () => {
    const vnode = super.create();
    vnode.relations = this.relations;
    return vnode;
  };
}

