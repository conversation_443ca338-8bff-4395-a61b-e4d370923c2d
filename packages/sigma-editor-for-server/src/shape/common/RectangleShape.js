import _ from 'lodash';
import { BASE_SIZE_2D, SHAPE_CATEGORY_COMMON, SIGMA_RECTANGLE_SHAPE } from '../../util/constants';
import Shape from '../Shape';
import { RECT_DEFAULT_ICON_SIZE } from '../../renders/rectangle/utils';

export class SigmaRectangleShape extends Shape {
  type = SIGMA_RECTANGLE_SHAPE;
  category = SHAPE_CATEGORY_COMMON;
  name = 'RECTANGLE';
  cName = '矩形';
  gIndex = 5;

  width = BASE_SIZE_2D * 2;
  height = BASE_SIZE_2D;

  data = {
    canResize: true, // 是否可以拖拽改变大小
  };

  editable = [
    'icon',
    'iconSize',
    'label',
    'xAlign',
    'location',
    'position',
    'fill',
    'stroke',
    'strokeWidth',
    'strokeStyle',
    'borderRadius',
    'fontSize',
    'color',
  ];
  styles = {
    fill: { name: '背景色', type: 'color', default: 'transparent', value: null },
    stroke: { name: '边框色', type: 'color', default: '#666', value: null },
    strokeWidth: { name: '边框宽度', type: 'number', default: 1, value: null },
    strokeStyle: {
      name: '边框样式',
      type: 'select',
      default: 'solid',
      value: null,
    },
    icon: {
      name: '图标',
      type: 'productIcon',
      // default: DEFAULT_BASE_ICON,
      default: null,
      value: null,
    },
    iconSize: {
      name: '图标大小',
      type: 'number',
      default: RECT_DEFAULT_ICON_SIZE,
      value: null,
    },
    label: {
      name: '标题',
      type: 'string',
      // default: '矩形容器',
      default: '',
      value: null,
    },
    xAlign: {
      name: '标题对齐方式',
      type: 'select',
      default: 'left',
      value: null,
    },
    location: {
      name: '标题定位',
      type: 'select',
      default: 'top',
      value: null,
    },
    position: {
      name: '标题位置',
      type: 'select',
      default: 'inside',
      value: null,
    },
    borderRadius: { name: '圆角', type: 'number', default: 0, value: null },
    fontSize: { name: '字号', type: 'number', default: 14, value: null },
    color: { name: '字体颜色', type: 'color', default: '#242A35', value: null },
  };

  connectable = true;

  create(key) {
    const vnode = this._create(key);
    vnode.data = this.data;
    return _.cloneDeep(vnode);
  };
}
