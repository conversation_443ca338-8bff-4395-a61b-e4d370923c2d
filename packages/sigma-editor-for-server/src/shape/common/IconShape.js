import _ from 'lodash';
import Shape from '../Shape';
import { DEFAULT_BASE_ICON, SHAPE_CATEGORY_COMMON, SIGMA_ICON_SHAPE } from '../../util/constants';

export class SigmaIconShape extends Shape {
  type = SIGMA_ICON_SHAPE;
  category = SHAPE_CATEGORY_COMMON;
  name = 'PRODUCT ICON';
  cName = '产品图标';
  description = '';
  connectable = true;
  linkable = true;
  gIndex = 10;
  data = {
    w: 48,
    h: 48,
  };
  editable = ['icon', 'scale', 'is3d', 'isFlat', 'route'];
  styles = {
    icon: {
      name: '图标',
      type: 'productIcon',
      default: DEFAULT_BASE_ICON,
      value: null,
    },
    scale: { name: '缩放', type: 'number', default: 1, value: null },
    is3d: { name: '3D', type: 'boolean', default: true, value: null },
    isFlat: { name: '垂直', type: 'boolean', default: false, value: null },
    route: { name: '旋转', type: 'select', default: 'e', value: null },
  };

  create = () => {
    const vnode = this._create();
    return _.cloneDeep(vnode);
  };
}
