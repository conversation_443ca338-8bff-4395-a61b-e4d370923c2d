import _ from 'lodash';
import Shape from '../Shape';
import {
  BASE_GRID_2D,
  BASE_SIZE_2D,
  SHAPE_CATEGORY_COMMON,
  SIGMA_TEXTLABEL_SHAPE,
} from '../../util/constants';

export class SigmaTextLabelShape extends Shape {
  type = SIGMA_TEXTLABEL_SHAPE;
  name = 'TEXT LABEL';
  cName = '文本';
  category = SHAPE_CATEGORY_COMMON;
  label = 'Click to Edit Text';
  data = {};
  gIndex = 11;
  editable = [
    'label',
    'outline',
    'xAlign',
    // 'yAlign',
    'fill',
    'fontSize',
    'is3d',
    // 'isFlat',
    'route',
  ];
  styles = {
    label: {
      name: '内容',
      type: 'textarea',
      default: 'Click to Edit Text',
      value: null,
    },
    outline: { name: '描边', type: 'boolean', default: true, value: null },
    xAlign: { name: '水平对齐', type: 'select', default: 'center', value: null },
    yAlign: { name: '垂直对齐', type: 'select', default: 'middle', value: null },
    fill: { name: '颜色', type: 'color', default: '#000000', value: null },
    fontSize: { name: '字号', type: 'number', default: 24, value: null },
    is3d: { name: '3D', type: 'boolean', default: true, value: null },
    isFlat: { name: '垂直', type: 'boolean', default: false, value: null },
    route: { name: '旋转', type: 'select', default: 'e', value: null },
  };
  attach = null; // 关联的 node
  // 这里要初始化一个宽高
  width = BASE_SIZE_2D;
  height = BASE_GRID_2D;

  connectable = true;
  // 如果是连线的label，记录位置百分比，连线位置大小变化时保持label在这个百分比位置
  positionPercent = NaN;
  // 如果是连线的label，记录位置偏移，线上，线左侧，线右侧：center | left | right
  positionSide = 'center';

  create = () => {
    const vnode = this._create();
    vnode.label = this.label;
    vnode.attach = this.attach;
    return _.cloneDeep(vnode); // 768 703  624 749
  };

  createLinkNode = ({ name, key }) => {
    const vnode = this._create();
    vnode.styles.label.default = name;
    vnode.attach = key; // 与之关联的 node;
    vnode.positionPercent = this.positionPercent;
    return _.cloneDeep(vnode);
  };
}
