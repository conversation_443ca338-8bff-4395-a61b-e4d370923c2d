import {
  SIGMA_AREA_SHAPE,
  DEFAULT_AREA_ICON,
} from '../../util/constants';
import { SigmaBaseGroupShape } from '../network/BaseGroupShape';

export class SigmaAreaShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '地域 Area';
    this.styles.icon.default = DEFAULT_AREA_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#18609C';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = 'transparent';
  }

  name = 'AREA';
  cName = '区域';
  type = SIGMA_AREA_SHAPE;
}
