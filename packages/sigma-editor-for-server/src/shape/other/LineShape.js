import _ from 'lodash';
import Shape from '../Shape';
import { ALL_CONNECTION_KEYS, SHAPE_CATEGORY_COMMON, SIGMA_LINE_SHAPE } from '../../util/constants';

export class SigmaLineShape extends Shape {
  name = 'LINE';
  cName = '连线';
  type = SIGMA_LINE_SHAPE;
  category = SHAPE_CATEGORY_COMMON;
  editable = [
    'lineColor',
    'line',
    'lineWidth',
    'lineStart',
    'lineEnd',
    'lineType',
  ];
  gIndex = 6;
  styles = {
    lineColor: { name: '颜色', type: 'color', default: '#888', value: null },
    line: { name: '线条', type: 'select', default: 'solid', value: null },
    lineStart: { name: '起点', type: 'select', default: 'line', value: null },
    lineEnd: { name: '终点', type: 'select', default: 'arrow', value: null },
    lineWidth: { name: '线宽', type: 'number', default: 2, value: null },
    lineType: { name: '类型', type: 'select', default: 'straight', value: null },
  };
  data = {
    start: {
      type: 'vnode',
      vkey: null,
      dir: 'c',
    },
    end: {
      type: 'mouse',
      dir: 'c',
    },
    arrow: [
      { x: 0, y: 1 },
      { x: -11, y: 24 },
      { x: 0, y: 18 },
      { x: 11, y: 24 },
    ],
  };

  create = (vnode, end) => {
    const _vnode = this._create();
    _vnode.isChecked = false;
    _vnode.data = this.data;
    _vnode.data.start.vkey = vnode.key;
    _vnode.data.end.type = 'mouse';
    // 初始化的时候是鼠标所在的位置
    _vnode.data.end.x = end.x;
    _vnode.data.end.y = end.y;
    // _vnode.styles.lineEnd.value = 'arrow';
    return _.cloneDeep(_vnode);
  };
  // arrowType: 'none' | 'start' | 'end' | 'both'
  createByVnode = ({
    startKey,
    endKey,
    startDir,
    endDir,
    arrowType = 'end',
  }) => {
    const vnode = this._create();
    vnode.isChecked = false;
    vnode.data = this.data;
    vnode.data.start.vkey = startKey;
    vnode.data.end.type = 'vnode';
    vnode.data.end.vkey = endKey;
    switch (arrowType) {
      case 'none': {
        vnode.styles.lineStart.value = 'line';
        vnode.styles.lineEnd.value = 'line';
        break;
      }
      case 'start': {
        vnode.styles.lineStart.value = 'arrow';
        vnode.styles.lineEnd.value = 'line';
        break;
      }
      case 'end': {
        vnode.styles.lineStart.value = 'line';
        vnode.styles.lineEnd.value = 'arrow';
        break;
      }
      case 'both': {
        vnode.styles.lineStart.value = 'arrow';
        vnode.styles.lineEnd.value = 'arrow';
        break;
      }
    }
    if (startDir && ALL_CONNECTION_KEYS.includes(startDir)) {
      vnode.data.start.dir = startDir;
      vnode.data.start.isFixed = true;
    }
    if (endDir && ALL_CONNECTION_KEYS.includes(endDir)) {
      vnode.data.end.dir = endDir;
      vnode.data.end.isFixed = true;
    }

    return _.cloneDeep(vnode);
  };
}
