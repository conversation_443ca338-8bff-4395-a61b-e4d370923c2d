import { v4 as uuid } from 'uuid';
import {
  SHAPE_CREATE_START_POSITION_X as startX,
  SHAPE_CREATE_START_POSITION_Y as startY,
  BASE_GRID_X,
  BASE_GRID_Y,
  SIGMA_GRAPH_MODE_3D,
  BASE_GRID_2D,
} from '../util/constants';
import { getSigmaPoint, get2DSigmaPoint } from '../util/tools';
import store from '../store';
import ShapeManager from './shapeManager';

const manager = ShapeManager.getManager();

class Shape {
  key = null; // 数据ID
  type = null; // 组件类型，用于执行渲染函数
  category = null; // 组件类别，用于组件分类
  name = null; // 组件名称
  cName = null; // 中文名
  label = null; // 文案描述，文案描述一
  description = null; // 文案描述，文案描述二
  lock = false; // 是否锁定
  isAreaShape = false; // 是否为区域类型组件 如何为区域类型组件添加事件，定义一个数据结构，新增自定义的数据进去
  is3DShape = true; // 是否为3D组件（用于自动转换）
  isChecked = true; // 是否被选中（创建时，默认为选中态）
  position = null; // 位置信息，默认位置信息，也有部分组件不依赖此，如 LineShape 为起始组件决定，与位置无关
  positionOffsetX = 0; // 偏移量
  positionOffsetY = 0; // 位置偏移量
  zIndex = 0;
  gIndex = 0;
  editable = []; // 可编辑的属性，与styles有绑定关系，用于渲染属性编辑窗口
  groups = {};  // 当前元素加入的组的列表
  sticky = null; // string 关联label
  styles = {
    //  fill: { name: '填充', type: 'color', default: '#000000', value: null }
    // fill: '#000000',          // 颜色值
    // border:                   // 布尔值
    // stroke: '#000000',        // 颜色值
    // strokeWidth: 4,           // 数值
    // strokeStyle: 'solid'      // 'solid' | 'dashed'
    // route: 'e',               // 枚举值 'e' | 's' | 'n' | 'w'
    // color: '#000000',         // 颜色值
    // label: '文本内容',         // string | textarea; string -> input, textarea -> textarea
    // fontSize: 14,             // 数值
    // fontWeight: 100,          // 数值 100 ~700
    // image: 'url',             // src 地址
    // width: 1,                 // 数值 宽度
    // height: 1,                // 数值 高度
    // depth: 1,                 // 数值 长度
    // scale: 1,                 // 数值 缩放倍数
    // line------------------------------------------
    // line: 'solid',            // 枚举值 'solid' | 'dashed'
    // lineStart: 'arrow',       // 枚举值 'arrow' | 'line'
    // lineEnd: 'arrow',         // 枚举值 'arrow' | 'line'
    // lineWidth: 2,             // 数值
    // lineColor: '#000000'      // 颜色值
    // other------------------------------------------
    // is3d: false,              // 布尔值
    // isFlat: false,            // 布尔值
    // productIcon               // 产品图标选择
    // shape                     // 枚举值 'rectangular' | 'dynamic'
  };
  connection = {}; // 连接点偏移量
  d2_connection = {};
  connectable = false; // 是否可连接
  checkedable = true; // 是否可选中
  linkable = false; // 是否可以创建 sticky
  component = null; // 数据渲染的 dom
  customize = {}; // 自定义数据
  // movablePoints = [];
  forever = false; // 该字段为 true 时，元素不可删除

  // 新增属性，shape在 2d 状态下的宽度和高度
  width = 0;
  height = 0;

  _create = (nodeKey = undefined) => {
    const {
      viewBox: { x, y },
      mode,
    } = store.getState();
    const key = nodeKey ?? uuid();
    const { col, row, __index } = manager.add({ key, type: this.type });
    let position = this.position || {};

    if (mode === SIGMA_GRAPH_MODE_3D) {
      position.x = position.x || startX + (col - row) * BASE_GRID_X + x;
      position.y = position.y || startY + (col + row) * BASE_GRID_Y + y;
      position = getSigmaPoint(position);
    } else {
      position.x = position.x || (1 + col) * BASE_GRID_2D * 2 + x;
      position.y = position.y || (2 + row) * BASE_GRID_2D * 2 + y;
      position = get2DSigmaPoint(position);
    }
    const vnode = {
      key,
      type: this.type,
      category: this.category,
      name: this.name,
      cName: this.cName,
      label: this.label,
      sticky: this.sticky,
      description: this.description,
      groups: this.groups,
      lock: this.lock,
      editable: this.editable,
      isAreaShape: this.isAreaShape,
      is3DShape: this.is3DShape,
      isChecked: this.isChecked,
      position,
      positionOffsetX: this.positionOffsetX,
      positionOffsetY: this.positionOffsetY,
      positionSide: 'center',
      zIndex: this.zIndex,
      gIndex: this.gIndex,
      styles: this.styles,
      connectable: this.connectable,
      connection: this.connection,
      d2_connection: this.d2_connection,
      checkedable: this.checkedable,
      linkable: this.linkable,
      customize: this.customize,
      component: null,
      // movablePoints: this.movablePoints,
      width: this.width,
      height: this.height,
      forever: this.forever,
      __index,
    };
    return vnode;
  };
}

export default Shape;
