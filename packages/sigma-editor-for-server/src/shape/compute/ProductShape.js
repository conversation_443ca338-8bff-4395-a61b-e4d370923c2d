import _ from 'lodash';
import Shape from '../Shape';
import {
  SHAPE_CATEGORY_COMPUTE,
  SIGMA_PRODUCT_SHAPE,
  STATIC_CHECKED_FILL_COLOR,
  STATIC_DEFAULT_2D_FILL_COLOR,
} from '../../util/constants';
import { PRODUCT_SHAPE_SIZE } from '../../renders/product/utils';

export const defaultProductStyle = {
  staticCheckedFill: STATIC_CHECKED_FILL_COLOR,
  opacity: {
    name: '透明度',
    type: 'number',
    default: 1,
    value: null,
  },
  dark: {
    name: '主边框颜色',
    type: 'color',
    default: '#334966',
    value: null,
  },
  fillLight: {
    name: '浅色填充色',
    type: 'color',
    default: '#ffffff',
    value: null,
  },
  fillDark: {
    name: '深色填充色',
    type: 'color',
    default: STATIC_DEFAULT_2D_FILL_COLOR,
    value: null,
  },
  baseFill: {
    name: '底座颜色',
    type: 'color',
    default: '#E2E6EC',
    value: null,
  },
  baseBgColor: {
    name: '底座背景颜色',
    type: 'color',
    default: '#E2E6EC',
    value: null,
  },
  baseBorderStroke: {
    name: '底座边框颜色',
    type: 'color',
    default: 'rgb(193, 198, 200)',
    value: null,
  },
  baseStroke: {
    name: '底座边框',
    type: 'color',
    default: '#D3D3D3',
    value: null,
  },
  pattern: { name: '模型', type: 'select', default: 'gary', value: null },
};

export class SigmaProductShape extends Shape {
  name = 'CVM';
  type = SIGMA_PRODUCT_SHAPE;
  category = SHAPE_CATEGORY_COMPUTE;
  description = 'Cloud virtual machine';
  connectable = true;
  linkable = true;
  gIndex = 8;
  data = {};
  positionOffsetY = 1;
  editable = ['fillDark', 'baseStroke'];
  styles = defaultProductStyle;

  width = PRODUCT_SHAPE_SIZE;
  height = PRODUCT_SHAPE_SIZE;

  constructor({ name, category, description, cName, d3, d2 } = {}) {
    super();
    this.name = name;
    this.cName = cName;
    if (category) {
      this.category = category;
    }
    this.description = description;
    this.data.d3 = d3;
    this.data.d2 = d2;
  }

  create = (key) => {
    const vnode = this._create(key);
    vnode.data = this.data;
    return _.cloneDeep(vnode);
  };
}
