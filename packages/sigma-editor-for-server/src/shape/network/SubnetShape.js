import { DEFAULT_SUBNET_ICON, SIGMA_SUBNET_SHAPE } from '../../util/constants';
import { SigmaBaseGroupShape } from './BaseGroupShape';

export class SigmaSubnetShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '子网 Subnet';
    this.styles.icon.default = DEFAULT_SUBNET_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#5865B4';
    this.styles.strokeStyle.default = 'dashed';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = 'transparent';
  }
  name = 'SUBNET';
  cName = '子网';
  type = SIGMA_SUBNET_SHAPE;
}

