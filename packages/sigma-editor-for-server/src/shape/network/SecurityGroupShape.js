import { DEFAULT_SECURITY_GROUP_ICON, SIGMA_SECURITY_GROUP_SHAPE } from '../../util/constants';
import { SigmaBaseGroupShape } from './BaseGroupShape';

export class SigmaSecurityGroupShape extends SigmaBaseGroupShape {
  constructor() {
    super();
    this.styles.label.default = '安全组 Security Group';
    this.styles.icon.default = DEFAULT_SECURITY_GROUP_ICON;
    this.styles.strokeWidth.default = 2;
    this.styles.stroke.default = '#06774A';
    this.styles.strokeStyle.default = 'dashed';
    this.styles.borderRadius.default = 5;
    this.styles.fill.default = 'transparent';
  }
  name = 'SECURITY GROUP';
  cName = '安全组';
  type = SIGMA_SECURITY_GROUP_SHAPE;
}
