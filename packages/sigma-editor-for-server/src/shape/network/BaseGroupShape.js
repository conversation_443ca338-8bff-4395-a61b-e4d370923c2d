import { DEFAULT_BASE_GROUP_ICON, SHAPE_CATEGORY_GROUP, SIGMA_BASE_GROUP_SHAPE } from '../../util/constants';
import { SigmaRectangleShape } from '../common/RectangleShape';

export class SigmaBaseGroupShape extends SigmaRectangleShape {
  constructor() {
    super();
    Object.assign(this.styles, {
      geometry: {
        name: '边框形状',
        type: 'select',
        default: 'rectangular', // manual rectangular
        value: null,
      },
      padding: { name: '边距', type: 'number', default: 0.5, value: null },
    });
    this.styles.label.default = '基础组 Base Group';
    this.styles.icon.default = DEFAULT_BASE_GROUP_ICON;
    this.styles.iconSize.default = 16;
    this.styles.strokeWidth.default = 2;
    this.styles.borderRadius.default = 5;
    this.styles.stroke.default = '#60708A';
    this.styles.fill.default = '#F3F4F7';
    this.editable.push('geometry', 'padding');
  }

  type = SIGMA_BASE_GROUP_SHAPE;
  category = SHAPE_CATEGORY_GROUP;
  name = 'BASE GROUP';
  cName = '基础组';

  gIndex = 4;

  zIndex = 1;
  relations = {}; // key -> true ; 关联的组件key值对象，当关联的任意一个node出现更新时，重新计算 data， 渲染;

  create = (key) => {
    const vnode = super.create(key);
    vnode.relations = this.relations;
    return vnode;
  };
}
