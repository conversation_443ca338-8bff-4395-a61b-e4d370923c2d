import {
  NO_POSITION_MANAGER_SHAPE_TYPES,
} from '../util/constants';


class ShapeManager {
  col = 4;
  counter = [];
  instance = null;
  static getManager = () => {
    if (this.instance) return this.instance;
    const instance = new ShapeManager();
    this.instance = instance;
    return instance;
  };

  setCol(col) {
    if (typeof col === 'number') {
      this.col = col;
    }
  }

  getPosition() {
    return {
      col: Math.floor(this.counter.length / this.col),
      row: this.counter.length % this.col,
    };
  }

  clear() {
    this.counter = [];
  }

  set(keys) {
    this.counter = [...keys];
  }

  add(vnode) {
    // 找到第一个位置为null 的位置或者最后一个
    const index = this.counter.indexOf(null);

    // 基础组需要这个能力
    if (NO_POSITION_MANAGER_SHAPE_TYPES.includes(vnode.type)) {
      return { col: 0, row: 0 };
    }
    let pos;

    // 如果没找到第一个位置为null 的位置
    if (index === -1) {
      pos = {
        col: Math.floor(this.counter.length / this.col),
        row: this.counter.length % this.col,
      };
      this.counter.push(vnode.key);
    } else {
      let __index;
      this.counter.find((k, i) => {
        if (i > index && typeof k === 'string' && !__index) {
          __index = k;
        }
      });
      pos = {
        col: Math.floor(index / this.col),
        row: index % this.col,
        __index,
      };
      this.counter[index] = vnode.key;
    }
    return pos;
  }

  remove(keys) {
    for (let i = 0; i < keys.length; i++) {
      const index = this.counter.indexOf(keys[i]);
      if (index !== -1) {
        this.counter[index] = null;
      }
    }
  }
}

export default ShapeManager;
