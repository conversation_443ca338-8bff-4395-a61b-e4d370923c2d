import { defineConfig } from 'rollup';
import nodeResolve from '@rollup/plugin-node-resolve';
import babel from '@rollup/plugin-babel';
import replace from '@rollup/plugin-replace';
import commonjs from '@rollup/plugin-commonjs';
import { terser } from 'rollup-terser';

import pkg from './package.json' assert { type: 'json' };

const extensions = ['.js'];
const babelRuntimeVersion = pkg.devDependencies['@babel/runtime'].replace(
  /^[^0-9]*/,
  ''
);

const external = [
  ...Object.keys(pkg.dependencies || {}),
  ...Object.keys(pkg.peerDependencies || {}),
].map((name) => RegExp(`^${name}($|/)`));

const isProd = process.env.NODE_ENV === 'production'

export default defineConfig([
  // CommonJS
  {
    input: 'src/index.js',
    output: {
      file: 'dist/index.cmd.js',
      format: 'cjs',
      indent: false,
      sourcemap: isProd ? false : true,
    },
    external,
    plugins: [
      nodeResolve({
        extensions,
      }),
      babel({
        extensions,
        plugins: [
          ['@babel/plugin-transform-runtime', { version: babelRuntimeVersion }],
        ],
        babelHelpers: 'runtime',
      }),
      terser(),
    ],
  },

  // ES
  {
    input: 'src/index.js',
    output: {
      file: 'dist/index.es.js',
      format: 'es',
      indent: false,
      sourcemap: isProd ? false : true,
    },
    external,
    plugins: [
      nodeResolve({
        extensions,
      }),
      babel({
        extensions,
        plugins: [
          [
            '@babel/plugin-transform-runtime',
            {
              version: babelRuntimeVersion,
              useESModules: true,
            },
          ],
        ],
        babelHelpers: 'runtime',
        exclude: 'node_modules/**',
      }),
      terser(),
    ],
  },

  // UMD
  {
    input: 'src/index.js',
    output: {
      file: 'dist/index.umd.js',
      format: 'umd',
      name: 'sigma-editor',
      indent: false,
      sourcemap: isProd ? false : true,
    },
    plugins: [
      nodeResolve({
        extensions,
      }),
      replace({
        preventAssignment: true,
        'process.env.NODE_ENV': JSON.stringify('production'),
      }),
      commonjs(),
      terser(),
    ],
  },
]);
