import { G, Svg } from '@svgdotjs/svg.js';

declare module '@tencent/sigma-editor' {
  export type SigmaOptionCallbacks = {
    onCheckedChange?: (vnodes: ShapeVnode[]) => void;
    onGraphChange?: (vnodes: { [key: string]: ShapeVnode }) => void;
    customizeSave?: () => void;
    onScaleChange?: (scale: number) => void;
    onShapeMouseRightClick?: (e: MouseEvent, vnode: ShapeVnode) => void;
    onShapeMouseUp?: (e: MouseEvent, vnode: ShapeVnode) => void;
    onShapeMouseOver?: (e: MouseEvent, vnode: ShapeVnode) => void;
    onShapeMouseOut?: (e: MouseEvent, vnode: ShapeVnode) => void;
    onShapeClick?: (e: MouseEvent, vnode: ShapeVnode) => void;
    onShapeDBLClick?: (e: MouseEvent, vnode: ShapeVnode) => void;
    onShapeMove?: () => void;
    onShapeMoveStart?: () => void;
    onShapeMoveEnd?: () => void;
    onShapeDelete?: (vnodes: ShapeVnode[]) => void;
    onDocClick?: (e: Event) => void;
    onDocMove?: () => void;
    onCreateArrowLine?: (e: Event, vnode: ShapeVnode) => void | boolean;
    onCreateLine?: (e: Event, vnode: ShapeVnode) => void | boolean;
    onCreateLabel?: (e: Event, vnode: ShapeVnode) => void | boolean;
    onShapesPaste?: (vnodes: ShapeVnode[]) => void;
    onShapeInit?: (vnode: ShapeVnode) => void;
    onDbClickSaveText?: (vnode: ShapeVnode) => void;
    // onError?: () => void;
  };

  class VnodeStickyUnit {
    id: string;
    vnode: ShapeVnode;
    offset: Point;
    element: string;
    width: number;
    height: number;
    updateChild(child: string): void;
    updateOffset(offset: string): void;
    remove(): void;
  }

  export interface ShapeVnode {
    attach?: string;
    key: string;
    type: string;
    category: string;
    name: string;
    cName: string;
    label: string;
    description: string;
    lock: boolean;
    isAreaShape: boolean;
    is3DShape: boolean;
    isChecked: boolean;
    position: Point;
    positionOffsetX: number;
    positionOffsetY: number;
    zIndex: number;
    gIndex: number;
    editable: string[];
    groups: { [key: string]: string }; //当前元素加入的组的列表
    relations?: { [key: string]: string }; // 当前组包含的元素
    // group: string;
    // groupVpc: string[];
    // groupSubnet: string[];
    // groupSecurity: string[];
    sticky: string;
    styles: any;
    data: any;
    connection: { [key: string]: Point };
    d2_connection: { [key: string]: Point };
    connectable: boolean;
    checkedable: boolean;
    linkable: boolean;
    // movablePoints?: any;
    resizePoints?: any;
    component?: Svg;
    customize: object;
    width: number;
    height: number;
    forever: boolean;
  }


  export interface GlobalValue {
    root: any;
    mode: string;
    scale: number;
    w: number;
    h: number;
    doc: any;
    g: any;
    defs: any;
    viewBox: any;
    container: any;
    uneditable: boolean;
    shapes: {[key: string]: ShapeVnode};
  }

  export type ViewMode = 'SIGMA_GRAPH_MODE_2D' | 'SIGMA_GRAPH_MODE_3D';

  export type Point = { x: number; y: number };
  export type Layer = { key: string; name: string; cName: string; layer: G };
  export type Graph = {
    (key: string): ShapeVnode;
  };
  export type GraphData = {
    id?: string;
    name?: string;
    description?: string;
    content: string;
  };

  type ShapeStyles = {
    name: string;
    type: string;
    default: string | number | boolean;
    value: string | number | boolean | null;
  };
  type CmdStatus = {
    undoSteps: number;
    redoSteps: number;
    hasUndo: boolean;
    hasRedo: boolean;
  };

  interface Core {
    instance: null | Core;
    data: {
      id: string;
      shapes: Graph;
      cached: ShapeVnode[];
    };
    add: () => {};
    update: () => {};
    remove: () => {};
  }

  export type ShapeRender = (c: Core, v: ShapeVnode, mode: ViewMode) => G;
  export type ShapeRerender = () => void;
  export type ShapeExports = (c: Core, v: ShapeVnode, mode: ViewMode) => G;
  export type ShapeTransform = (v: ShapeVnode) => ShapeVnode;
  export type ShapeReduction = (v: ShapeVnode) => ShapeVnode;
  export type ShapeGetflat = (v: ShapeVnode) => Point[];
  export function setCursorBehavior(action: 'grab' | 'move'): void;
  export function render3DProductShape(content: string): string;

  export type Renderer = {
    render: ShapeRender;
    rerender: ShapeRerender;
    exports: ShapeExports;
    transform: ShapeTransform;
    reduction: ShapeReduction;
    getflat: ShapeGetflat;
  };

  export type EditorParam = {
    backgroundColor: string;
    quarterColor: string;
    entireColor: string;
    remember: boolean;
  };

  export type CreateNodeOption = {
    customize?: any;
    label?: string;
    position?: Point;
    key?: string;
  };

  export interface ShapeType extends ShapeVnode {
    create(): ShapeVnode;
  }

  export class Shape implements ShapeType {
    create(): ShapeVnode;
    key: string;
    type: string;
    category: string;
    name: string;
    cName: string;
    label: string;
    description: string;
    lock: boolean;
    isAreaShape: boolean;
    is3DShape: boolean;
    isChecked: boolean;
    position: Point;
    positionOffsetX: number;
    positionOffsetY: number;
    zIndex: number;
    gIndex: number;
    editable: string[];
    groups: { [key: string]: string }; //当前元素加入的组的列表
    relations?: { [key: string]: string }; // 当前组包含的元素
    // group: string;
    // groupVpc: string[];
    // groupSubnet: string[];
    // groupSecurity: string[];
    sticky: string;
    styles: any;
    data: any;
    connection: { [key: string]: Point };
    d2_connection: { [key: string]: Point };
    connectable: boolean;
    checkedable: boolean;
    linkable: boolean;
    component?: Svg | undefined;
    customize: object;
    width: number;
    height: number;
    forever: boolean;
  }

  export interface SigmaType {
    getComponents(): ShapeType[];

    getLayers(): Layer[];
    registerComponent<S extends ShapeType>(s: S, r: Renderer): boolean;
    toggleMode(): void;

    setScale(scale: number): void;
    scaleStep(step: number): void;
    loadShapes(shapes: any): void;

    // setCursorBehavior(action: 'grab' | 'move'): void;
    setStyleNode(data: { (k: string): ShapeStyles }, keys: string[], opt?: { _historical : boolean;}): void;
    createNode(s: ShapeType, ops?: CreateNodeOption): ShapeVnode['key'];
    createLineNode(options: {
      startKey: string,
      endKey: string,
      arrowType?: string,
      startDir?: string,
      endDir?: string,
    }): ShapeVnode['key'];
    replaceNode<T extends object>(s: Shape, ops: { customize: T }): void;
    cloneNode(): void;
    cutNode(): void;
    copyNode(): void;
    deleteNode(): void;
    appendChild(
      vkey: string,
      child: string,
      options?: { [key: string]: any }
    ): void;
    bindStickyUnit: (
      vkey: string,
      child: string,
      options?: { [key: string]: any }
    ) => VnodeStickyUnit;
    lockNode(): void;
    unlockNode(): void;
    toggleLockNode(): void;
    descriptNode(desc: string): void;
    setNodePosition(position: Point, key: string): void;
    setVnodePosition(vnode: ShapeVnode, position: Point): void;

    redo(): void;
    undo(): void;

    raiseShape(): void;
    lowerShape(): void;

    toggleLayerVisable(l: Layer): void;

    setBackgroundGrid(w: number, h: number): void;
    getBackgroundGrid(): { w: number; h: number; x: number; y: number };

    getRenderedXML(config?: {
      width: number;
      height: number;
      includeGrid: boolean;
      onlySelection: boolean;
      exportMode: ViewMode;
    }): string;

    clearPaint(): void;
    resetViewbox(): void;

    initWithGraphData(g: GraphData): void;
    initWithTPLGraphData(content: string): void;
    getLocalGraphData(): string;
    getNetworkShapes(): ShapeVnode[];

    // setGroup(key: string): void;
    setSecurityGroup(key?: string): string;
    setVPCGroup(key?: string): string;
    setSubnetGroup(key?: string): string;
    setBaseGroup: (key?: string) => string;
    setAreaGroup: (key?: string) => string;
    setAvailabilityZoneGroup: (key?: string) => string;
    setCcnGroup: (key?: string) => string;
    setTkeGroup: (key?: string) => string;
    zoomGraphFit(): void;

    setEditor(config: EditorParam): void;
    resetEditor(): void;
    getReferOriginPosition(): Point;

    setCustomize(data: any, shapekey: string): boolean;
    resetCustomize(shapekey: string): boolean;
    getCustomize(shapekey: string): any;

    alignLeft(keys?: string[]): void;
    alignRight(keys?: string[]): void;
    alignTop(keys?: string[]): void;
    alignBottom(keys?: string[]): void;
    arrangeRow(step?: number, keys?: string[]): void;
    arrangeCol(step?: number, keys?: string[]): void;
    arrangeSquare(step?: number, keys?: string[]): void;

    setDispersion(type: 'row' | 'col'): void;

    setUneditable(): boolean;
    setEditable(): boolean;
    toggleEditable(): boolean;
    setGlobalValue(value: { [key: string]: any }): void;
    getGlobalValue(): GlobalValue;
    transformPoint(
      targetMode: ViewMode,
      point: Point,
      exact?: boolean,
      offsetX?: number,
      offsetY?: number,
    ): Point;

    refreshViewport(): void;

    toggleShapeChecked(vnodes: ShapeVnode[]): void;
    setShapeChecked(vnodes: ShapeVnode[], is: boolean): void;
    getCheckedShapes(options?: {
      excludeKeys?: string[];
      excludeType?: string;
    }): ShapeVnode[];
    clearHistory: () => void;
    createNodeLabel: (vnode: ShapeVnode, name: string) => void;
    setConnectionNumber: (n: number) => void;
    getMode: () => string;
    transform3DTo2D: (point: {x: number, y: number}, exact?: boolean, offsetX?: number, offsetY?: number) => {
      x: number;
      y: number;
    };
    transform2DTo3D: (point: {x: number, y: number}, exact?: boolean, offsetX?: number, offsetY?: number) => {
      x: number;
      y: number;
    };
    zoomNodeToMid: (key: string, containerId?: string) => void;
    getStandardPosition: (point: {x: number, y: number}) => {x: number, y: number};
    cmdStack: {
      undoableSetState: (setState: <A>(value: A) => void) => <A>(value: A) => void;
      pushCmd: <T extends (...args: any) => any>(cmd: {
        undo: Function;
        execute: T;
      }) => ReturnType<T>;
      clearCmd: () => void;
      getUndoStatus: () => CmdStatus;
      addListener: (listener: (status: CmdStatus) => void) => () => void;
    };
  }

  export type SigmaConstructorOptions = {
    mode?: ViewMode | string;
    callbacks?: SigmaOptionCallbacks;
  };

  type SigmaConstructor = new (
    el: HTMLElement,
    ops?: SigmaConstructorOptions
  ) => SigmaType;

  const Sigma: SigmaConstructor;

  export default Sigma;
}
