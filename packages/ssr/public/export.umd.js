(function(Ue,Ve){typeof exports=="object"&&typeof module<"u"?module.exports=Ve(require("react")):typeof define=="function"&&define.amd?define(["react"],Ve):(Ue=typeof globalThis<"u"?globalThis:Ue||self,Ue.Export=Ve(Ue.React))})(this,function(Ue){"use strict";var w5=Object.defineProperty;var k5=(Ue,Ve,wr)=>Ve in Ue?w5(Ue,Ve,{enumerable:!0,configurable:!0,writable:!0,value:wr}):Ue[Ve]=wr;var M=(Ue,Ve,wr)=>k5(Ue,typeof Ve!="symbol"?Ve+"":Ve,wr);var Ve=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function wr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var yo={exports:{}},fs={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xh;function Kg(){if(xh)return fs;xh=1;var e=Ue,t=Symbol.for("react.element"),n=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function c(l,u,f){var d,p={},y=null,m=null;f!==void 0&&(y=""+f),u.key!==void 0&&(y=""+u.key),u.ref!==void 0&&(m=u.ref);for(d in u)i.call(u,d)&&!a.hasOwnProperty(d)&&(p[d]=u[d]);if(l&&l.defaultProps)for(d in u=l.defaultProps,u)p[d]===void 0&&(p[d]=u[d]);return{$$typeof:t,type:l,key:y,ref:m,props:p,_owner:s.current}}return fs.Fragment=n,fs.jsx=c,fs.jsxs=c,fs}var ds={},_h;function Zg(){if(_h)return ds;_h=1;var e={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */return e.NODE_ENV!=="production"&&function(){var t=Ue,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),u=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),_=Symbol.for("react.offscreen"),x=Symbol.iterator,b="@@iterator";function w(T){if(T===null||typeof T!="object")return null;var K=x&&T[x]||T[b];return typeof K=="function"?K:null}var C=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function A(T){{for(var K=arguments.length,it=new Array(K>1?K-1:0),mt=1;mt<K;mt++)it[mt-1]=arguments[mt];L("error",T,it)}}function L(T,K,it){{var mt=C.ReactDebugCurrentFrame,Kt=mt.getStackAddendum();Kt!==""&&(K+="%s",it=it.concat([Kt]));var ne=it.map(function(Ht){return String(Ht)});ne.unshift("Warning: "+K),Function.prototype.apply.call(console[T],console,ne)}}var P=!1,R=!1,O=!1,W=!1,B=!1,H;H=Symbol.for("react.module.reference");function Y(T){return!!(typeof T=="string"||typeof T=="function"||T===s||T===c||B||T===a||T===d||T===p||W||T===_||P||R||O||typeof T=="object"&&T!==null&&(T.$$typeof===m||T.$$typeof===y||T.$$typeof===l||T.$$typeof===u||T.$$typeof===f||T.$$typeof===H||T.getModuleId!==void 0))}function V(T,K,it){var mt=T.displayName;if(mt)return mt;var Kt=K.displayName||K.name||"";return Kt!==""?it+"("+Kt+")":it}function at(T){return T.displayName||"Context"}function rt(T){if(T==null)return null;if(typeof T.tag=="number"&&A("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof T=="function")return T.displayName||T.name||null;if(typeof T=="string")return T;switch(T){case s:return"Fragment";case i:return"Portal";case c:return"Profiler";case a:return"StrictMode";case d:return"Suspense";case p:return"SuspenseList"}if(typeof T=="object")switch(T.$$typeof){case u:var K=T;return at(K)+".Consumer";case l:var it=T;return at(it._context)+".Provider";case f:return V(T,T.render,"ForwardRef");case y:var mt=T.displayName||null;return mt!==null?mt:rt(T.type)||"Memo";case m:{var Kt=T,ne=Kt._payload,Ht=Kt._init;try{return rt(Ht(ne))}catch{return null}}}return null}var ft=Object.assign,$t=0,Mt,Gt,Qt,et,nt,ot,xt;function Vt(){}Vt.__reactDisabledLog=!0;function Tt(){{if($t===0){Mt=console.log,Gt=console.info,Qt=console.warn,et=console.error,nt=console.group,ot=console.groupCollapsed,xt=console.groupEnd;var T={configurable:!0,enumerable:!0,value:Vt,writable:!0};Object.defineProperties(console,{info:T,log:T,warn:T,error:T,group:T,groupCollapsed:T,groupEnd:T})}$t++}}function Wt(){{if($t--,$t===0){var T={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:ft({},T,{value:Mt}),info:ft({},T,{value:Gt}),warn:ft({},T,{value:Qt}),error:ft({},T,{value:et}),group:ft({},T,{value:nt}),groupCollapsed:ft({},T,{value:ot}),groupEnd:ft({},T,{value:xt})})}$t<0&&A("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Xt=C.ReactCurrentDispatcher,oe;function se(T,K,it){{if(oe===void 0)try{throw Error()}catch(Kt){var mt=Kt.stack.trim().match(/\n( *(at )?)/);oe=mt&&mt[1]||""}return`
`+oe+T}}var Le=!1,Ee;{var ln=typeof WeakMap=="function"?WeakMap:Map;Ee=new ln}function un(T,K){if(!T||Le)return"";{var it=Ee.get(T);if(it!==void 0)return it}var mt;Le=!0;var Kt=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var ne;ne=Xt.current,Xt.current=null,Tt();try{if(K){var Ht=function(){throw Error()};if(Object.defineProperty(Ht.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Ht,[])}catch(Xe){mt=Xe}Reflect.construct(T,[],Ht)}else{try{Ht.call()}catch(Xe){mt=Xe}T.call(Ht.prototype)}}else{try{throw Error()}catch(Xe){mt=Xe}T()}}catch(Xe){if(Xe&&mt&&typeof Xe.stack=="string"){for(var It=Xe.stack.split(`
`),Be=mt.stack.split(`
`),me=It.length-1,fe=Be.length-1;me>=1&&fe>=0&&It[me]!==Be[fe];)fe--;for(;me>=1&&fe>=0;me--,fe--)if(It[me]!==Be[fe]){if(me!==1||fe!==1)do if(me--,fe--,fe<0||It[me]!==Be[fe]){var fn=`
`+It[me].replace(" at new "," at ");return T.displayName&&fn.includes("<anonymous>")&&(fn=fn.replace("<anonymous>",T.displayName)),typeof T=="function"&&Ee.set(T,fn),fn}while(me>=1&&fe>=0);break}}}finally{Le=!1,Xt.current=ne,Wt(),Error.prepareStackTrace=Kt}var Kn=T?T.displayName||T.name:"",wi=Kn?se(Kn):"";return typeof T=="function"&&Ee.set(T,wi),wi}function ce(T,K,it){return un(T,!1)}function wn(T){var K=T.prototype;return!!(K&&K.isReactComponent)}function hn(T,K,it){if(T==null)return"";if(typeof T=="function")return un(T,wn(T));if(typeof T=="string")return se(T);switch(T){case d:return se("Suspense");case p:return se("SuspenseList")}if(typeof T=="object")switch(T.$$typeof){case f:return ce(T.render);case y:return hn(T.type,K,it);case m:{var mt=T,Kt=mt._payload,ne=mt._init;try{return hn(ne(Kt),K,it)}catch{}}}return""}var Me=Object.prototype.hasOwnProperty,li={},Yr=C.ReactDebugCurrentFrame;function kn(T){if(T){var K=T._owner,it=hn(T.type,T._source,K?K.type:null);Yr.setExtraStackFrame(it)}else Yr.setExtraStackFrame(null)}function tn(T,K,it,mt,Kt){{var ne=Function.call.bind(Me);for(var Ht in T)if(ne(T,Ht)){var It=void 0;try{if(typeof T[Ht]!="function"){var Be=Error((mt||"React class")+": "+it+" type `"+Ht+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof T[Ht]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Be.name="Invariant Violation",Be}It=T[Ht](K,Ht,mt,it,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(me){It=me}It&&!(It instanceof Error)&&(kn(Kt),A("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",mt||"React class",it,Ht,typeof It),kn(null)),It instanceof Error&&!(It.message in li)&&(li[It.message]=!0,kn(Kt),A("Failed %s type: %s",it,It.message),kn(null))}}}var vi=Array.isArray;function Bi(T){return vi(T)}function Jl(T){{var K=typeof Symbol=="function"&&Symbol.toStringTag,it=K&&T[Symbol.toStringTag]||T.constructor.name||"Object";return it}}function hr(T){try{return da(T),!1}catch{return!0}}function da(T){return""+T}function Gi(T){if(hr(T))return A("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Jl(T)),da(T)}var In=C.ReactCurrentOwner,Bs={key:!0,ref:!0,__self:!0,__source:!0},Xr,Kr,fr;fr={};function Gs(T){if(Me.call(T,"ref")){var K=Object.getOwnPropertyDescriptor(T,"ref").get;if(K&&K.isReactWarning)return!1}return T.ref!==void 0}function Hs(T){if(Me.call(T,"key")){var K=Object.getOwnPropertyDescriptor(T,"key").get;if(K&&K.isReactWarning)return!1}return T.key!==void 0}function Us(T,K){if(typeof T.ref=="string"&&In.current&&K&&In.current.stateNode!==K){var it=rt(In.current.type);fr[it]||(A('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',rt(In.current.type),T.ref),fr[it]=!0)}}function Vs(T,K){{var it=function(){Xr||(Xr=!0,A("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",K))};it.isReactWarning=!0,Object.defineProperty(T,"key",{get:it,configurable:!0})}}function js(T,K){{var it=function(){Kr||(Kr=!0,A("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",K))};it.isReactWarning=!0,Object.defineProperty(T,"ref",{get:it,configurable:!0})}}var Ql=function(T,K,it,mt,Kt,ne,Ht){var It={$$typeof:n,type:T,key:K,ref:it,props:Ht,_owner:ne};return It._store={},Object.defineProperty(It._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(It,"_self",{configurable:!1,enumerable:!1,writable:!1,value:mt}),Object.defineProperty(It,"_source",{configurable:!1,enumerable:!1,writable:!1,value:Kt}),Object.freeze&&(Object.freeze(It.props),Object.freeze(It)),It};function tu(T,K,it,mt,Kt){{var ne,Ht={},It=null,Be=null;it!==void 0&&(Gi(it),It=""+it),Hs(K)&&(Gi(K.key),It=""+K.key),Gs(K)&&(Be=K.ref,Us(K,Kt));for(ne in K)Me.call(K,ne)&&!Bs.hasOwnProperty(ne)&&(Ht[ne]=K[ne]);if(T&&T.defaultProps){var me=T.defaultProps;for(ne in me)Ht[ne]===void 0&&(Ht[ne]=me[ne])}if(It||Be){var fe=typeof T=="function"?T.displayName||T.name||"Unknown":T;It&&Vs(Ht,fe),Be&&js(Ht,fe)}return Ql(T,It,Be,Kt,mt,In.current,Ht)}}var qs=C.ReactCurrentOwner,Ys=C.ReactDebugCurrentFrame;function bi(T){if(T){var K=T._owner,it=hn(T.type,T._source,K?K.type:null);Ys.setExtraStackFrame(it)}else Ys.setExtraStackFrame(null)}var Xs;Xs=!1;function Ks(T){return typeof T=="object"&&T!==null&&T.$$typeof===n}function pa(){{if(qs.current){var T=rt(qs.current.type);if(T)return`

Check the render method of \``+T+"`."}return""}}function eu(T){return""}var Zs={};function nu(T){{var K=pa();if(!K){var it=typeof T=="string"?T:T.displayName||T.name;it&&(K=`

Check the top-level render call using <`+it+">.")}return K}}function ga(T,K){{if(!T._store||T._store.validated||T.key!=null)return;T._store.validated=!0;var it=nu(K);if(Zs[it])return;Zs[it]=!0;var mt="";T&&T._owner&&T._owner!==qs.current&&(mt=" It was passed a child from "+rt(T._owner.type)+"."),bi(T),A('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',it,mt),bi(null)}}function ya(T,K){{if(typeof T!="object")return;if(Bi(T))for(var it=0;it<T.length;it++){var mt=T[it];Ks(mt)&&ga(mt,K)}else if(Ks(T))T._store&&(T._store.validated=!0);else if(T){var Kt=w(T);if(typeof Kt=="function"&&Kt!==T.entries)for(var ne=Kt.call(T),Ht;!(Ht=ne.next()).done;)Ks(Ht.value)&&ga(Ht.value,K)}}}function Js(T){{var K=T.type;if(K==null||typeof K=="string")return;var it;if(typeof K=="function")it=K.propTypes;else if(typeof K=="object"&&(K.$$typeof===f||K.$$typeof===y))it=K.propTypes;else return;if(it){var mt=rt(K);tn(it,T.props,"prop",mt,T)}else if(K.PropTypes!==void 0&&!Xs){Xs=!0;var Kt=rt(K);A("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",Kt||"Unknown")}typeof K.getDefaultProps=="function"&&!K.getDefaultProps.isReactClassApproved&&A("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function iu(T){{for(var K=Object.keys(T.props),it=0;it<K.length;it++){var mt=K[it];if(mt!=="children"&&mt!=="key"){bi(T),A("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",mt),bi(null);break}}T.ref!==null&&(bi(T),A("Invalid attribute `ref` supplied to `React.Fragment`."),bi(null))}}var Zr={};function ma(T,K,it,mt,Kt,ne){{var Ht=Y(T);if(!Ht){var It="";(T===void 0||typeof T=="object"&&T!==null&&Object.keys(T).length===0)&&(It+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var Be=eu();Be?It+=Be:It+=pa();var me;T===null?me="null":Bi(T)?me="array":T!==void 0&&T.$$typeof===n?(me="<"+(rt(T.type)||"Unknown")+" />",It=" Did you accidentally export a JSX literal instead of a component?"):me=typeof T,A("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",me,It)}var fe=tu(T,K,it,Kt,ne);if(fe==null)return fe;if(Ht){var fn=K.children;if(fn!==void 0)if(mt)if(Bi(fn)){for(var Kn=0;Kn<fn.length;Kn++)ya(fn[Kn],T);Object.freeze&&Object.freeze(fn)}else A("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ya(fn,T)}if(Me.call(K,"key")){var wi=rt(T),Xe=Object.keys(K).filter(function(_a){return _a!=="key"}),Qs=Xe.length>0?"{key: someKey, "+Xe.join(": ..., ")+": ...}":"{key: someKey}";if(!Zr[wi+Qs]){var xa=Xe.length>0?"{"+Xe.join(": ..., ")+": ...}":"{}";A(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,Qs,wi,xa,wi),Zr[wi+Qs]=!0}}return T===s?iu(fe):Js(fe),fe}}function ru(T,K,it){return ma(T,K,it,!0)}function su(T,K,it){return ma(T,K,it,!1)}var ou=su,au=ru;ds.Fragment=s,ds.jsx=ou,ds.jsxs=au}(),ds}var vh;function Jg(){if(vh)return yo.exports;vh=1;var e={};return e.NODE_ENV==="production"?yo.exports=Kg():yo.exports=Zg(),yo.exports}var oc=Jg();const ac={},bh=[];function Ct(e,t){if(Array.isArray(e)){for(const n of e)Ct(n,t);return}if(typeof e=="object"){for(const n in e)Ct(n,e[n]);return}wh(Object.getOwnPropertyNames(t)),ac[e]=Object.assign(ac[e]||{},t)}function sn(e){return ac[e]||{}}function Qg(){return[...new Set(bh)]}function wh(e){bh.push(...e)}function cc(e,t){let n;const i=e.length,s=[];for(n=0;n<i;n++)s.push(t(e[n]));return s}function ty(e,t){let n;const i=e.length,s=[];for(n=0;n<i;n++)t(e[n])&&s.push(e[n]);return s}function lc(e){return e%360*Math.PI/180}function ey(e){return e.replace(/([A-Z])/g,function(t,n){return"-"+n.toLowerCase()})}function kh(e){return e.charAt(0).toUpperCase()+e.slice(1)}function kr(e,t,n,i){return(t==null||n==null)&&(i=i||e.bbox(),t==null?t=i.width/i.height*n:n==null&&(n=i.height/i.width*t)),{width:t,height:n}}function uc(e,t){const n=e.origin;let i=e.ox!=null?e.ox:e.originX!=null?e.originX:"center",s=e.oy!=null?e.oy:e.originY!=null?e.originY:"center";n!=null&&([i,s]=Array.isArray(n)?n:typeof n=="object"?[n.x,n.y]:[n,n]);const a=typeof i=="string",c=typeof s=="string";if(a||c){const{height:l,width:u,x:f,y:d}=t.bbox();a&&(i=i.includes("left")?f:i.includes("right")?f+u:f+u/2),c&&(s=s.includes("top")?d:s.includes("bottom")?d+l:d+l/2)}return[i,s]}const ny=new Set(["desc","metadata","title"]),hc=e=>ny.has(e.nodeName),Sh=(e,t,n={})=>{const i={...t};for(const s in i)i[s].valueOf()===n[s]&&delete i[s];Object.keys(i).length?e.node.setAttribute("data-svgjs",JSON.stringify(i)):(e.node.removeAttribute("data-svgjs"),e.node.removeAttribute("svgjs:data"))},fc="http://www.w3.org/2000/svg",iy="http://www.w3.org/1999/xhtml",dc="http://www.w3.org/2000/xmlns/",ps="http://www.w3.org/1999/xlink",qt={window:typeof window>"u"?null:window,document:typeof document>"u"?null:document};function ry(){return qt.window}class pc{}const Ji={},gc="___SYMBOL___ROOT___";function gs(e,t=fc){return qt.document.createElementNS(t,e)}function Je(e,t=!1){if(e instanceof pc)return e;if(typeof e=="object")return yc(e);if(e==null)return new Ji[gc];if(typeof e=="string"&&e.charAt(0)!=="<")return yc(qt.document.querySelector(e));const n=t?qt.document.createElement("div"):gs("svg");return n.innerHTML=e,e=yc(n.firstChild),n.removeChild(n.firstChild),e}function ue(e,t){return t&&(t instanceof qt.window.Node||t.ownerDocument&&t instanceof t.ownerDocument.defaultView.Node)?t:gs(e)}function Ln(e){if(!e)return null;if(e.instance instanceof pc)return e.instance;if(e.nodeName==="#document-fragment")return new Ji.Fragment(e);let t=kh(e.nodeName||"Dom");return t==="LinearGradient"||t==="RadialGradient"?t="Gradient":Ji[t]||(t="Dom"),new Ji[t](e)}let yc=Ln;function Yt(e,t=e.name,n=!1){return Ji[t]=e,n&&(Ji[gc]=e),wh(Object.getOwnPropertyNames(e.prototype)),e}function sy(e){return Ji[e]}let oy=1e3;function Ch(e){return"Svgjs"+kh(e)+oy++}function Ah(e){for(let t=e.children.length-1;t>=0;t--)Ah(e.children[t]);return e.id&&(e.id=Ch(e.nodeName)),e}function Dt(e,t){let n,i;for(e=Array.isArray(e)?e:[e],i=e.length-1;i>=0;i--)for(n in t)e[i].prototype[n]=t[n]}function he(e){return function(...t){const n=t[t.length-1];return n&&n.constructor===Object&&!(n instanceof Array)?e.apply(this,t.slice(0,-1)).attr(n):e.apply(this,t)}}function ay(){return this.parent().children()}function cy(){return this.parent().index(this)}function ly(){return this.siblings()[this.position()+1]}function uy(){return this.siblings()[this.position()-1]}function hy(){const e=this.position();return this.parent().add(this.remove(),e+1),this}function fy(){const e=this.position();return this.parent().add(this.remove(),e?e-1:0),this}function dy(){return this.parent().add(this.remove()),this}function py(){return this.parent().add(this.remove(),0),this}function gy(e){e=Je(e),e.remove();const t=this.position();return this.parent().add(e,t),this}function yy(e){e=Je(e),e.remove();const t=this.position();return this.parent().add(e,t+1),this}function my(e){return e=Je(e),e.before(this),this}function xy(e){return e=Je(e),e.after(this),this}Ct("Dom",{siblings:ay,position:cy,next:ly,prev:uy,forward:hy,backward:fy,front:dy,back:py,before:gy,after:yy,insertBefore:my,insertAfter:xy});const Eh=/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,_y=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,vy=/rgb\((\d+),(\d+),(\d+)\)/,by=/(#[a-z_][a-z0-9\-_]*)/i,wy=/\)\s*,?\s*/,ky=/\s/g,Mh=/^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i,Th=/^rgb\(/,Lh=/^(\s+)?$/,Oh=/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Sy=/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,di=/[\s,]+/,mc=/[MLHVCSQTAZ]/i;function Cy(){const e=this.attr("class");return e==null?[]:e.trim().split(di)}function Ay(e){return this.classes().indexOf(e)!==-1}function Ey(e){if(!this.hasClass(e)){const t=this.classes();t.push(e),this.attr("class",t.join(" "))}return this}function My(e){return this.hasClass(e)&&this.attr("class",this.classes().filter(function(t){return t!==e}).join(" ")),this}function Ty(e){return this.hasClass(e)?this.removeClass(e):this.addClass(e)}Ct("Dom",{classes:Cy,hasClass:Ay,addClass:Ey,removeClass:My,toggleClass:Ty});function Ly(e,t){const n={};if(arguments.length===0)return this.node.style.cssText.split(/\s*;\s*/).filter(function(i){return!!i.length}).forEach(function(i){const s=i.split(/\s*:\s*/);n[s[0]]=s[1]}),n;if(arguments.length<2){if(Array.isArray(e)){for(const i of e){const s=i;n[i]=this.node.style.getPropertyValue(s)}return n}if(typeof e=="string")return this.node.style.getPropertyValue(e);if(typeof e=="object")for(const i in e)this.node.style.setProperty(i,e[i]==null||Lh.test(e[i])?"":e[i])}return arguments.length===2&&this.node.style.setProperty(e,t==null||Lh.test(t)?"":t),this}function Oy(){return this.css("display","")}function Dy(){return this.css("display","none")}function Py(){return this.css("display")!=="none"}Ct("Dom",{css:Ly,show:Oy,hide:Dy,visible:Py});function Ry(e,t,n){if(e==null)return this.data(cc(ty(this.node.attributes,i=>i.nodeName.indexOf("data-")===0),i=>i.nodeName.slice(5)));if(e instanceof Array){const i={};for(const s of e)i[s]=this.data(s);return i}else if(typeof e=="object")for(t in e)this.data(t,e[t]);else if(arguments.length<2)try{return JSON.parse(this.attr("data-"+e))}catch{return this.attr("data-"+e)}else this.attr("data-"+e,t===null?null:n===!0||typeof t=="string"||typeof t=="number"?t:JSON.stringify(t));return this}Ct("Dom",{data:Ry});function Iy(e,t){if(typeof arguments[0]=="object")for(const n in e)this.remember(n,e[n]);else{if(arguments.length===1)return this.memory()[e];this.memory()[e]=t}return this}function Ny(){if(arguments.length===0)this._memory={};else for(let e=arguments.length-1;e>=0;e--)delete this.memory()[arguments[e]];return this}function $y(){return this._memory=this._memory||{}}Ct("Dom",{remember:Iy,forget:Ny,memory:$y});function Wy(e){return e.length===4?["#",e.substring(1,2),e.substring(1,2),e.substring(2,3),e.substring(2,3),e.substring(3,4),e.substring(3,4)].join(""):e}function zy(e){const t=Math.round(e),i=Math.max(0,Math.min(255,t)).toString(16);return i.length===1?"0"+i:i}function Sr(e,t){for(let n=t.length;n--;)if(e[t[n]]==null)return!1;return!0}function Fy(e,t){const n=Sr(e,"rgb")?{_a:e.r,_b:e.g,_c:e.b,_d:0,space:"rgb"}:Sr(e,"xyz")?{_a:e.x,_b:e.y,_c:e.z,_d:0,space:"xyz"}:Sr(e,"hsl")?{_a:e.h,_b:e.s,_c:e.l,_d:0,space:"hsl"}:Sr(e,"lab")?{_a:e.l,_b:e.a,_c:e.b,_d:0,space:"lab"}:Sr(e,"lch")?{_a:e.l,_b:e.c,_c:e.h,_d:0,space:"lch"}:Sr(e,"cmyk")?{_a:e.c,_b:e.m,_c:e.y,_d:e.k,space:"cmyk"}:{_a:0,_b:0,_c:0,space:"rgb"};return n.space=t||n.space,n}function By(e){return e==="lab"||e==="xyz"||e==="lch"}function xc(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}let ei=class Pe{constructor(...t){this.init(...t)}static isColor(t){return t&&(t instanceof Pe||this.isRgb(t)||this.test(t))}static isRgb(t){return t&&typeof t.r=="number"&&typeof t.g=="number"&&typeof t.b=="number"}static random(t="vibrant",n){const{random:i,round:s,sin:a,PI:c}=Math;if(t==="vibrant"){const l=24*i()+57,u=38*i()+45,f=360*i();return new Pe(l,u,f,"lch")}else if(t==="sine"){n=n??i();const l=s(80*a(2*c*n/.5+.01)+150),u=s(50*a(2*c*n/.5****)+200),f=s(100*a(2*c*n/.5****)+150);return new Pe(l,u,f)}else if(t==="pastel"){const l=8*i()+86,u=17*i()+9,f=360*i();return new Pe(l,u,f,"lch")}else if(t==="dark"){const l=10+10*i(),u=50*i()+86,f=360*i();return new Pe(l,u,f,"lch")}else if(t==="rgb"){const l=255*i(),u=255*i(),f=255*i();return new Pe(l,u,f)}else if(t==="lab"){const l=100*i(),u=256*i()-128,f=256*i()-128;return new Pe(l,u,f,"lab")}else if(t==="grey"){const l=255*i();return new Pe(l,l,l)}else throw new Error("Unsupported random color mode")}static test(t){return typeof t=="string"&&(Mh.test(t)||Th.test(t))}cmyk(){const{_a:t,_b:n,_c:i}=this.rgb(),[s,a,c]=[t,n,i].map(y=>y/255),l=Math.min(1-s,1-a,1-c);if(l===1)return new Pe(0,0,0,1,"cmyk");const u=(1-s-l)/(1-l),f=(1-a-l)/(1-l),d=(1-c-l)/(1-l);return new Pe(u,f,d,l,"cmyk")}hsl(){const{_a:t,_b:n,_c:i}=this.rgb(),[s,a,c]=[t,n,i].map(x=>x/255),l=Math.max(s,a,c),u=Math.min(s,a,c),f=(l+u)/2,d=l===u,p=l-u,y=d?0:f>.5?p/(2-l-u):p/(l+u),m=d?0:l===s?((a-c)/p+(a<c?6:0))/6:l===a?((c-s)/p+2)/6:l===c?((s-a)/p+4)/6:0;return new Pe(360*m,100*y,100*f,"hsl")}init(t=0,n=0,i=0,s=0,a="rgb"){if(t=t||0,this.space)for(const p in this.space)delete this[this.space[p]];if(typeof t=="number")a=typeof s=="string"?s:a,s=typeof s=="string"?0:s,Object.assign(this,{_a:t,_b:n,_c:i,_d:s,space:a});else if(t instanceof Array)this.space=n||(typeof t[3]=="string"?t[3]:t[4])||"rgb",Object.assign(this,{_a:t[0],_b:t[1],_c:t[2],_d:t[3]||0});else if(t instanceof Object){const p=Fy(t,n);Object.assign(this,p)}else if(typeof t=="string")if(Th.test(t)){const p=t.replace(ky,""),[y,m,_]=vy.exec(p).slice(1,4).map(x=>parseInt(x));Object.assign(this,{_a:y,_b:m,_c:_,_d:0,space:"rgb"})}else if(Mh.test(t)){const p=x=>parseInt(x,16),[,y,m,_]=_y.exec(Wy(t)).map(p);Object.assign(this,{_a:y,_b:m,_c:_,_d:0,space:"rgb"})}else throw Error("Unsupported string format, can't construct Color");const{_a:c,_b:l,_c:u,_d:f}=this,d=this.space==="rgb"?{r:c,g:l,b:u}:this.space==="xyz"?{x:c,y:l,z:u}:this.space==="hsl"?{h:c,s:l,l:u}:this.space==="lab"?{l:c,a:l,b:u}:this.space==="lch"?{l:c,c:l,h:u}:this.space==="cmyk"?{c,m:l,y:u,k:f}:{};Object.assign(this,d)}lab(){const{x:t,y:n,z:i}=this.xyz(),s=116*n-16,a=500*(t-n),c=200*(n-i);return new Pe(s,a,c,"lab")}lch(){const{l:t,a:n,b:i}=this.lab(),s=Math.sqrt(n**2+i**2);let a=180*Math.atan2(i,n)/Math.PI;return a<0&&(a*=-1,a=360-a),new Pe(t,s,a,"lch")}rgb(){if(this.space==="rgb")return this;if(By(this.space)){let{x:t,y:n,z:i}=this;if(this.space==="lab"||this.space==="lch"){let{l:m,a:_,b:x}=this;if(this.space==="lch"){const{c:R,h:O}=this,W=Math.PI/180;_=R*Math.cos(W*O),x=R*Math.sin(W*O)}const b=(m+16)/116,w=_/500+b,C=b-x/200,A=16/116,L=.008856,P=7.787;t=.95047*(w**3>L?w**3:(w-A)/P),n=1*(b**3>L?b**3:(b-A)/P),i=1.08883*(C**3>L?C**3:(C-A)/P)}const s=t*3.2406+n*-1.5372+i*-.4986,a=t*-.9689+n*1.8758+i*.0415,c=t*.0557+n*-.204+i*1.057,l=Math.pow,u=.0031308,f=s>u?1.055*l(s,1/2.4)-.055:12.92*s,d=a>u?1.055*l(a,1/2.4)-.055:12.92*a,p=c>u?1.055*l(c,1/2.4)-.055:12.92*c;return new Pe(255*f,255*d,255*p)}else if(this.space==="hsl"){let{h:t,s:n,l:i}=this;if(t/=360,n/=100,i/=100,n===0)return i*=255,new Pe(i,i,i);const s=i<.5?i*(1+n):i+n-i*n,a=2*i-s,c=255*xc(a,s,t+1/3),l=255*xc(a,s,t),u=255*xc(a,s,t-1/3);return new Pe(c,l,u)}else if(this.space==="cmyk"){const{c:t,m:n,y:i,k:s}=this,a=255*(1-Math.min(1,t*(1-s)+s)),c=255*(1-Math.min(1,n*(1-s)+s)),l=255*(1-Math.min(1,i*(1-s)+s));return new Pe(a,c,l)}else return this}toArray(){const{_a:t,_b:n,_c:i,_d:s,space:a}=this;return[t,n,i,s,a]}toHex(){const[t,n,i]=this._clamped().map(zy);return`#${t}${n}${i}`}toRgb(){const[t,n,i]=this._clamped();return`rgb(${t},${n},${i})`}toString(){return this.toHex()}xyz(){const{_a:t,_b:n,_c:i}=this.rgb(),[s,a,c]=[t,n,i].map(w=>w/255),l=s>.04045?Math.pow((s+.055)/1.055,2.4):s/12.92,u=a>.04045?Math.pow((a+.055)/1.055,2.4):a/12.92,f=c>.04045?Math.pow((c+.055)/1.055,2.4):c/12.92,d=(l*.4124+u*.3576+f*.1805)/.95047,p=(l*.2126+u*.7152+f*.0722)/1,y=(l*.0193+u*.1192+f*.9505)/1.08883,m=d>.008856?Math.pow(d,1/3):7.787*d+16/116,_=p>.008856?Math.pow(p,1/3):7.787*p+16/116,x=y>.008856?Math.pow(y,1/3):7.787*y+16/116;return new Pe(m,_,x,"xyz")}_clamped(){const{_a:t,_b:n,_c:i}=this.rgb(),{max:s,min:a,round:c}=Math,l=u=>s(0,a(c(u),255));return[t,n,i].map(l)}};class we{constructor(...t){this.init(...t)}clone(){return new we(this)}init(t,n){const i={x:0,y:0},s=Array.isArray(t)?{x:t[0],y:t[1]}:typeof t=="object"?{x:t.x,y:t.y}:{x:t,y:n};return this.x=s.x==null?i.x:s.x,this.y=s.y==null?i.y:s.y,this}toArray(){return[this.x,this.y]}transform(t){return this.clone().transformO(t)}transformO(t){ut.isMatrixLike(t)||(t=new ut(t));const{x:n,y:i}=this;return this.x=t.a*n+t.c*i+t.e,this.y=t.b*n+t.d*i+t.f,this}}function Gy(e,t){return new we(e,t).transformO(this.screenCTM().inverseO())}function Cr(e,t,n){return Math.abs(t-e)<1e-6}class ut{constructor(...t){this.init(...t)}static formatTransforms(t){const n=t.flip==="both"||t.flip===!0,i=t.flip&&(n||t.flip==="x")?-1:1,s=t.flip&&(n||t.flip==="y")?-1:1,a=t.skew&&t.skew.length?t.skew[0]:isFinite(t.skew)?t.skew:isFinite(t.skewX)?t.skewX:0,c=t.skew&&t.skew.length?t.skew[1]:isFinite(t.skew)?t.skew:isFinite(t.skewY)?t.skewY:0,l=t.scale&&t.scale.length?t.scale[0]*i:isFinite(t.scale)?t.scale*i:isFinite(t.scaleX)?t.scaleX*i:i,u=t.scale&&t.scale.length?t.scale[1]*s:isFinite(t.scale)?t.scale*s:isFinite(t.scaleY)?t.scaleY*s:s,f=t.shear||0,d=t.rotate||t.theta||0,p=new we(t.origin||t.around||t.ox||t.originX,t.oy||t.originY),y=p.x,m=p.y,_=new we(t.position||t.px||t.positionX||NaN,t.py||t.positionY||NaN),x=_.x,b=_.y,w=new we(t.translate||t.tx||t.translateX,t.ty||t.translateY),C=w.x,A=w.y,L=new we(t.relative||t.rx||t.relativeX,t.ry||t.relativeY),P=L.x,R=L.y;return{scaleX:l,scaleY:u,skewX:a,skewY:c,shear:f,theta:d,rx:P,ry:R,tx:C,ty:A,ox:y,oy:m,px:x,py:b}}static fromArray(t){return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}}static isMatrixLike(t){return t.a!=null||t.b!=null||t.c!=null||t.d!=null||t.e!=null||t.f!=null}static matrixMultiply(t,n,i){const s=t.a*n.a+t.c*n.b,a=t.b*n.a+t.d*n.b,c=t.a*n.c+t.c*n.d,l=t.b*n.c+t.d*n.d,u=t.e+t.a*n.e+t.c*n.f,f=t.f+t.b*n.e+t.d*n.f;return i.a=s,i.b=a,i.c=c,i.d=l,i.e=u,i.f=f,i}around(t,n,i){return this.clone().aroundO(t,n,i)}aroundO(t,n,i){const s=t||0,a=n||0;return this.translateO(-s,-a).lmultiplyO(i).translateO(s,a)}clone(){return new ut(this)}decompose(t=0,n=0){const i=this.a,s=this.b,a=this.c,c=this.d,l=this.e,u=this.f,f=i*c-s*a,d=f>0?1:-1,p=d*Math.sqrt(i*i+s*s),y=Math.atan2(d*s,d*i),m=180/Math.PI*y,_=Math.cos(y),x=Math.sin(y),b=(i*a+s*c)/f,w=a*p/(b*i-s)||c*p/(b*s+i),C=l-t+t*_*p+n*(b*_*p-x*w),A=u-n+t*x*p+n*(b*x*p+_*w);return{scaleX:p,scaleY:w,shear:b,rotate:m,translateX:C,translateY:A,originX:t,originY:n,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}equals(t){if(t===this)return!0;const n=new ut(t);return Cr(this.a,n.a)&&Cr(this.b,n.b)&&Cr(this.c,n.c)&&Cr(this.d,n.d)&&Cr(this.e,n.e)&&Cr(this.f,n.f)}flip(t,n){return this.clone().flipO(t,n)}flipO(t,n){return t==="x"?this.scaleO(-1,1,n,0):t==="y"?this.scaleO(1,-1,0,n):this.scaleO(-1,-1,t,n||t)}init(t){const n=ut.fromArray([1,0,0,1,0,0]);return t=t instanceof Un?t.matrixify():typeof t=="string"?ut.fromArray(t.split(di).map(parseFloat)):Array.isArray(t)?ut.fromArray(t):typeof t=="object"&&ut.isMatrixLike(t)?t:typeof t=="object"?new ut().transform(t):arguments.length===6?ut.fromArray([].slice.call(arguments)):n,this.a=t.a!=null?t.a:n.a,this.b=t.b!=null?t.b:n.b,this.c=t.c!=null?t.c:n.c,this.d=t.d!=null?t.d:n.d,this.e=t.e!=null?t.e:n.e,this.f=t.f!=null?t.f:n.f,this}inverse(){return this.clone().inverseO()}inverseO(){const t=this.a,n=this.b,i=this.c,s=this.d,a=this.e,c=this.f,l=t*s-n*i;if(!l)throw new Error("Cannot invert "+this);const u=s/l,f=-n/l,d=-i/l,p=t/l,y=-(u*a+d*c),m=-(f*a+p*c);return this.a=u,this.b=f,this.c=d,this.d=p,this.e=y,this.f=m,this}lmultiply(t){return this.clone().lmultiplyO(t)}lmultiplyO(t){const n=this,i=t instanceof ut?t:new ut(t);return ut.matrixMultiply(i,n,this)}multiply(t){return this.clone().multiplyO(t)}multiplyO(t){const n=this,i=t instanceof ut?t:new ut(t);return ut.matrixMultiply(n,i,this)}rotate(t,n,i){return this.clone().rotateO(t,n,i)}rotateO(t,n=0,i=0){t=lc(t);const s=Math.cos(t),a=Math.sin(t),{a:c,b:l,c:u,d:f,e:d,f:p}=this;return this.a=c*s-l*a,this.b=l*s+c*a,this.c=u*s-f*a,this.d=f*s+u*a,this.e=d*s-p*a+i*a-n*s+n,this.f=p*s+d*a-n*a-i*s+i,this}scale(){return this.clone().scaleO(...arguments)}scaleO(t,n=t,i=0,s=0){arguments.length===3&&(s=i,i=n,n=t);const{a,b:c,c:l,d:u,e:f,f:d}=this;return this.a=a*t,this.b=c*n,this.c=l*t,this.d=u*n,this.e=f*t-i*t+i,this.f=d*n-s*n+s,this}shear(t,n,i){return this.clone().shearO(t,n,i)}shearO(t,n=0,i=0){const{a:s,b:a,c,d:l,e:u,f}=this;return this.a=s+a*t,this.c=c+l*t,this.e=u+f*t-i*t,this}skew(){return this.clone().skewO(...arguments)}skewO(t,n=t,i=0,s=0){arguments.length===3&&(s=i,i=n,n=t),t=lc(t),n=lc(n);const a=Math.tan(t),c=Math.tan(n),{a:l,b:u,c:f,d,e:p,f:y}=this;return this.a=l+u*a,this.b=u+l*c,this.c=f+d*a,this.d=d+f*c,this.e=p+y*a-s*a,this.f=y+p*c-i*c,this}skewX(t,n,i){return this.skew(t,0,n,i)}skewY(t,n,i){return this.skew(0,t,n,i)}toArray(){return[this.a,this.b,this.c,this.d,this.e,this.f]}toString(){return"matrix("+this.a+","+this.b+","+this.c+","+this.d+","+this.e+","+this.f+")"}transform(t){if(ut.isMatrixLike(t))return new ut(t).multiplyO(this);const n=ut.formatTransforms(t),i=this,{x:s,y:a}=new we(n.ox,n.oy).transform(i),c=new ut().translateO(n.rx,n.ry).lmultiplyO(i).translateO(-s,-a).scaleO(n.scaleX,n.scaleY).skewO(n.skewX,n.skewY).shearO(n.shear).rotateO(n.theta).translateO(s,a);if(isFinite(n.px)||isFinite(n.py)){const l=new we(s,a).transform(c),u=isFinite(n.px)?n.px-l.x:0,f=isFinite(n.py)?n.py-l.y:0;c.translateO(u,f)}return c.translateO(n.tx,n.ty),c}translate(t,n){return this.clone().translateO(t,n)}translateO(t,n){return this.e+=t||0,this.f+=n||0,this}valueOf(){return{a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}}function Hy(){return new ut(this.node.getCTM())}function Uy(){try{if(typeof this.isRoot=="function"&&!this.isRoot()){const e=this.rect(1,1),t=e.node.getScreenCTM();return e.remove(),new ut(t)}return new ut(this.node.getScreenCTM())}catch{return console.warn(`Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`),new ut}}Yt(ut,"Matrix");function Di(){if(!Di.nodes){const e=Je().size(2,0);e.node.style.cssText=["opacity: 0","position: absolute","left: -100%","top: -100%","overflow: hidden"].join(";"),e.attr("focusable","false"),e.attr("aria-hidden","true");const t=e.path().node;Di.nodes={svg:e,path:t}}if(!Di.nodes.svg.node.parentNode){const e=qt.document.body||qt.document.documentElement;Di.nodes.svg.addTo(e)}return Di.nodes}function Dh(e){return!e.width&&!e.height&&!e.x&&!e.y}function Vy(e){return e===qt.document||(qt.document.documentElement.contains||function(t){for(;t.parentNode;)t=t.parentNode;return t===qt.document}).call(qt.document.documentElement,e)}class Re{constructor(...t){this.init(...t)}addOffset(){return this.x+=qt.window.pageXOffset,this.y+=qt.window.pageYOffset,new Re(this)}init(t){const n=[0,0,0,0];return t=typeof t=="string"?t.split(di).map(parseFloat):Array.isArray(t)?t:typeof t=="object"?[t.left!=null?t.left:t.x,t.top!=null?t.top:t.y,t.width,t.height]:arguments.length===4?[].slice.call(arguments):n,this.x=t[0]||0,this.y=t[1]||0,this.width=this.w=t[2]||0,this.height=this.h=t[3]||0,this.x2=this.x+this.w,this.y2=this.y+this.h,this.cx=this.x+this.w/2,this.cy=this.y+this.h/2,this}isNulled(){return Dh(this)}merge(t){const n=Math.min(this.x,t.x),i=Math.min(this.y,t.y),s=Math.max(this.x+this.width,t.x+t.width)-n,a=Math.max(this.y+this.height,t.y+t.height)-i;return new Re(n,i,s,a)}toArray(){return[this.x,this.y,this.width,this.height]}toString(){return this.x+" "+this.y+" "+this.width+" "+this.height}transform(t){t instanceof ut||(t=new ut(t));let n=1/0,i=-1/0,s=1/0,a=-1/0;return[new we(this.x,this.y),new we(this.x2,this.y),new we(this.x,this.y2),new we(this.x2,this.y2)].forEach(function(l){l=l.transform(t),n=Math.min(n,l.x),i=Math.max(i,l.x),s=Math.min(s,l.y),a=Math.max(a,l.y)}),new Re(n,s,i-n,a-s)}}function Ph(e,t,n){let i;try{if(i=t(e.node),Dh(i)&&!Vy(e.node))throw new Error("Element not in the dom")}catch{i=n(e)}return i}function jy(){const n=Ph(this,s=>s.getBBox(),s=>{try{const a=s.clone().addTo(Di().svg).show(),c=a.node.getBBox();return a.remove(),c}catch(a){throw new Error(`Getting bbox of element "${s.node.nodeName}" is not possible: ${a.toString()}`)}});return new Re(n)}function qy(e){const i=Ph(this,a=>a.getBoundingClientRect(),a=>{throw new Error(`Getting rbox of element "${a.node.nodeName}" is not possible`)}),s=new Re(i);return e?s.transform(e.screenCTM().inverseO()):s.addOffset()}function Yy(e,t){const n=this.bbox();return e>n.x&&t>n.y&&e<n.x+n.width&&t<n.y+n.height}Ct({viewbox:{viewbox(e,t,n,i){return e==null?new Re(this.attr("viewBox")):this.attr("viewBox",new Re(e,t,n,i))},zoom(e,t){let{width:n,height:i}=this.attr(["width","height"]);if((!n&&!i||typeof n=="string"||typeof i=="string")&&(n=this.node.clientWidth,i=this.node.clientHeight),!n||!i)throw new Error("Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element");const s=this.viewbox(),a=n/s.width,c=i/s.height,l=Math.min(a,c);if(e==null)return l;let u=l/e;u===1/0&&(u=Number.MAX_SAFE_INTEGER/100),t=t||new we(n/2/a+s.x,i/2/c+s.y);const f=new Re(s).transform(new ut({scale:u,origin:t}));return this.viewbox(f)}}}),Yt(Re,"Box");class Qi extends Array{constructor(t=[],...n){if(super(t,...n),typeof t=="number")return this;this.length=0,this.push(...t)}}Dt([Qi],{each(e,...t){return typeof e=="function"?this.map((n,i,s)=>e.call(n,n,i,s)):this.map(n=>n[e](...t))},toArray(){return Array.prototype.concat.apply([],this)}});const Xy=["toArray","constructor","each"];Qi.extend=function(e){e=e.reduce((t,n)=>(Xy.includes(n)||n[0]==="_"||(n in Array.prototype&&(t["$"+n]=Array.prototype[n]),t[n]=function(...i){return this.each(n,...i)}),t),{}),Dt([Qi],e)};function Ar(e,t){return new Qi(cc((t||qt.document).querySelectorAll(e),function(n){return Ln(n)}))}function Ky(e){return Ar(e,this.node)}function Zy(e){return Ln(this.node.querySelector(e))}let Jy=0;const Rh={};function Ih(e){let t=e.getEventHolder();return t===qt.window&&(t=Rh),t.events||(t.events={}),t.events}function _c(e){return e.getEventTarget()}function Qy(e){let t=e.getEventHolder();t===qt.window&&(t=Rh),t.events&&(t.events={})}function Ie(e,t,n,i,s){const a=n.bind(i||e),c=Je(e),l=Ih(c),u=_c(c);t=Array.isArray(t)?t:t.split(di),n._svgjsListenerId||(n._svgjsListenerId=++Jy),t.forEach(function(f){const d=f.split(".")[0],p=f.split(".")[1]||"*";l[d]=l[d]||{},l[d][p]=l[d][p]||{},l[d][p][n._svgjsListenerId]=a,u.addEventListener(d,a,s||!1)})}function On(e,t,n,i){const s=Je(e),a=Ih(s),c=_c(s);typeof n=="function"&&(n=n._svgjsListenerId,!n)||(t=Array.isArray(t)?t:(t||"").split(di),t.forEach(function(l){const u=l&&l.split(".")[0],f=l&&l.split(".")[1];let d,p;if(n)a[u]&&a[u][f||"*"]&&(c.removeEventListener(u,a[u][f||"*"][n],i||!1),delete a[u][f||"*"][n]);else if(u&&f){if(a[u]&&a[u][f]){for(p in a[u][f])On(c,[u,f].join("."),p);delete a[u][f]}}else if(f)for(l in a)for(d in a[l])f===d&&On(c,[l,f].join("."));else if(u){if(a[u]){for(d in a[u])On(c,[u,d].join("."));delete a[u]}}else{for(l in a)On(c,l);Qy(s)}}))}function tm(e,t,n,i){const s=_c(e);return t instanceof qt.window.Event||(t=new qt.window.CustomEvent(t,{detail:n,cancelable:!0,...i})),s.dispatchEvent(t),t}class ys extends pc{addEventListener(){}dispatch(t,n,i){return tm(this,t,n,i)}dispatchEvent(t){const n=this.getEventHolder().events;if(!n)return!0;const i=n[t.type];for(const s in i)for(const a in i[s])i[s][a](t);return!t.defaultPrevented}fire(t,n,i){return this.dispatch(t,n,i),this}getEventHolder(){return this}getEventTarget(){return this}off(t,n,i){return On(this,t,n,i),this}on(t,n,i,s){return Ie(this,t,n,i,s),this}removeEventListener(){}}Yt(ys,"EventTarget");function Nh(){}const ms={duration:400,ease:">",delay:0},em={"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","text-anchor":"start"};class Er extends Array{constructor(...t){super(...t),this.init(...t)}clone(){return new this.constructor(this)}init(t){return typeof t=="number"?this:(this.length=0,this.push(...this.parse(t)),this)}parse(t=[]){return t instanceof Array?t:t.trim().split(di).map(parseFloat)}toArray(){return Array.prototype.concat.apply([],this)}toSet(){return new Set(this)}toString(){return this.join(" ")}valueOf(){const t=[];return t.push(...this),t}}class vt{constructor(...t){this.init(...t)}convert(t){return new vt(this.value,t)}divide(t){return t=new vt(t),new vt(this/t,this.unit||t.unit)}init(t,n){return n=Array.isArray(t)?t[1]:n,t=Array.isArray(t)?t[0]:t,this.value=0,this.unit=n||"",typeof t=="number"?this.value=isNaN(t)?0:isFinite(t)?t:t<0?-34e37:34e37:typeof t=="string"?(n=t.match(Eh),n&&(this.value=parseFloat(n[1]),n[5]==="%"?this.value/=100:n[5]==="s"&&(this.value*=1e3),this.unit=n[5])):t instanceof vt&&(this.value=t.valueOf(),this.unit=t.unit),this}minus(t){return t=new vt(t),new vt(this-t,this.unit||t.unit)}plus(t){return t=new vt(t),new vt(this+t,this.unit||t.unit)}times(t){return t=new vt(t),new vt(this*t,this.unit||t.unit)}toArray(){return[this.value,this.unit]}toJSON(){return this.toString()}toString(){return(this.unit==="%"?~~(this.value*1e8)/1e6:this.unit==="s"?this.value/1e3:this.value)+this.unit}valueOf(){return this.value}}const nm=new Set(["fill","stroke","color","bgcolor","stop-color","flood-color","lighting-color"]),$h=[];function im(e){$h.push(e)}function rm(e,t,n){if(e==null){e={},t=this.node.attributes;for(const i of t)e[i.nodeName]=Oh.test(i.nodeValue)?parseFloat(i.nodeValue):i.nodeValue;return e}else{if(e instanceof Array)return e.reduce((i,s)=>(i[s]=this.attr(s),i),{});if(typeof e=="object"&&e.constructor===Object)for(t in e)this.attr(t,e[t]);else if(t===null)this.node.removeAttribute(e);else{if(t==null)return t=this.node.getAttribute(e),t==null?em[e]:Oh.test(t)?parseFloat(t):t;t=$h.reduce((i,s)=>s(e,i,this),t),typeof t=="number"?t=new vt(t):nm.has(e)&&ei.isColor(t)?t=new ei(t):t.constructor===Array&&(t=new Er(t)),e==="leading"?this.leading&&this.leading(t):typeof n=="string"?this.node.setAttributeNS(n,e,t.toString()):this.node.setAttribute(e,t.toString()),this.rebuild&&(e==="font-size"||e==="x")&&this.rebuild()}}return this}class Pi extends ys{constructor(t,n){super(),this.node=t,this.type=t.nodeName,n&&t!==n&&this.attr(n)}add(t,n){return t=Je(t),t.removeNamespace&&this.node instanceof qt.window.SVGElement&&t.removeNamespace(),n==null?this.node.appendChild(t.node):t.node!==this.node.childNodes[n]&&this.node.insertBefore(t.node,this.node.childNodes[n]),this}addTo(t,n){return Je(t).put(this,n)}children(){return new Qi(cc(this.node.children,function(t){return Ln(t)}))}clear(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return this}clone(t=!0,n=!0){this.writeDataToDom();let i=this.node.cloneNode(t);return n&&(i=Ah(i)),new this.constructor(i)}each(t,n){const i=this.children();let s,a;for(s=0,a=i.length;s<a;s++)t.apply(i[s],[s,i]),n&&i[s].each(t,n);return this}element(t,n){return this.put(new Pi(gs(t),n))}first(){return Ln(this.node.firstChild)}get(t){return Ln(this.node.childNodes[t])}getEventHolder(){return this.node}getEventTarget(){return this.node}has(t){return this.index(t)>=0}html(t,n){return this.xml(t,n,iy)}id(t){return typeof t>"u"&&!this.node.id&&(this.node.id=Ch(this.type)),this.attr("id",t)}index(t){return[].slice.call(this.node.childNodes).indexOf(t.node)}last(){return Ln(this.node.lastChild)}matches(t){const n=this.node,i=n.matches||n.matchesSelector||n.msMatchesSelector||n.mozMatchesSelector||n.webkitMatchesSelector||n.oMatchesSelector||null;return i&&i.call(n,t)}parent(t){let n=this;if(!n.node.parentNode)return null;if(n=Ln(n.node.parentNode),!t)return n;do if(typeof t=="string"?n.matches(t):n instanceof t)return n;while(n=Ln(n.node.parentNode));return n}put(t,n){return t=Je(t),this.add(t,n),t}putIn(t,n){return Je(t).add(this,n)}remove(){return this.parent()&&this.parent().removeElement(this),this}removeElement(t){return this.node.removeChild(t.node),this}replace(t){return t=Je(t),this.node.parentNode&&this.node.parentNode.replaceChild(t.node,this.node),t}round(t=2,n=null){const i=10**t,s=this.attr(n);for(const a in s)typeof s[a]=="number"&&(s[a]=Math.round(s[a]*i)/i);return this.attr(s),this}svg(t,n){return this.xml(t,n,fc)}toString(){return this.id()}words(t){return this.node.textContent=t,this}wrap(t){const n=this.parent();if(!n)return this.addTo(t);const i=n.index(this);return n.put(t,i).put(this)}writeDataToDom(){return this.each(function(){this.writeDataToDom()}),this}xml(t,n,i){if(typeof t=="boolean"&&(i=n,n=t,t=null),t==null||typeof t=="function"){n=n??!0,this.writeDataToDom();let l=this;if(t!=null){if(l=Ln(l.node.cloneNode(!0)),n){const u=t(l);if(l=u||l,u===!1)return""}l.each(function(){const u=t(this),f=u||this;u===!1?this.remove():u&&this!==f&&this.replace(f)},!0)}return n?l.node.outerHTML:l.node.innerHTML}n=n??!1;const s=gs("wrapper",i),a=qt.document.createDocumentFragment();s.innerHTML=t;for(let l=s.children.length;l--;)a.appendChild(s.firstElementChild);const c=this.parent();return n?this.replace(a)&&c:this.add(a)}}Dt(Pi,{attr:rm,find:Ky,findOne:Zy}),Yt(Pi,"Dom");class Un extends Pi{constructor(t,n){super(t,n),this.dom={},this.node.instance=this,(t.hasAttribute("data-svgjs")||t.hasAttribute("svgjs:data"))&&this.setData(JSON.parse(t.getAttribute("data-svgjs"))??JSON.parse(t.getAttribute("svgjs:data"))??{})}center(t,n){return this.cx(t).cy(n)}cx(t){return t==null?this.x()+this.width()/2:this.x(t-this.width()/2)}cy(t){return t==null?this.y()+this.height()/2:this.y(t-this.height()/2)}defs(){const t=this.root();return t&&t.defs()}dmove(t,n){return this.dx(t).dy(n)}dx(t=0){return this.x(new vt(t).plus(this.x()))}dy(t=0){return this.y(new vt(t).plus(this.y()))}getEventHolder(){return this}height(t){return this.attr("height",t)}move(t,n){return this.x(t).y(n)}parents(t=this.root()){const n=typeof t=="string";n||(t=Je(t));const i=new Qi;let s=this;for(;(s=s.parent())&&s.node!==qt.document&&s.nodeName!=="#document-fragment"&&(i.push(s),!(!n&&s.node===t.node||n&&s.matches(t)));)if(s.node===this.root().node)return null;return i}reference(t){if(t=this.attr(t),!t)return null;const n=(t+"").match(by);return n?Je(n[1]):null}root(){const t=this.parent(sy(gc));return t&&t.root()}setData(t){return this.dom=t,this}size(t,n){const i=kr(this,t,n);return this.width(new vt(i.width)).height(new vt(i.height))}width(t){return this.attr("width",t)}writeDataToDom(){return Sh(this,this.dom),super.writeDataToDom()}x(t){return this.attr("x",t)}y(t){return this.attr("y",t)}}Dt(Un,{bbox:jy,rbox:qy,inside:Yy,point:Gy,ctm:Hy,screenCTM:Uy}),Yt(Un,"Element");const xs={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(e,t){return t==="color"?e:e+"-"+t}};["fill","stroke"].forEach(function(e){const t={};let n;t[e]=function(i){if(typeof i>"u")return this.attr(e);if(typeof i=="string"||i instanceof ei||ei.isRgb(i)||i instanceof Un)this.attr(e,i);else for(n=xs[e].length-1;n>=0;n--)i[xs[e][n]]!=null&&this.attr(xs.prefix(e,xs[e][n]),i[xs[e][n]]);return this},Ct(["Element","Runner"],t)}),Ct(["Element","Runner"],{matrix:function(e,t,n,i,s,a){return e==null?new ut(this):this.attr("transform",new ut(e,t,n,i,s,a))},rotate:function(e,t,n){return this.transform({rotate:e,ox:t,oy:n},!0)},skew:function(e,t,n,i){return arguments.length===1||arguments.length===3?this.transform({skew:e,ox:t,oy:n},!0):this.transform({skew:[e,t],ox:n,oy:i},!0)},shear:function(e,t,n){return this.transform({shear:e,ox:t,oy:n},!0)},scale:function(e,t,n,i){return arguments.length===1||arguments.length===3?this.transform({scale:e,ox:t,oy:n},!0):this.transform({scale:[e,t],ox:n,oy:i},!0)},translate:function(e,t){return this.transform({translate:[e,t]},!0)},relative:function(e,t){return this.transform({relative:[e,t]},!0)},flip:function(e="both",t="center"){return"xybothtrue".indexOf(e)===-1&&(t=e,e="both"),this.transform({flip:e,origin:t},!0)},opacity:function(e){return this.attr("opacity",e)}}),Ct("radius",{radius:function(e,t=e){return(this._element||this).type==="radialGradient"?this.attr("r",new vt(e)):this.rx(e).ry(t)}}),Ct("Path",{length:function(){return this.node.getTotalLength()},pointAt:function(e){return new we(this.node.getPointAtLength(e))}}),Ct(["Element","Runner"],{font:function(e,t){if(typeof e=="object"){for(t in e)this.font(t,e[t]);return this}return e==="leading"?this.leading(t):e==="anchor"?this.attr("text-anchor",t):e==="size"||e==="family"||e==="weight"||e==="stretch"||e==="variant"||e==="style"?this.attr("font-"+e,t):this.attr(e,t)}});const sm=["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","mouseenter","mouseleave","touchstart","touchmove","touchleave","touchend","touchcancel","contextmenu","wheel","pointerdown","pointermove","pointerup","pointerleave","pointercancel"].reduce(function(e,t){const n=function(i){return i===null?this.off(t):this.on(t,i),this};return e[t]=n,e},{});Ct("Element",sm);function om(){return this.attr("transform",null)}function am(){return(this.attr("transform")||"").split(wy).slice(0,-1).map(function(t){const n=t.trim().split("(");return[n[0],n[1].split(di).map(function(i){return parseFloat(i)})]}).reverse().reduce(function(t,n){return n[0]==="matrix"?t.lmultiply(ut.fromArray(n[1])):t[n[0]].apply(t,n[1])},new ut)}function cm(e,t){if(this===e)return this;if(hc(this.node))return this.addTo(e,t);const n=this.screenCTM(),i=e.screenCTM().inverse();return this.addTo(e,t).untransform().transform(i.multiply(n)),this}function lm(e){return this.toParent(this.root(),e)}function um(e,t){if(e==null||typeof e=="string"){const s=new ut(this).decompose();return e==null?s:s[e]}ut.isMatrixLike(e)||(e={...e,origin:uc(e,this)});const n=t===!0?this:t||!1,i=new ut(n).transform(e);return this.attr("transform",i)}Ct("Element",{untransform:om,matrixify:am,toParent:cm,toRoot:lm,transform:um});class on extends Un{flatten(){return this.each(function(){if(this instanceof on)return this.flatten().ungroup()}),this}ungroup(t=this.parent(),n=t.index(this)){return n=n===-1?t.children().length:n,this.each(function(i,s){return s[s.length-i-1].toParent(t,n)}),this.remove()}}Yt(on,"Container");class vc extends on{constructor(t,n=t){super(ue("defs",t),n)}flatten(){return this}ungroup(){return this}}Yt(vc,"Defs");class mn extends Un{}Yt(mn,"Shape");function bc(e){return this.attr("rx",e)}function wc(e){return this.attr("ry",e)}function Wh(e){return e==null?this.cx()-this.rx():this.cx(e+this.rx())}function zh(e){return e==null?this.cy()-this.ry():this.cy(e+this.ry())}function Fh(e){return this.attr("cx",e)}function Bh(e){return this.attr("cy",e)}function Gh(e){return e==null?this.rx()*2:this.rx(new vt(e).divide(2))}function Hh(e){return e==null?this.ry()*2:this.ry(new vt(e).divide(2))}const hm=Object.freeze(Object.defineProperty({__proto__:null,cx:Fh,cy:Bh,height:Hh,rx:bc,ry:wc,width:Gh,x:Wh,y:zh},Symbol.toStringTag,{value:"Module"}));class mo extends mn{constructor(t,n=t){super(ue("ellipse",t),n)}size(t,n){const i=kr(this,t,n);return this.rx(new vt(i.width).divide(2)).ry(new vt(i.height).divide(2))}}Dt(mo,hm),Ct("Container",{ellipse:he(function(e=0,t=e){return this.put(new mo).size(e,t).move(0,0)})}),Yt(mo,"Ellipse");class Uh extends Pi{constructor(t=qt.document.createDocumentFragment()){super(t)}xml(t,n,i){if(typeof t=="boolean"&&(i=n,n=t,t=null),t==null||typeof t=="function"){const s=new Pi(gs("wrapper",i));return s.add(this.node.cloneNode(!0)),s.xml(!1,i)}return super.xml(t,!1,i)}}Yt(Uh,"Fragment");function Vh(e,t){return(this._element||this).type==="radialGradient"?this.attr({fx:new vt(e),fy:new vt(t)}):this.attr({x1:new vt(e),y1:new vt(t)})}function jh(e,t){return(this._element||this).type==="radialGradient"?this.attr({cx:new vt(e),cy:new vt(t)}):this.attr({x2:new vt(e),y2:new vt(t)})}const fm=Object.freeze(Object.defineProperty({__proto__:null,from:Vh,to:jh},Symbol.toStringTag,{value:"Module"}));class _s extends on{constructor(t,n){super(ue(t+"Gradient",typeof t=="string"?null:t),n)}attr(t,n,i){return t==="transform"&&(t="gradientTransform"),super.attr(t,n,i)}bbox(){return new Re}targets(){return Ar("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}Dt(_s,fm),Ct({Container:{gradient(...e){return this.defs().gradient(...e)}},Defs:{gradient:he(function(e,t){return this.put(new _s(e)).update(t)})}}),Yt(_s,"Gradient");class vs extends on{constructor(t,n=t){super(ue("pattern",t),n)}attr(t,n,i){return t==="transform"&&(t="patternTransform"),super.attr(t,n,i)}bbox(){return new Re}targets(){return Ar("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}Ct({Container:{pattern(...e){return this.defs().pattern(...e)}},Defs:{pattern:he(function(e,t,n){return this.put(new vs).update(n).attr({x:0,y:0,width:e,height:t,patternUnits:"userSpaceOnUse"})})}}),Yt(vs,"Pattern");let xo=class extends mn{constructor(t,n=t){super(ue("image",t),n)}load(t,n){if(!t)return this;const i=new qt.window.Image;return Ie(i,"load",function(s){const a=this.parent(vs);this.width()===0&&this.height()===0&&this.size(i.width,i.height),a instanceof vs&&a.width()===0&&a.height()===0&&a.size(this.width(),this.height()),typeof n=="function"&&n.call(this,s)},this),Ie(i,"load error",function(){On(i)}),this.attr("href",i.src=t,ps)}};im(function(e,t,n){return(e==="fill"||e==="stroke")&&Sy.test(t)&&(t=n.root().defs().image(t)),t instanceof xo&&(t=n.root().defs().pattern(0,0,i=>{i.add(t)})),t}),Ct({Container:{image:he(function(e,t){return this.put(new xo).size(0,0).load(e,t)})}}),Yt(xo,"Image");class Ri extends Er{bbox(){let t=-1/0,n=-1/0,i=1/0,s=1/0;return this.forEach(function(a){t=Math.max(a[0],t),n=Math.max(a[1],n),i=Math.min(a[0],i),s=Math.min(a[1],s)}),new Re(i,s,t-i,n-s)}move(t,n){const i=this.bbox();if(t-=i.x,n-=i.y,!isNaN(t)&&!isNaN(n))for(let s=this.length-1;s>=0;s--)this[s]=[this[s][0]+t,this[s][1]+n];return this}parse(t=[0,0]){const n=[];t instanceof Array?t=Array.prototype.concat.apply([],t):t=t.trim().split(di).map(parseFloat),t.length%2!==0&&t.pop();for(let i=0,s=t.length;i<s;i=i+2)n.push([t[i],t[i+1]]);return n}size(t,n){let i;const s=this.bbox();for(i=this.length-1;i>=0;i--)s.width&&(this[i][0]=(this[i][0]-s.x)*t/s.width+s.x),s.height&&(this[i][1]=(this[i][1]-s.y)*n/s.height+s.y);return this}toLine(){return{x1:this[0][0],y1:this[0][1],x2:this[1][0],y2:this[1][1]}}toString(){const t=[];for(let n=0,i=this.length;n<i;n++)t.push(this[n].join(","));return t.join(" ")}transform(t){return this.clone().transformO(t)}transformO(t){ut.isMatrixLike(t)||(t=new ut(t));for(let n=this.length;n--;){const[i,s]=this[n];this[n][0]=t.a*i+t.c*s+t.e,this[n][1]=t.b*i+t.d*s+t.f}return this}}const dm=Ri;function pm(e){return e==null?this.bbox().x:this.move(e,this.bbox().y)}function gm(e){return e==null?this.bbox().y:this.move(this.bbox().x,e)}function ym(e){const t=this.bbox();return e==null?t.width:this.size(e,t.height)}function mm(e){const t=this.bbox();return e==null?t.height:this.size(t.width,e)}const kc=Object.freeze(Object.defineProperty({__proto__:null,MorphArray:dm,height:mm,width:ym,x:pm,y:gm},Symbol.toStringTag,{value:"Module"}));class Mr extends mn{constructor(t,n=t){super(ue("line",t),n)}array(){return new Ri([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])}move(t,n){return this.attr(this.array().move(t,n).toLine())}plot(t,n,i,s){return t==null?this.array():(typeof n<"u"?t={x1:t,y1:n,x2:i,y2:s}:t=new Ri(t).toLine(),this.attr(t))}size(t,n){const i=kr(this,t,n);return this.attr(this.array().size(i.width,i.height).toLine())}}Dt(Mr,kc),Ct({Container:{line:he(function(...e){return Mr.prototype.plot.apply(this.put(new Mr),e[0]!=null?e:[0,0,0,0])})}}),Yt(Mr,"Line");class _o extends on{constructor(t,n=t){super(ue("marker",t),n)}height(t){return this.attr("markerHeight",t)}orient(t){return this.attr("orient",t)}ref(t,n){return this.attr("refX",t).attr("refY",n)}toString(){return"url(#"+this.id()+")"}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}width(t){return this.attr("markerWidth",t)}}Ct({Container:{marker(...e){return this.defs().marker(...e)}},Defs:{marker:he(function(e,t,n){return this.put(new _o).size(e,t).ref(e/2,t/2).viewbox(0,0,e,t).attr("orient","auto").update(n)})},marker:{marker(e,t,n,i){let s=["marker"];return e!=="all"&&s.push(e),s=s.join("-"),e=arguments[1]instanceof _o?arguments[1]:this.defs().marker(t,n,i),this.attr(s,e)}}}),Yt(_o,"Marker");function Tr(e,t){return function(n){return n==null?this[e]:(this[e]=n,t&&t.call(this),this)}}const xm={"-":function(e){return e},"<>":function(e){return-Math.cos(e*Math.PI)/2+.5},">":function(e){return Math.sin(e*Math.PI/2)},"<":function(e){return-Math.cos(e*Math.PI/2)+1},bezier:function(e,t,n,i){return function(s){return s<0?e>0?t/e*s:n>0?i/n*s:0:s>1?n<1?(1-i)/(1-n)*s+(i-n)/(1-n):e<1?(1-t)/(1-e)*s+(t-e)/(1-e):1:3*s*(1-s)**2*t+3*s**2*(1-s)*i+s**3}},steps:function(e,t="end"){t=t.split("-").reverse()[0];let n=e;return t==="none"?--n:t==="both"&&++n,(i,s=!1)=>{let a=Math.floor(i*e);const c=i*a%1===0;return(t==="start"||t==="both")&&++a,s&&c&&--a,i>=0&&a<0&&(a=0),i<=1&&a>n&&(a=n),a/n}}};class Sc{done(){return!1}}class Cc extends Sc{constructor(t=ms.ease){super(),this.ease=xm[t]||t}step(t,n,i){return typeof t!="number"?i<1?t:n:t+(n-t)*this.ease(i)}}class vo extends Sc{constructor(t){super(),this.stepper=t}done(t){return t.done}step(t,n,i,s){return this.stepper(t,n,i,s)}}function qh(){const e=(this._duration||500)/1e3,t=this._overshoot||0,n=1e-10,i=Math.PI,s=Math.log(t/100+n),a=-s/Math.sqrt(i*i+s*s),c=3.9/(a*e);this.d=2*a*c,this.k=c*c}class _m extends vo{constructor(t=500,n=0){super(),this.duration(t).overshoot(n)}step(t,n,i,s){if(typeof t=="string")return t;if(s.done=i===1/0,i===1/0)return n;if(i===0)return t;i>100&&(i=16),i/=1e3;const a=s.velocity||0,c=-this.d*a-this.k*(t-n),l=t+a*i+c*i*i/2;return s.velocity=a+c*i,s.done=Math.abs(n-l)+Math.abs(a)<.002,s.done?n:l}}Dt(_m,{duration:Tr("_duration",qh),overshoot:Tr("_overshoot",qh)});class vm extends vo{constructor(t=.1,n=.01,i=0,s=1e3){super(),this.p(t).i(n).d(i).windup(s)}step(t,n,i,s){if(typeof t=="string")return t;if(s.done=i===1/0,i===1/0)return n;if(i===0)return t;const a=n-t;let c=(s.integral||0)+a*i;const l=(a-(s.error||0))/i,u=this._windup;return u!==!1&&(c=Math.max(-u,Math.min(c,u))),s.error=a,s.integral=c,s.done=Math.abs(a)<.001,s.done?n:t+(this.P*a+this.I*c+this.D*l)}}Dt(vm,{windup:Tr("_windup"),p:Tr("P"),i:Tr("I"),d:Tr("D")});const bm={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0},Ac={M:function(e,t,n){return t.x=n.x=e[0],t.y=n.y=e[1],["M",t.x,t.y]},L:function(e,t){return t.x=e[0],t.y=e[1],["L",e[0],e[1]]},H:function(e,t){return t.x=e[0],["H",e[0]]},V:function(e,t){return t.y=e[0],["V",e[0]]},C:function(e,t){return t.x=e[4],t.y=e[5],["C",e[0],e[1],e[2],e[3],e[4],e[5]]},S:function(e,t){return t.x=e[2],t.y=e[3],["S",e[0],e[1],e[2],e[3]]},Q:function(e,t){return t.x=e[2],t.y=e[3],["Q",e[0],e[1],e[2],e[3]]},T:function(e,t){return t.x=e[0],t.y=e[1],["T",e[0],e[1]]},Z:function(e,t,n){return t.x=n.x,t.y=n.y,["Z"]},A:function(e,t){return t.x=e[5],t.y=e[6],["A",e[0],e[1],e[2],e[3],e[4],e[5],e[6]]}},Ec="mlhvqtcsaz".split("");for(let e=0,t=Ec.length;e<t;++e)Ac[Ec[e]]=function(n){return function(i,s,a){if(n==="H")i[0]=i[0]+s.x;else if(n==="V")i[0]=i[0]+s.y;else if(n==="A")i[5]=i[5]+s.x,i[6]=i[6]+s.y;else for(let c=0,l=i.length;c<l;++c)i[c]=i[c]+(c%2?s.y:s.x);return Ac[n](i,s,a)}}(Ec[e].toUpperCase());function wm(e){const t=e.segment[0];return Ac[t](e.segment.slice(1),e.p,e.p0)}function Mc(e){return e.segment.length&&e.segment.length-1===bm[e.segment[0].toUpperCase()]}function km(e,t){e.inNumber&&tr(e,!1);const n=mc.test(t);if(n)e.segment=[t];else{const i=e.lastCommand,s=i.toLowerCase(),a=i===s;e.segment=[s==="m"?a?"l":"L":i]}return e.inSegment=!0,e.lastCommand=e.segment[0],n}function tr(e,t){if(!e.inNumber)throw new Error("Parser Error");e.number&&e.segment.push(parseFloat(e.number)),e.inNumber=t,e.number="",e.pointSeen=!1,e.hasExponent=!1,Mc(e)&&Tc(e)}function Tc(e){e.inSegment=!1,e.absolute&&(e.segment=wm(e)),e.segments.push(e.segment)}function Sm(e){if(!e.segment.length)return!1;const t=e.segment[0].toUpperCase()==="A",n=e.segment.length;return t&&(n===4||n===5)}function Cm(e){return e.lastToken.toUpperCase()==="E"}const Am=new Set([" ",",","	",`
`,"\r","\f"]);function Em(e,t=!0){let n=0,i="";const s={segment:[],inNumber:!1,number:"",lastToken:"",inSegment:!1,segments:[],pointSeen:!1,hasExponent:!1,absolute:t,p0:new we,p:new we};for(;s.lastToken=i,i=e.charAt(n++);)if(!(!s.inSegment&&km(s,i))){if(i==="."){if(s.pointSeen||s.hasExponent){tr(s,!1),--n;continue}s.inNumber=!0,s.pointSeen=!0,s.number+=i;continue}if(!isNaN(parseInt(i))){if(s.number==="0"||Sm(s)){s.inNumber=!0,s.number=i,tr(s,!0);continue}s.inNumber=!0,s.number+=i;continue}if(Am.has(i)){s.inNumber&&tr(s,!1);continue}if(i==="-"||i==="+"){if(s.inNumber&&!Cm(s)){tr(s,!1),--n;continue}s.number+=i,s.inNumber=!0;continue}if(i.toUpperCase()==="E"){s.number+=i,s.hasExponent=!0;continue}if(mc.test(i)){if(s.inNumber)tr(s,!1);else if(Mc(s))Tc(s);else throw new Error("parser Error");--n}}return s.inNumber&&tr(s,!1),s.inSegment&&Mc(s)&&Tc(s),s.segments}function Mm(e){let t="";for(let n=0,i=e.length;n<i;n++)t+=e[n][0],e[n][1]!=null&&(t+=e[n][1],e[n][2]!=null&&(t+=" ",t+=e[n][2],e[n][3]!=null&&(t+=" ",t+=e[n][3],t+=" ",t+=e[n][4],e[n][5]!=null&&(t+=" ",t+=e[n][5],t+=" ",t+=e[n][6],e[n][7]!=null&&(t+=" ",t+=e[n][7])))));return t+" "}class er extends Er{bbox(){return Di().path.setAttribute("d",this.toString()),new Re(Di.nodes.path.getBBox())}move(t,n){const i=this.bbox();if(t-=i.x,n-=i.y,!isNaN(t)&&!isNaN(n))for(let s,a=this.length-1;a>=0;a--)s=this[a][0],s==="M"||s==="L"||s==="T"?(this[a][1]+=t,this[a][2]+=n):s==="H"?this[a][1]+=t:s==="V"?this[a][1]+=n:s==="C"||s==="S"||s==="Q"?(this[a][1]+=t,this[a][2]+=n,this[a][3]+=t,this[a][4]+=n,s==="C"&&(this[a][5]+=t,this[a][6]+=n)):s==="A"&&(this[a][6]+=t,this[a][7]+=n);return this}parse(t="M0 0"){return Array.isArray(t)&&(t=Array.prototype.concat.apply([],t).toString()),Em(t)}size(t,n){const i=this.bbox();let s,a;for(i.width=i.width===0?1:i.width,i.height=i.height===0?1:i.height,s=this.length-1;s>=0;s--)a=this[s][0],a==="M"||a==="L"||a==="T"?(this[s][1]=(this[s][1]-i.x)*t/i.width+i.x,this[s][2]=(this[s][2]-i.y)*n/i.height+i.y):a==="H"?this[s][1]=(this[s][1]-i.x)*t/i.width+i.x:a==="V"?this[s][1]=(this[s][1]-i.y)*n/i.height+i.y:a==="C"||a==="S"||a==="Q"?(this[s][1]=(this[s][1]-i.x)*t/i.width+i.x,this[s][2]=(this[s][2]-i.y)*n/i.height+i.y,this[s][3]=(this[s][3]-i.x)*t/i.width+i.x,this[s][4]=(this[s][4]-i.y)*n/i.height+i.y,a==="C"&&(this[s][5]=(this[s][5]-i.x)*t/i.width+i.x,this[s][6]=(this[s][6]-i.y)*n/i.height+i.y)):a==="A"&&(this[s][1]=this[s][1]*t/i.width,this[s][2]=this[s][2]*n/i.height,this[s][6]=(this[s][6]-i.x)*t/i.width+i.x,this[s][7]=(this[s][7]-i.y)*n/i.height+i.y);return this}toString(){return Mm(this)}}const Yh=e=>{const t=typeof e;return t==="number"?vt:t==="string"?ei.isColor(e)?ei:di.test(e)?mc.test(e)?er:Er:Eh.test(e)?vt:Lc:Oc.indexOf(e.constructor)>-1?e.constructor:Array.isArray(e)?Er:t==="object"?ws:Lc};class nr{constructor(t){this._stepper=t||new Cc("-"),this._from=null,this._to=null,this._type=null,this._context=null,this._morphObj=null}at(t){return this._morphObj.morph(this._from,this._to,t,this._stepper,this._context)}done(){return this._context.map(this._stepper.done).reduce(function(n,i){return n&&i},!0)}from(t){return t==null?this._from:(this._from=this._set(t),this)}stepper(t){return t==null?this._stepper:(this._stepper=t,this)}to(t){return t==null?this._to:(this._to=this._set(t),this)}type(t){return t==null?this._type:(this._type=t,this)}_set(t){this._type||this.type(Yh(t));let n=new this._type(t);return this._type===ei&&(n=this._to?n[this._to[4]]():this._from?n[this._from[4]]():n),this._type===ws&&(n=this._to?n.align(this._to):this._from?n.align(this._from):n),n=n.toConsumable(),this._morphObj=this._morphObj||new this._type,this._context=this._context||Array.apply(null,Array(n.length)).map(Object).map(function(i){return i.done=!0,i}),n}}class Lc{constructor(...t){this.init(...t)}init(t){return t=Array.isArray(t)?t[0]:t,this.value=t,this}toArray(){return[this.value]}valueOf(){return this.value}}class bs{constructor(...t){this.init(...t)}init(t){return Array.isArray(t)&&(t={scaleX:t[0],scaleY:t[1],shear:t[2],rotate:t[3],translateX:t[4],translateY:t[5],originX:t[6],originY:t[7]}),Object.assign(this,bs.defaults,t),this}toArray(){const t=this;return[t.scaleX,t.scaleY,t.shear,t.rotate,t.translateX,t.translateY,t.originX,t.originY]}}bs.defaults={scaleX:1,scaleY:1,shear:0,rotate:0,translateX:0,translateY:0,originX:0,originY:0};const Tm=(e,t)=>e[0]<t[0]?-1:e[0]>t[0]?1:0;class ws{constructor(...t){this.init(...t)}align(t){const n=this.values;for(let i=0,s=n.length;i<s;++i){if(n[i+1]===t[i+1]){if(n[i+1]===ei&&t[i+7]!==n[i+7]){const l=t[i+7],u=new ei(this.values.splice(i+3,5))[l]().toArray();this.values.splice(i+3,0,...u)}i+=n[i+2]+2;continue}if(!t[i+1])return this;const a=new t[i+1]().toArray(),c=n[i+2]+3;n.splice(i,c,t[i],t[i+1],t[i+2],...a),i+=n[i+2]+2}return this}init(t){if(this.values=[],Array.isArray(t)){this.values=t.slice();return}t=t||{};const n=[];for(const i in t){const s=Yh(t[i]),a=new s(t[i]).toArray();n.push([i,s,a.length,...a])}return n.sort(Tm),this.values=n.reduce((i,s)=>i.concat(s),[]),this}toArray(){return this.values}valueOf(){const t={},n=this.values;for(;n.length;){const i=n.shift(),s=n.shift(),a=n.shift(),c=n.splice(0,a);t[i]=new s(c)}return t}}const Oc=[Lc,bs,ws];function Lm(e=[]){Oc.push(...[].concat(e))}function Om(){Dt(Oc,{to(e){return new nr().type(this.constructor).from(this.toArray()).to(e)},fromArray(e){return this.init(e),this},toConsumable(){return this.toArray()},morph(e,t,n,i,s){const a=function(c,l){return i.step(c,t[l],n,s[l],s)};return this.fromArray(e.map(a))}})}class Lr extends mn{constructor(t,n=t){super(ue("path",t),n)}array(){return this._array||(this._array=new er(this.attr("d")))}clear(){return delete this._array,this}height(t){return t==null?this.bbox().height:this.size(this.bbox().width,t)}move(t,n){return this.attr("d",this.array().move(t,n))}plot(t){return t==null?this.array():this.clear().attr("d",typeof t=="string"?t:this._array=new er(t))}size(t,n){const i=kr(this,t,n);return this.attr("d",this.array().size(i.width,i.height))}width(t){return t==null?this.bbox().width:this.size(t,this.bbox().height)}x(t){return t==null?this.bbox().x:this.move(t,this.bbox().y)}y(t){return t==null?this.bbox().y:this.move(this.bbox().x,t)}}Lr.prototype.MorphArray=er,Ct({Container:{path:he(function(e){return this.put(new Lr).plot(e||new er)})}}),Yt(Lr,"Path");function Dm(){return this._array||(this._array=new Ri(this.attr("points")))}function Pm(){return delete this._array,this}function Rm(e,t){return this.attr("points",this.array().move(e,t))}function Im(e){return e==null?this.array():this.clear().attr("points",typeof e=="string"?e:this._array=new Ri(e))}function Nm(e,t){const n=kr(this,e,t);return this.attr("points",this.array().size(n.width,n.height))}const Xh=Object.freeze(Object.defineProperty({__proto__:null,array:Dm,clear:Pm,move:Rm,plot:Im,size:Nm},Symbol.toStringTag,{value:"Module"}));class ks extends mn{constructor(t,n=t){super(ue("polygon",t),n)}}Ct({Container:{polygon:he(function(e){return this.put(new ks).plot(e||new Ri)})}}),Dt(ks,kc),Dt(ks,Xh),Yt(ks,"Polygon");class Ss extends mn{constructor(t,n=t){super(ue("polyline",t),n)}}Ct({Container:{polyline:he(function(e){return this.put(new Ss).plot(e||new Ri)})}}),Dt(Ss,kc),Dt(Ss,Xh),Yt(Ss,"Polyline");class bo extends mn{constructor(t,n=t){super(ue("rect",t),n)}}Dt(bo,{rx:bc,ry:wc}),Ct({Container:{rect:he(function(e,t){return this.put(new bo).size(e,t)})}}),Yt(bo,"Rect");class Dc{constructor(){this._first=null,this._last=null}first(){return this._first&&this._first.value}last(){return this._last&&this._last.value}push(t){const n=typeof t.next<"u"?t:{value:t,next:null,prev:null};return this._last?(n.prev=this._last,this._last.next=n,this._last=n):(this._last=n,this._first=n),n}remove(t){t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t===this._last&&(this._last=t.prev),t===this._first&&(this._first=t.next),t.prev=null,t.next=null}shift(){const t=this._first;return t?(this._first=t.next,this._first&&(this._first.prev=null),this._last=this._first?this._last:null,t.value):null}}const Jt={nextDraw:null,frames:new Dc,timeouts:new Dc,immediates:new Dc,timer:()=>qt.window.performance||qt.window.Date,transforms:[],frame(e){const t=Jt.frames.push({run:e});return Jt.nextDraw===null&&(Jt.nextDraw=qt.window.requestAnimationFrame(Jt._draw)),t},timeout(e,t){t=t||0;const n=Jt.timer().now()+t,i=Jt.timeouts.push({run:e,time:n});return Jt.nextDraw===null&&(Jt.nextDraw=qt.window.requestAnimationFrame(Jt._draw)),i},immediate(e){const t=Jt.immediates.push(e);return Jt.nextDraw===null&&(Jt.nextDraw=qt.window.requestAnimationFrame(Jt._draw)),t},cancelFrame(e){e!=null&&Jt.frames.remove(e)},clearTimeout(e){e!=null&&Jt.timeouts.remove(e)},cancelImmediate(e){e!=null&&Jt.immediates.remove(e)},_draw(e){let t=null;const n=Jt.timeouts.last();for(;(t=Jt.timeouts.shift())&&(e>=t.time?t.run():Jt.timeouts.push(t),t!==n););let i=null;const s=Jt.frames.last();for(;i!==s&&(i=Jt.frames.shift());)i.run(e);let a=null;for(;a=Jt.immediates.shift();)a();Jt.nextDraw=Jt.timeouts.first()||Jt.frames.first()?qt.window.requestAnimationFrame(Jt._draw):null}},$m=function(e){const t=e.start,n=e.runner.duration(),i=t+n;return{start:t,duration:n,end:i,runner:e.runner}},Wm=function(){const e=qt.window;return(e.performance||e.Date).now()};class Kh extends ys{constructor(t=Wm){super(),this._timeSource=t,this.terminate()}active(){return!!this._nextFrame}finish(){return this.time(this.getEndTimeOfTimeline()+1),this.pause()}getEndTime(){const t=this.getLastRunnerInfo(),n=t?t.runner.duration():0;return(t?t.start:this._time)+n}getEndTimeOfTimeline(){const t=this._runners.map(n=>n.start+n.runner.duration());return Math.max(0,...t)}getLastRunnerInfo(){return this.getRunnerInfoById(this._lastRunnerId)}getRunnerInfoById(t){return this._runners[this._runnerIds.indexOf(t)]||null}pause(){return this._paused=!0,this._continue()}persist(t){return t==null?this._persist:(this._persist=t,this)}play(){return this._paused=!1,this.updateTime()._continue()}reverse(t){const n=this.speed();if(t==null)return this.speed(-n);const i=Math.abs(n);return this.speed(t?-i:i)}schedule(t,n,i){if(t==null)return this._runners.map($m);let s=0;const a=this.getEndTime();if(n=n||0,i==null||i==="last"||i==="after")s=a;else if(i==="absolute"||i==="start")s=n,n=0;else if(i==="now")s=this._time;else if(i==="relative"){const u=this.getRunnerInfoById(t.id);u&&(s=u.start+n,n=0)}else if(i==="with-last"){const u=this.getLastRunnerInfo();s=u?u.start:this._time}else throw new Error('Invalid value for the "when" parameter');t.unschedule(),t.timeline(this);const c=t.persist(),l={persist:c===null?this._persist:c,start:s+n,runner:t};return this._lastRunnerId=t.id,this._runners.push(l),this._runners.sort((u,f)=>u.start-f.start),this._runnerIds=this._runners.map(u=>u.runner.id),this.updateTime()._continue(),this}seek(t){return this.time(this._time+t)}source(t){return t==null?this._timeSource:(this._timeSource=t,this)}speed(t){return t==null?this._speed:(this._speed=t,this)}stop(){return this.time(0),this.pause()}time(t){return t==null?this._time:(this._time=t,this._continue(!0))}unschedule(t){const n=this._runnerIds.indexOf(t.id);return n<0?this:(this._runners.splice(n,1),this._runnerIds.splice(n,1),t.timeline(null),this)}updateTime(){return this.active()||(this._lastSourceTime=this._timeSource()),this}_continue(t=!1){return Jt.cancelFrame(this._nextFrame),this._nextFrame=null,t?this._stepImmediate():this._paused?this:(this._nextFrame=Jt.frame(this._step),this)}_stepFn(t=!1){const n=this._timeSource();let i=n-this._lastSourceTime;t&&(i=0);const s=this._speed*i+(this._time-this._lastStepTime);this._lastSourceTime=n,t||(this._time+=s,this._time=this._time<0?0:this._time),this._lastStepTime=this._time,this.fire("time",this._time);for(let c=this._runners.length;c--;){const l=this._runners[c],u=l.runner;this._time-l.start<=0&&u.reset()}let a=!1;for(let c=0,l=this._runners.length;c<l;c++){const u=this._runners[c],f=u.runner;let d=s;const p=this._time-u.start;if(p<=0){a=!0;continue}else p<d&&(d=p);if(!f.active())continue;f.step(d).done?u.persist!==!0&&f.duration()-f.time()+this._time+u.persist<this._time&&(f.unschedule(),--c,--l):a=!0}return a&&!(this._speed<0&&this._time===0)||this._runnerIds.length&&this._speed<0&&this._time>0?this._continue():(this.pause(),this.fire("finished")),this}terminate(){this._startTime=0,this._speed=1,this._persist=0,this._nextFrame=null,this._paused=!0,this._runners=[],this._runnerIds=[],this._lastRunnerId=-1,this._time=0,this._lastSourceTime=0,this._lastStepTime=0,this._step=this._stepFn.bind(this,!1),this._stepImmediate=this._stepFn.bind(this,!0)}}Ct({Element:{timeline:function(e){return e==null?(this._timeline=this._timeline||new Kh,this._timeline):(this._timeline=e,this)}}});class xn extends ys{constructor(t){super(),this.id=xn.id++,t=t??ms.duration,t=typeof t=="function"?new vo(t):t,this._element=null,this._timeline=null,this.done=!1,this._queue=[],this._duration=typeof t=="number"&&t,this._isDeclarative=t instanceof vo,this._stepper=this._isDeclarative?t:new Cc,this._history={},this.enabled=!0,this._time=0,this._lastTime=0,this._reseted=!0,this.transforms=new ut,this.transformId=1,this._haveReversed=!1,this._reverse=!1,this._loopsDone=0,this._swing=!1,this._wait=0,this._times=1,this._frameId=null,this._persist=this._isDeclarative?!0:null}static sanitise(t,n,i){let s=1,a=!1,c=0;return t=t??ms.duration,n=n??ms.delay,i=i||"last",typeof t=="object"&&!(t instanceof Sc)&&(n=t.delay??n,i=t.when??i,a=t.swing||a,s=t.times??s,c=t.wait??c,t=t.duration??ms.duration),{duration:t,delay:n,swing:a,times:s,wait:c,when:i}}active(t){return t==null?this.enabled:(this.enabled=t,this)}addTransform(t){return this.transforms.lmultiplyO(t),this}after(t){return this.on("finished",t)}animate(t,n,i){const s=xn.sanitise(t,n,i),a=new xn(s.duration);return this._timeline&&a.timeline(this._timeline),this._element&&a.element(this._element),a.loop(s).schedule(s.delay,s.when)}clearTransform(){return this.transforms=new ut,this}clearTransformsFromQueue(){(!this.done||!this._timeline||!this._timeline._runnerIds.includes(this.id))&&(this._queue=this._queue.filter(t=>!t.isTransform))}delay(t){return this.animate(0,t)}duration(){return this._times*(this._wait+this._duration)-this._wait}during(t){return this.queue(null,t)}ease(t){return this._stepper=new Cc(t),this}element(t){return t==null?this._element:(this._element=t,t._prepareRunner(),this)}finish(){return this.step(1/0)}loop(t,n,i){return typeof t=="object"&&(n=t.swing,i=t.wait,t=t.times),this._times=t||1/0,this._swing=n||!1,this._wait=i||0,this._times===!0&&(this._times=1/0),this}loops(t){const n=this._duration+this._wait;if(t==null){const c=Math.floor(this._time/n),u=(this._time-c*n)/this._duration;return Math.min(c+u,this._times)}const i=Math.floor(t),s=t%1,a=n*i+this._duration*s;return this.time(a)}persist(t){return t==null?this._persist:(this._persist=t,this)}position(t){const n=this._time,i=this._duration,s=this._wait,a=this._times,c=this._swing,l=this._reverse;let u;if(t==null){const y=function(_){const x=c*Math.floor(_%(2*(s+i))/(s+i)),b=x&&!l||!x&&l,w=Math.pow(-1,b)*(_%(s+i))/i+b;return Math.max(Math.min(w,1),0)},m=a*(s+i)-s;return u=n<=0?Math.round(y(1e-5)):n<m?y(n):Math.round(y(m-1e-5)),u}const f=Math.floor(this.loops()),d=c&&f%2===0;return u=f+(d&&!l||l&&d?t:1-t),this.loops(u)}progress(t){return t==null?Math.min(1,this._time/this.duration()):this.time(t*this.duration())}queue(t,n,i,s){return this._queue.push({initialiser:t||Nh,runner:n||Nh,retarget:i,isTransform:s,initialised:!1,finished:!1}),this.timeline()&&this.timeline()._continue(),this}reset(){return this._reseted?this:(this.time(0),this._reseted=!0,this)}reverse(t){return this._reverse=t??!this._reverse,this}schedule(t,n,i){if(t instanceof Kh||(i=n,n=t,t=this.timeline()),!t)throw Error("Runner cannot be scheduled without timeline");return t.schedule(this,n,i),this}step(t){if(!this.enabled)return this;t=t??16,this._time+=t;const n=this.position(),i=this._lastPosition!==n&&this._time>=0;this._lastPosition=n;const s=this.duration(),a=this._lastTime<=0&&this._time>0,c=this._lastTime<s&&this._time>=s;this._lastTime=this._time,a&&this.fire("start",this);const l=this._isDeclarative;this.done=!l&&!c&&this._time>=s,this._reseted=!1;let u=!1;return(i||l)&&(this._initialise(i),this.transforms=new ut,u=this._run(l?t:n),this.fire("step",this)),this.done=this.done||u&&l,c&&this.fire("finished",this),this}time(t){if(t==null)return this._time;const n=t-this._time;return this.step(n),this}timeline(t){return typeof t>"u"?this._timeline:(this._timeline=t,this)}unschedule(){const t=this.timeline();return t&&t.unschedule(this),this}_initialise(t){if(!(!t&&!this._isDeclarative))for(let n=0,i=this._queue.length;n<i;++n){const s=this._queue[n],a=this._isDeclarative||!s.initialised&&t;t=!s.finished,a&&t&&(s.initialiser.call(this),s.initialised=!0)}}_rememberMorpher(t,n){if(this._history[t]={morpher:n,caller:this._queue[this._queue.length-1]},this._isDeclarative){const i=this.timeline();i&&i.play()}}_run(t){let n=!0;for(let i=0,s=this._queue.length;i<s;++i){const a=this._queue[i],c=a.runner.call(this,t);a.finished=a.finished||c===!0,n=n&&a.finished}return n}_tryRetarget(t,n,i){if(this._history[t]){if(!this._history[t].caller.initialised){const a=this._queue.indexOf(this._history[t].caller);return this._queue.splice(a,1),!1}this._history[t].caller.retarget?this._history[t].caller.retarget.call(this,n,i):this._history[t].morpher.to(n),this._history[t].caller.finished=!1;const s=this.timeline();return s&&s.play(),!0}return!1}}xn.id=0;class wo{constructor(t=new ut,n=-1,i=!0){this.transforms=t,this.id=n,this.done=i}clearTransformsFromQueue(){}}Dt([xn,wo],{mergeWith(e){return new wo(e.transforms.lmultiply(this.transforms),e.id)}});const Zh=(e,t)=>e.lmultiplyO(t),Jh=e=>e.transforms;function zm(){const t=this._transformationRunners.runners.map(Jh).reduce(Zh,new ut);this.transform(t),this._transformationRunners.merge(),this._transformationRunners.length()===1&&(this._frameId=null)}class Fm{constructor(){this.runners=[],this.ids=[]}add(t){if(this.runners.includes(t))return;const n=t.id+1;return this.runners.push(t),this.ids.push(n),this}clearBefore(t){const n=this.ids.indexOf(t+1)||1;return this.ids.splice(0,n,0),this.runners.splice(0,n,new wo).forEach(i=>i.clearTransformsFromQueue()),this}edit(t,n){const i=this.ids.indexOf(t+1);return this.ids.splice(i,1,t+1),this.runners.splice(i,1,n),this}getByID(t){return this.runners[this.ids.indexOf(t+1)]}length(){return this.ids.length}merge(){let t=null;for(let n=0;n<this.runners.length;++n){const i=this.runners[n];if(t&&i.done&&t.done&&(!i._timeline||!i._timeline._runnerIds.includes(i.id))&&(!t._timeline||!t._timeline._runnerIds.includes(t.id))){this.remove(i.id);const a=i.mergeWith(t);this.edit(t.id,a),t=a,--n}else t=i}return this}remove(t){const n=this.ids.indexOf(t+1);return this.ids.splice(n,1),this.runners.splice(n,1),this}}Ct({Element:{animate(e,t,n){const i=xn.sanitise(e,t,n),s=this.timeline();return new xn(i.duration).loop(i).element(this).timeline(s.play()).schedule(i.delay,i.when)},delay(e,t){return this.animate(0,e,t)},_clearTransformRunnersBefore(e){this._transformationRunners.clearBefore(e.id)},_currentTransform(e){return this._transformationRunners.runners.filter(t=>t.id<=e.id).map(Jh).reduce(Zh,new ut)},_addRunner(e){this._transformationRunners.add(e),Jt.cancelImmediate(this._frameId),this._frameId=Jt.immediate(zm.bind(this))},_prepareRunner(){this._frameId==null&&(this._transformationRunners=new Fm().add(new wo(new ut(this))))}}});const Bm=(e,t)=>e.filter(n=>!t.includes(n));Dt(xn,{attr(e,t){return this.styleAttr("attr",e,t)},css(e,t){return this.styleAttr("css",e,t)},styleAttr(e,t,n){if(typeof t=="string")return this.styleAttr(e,{[t]:n});let i=t;if(this._tryRetarget(e,i))return this;let s=new nr(this._stepper).to(i),a=Object.keys(i);return this.queue(function(){s=s.from(this.element()[e](a))},function(c){return this.element()[e](s.at(c).valueOf()),s.done()},function(c){const l=Object.keys(c),u=Bm(l,a);if(u.length){const d=this.element()[e](u),p=new ws(s.from()).valueOf();Object.assign(p,d),s.from(p)}const f=new ws(s.to()).valueOf();Object.assign(f,c),s.to(f),a=l,i=c}),this._rememberMorpher(e,s),this},zoom(e,t){if(this._tryRetarget("zoom",e,t))return this;let n=new nr(this._stepper).to(new vt(e));return this.queue(function(){n=n.from(this.element().zoom())},function(i){return this.element().zoom(n.at(i),t),n.done()},function(i,s){t=s,n.to(i)}),this._rememberMorpher("zoom",n),this},transform(e,t,n){if(t=e.relative||t,this._isDeclarative&&!t&&this._tryRetarget("transform",e))return this;const i=ut.isMatrixLike(e);n=e.affine!=null?e.affine:n??!i;const s=new nr(this._stepper).type(n?bs:ut);let a,c,l,u,f;function d(){c=c||this.element(),a=a||uc(e,c),f=new ut(t?void 0:c),c._addRunner(this),t||c._clearTransformRunnersBefore(this)}function p(m){t||this.clearTransform();const{x:_,y:x}=new we(a).transform(c._currentTransform(this));let b=new ut({...e,origin:[_,x]}),w=this._isDeclarative&&l?l:f;if(n){b=b.decompose(_,x),w=w.decompose(_,x);const A=b.rotate,L=w.rotate,P=[A-360,A,A+360],R=P.map(B=>Math.abs(B-L)),O=Math.min(...R),W=R.indexOf(O);b.rotate=P[W]}t&&(i||(b.rotate=e.rotate||0),this._isDeclarative&&u&&(w.rotate=u)),s.from(w),s.to(b);const C=s.at(m);return u=C.rotate,l=new ut(C),this.addTransform(l),c._addRunner(this),s.done()}function y(m){(m.origin||"center").toString()!==(e.origin||"center").toString()&&(a=uc(m,c)),e={...m,origin:a}}return this.queue(d,p,y,!0),this._isDeclarative&&this._rememberMorpher("transform",s),this},x(e){return this._queueNumber("x",e)},y(e){return this._queueNumber("y",e)},ax(e){return this._queueNumber("ax",e)},ay(e){return this._queueNumber("ay",e)},dx(e=0){return this._queueNumberDelta("x",e)},dy(e=0){return this._queueNumberDelta("y",e)},dmove(e,t){return this.dx(e).dy(t)},_queueNumberDelta(e,t){if(t=new vt(t),this._tryRetarget(e,t))return this;const n=new nr(this._stepper).to(t);let i=null;return this.queue(function(){i=this.element()[e](),n.from(i),n.to(i+t)},function(s){return this.element()[e](n.at(s)),n.done()},function(s){n.to(i+new vt(s))}),this._rememberMorpher(e,n),this},_queueObject(e,t){if(this._tryRetarget(e,t))return this;const n=new nr(this._stepper).to(t);return this.queue(function(){n.from(this.element()[e]())},function(i){return this.element()[e](n.at(i)),n.done()}),this._rememberMorpher(e,n),this},_queueNumber(e,t){return this._queueObject(e,new vt(t))},cx(e){return this._queueNumber("cx",e)},cy(e){return this._queueNumber("cy",e)},move(e,t){return this.x(e).y(t)},amove(e,t){return this.ax(e).ay(t)},center(e,t){return this.cx(e).cy(t)},size(e,t){let n;return(!e||!t)&&(n=this._element.bbox()),e||(e=n.width/n.height*t),t||(t=n.height/n.width*e),this.width(e).height(t)},width(e){return this._queueNumber("width",e)},height(e){return this._queueNumber("height",e)},plot(e,t,n,i){if(arguments.length===4)return this.plot([e,t,n,i]);if(this._tryRetarget("plot",e))return this;const s=new nr(this._stepper).type(this._element.MorphArray).to(e);return this.queue(function(){s.from(this._element.array())},function(a){return this._element.plot(s.at(a)),s.done()}),this._rememberMorpher("plot",s),this},leading(e){return this._queueNumber("leading",e)},viewbox(e,t,n,i){return this._queueObject("viewbox",new Re(e,t,n,i))},update(e){return typeof e!="object"?this.update({offset:arguments[0],color:arguments[1],opacity:arguments[2]}):(e.opacity!=null&&this.attr("stop-opacity",e.opacity),e.color!=null&&this.attr("stop-color",e.color),e.offset!=null&&this.attr("offset",e.offset),this)}}),Dt(xn,{rx:bc,ry:wc,from:Vh,to:jh}),Yt(xn,"Runner");class Pc extends on{constructor(t,n=t){super(ue("svg",t),n),this.namespace()}defs(){return this.isRoot()?Ln(this.node.querySelector("defs"))||this.put(new vc):this.root().defs()}isRoot(){return!this.node.parentNode||!(this.node.parentNode instanceof qt.window.SVGElement)&&this.node.parentNode.nodeName!=="#document-fragment"}namespace(){return this.isRoot()?this.attr({xmlns:fc,version:"1.1"}).attr("xmlns:xlink",ps,dc):this.root().namespace()}removeNamespace(){return this.attr({xmlns:null,version:null}).attr("xmlns:xlink",null,dc).attr("xmlns:svgjs",null,dc)}root(){return this.isRoot()?this:super.root()}}Ct({Container:{nested:he(function(){return this.put(new Pc)})}}),Yt(Pc,"Svg",!0);let Rc=class extends on{constructor(t,n=t){super(ue("symbol",t),n)}};Ct({Container:{symbol:he(function(){return this.put(new Rc)})}}),Yt(Rc,"Symbol");function Gm(e){return this._build===!1&&this.clear(),this.node.appendChild(qt.document.createTextNode(e)),this}function Hm(){return this.node.getComputedTextLength()}function Um(e,t=this.bbox()){return e==null?t.x:this.attr("x",this.attr("x")+e-t.x)}function Vm(e,t=this.bbox()){return e==null?t.y:this.attr("y",this.attr("y")+e-t.y)}function jm(e,t,n=this.bbox()){return this.x(e,n).y(t,n)}function qm(e,t=this.bbox()){return e==null?t.cx:this.attr("x",this.attr("x")+e-t.cx)}function Ym(e,t=this.bbox()){return e==null?t.cy:this.attr("y",this.attr("y")+e-t.cy)}function Xm(e,t,n=this.bbox()){return this.cx(e,n).cy(t,n)}function Km(e){return this.attr("x",e)}function Zm(e){return this.attr("y",e)}function Jm(e,t){return this.ax(e).ay(t)}function Qm(e){return this._build=!!e,this}const Qh=Object.freeze(Object.defineProperty({__proto__:null,amove:Jm,ax:Km,ay:Zm,build:Qm,center:Xm,cx:qm,cy:Ym,length:Hm,move:jm,plain:Gm,x:Um,y:Vm},Symbol.toStringTag,{value:"Module"}));class Vn extends mn{constructor(t,n=t){super(ue("text",t),n),this.dom.leading=this.dom.leading??new vt(1.3),this._rebuild=!0,this._build=!1}leading(t){return t==null?this.dom.leading:(this.dom.leading=new vt(t),this.rebuild())}rebuild(t){if(typeof t=="boolean"&&(this._rebuild=t),this._rebuild){const n=this;let i=0;const s=this.dom.leading;this.each(function(a){if(hc(this.node))return;const c=qt.window.getComputedStyle(this.node).getPropertyValue("font-size"),l=s*new vt(c);this.dom.newLined&&(this.attr("x",n.attr("x")),this.text()===`
`?i+=l:(this.attr("dy",a?l+i:0),i=0))}),this.fire("rebuild")}return this}setData(t){return this.dom=t,this.dom.leading=new vt(t.leading||1.3),this}writeDataToDom(){return Sh(this,this.dom,{leading:1.3}),this}text(t){if(t===void 0){const n=this.node.childNodes;let i=0;t="";for(let s=0,a=n.length;s<a;++s){if(n[s].nodeName==="textPath"||hc(n[s])){s===0&&(i=s+1);continue}s!==i&&n[s].nodeType!==3&&Ln(n[s]).dom.newLined===!0&&(t+=`
`),t+=n[s].textContent}return t}if(this.clear().build(!0),typeof t=="function")t.call(this,this);else{t=(t+"").split(`
`);for(let n=0,i=t.length;n<i;n++)this.newLine(t[n])}return this.build(!1).rebuild()}}Dt(Vn,Qh),Ct({Container:{text:he(function(e=""){return this.put(new Vn).text(e)}),plain:he(function(e=""){return this.put(new Vn).plain(e)})}}),Yt(Vn,"Text");class ko extends mn{constructor(t,n=t){super(ue("tspan",t),n),this._build=!1}dx(t){return this.attr("dx",t)}dy(t){return this.attr("dy",t)}newLine(){this.dom.newLined=!0;const t=this.parent();if(!(t instanceof Vn))return this;const n=t.index(this),i=qt.window.getComputedStyle(this.node).getPropertyValue("font-size"),s=t.dom.leading*new vt(i);return this.dy(n?s:0).attr("x",t.x())}text(t){return t==null?this.node.textContent+(this.dom.newLined?`
`:""):(typeof t=="function"?(this.clear().build(!0),t.call(this,this),this.build(!1)):this.plain(t),this)}}Dt(ko,Qh),Ct({Tspan:{tspan:he(function(e=""){const t=new ko;return this._build||this.clear(),this.put(t).text(e)})},Text:{newLine:function(e=""){return this.tspan(e).newLine()}}}),Yt(ko,"Tspan");class Ic extends mn{constructor(t,n=t){super(ue("circle",t),n)}radius(t){return this.attr("r",t)}rx(t){return this.attr("r",t)}ry(t){return this.rx(t)}size(t){return this.radius(new vt(t).divide(2))}}Dt(Ic,{x:Wh,y:zh,cx:Fh,cy:Bh,width:Gh,height:Hh}),Ct({Container:{circle:he(function(e=0){return this.put(new Ic).size(e).move(0,0)})}}),Yt(Ic,"Circle");class Nc extends on{constructor(t,n=t){super(ue("clipPath",t),n)}remove(){return this.targets().forEach(function(t){t.unclip()}),super.remove()}targets(){return Ar("svg [clip-path*="+this.id()+"]")}}Ct({Container:{clip:he(function(){return this.defs().put(new Nc)})},Element:{clipper(){return this.reference("clip-path")},clipWith(e){const t=e instanceof Nc?e:this.parent().clip().add(e);return this.attr("clip-path","url(#"+t.id()+")")},unclip(){return this.attr("clip-path",null)}}}),Yt(Nc,"ClipPath");class tf extends Un{constructor(t,n=t){super(ue("foreignObject",t),n)}}Ct({Container:{foreignObject:he(function(e,t){return this.put(new tf).size(e,t)})}}),Yt(tf,"ForeignObject");function t1(e,t){return this.children().forEach(n=>{let i;try{i=n.node instanceof ry().SVGSVGElement?new Re(n.attr(["x","y","width","height"])):n.bbox()}catch{return}const s=new ut(n),a=s.translate(e,t).transform(s.inverse()),c=new we(i.x,i.y).transform(a);n.move(c.x,c.y)}),this}function e1(e){return this.dmove(e,0)}function n1(e){return this.dmove(0,e)}function i1(e,t=this.bbox()){return e==null?t.height:this.size(t.width,e,t)}function r1(e=0,t=0,n=this.bbox()){const i=e-n.x,s=t-n.y;return this.dmove(i,s)}function s1(e,t,n=this.bbox()){const i=kr(this,e,t,n),s=i.width/n.width,a=i.height/n.height;return this.children().forEach(c=>{const l=new we(n).transform(new ut(c).inverse());c.scale(s,a,l.x,l.y)}),this}function o1(e,t=this.bbox()){return e==null?t.width:this.size(e,t.height,t)}function a1(e,t=this.bbox()){return e==null?t.x:this.move(e,t.y,t)}function c1(e,t=this.bbox()){return e==null?t.y:this.move(t.x,e,t)}const ef=Object.freeze(Object.defineProperty({__proto__:null,dmove:t1,dx:e1,dy:n1,height:i1,move:r1,size:s1,width:o1,x:a1,y:c1},Symbol.toStringTag,{value:"Module"}));let So=class extends on{constructor(t,n=t){super(ue("g",t),n)}};Dt(So,ef),Ct({Container:{group:he(function(){return this.put(new So)})}}),Yt(So,"G");let Co=class extends on{constructor(t,n=t){super(ue("a",t),n)}target(t){return this.attr("target",t)}to(t){return this.attr("href",t,ps)}};Dt(Co,ef),Ct({Container:{link:he(function(e){return this.put(new Co).to(e)})},Element:{unlink(){const e=this.linker();if(!e)return this;const t=e.parent();if(!t)return this.remove();const n=t.index(e);return t.add(this,n),e.remove(),this},linkTo(e){let t=this.linker();return t||(t=new Co,this.wrap(t)),typeof e=="function"?e.call(t,t):t.to(e),this},linker(){const e=this.parent();return e&&e.node.nodeName.toLowerCase()==="a"?e:null}}}),Yt(Co,"A");class $c extends on{constructor(t,n=t){super(ue("mask",t),n)}remove(){return this.targets().forEach(function(t){t.unmask()}),super.remove()}targets(){return Ar("svg [mask*="+this.id()+"]")}}Ct({Container:{mask:he(function(){return this.defs().put(new $c)})},Element:{masker(){return this.reference("mask")},maskWith(e){const t=e instanceof $c?e:this.parent().mask().add(e);return this.attr("mask","url(#"+t.id()+")")},unmask(){return this.attr("mask",null)}}}),Yt($c,"Mask");class nf extends Un{constructor(t,n=t){super(ue("stop",t),n)}update(t){return(typeof t=="number"||t instanceof vt)&&(t={offset:arguments[0],color:arguments[1],opacity:arguments[2]}),t.opacity!=null&&this.attr("stop-opacity",t.opacity),t.color!=null&&this.attr("stop-color",t.color),t.offset!=null&&this.attr("offset",new vt(t.offset)),this}}Ct({Gradient:{stop:function(e,t,n){return this.put(new nf).update(e,t,n)}}}),Yt(nf,"Stop");function l1(e,t){if(!e)return"";if(!t)return e;let n=e+"{";for(const i in t)n+=ey(i)+":"+t[i]+";";return n+="}",n}class Wc extends Un{constructor(t,n=t){super(ue("style",t),n)}addText(t=""){return this.node.textContent+=t,this}font(t,n,i={}){return this.rule("@font-face",{fontFamily:t,src:n,...i})}rule(t,n){return this.addText(l1(t,n))}}Ct("Dom",{style(e,t){return this.put(new Wc).rule(e,t)},fontface(e,t,n){return this.put(new Wc).font(e,t,n)}}),Yt(Wc,"Style");class zc extends Vn{constructor(t,n=t){super(ue("textPath",t),n)}array(){const t=this.track();return t?t.array():null}plot(t){const n=this.track();let i=null;return n&&(i=n.plot(t)),t==null?i:this}track(){return this.reference("href")}}Ct({Container:{textPath:he(function(e,t){return e instanceof Vn||(e=this.text(e)),e.path(t)})},Text:{path:he(function(e,t=!0){const n=new zc;e instanceof Lr||(e=this.defs().path(e)),n.attr("href","#"+e,ps);let i;if(t)for(;i=this.node.firstChild;)n.node.appendChild(i);return this.put(n)}),textPath(){return this.findOne("textPath")}},Path:{text:he(function(e){return e instanceof Vn||(e=new Vn().addTo(this.parent()).text(e)),e.path(this)}),targets(){return Ar("svg textPath").filter(e=>(e.attr("href")||"").includes(this.id()))}}}),zc.prototype.MorphArray=er,Yt(zc,"TextPath");class rf extends mn{constructor(t,n=t){super(ue("use",t),n)}use(t,n){return this.attr("href",(n||"")+"#"+t,ps)}}Ct({Container:{use:he(function(e,t){return this.put(new rf).use(e,t)})}}),Yt(rf,"Use");const ht=Je;Dt([Pc,Rc,xo,vs,_o],sn("viewbox")),Dt([Mr,Ss,ks,Lr],sn("marker")),Dt(Vn,sn("Text")),Dt(Lr,sn("Path")),Dt(vc,sn("Defs")),Dt([Vn,ko],sn("Tspan")),Dt([bo,mo,_s,xn],sn("radius")),Dt(ys,sn("EventTarget")),Dt(Pi,sn("Dom")),Dt(Un,sn("Element")),Dt(mn,sn("Shape")),Dt([on,Uh],sn("Container")),Dt(_s,sn("Gradient")),Dt(xn,sn("Runner")),Qi.extend(Qg()),Lm([vt,ei,Re,ut,Er,Ri,er,we]),Om();var Cs={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */var u1=Cs.exports,sf;function h1(){return sf||(sf=1,function(e,t){(function(){var n,i="4.17.21",s=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",c="Expected a function",l="Invalid `variable` option passed into `_.template`",u="__lodash_hash_undefined__",f=500,d="__lodash_placeholder__",p=1,y=2,m=4,_=1,x=2,b=1,w=2,C=4,A=8,L=16,P=32,R=64,O=128,W=256,B=512,H=30,Y="...",V=800,at=16,rt=1,ft=2,$t=3,Mt=1/0,Gt=9007199254740991,Qt=17976931348623157e292,et=NaN,nt=**********,ot=nt-1,xt=nt>>>1,Vt=[["ary",O],["bind",b],["bindKey",w],["curry",A],["curryRight",L],["flip",B],["partial",P],["partialRight",R],["rearg",W]],Tt="[object Arguments]",Wt="[object Array]",Xt="[object AsyncFunction]",oe="[object Boolean]",se="[object Date]",Le="[object DOMException]",Ee="[object Error]",ln="[object Function]",un="[object GeneratorFunction]",ce="[object Map]",wn="[object Number]",hn="[object Null]",Me="[object Object]",li="[object Promise]",Yr="[object Proxy]",kn="[object RegExp]",tn="[object Set]",vi="[object String]",Bi="[object Symbol]",Jl="[object Undefined]",hr="[object WeakMap]",da="[object WeakSet]",Gi="[object ArrayBuffer]",In="[object DataView]",Bs="[object Float32Array]",Xr="[object Float64Array]",Kr="[object Int8Array]",fr="[object Int16Array]",Gs="[object Int32Array]",Hs="[object Uint8Array]",Us="[object Uint8ClampedArray]",Vs="[object Uint16Array]",js="[object Uint32Array]",Ql=/\b__p \+= '';/g,tu=/\b(__p \+=) '' \+/g,qs=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ys=/&(?:amp|lt|gt|quot|#39);/g,bi=/[&<>"']/g,Xs=RegExp(Ys.source),Ks=RegExp(bi.source),pa=/<%-([\s\S]+?)%>/g,eu=/<%([\s\S]+?)%>/g,Zs=/<%=([\s\S]+?)%>/g,nu=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ga=/^\w*$/,ya=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Js=/[\\^$.*+?()[\]{}|]/g,iu=RegExp(Js.source),Zr=/^\s+/,ma=/\s/,ru=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,su=/\{\n\/\* \[wrapped with (.+)\] \*/,ou=/,? & /,au=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,T=/[()=,{}\[\]\/\s]/,K=/\\(\\)?/g,it=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,mt=/\w*$/,Kt=/^[-+]0x[0-9a-f]+$/i,ne=/^0b[01]+$/i,Ht=/^\[object .+?Constructor\]$/,It=/^0o[0-7]+$/i,Be=/^(?:0|[1-9]\d*)$/,me=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,fe=/($^)/,fn=/['\n\r\u2028\u2029\\]/g,Kn="\\ud800-\\udfff",wi="\\u0300-\\u036f",Xe="\\ufe20-\\ufe2f",Qs="\\u20d0-\\u20ff",xa=wi+Xe+Qs,_a="\\u2700-\\u27bf",Ep="a-z\\xdf-\\xf6\\xf8-\\xff",xv="\\xac\\xb1\\xd7\\xf7",_v="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",vv="\\u2000-\\u206f",bv=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Mp="A-Z\\xc0-\\xd6\\xd8-\\xde",Tp="\\ufe0e\\ufe0f",Lp=xv+_v+vv+bv,cu="['’]",wv="["+Kn+"]",Op="["+Lp+"]",va="["+xa+"]",Dp="\\d+",kv="["+_a+"]",Pp="["+Ep+"]",Rp="[^"+Kn+Lp+Dp+_a+Ep+Mp+"]",lu="\\ud83c[\\udffb-\\udfff]",Sv="(?:"+va+"|"+lu+")",Ip="[^"+Kn+"]",uu="(?:\\ud83c[\\udde6-\\uddff]){2}",hu="[\\ud800-\\udbff][\\udc00-\\udfff]",Jr="["+Mp+"]",Np="\\u200d",$p="(?:"+Pp+"|"+Rp+")",Cv="(?:"+Jr+"|"+Rp+")",Wp="(?:"+cu+"(?:d|ll|m|re|s|t|ve))?",zp="(?:"+cu+"(?:D|LL|M|RE|S|T|VE))?",Fp=Sv+"?",Bp="["+Tp+"]?",Av="(?:"+Np+"(?:"+[Ip,uu,hu].join("|")+")"+Bp+Fp+")*",Ev="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Mv="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Gp=Bp+Fp+Av,Tv="(?:"+[kv,uu,hu].join("|")+")"+Gp,Lv="(?:"+[Ip+va+"?",va,uu,hu,wv].join("|")+")",Ov=RegExp(cu,"g"),Dv=RegExp(va,"g"),fu=RegExp(lu+"(?="+lu+")|"+Lv+Gp,"g"),Pv=RegExp([Jr+"?"+Pp+"+"+Wp+"(?="+[Op,Jr,"$"].join("|")+")",Cv+"+"+zp+"(?="+[Op,Jr+$p,"$"].join("|")+")",Jr+"?"+$p+"+"+Wp,Jr+"+"+zp,Mv,Ev,Dp,Tv].join("|"),"g"),Rv=RegExp("["+Np+Kn+xa+Tp+"]"),Iv=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Nv=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],$v=-1,de={};de[Bs]=de[Xr]=de[Kr]=de[fr]=de[Gs]=de[Hs]=de[Us]=de[Vs]=de[js]=!0,de[Tt]=de[Wt]=de[Gi]=de[oe]=de[In]=de[se]=de[Ee]=de[ln]=de[ce]=de[wn]=de[Me]=de[kn]=de[tn]=de[vi]=de[hr]=!1;var le={};le[Tt]=le[Wt]=le[Gi]=le[In]=le[oe]=le[se]=le[Bs]=le[Xr]=le[Kr]=le[fr]=le[Gs]=le[ce]=le[wn]=le[Me]=le[kn]=le[tn]=le[vi]=le[Bi]=le[Hs]=le[Us]=le[Vs]=le[js]=!0,le[Ee]=le[ln]=le[hr]=!1;var Wv={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},zv={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Fv={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Bv={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Gv=parseFloat,Hv=parseInt,Hp=typeof Ve=="object"&&Ve&&Ve.Object===Object&&Ve,Uv=typeof self=="object"&&self&&self.Object===Object&&self,Ge=Hp||Uv||Function("return this")(),du=t&&!t.nodeType&&t,dr=du&&!0&&e&&!e.nodeType&&e,Up=dr&&dr.exports===du,pu=Up&&Hp.process,Nn=function(){try{var I=dr&&dr.require&&dr.require("util").types;return I||pu&&pu.binding&&pu.binding("util")}catch{}}(),Vp=Nn&&Nn.isArrayBuffer,jp=Nn&&Nn.isDate,qp=Nn&&Nn.isMap,Yp=Nn&&Nn.isRegExp,Xp=Nn&&Nn.isSet,Kp=Nn&&Nn.isTypedArray;function Sn(I,G,z){switch(z.length){case 0:return I.call(G);case 1:return I.call(G,z[0]);case 2:return I.call(G,z[0],z[1]);case 3:return I.call(G,z[0],z[1],z[2])}return I.apply(G,z)}function Vv(I,G,z,st){for(var bt=-1,Zt=I==null?0:I.length;++bt<Zt;){var Oe=I[bt];G(st,Oe,z(Oe),I)}return st}function $n(I,G){for(var z=-1,st=I==null?0:I.length;++z<st&&G(I[z],z,I)!==!1;);return I}function jv(I,G){for(var z=I==null?0:I.length;z--&&G(I[z],z,I)!==!1;);return I}function Zp(I,G){for(var z=-1,st=I==null?0:I.length;++z<st;)if(!G(I[z],z,I))return!1;return!0}function Hi(I,G){for(var z=-1,st=I==null?0:I.length,bt=0,Zt=[];++z<st;){var Oe=I[z];G(Oe,z,I)&&(Zt[bt++]=Oe)}return Zt}function ba(I,G){var z=I==null?0:I.length;return!!z&&Qr(I,G,0)>-1}function gu(I,G,z){for(var st=-1,bt=I==null?0:I.length;++st<bt;)if(z(G,I[st]))return!0;return!1}function ge(I,G){for(var z=-1,st=I==null?0:I.length,bt=Array(st);++z<st;)bt[z]=G(I[z],z,I);return bt}function Ui(I,G){for(var z=-1,st=G.length,bt=I.length;++z<st;)I[bt+z]=G[z];return I}function yu(I,G,z,st){var bt=-1,Zt=I==null?0:I.length;for(st&&Zt&&(z=I[++bt]);++bt<Zt;)z=G(z,I[bt],bt,I);return z}function qv(I,G,z,st){var bt=I==null?0:I.length;for(st&&bt&&(z=I[--bt]);bt--;)z=G(z,I[bt],bt,I);return z}function mu(I,G){for(var z=-1,st=I==null?0:I.length;++z<st;)if(G(I[z],z,I))return!0;return!1}var Yv=xu("length");function Xv(I){return I.split("")}function Kv(I){return I.match(au)||[]}function Jp(I,G,z){var st;return z(I,function(bt,Zt,Oe){if(G(bt,Zt,Oe))return st=Zt,!1}),st}function wa(I,G,z,st){for(var bt=I.length,Zt=z+(st?1:-1);st?Zt--:++Zt<bt;)if(G(I[Zt],Zt,I))return Zt;return-1}function Qr(I,G,z){return G===G?cb(I,G,z):wa(I,Qp,z)}function Zv(I,G,z,st){for(var bt=z-1,Zt=I.length;++bt<Zt;)if(st(I[bt],G))return bt;return-1}function Qp(I){return I!==I}function t0(I,G){var z=I==null?0:I.length;return z?vu(I,G)/z:et}function xu(I){return function(G){return G==null?n:G[I]}}function _u(I){return function(G){return I==null?n:I[G]}}function e0(I,G,z,st,bt){return bt(I,function(Zt,Oe,ae){z=st?(st=!1,Zt):G(z,Zt,Oe,ae)}),z}function Jv(I,G){var z=I.length;for(I.sort(G);z--;)I[z]=I[z].value;return I}function vu(I,G){for(var z,st=-1,bt=I.length;++st<bt;){var Zt=G(I[st]);Zt!==n&&(z=z===n?Zt:z+Zt)}return z}function bu(I,G){for(var z=-1,st=Array(I);++z<I;)st[z]=G(z);return st}function Qv(I,G){return ge(G,function(z){return[z,I[z]]})}function n0(I){return I&&I.slice(0,o0(I)+1).replace(Zr,"")}function Cn(I){return function(G){return I(G)}}function wu(I,G){return ge(G,function(z){return I[z]})}function to(I,G){return I.has(G)}function i0(I,G){for(var z=-1,st=I.length;++z<st&&Qr(G,I[z],0)>-1;);return z}function r0(I,G){for(var z=I.length;z--&&Qr(G,I[z],0)>-1;);return z}function tb(I,G){for(var z=I.length,st=0;z--;)I[z]===G&&++st;return st}var eb=_u(Wv),nb=_u(zv);function ib(I){return"\\"+Bv[I]}function rb(I,G){return I==null?n:I[G]}function ts(I){return Rv.test(I)}function sb(I){return Iv.test(I)}function ob(I){for(var G,z=[];!(G=I.next()).done;)z.push(G.value);return z}function ku(I){var G=-1,z=Array(I.size);return I.forEach(function(st,bt){z[++G]=[bt,st]}),z}function s0(I,G){return function(z){return I(G(z))}}function Vi(I,G){for(var z=-1,st=I.length,bt=0,Zt=[];++z<st;){var Oe=I[z];(Oe===G||Oe===d)&&(I[z]=d,Zt[bt++]=z)}return Zt}function ka(I){var G=-1,z=Array(I.size);return I.forEach(function(st){z[++G]=st}),z}function ab(I){var G=-1,z=Array(I.size);return I.forEach(function(st){z[++G]=[st,st]}),z}function cb(I,G,z){for(var st=z-1,bt=I.length;++st<bt;)if(I[st]===G)return st;return-1}function lb(I,G,z){for(var st=z+1;st--;)if(I[st]===G)return st;return st}function es(I){return ts(I)?hb(I):Yv(I)}function Zn(I){return ts(I)?fb(I):Xv(I)}function o0(I){for(var G=I.length;G--&&ma.test(I.charAt(G)););return G}var ub=_u(Fv);function hb(I){for(var G=fu.lastIndex=0;fu.test(I);)++G;return G}function fb(I){return I.match(fu)||[]}function db(I){return I.match(Pv)||[]}var pb=function I(G){G=G==null?Ge:ns.defaults(Ge.Object(),G,ns.pick(Ge,Nv));var z=G.Array,st=G.Date,bt=G.Error,Zt=G.Function,Oe=G.Math,ae=G.Object,Su=G.RegExp,gb=G.String,Wn=G.TypeError,Sa=z.prototype,yb=Zt.prototype,is=ae.prototype,Ca=G["__core-js_shared__"],Aa=yb.toString,ie=is.hasOwnProperty,mb=0,a0=function(){var r=/[^.]+$/.exec(Ca&&Ca.keys&&Ca.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),Ea=is.toString,xb=Aa.call(ae),_b=Ge._,vb=Su("^"+Aa.call(ie).replace(Js,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ma=Up?G.Buffer:n,ji=G.Symbol,Ta=G.Uint8Array,c0=Ma?Ma.allocUnsafe:n,La=s0(ae.getPrototypeOf,ae),l0=ae.create,u0=is.propertyIsEnumerable,Oa=Sa.splice,h0=ji?ji.isConcatSpreadable:n,eo=ji?ji.iterator:n,pr=ji?ji.toStringTag:n,Da=function(){try{var r=_r(ae,"defineProperty");return r({},"",{}),r}catch{}}(),bb=G.clearTimeout!==Ge.clearTimeout&&G.clearTimeout,wb=st&&st.now!==Ge.Date.now&&st.now,kb=G.setTimeout!==Ge.setTimeout&&G.setTimeout,Pa=Oe.ceil,Ra=Oe.floor,Cu=ae.getOwnPropertySymbols,Sb=Ma?Ma.isBuffer:n,f0=G.isFinite,Cb=Sa.join,Ab=s0(ae.keys,ae),De=Oe.max,Ke=Oe.min,Eb=st.now,Mb=G.parseInt,d0=Oe.random,Tb=Sa.reverse,Au=_r(G,"DataView"),no=_r(G,"Map"),Eu=_r(G,"Promise"),rs=_r(G,"Set"),io=_r(G,"WeakMap"),ro=_r(ae,"create"),Ia=io&&new io,ss={},Lb=vr(Au),Ob=vr(no),Db=vr(Eu),Pb=vr(rs),Rb=vr(io),Na=ji?ji.prototype:n,so=Na?Na.valueOf:n,p0=Na?Na.toString:n;function k(r){if(be(r)&&!kt(r)&&!(r instanceof Ut)){if(r instanceof zn)return r;if(ie.call(r,"__wrapped__"))return gg(r)}return new zn(r)}var os=function(){function r(){}return function(o){if(!xe(o))return{};if(l0)return l0(o);r.prototype=o;var h=new r;return r.prototype=n,h}}();function $a(){}function zn(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=n}k.templateSettings={escape:pa,evaluate:eu,interpolate:Zs,variable:"",imports:{_:k}},k.prototype=$a.prototype,k.prototype.constructor=k,zn.prototype=os($a.prototype),zn.prototype.constructor=zn;function Ut(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=nt,this.__views__=[]}function Ib(){var r=new Ut(this.__wrapped__);return r.__actions__=dn(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=dn(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=dn(this.__views__),r}function Nb(){if(this.__filtered__){var r=new Ut(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function $b(){var r=this.__wrapped__.value(),o=this.__dir__,h=kt(r),g=o<0,v=h?r.length:0,S=Xw(0,v,this.__views__),E=S.start,D=S.end,N=D-E,U=g?D:E-1,j=this.__iteratees__,X=j.length,tt=0,ct=Ke(N,this.__takeCount__);if(!h||!g&&v==N&&ct==N)return W0(r,this.__actions__);var gt=[];t:for(;N--&&tt<ct;){U+=o;for(var Lt=-1,yt=r[U];++Lt<X;){var zt=j[Lt],jt=zt.iteratee,Mn=zt.type,rn=jt(yt);if(Mn==ft)yt=rn;else if(!rn){if(Mn==rt)continue t;break t}}gt[tt++]=yt}return gt}Ut.prototype=os($a.prototype),Ut.prototype.constructor=Ut;function gr(r){var o=-1,h=r==null?0:r.length;for(this.clear();++o<h;){var g=r[o];this.set(g[0],g[1])}}function Wb(){this.__data__=ro?ro(null):{},this.size=0}function zb(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function Fb(r){var o=this.__data__;if(ro){var h=o[r];return h===u?n:h}return ie.call(o,r)?o[r]:n}function Bb(r){var o=this.__data__;return ro?o[r]!==n:ie.call(o,r)}function Gb(r,o){var h=this.__data__;return this.size+=this.has(r)?0:1,h[r]=ro&&o===n?u:o,this}gr.prototype.clear=Wb,gr.prototype.delete=zb,gr.prototype.get=Fb,gr.prototype.has=Bb,gr.prototype.set=Gb;function ki(r){var o=-1,h=r==null?0:r.length;for(this.clear();++o<h;){var g=r[o];this.set(g[0],g[1])}}function Hb(){this.__data__=[],this.size=0}function Ub(r){var o=this.__data__,h=Wa(o,r);if(h<0)return!1;var g=o.length-1;return h==g?o.pop():Oa.call(o,h,1),--this.size,!0}function Vb(r){var o=this.__data__,h=Wa(o,r);return h<0?n:o[h][1]}function jb(r){return Wa(this.__data__,r)>-1}function qb(r,o){var h=this.__data__,g=Wa(h,r);return g<0?(++this.size,h.push([r,o])):h[g][1]=o,this}ki.prototype.clear=Hb,ki.prototype.delete=Ub,ki.prototype.get=Vb,ki.prototype.has=jb,ki.prototype.set=qb;function Si(r){var o=-1,h=r==null?0:r.length;for(this.clear();++o<h;){var g=r[o];this.set(g[0],g[1])}}function Yb(){this.size=0,this.__data__={hash:new gr,map:new(no||ki),string:new gr}}function Xb(r){var o=Ka(this,r).delete(r);return this.size-=o?1:0,o}function Kb(r){return Ka(this,r).get(r)}function Zb(r){return Ka(this,r).has(r)}function Jb(r,o){var h=Ka(this,r),g=h.size;return h.set(r,o),this.size+=h.size==g?0:1,this}Si.prototype.clear=Yb,Si.prototype.delete=Xb,Si.prototype.get=Kb,Si.prototype.has=Zb,Si.prototype.set=Jb;function yr(r){var o=-1,h=r==null?0:r.length;for(this.__data__=new Si;++o<h;)this.add(r[o])}function Qb(r){return this.__data__.set(r,u),this}function tw(r){return this.__data__.has(r)}yr.prototype.add=yr.prototype.push=Qb,yr.prototype.has=tw;function Jn(r){var o=this.__data__=new ki(r);this.size=o.size}function ew(){this.__data__=new ki,this.size=0}function nw(r){var o=this.__data__,h=o.delete(r);return this.size=o.size,h}function iw(r){return this.__data__.get(r)}function rw(r){return this.__data__.has(r)}function sw(r,o){var h=this.__data__;if(h instanceof ki){var g=h.__data__;if(!no||g.length<s-1)return g.push([r,o]),this.size=++h.size,this;h=this.__data__=new Si(g)}return h.set(r,o),this.size=h.size,this}Jn.prototype.clear=ew,Jn.prototype.delete=nw,Jn.prototype.get=iw,Jn.prototype.has=rw,Jn.prototype.set=sw;function g0(r,o){var h=kt(r),g=!h&&br(r),v=!h&&!g&&Zi(r),S=!h&&!g&&!v&&us(r),E=h||g||v||S,D=E?bu(r.length,gb):[],N=D.length;for(var U in r)(o||ie.call(r,U))&&!(E&&(U=="length"||v&&(U=="offset"||U=="parent")||S&&(U=="buffer"||U=="byteLength"||U=="byteOffset")||Mi(U,N)))&&D.push(U);return D}function y0(r){var o=r.length;return o?r[Wu(0,o-1)]:n}function ow(r,o){return Za(dn(r),mr(o,0,r.length))}function aw(r){return Za(dn(r))}function Mu(r,o,h){(h!==n&&!Qn(r[o],h)||h===n&&!(o in r))&&Ci(r,o,h)}function oo(r,o,h){var g=r[o];(!(ie.call(r,o)&&Qn(g,h))||h===n&&!(o in r))&&Ci(r,o,h)}function Wa(r,o){for(var h=r.length;h--;)if(Qn(r[h][0],o))return h;return-1}function cw(r,o,h,g){return qi(r,function(v,S,E){o(g,v,h(v),E)}),g}function m0(r,o){return r&&hi(o,$e(o),r)}function lw(r,o){return r&&hi(o,gn(o),r)}function Ci(r,o,h){o=="__proto__"&&Da?Da(r,o,{configurable:!0,enumerable:!0,value:h,writable:!0}):r[o]=h}function Tu(r,o){for(var h=-1,g=o.length,v=z(g),S=r==null;++h<g;)v[h]=S?n:lh(r,o[h]);return v}function mr(r,o,h){return r===r&&(h!==n&&(r=r<=h?r:h),o!==n&&(r=r>=o?r:o)),r}function Fn(r,o,h,g,v,S){var E,D=o&p,N=o&y,U=o&m;if(h&&(E=v?h(r,g,v,S):h(r)),E!==n)return E;if(!xe(r))return r;var j=kt(r);if(j){if(E=Zw(r),!D)return dn(r,E)}else{var X=Ze(r),tt=X==ln||X==un;if(Zi(r))return B0(r,D);if(X==Me||X==Tt||tt&&!v){if(E=N||tt?{}:og(r),!D)return N?Fw(r,lw(E,r)):zw(r,m0(E,r))}else{if(!le[X])return v?r:{};E=Jw(r,X,D)}}S||(S=new Jn);var ct=S.get(r);if(ct)return ct;S.set(r,E),Ig(r)?r.forEach(function(yt){E.add(Fn(yt,o,h,yt,r,S))}):Pg(r)&&r.forEach(function(yt,zt){E.set(zt,Fn(yt,o,h,zt,r,S))});var gt=U?N?Xu:Yu:N?gn:$e,Lt=j?n:gt(r);return $n(Lt||r,function(yt,zt){Lt&&(zt=yt,yt=r[zt]),oo(E,zt,Fn(yt,o,h,zt,r,S))}),E}function uw(r){var o=$e(r);return function(h){return x0(h,r,o)}}function x0(r,o,h){var g=h.length;if(r==null)return!g;for(r=ae(r);g--;){var v=h[g],S=o[v],E=r[v];if(E===n&&!(v in r)||!S(E))return!1}return!0}function _0(r,o,h){if(typeof r!="function")throw new Wn(c);return po(function(){r.apply(n,h)},o)}function ao(r,o,h,g){var v=-1,S=ba,E=!0,D=r.length,N=[],U=o.length;if(!D)return N;h&&(o=ge(o,Cn(h))),g?(S=gu,E=!1):o.length>=s&&(S=to,E=!1,o=new yr(o));t:for(;++v<D;){var j=r[v],X=h==null?j:h(j);if(j=g||j!==0?j:0,E&&X===X){for(var tt=U;tt--;)if(o[tt]===X)continue t;N.push(j)}else S(o,X,g)||N.push(j)}return N}var qi=j0(ui),v0=j0(Ou,!0);function hw(r,o){var h=!0;return qi(r,function(g,v,S){return h=!!o(g,v,S),h}),h}function za(r,o,h){for(var g=-1,v=r.length;++g<v;){var S=r[g],E=o(S);if(E!=null&&(D===n?E===E&&!En(E):h(E,D)))var D=E,N=S}return N}function fw(r,o,h,g){var v=r.length;for(h=Et(h),h<0&&(h=-h>v?0:v+h),g=g===n||g>v?v:Et(g),g<0&&(g+=v),g=h>g?0:$g(g);h<g;)r[h++]=o;return r}function b0(r,o){var h=[];return qi(r,function(g,v,S){o(g,v,S)&&h.push(g)}),h}function He(r,o,h,g,v){var S=-1,E=r.length;for(h||(h=t2),v||(v=[]);++S<E;){var D=r[S];o>0&&h(D)?o>1?He(D,o-1,h,g,v):Ui(v,D):g||(v[v.length]=D)}return v}var Lu=q0(),w0=q0(!0);function ui(r,o){return r&&Lu(r,o,$e)}function Ou(r,o){return r&&w0(r,o,$e)}function Fa(r,o){return Hi(o,function(h){return Ti(r[h])})}function xr(r,o){o=Xi(o,r);for(var h=0,g=o.length;r!=null&&h<g;)r=r[fi(o[h++])];return h&&h==g?r:n}function k0(r,o,h){var g=o(r);return kt(r)?g:Ui(g,h(r))}function en(r){return r==null?r===n?Jl:hn:pr&&pr in ae(r)?Yw(r):a2(r)}function Du(r,o){return r>o}function dw(r,o){return r!=null&&ie.call(r,o)}function pw(r,o){return r!=null&&o in ae(r)}function gw(r,o,h){return r>=Ke(o,h)&&r<De(o,h)}function Pu(r,o,h){for(var g=h?gu:ba,v=r[0].length,S=r.length,E=S,D=z(S),N=1/0,U=[];E--;){var j=r[E];E&&o&&(j=ge(j,Cn(o))),N=Ke(j.length,N),D[E]=!h&&(o||v>=120&&j.length>=120)?new yr(E&&j):n}j=r[0];var X=-1,tt=D[0];t:for(;++X<v&&U.length<N;){var ct=j[X],gt=o?o(ct):ct;if(ct=h||ct!==0?ct:0,!(tt?to(tt,gt):g(U,gt,h))){for(E=S;--E;){var Lt=D[E];if(!(Lt?to(Lt,gt):g(r[E],gt,h)))continue t}tt&&tt.push(gt),U.push(ct)}}return U}function yw(r,o,h,g){return ui(r,function(v,S,E){o(g,h(v),S,E)}),g}function co(r,o,h){o=Xi(o,r),r=ug(r,o);var g=r==null?r:r[fi(Gn(o))];return g==null?n:Sn(g,r,h)}function S0(r){return be(r)&&en(r)==Tt}function mw(r){return be(r)&&en(r)==Gi}function xw(r){return be(r)&&en(r)==se}function lo(r,o,h,g,v){return r===o?!0:r==null||o==null||!be(r)&&!be(o)?r!==r&&o!==o:_w(r,o,h,g,lo,v)}function _w(r,o,h,g,v,S){var E=kt(r),D=kt(o),N=E?Wt:Ze(r),U=D?Wt:Ze(o);N=N==Tt?Me:N,U=U==Tt?Me:U;var j=N==Me,X=U==Me,tt=N==U;if(tt&&Zi(r)){if(!Zi(o))return!1;E=!0,j=!1}if(tt&&!j)return S||(S=new Jn),E||us(r)?ig(r,o,h,g,v,S):jw(r,o,N,h,g,v,S);if(!(h&_)){var ct=j&&ie.call(r,"__wrapped__"),gt=X&&ie.call(o,"__wrapped__");if(ct||gt){var Lt=ct?r.value():r,yt=gt?o.value():o;return S||(S=new Jn),v(Lt,yt,h,g,S)}}return tt?(S||(S=new Jn),qw(r,o,h,g,v,S)):!1}function vw(r){return be(r)&&Ze(r)==ce}function Ru(r,o,h,g){var v=h.length,S=v,E=!g;if(r==null)return!S;for(r=ae(r);v--;){var D=h[v];if(E&&D[2]?D[1]!==r[D[0]]:!(D[0]in r))return!1}for(;++v<S;){D=h[v];var N=D[0],U=r[N],j=D[1];if(E&&D[2]){if(U===n&&!(N in r))return!1}else{var X=new Jn;if(g)var tt=g(U,j,N,r,o,X);if(!(tt===n?lo(j,U,_|x,g,X):tt))return!1}}return!0}function C0(r){if(!xe(r)||n2(r))return!1;var o=Ti(r)?vb:Ht;return o.test(vr(r))}function bw(r){return be(r)&&en(r)==kn}function ww(r){return be(r)&&Ze(r)==tn}function kw(r){return be(r)&&ic(r.length)&&!!de[en(r)]}function A0(r){return typeof r=="function"?r:r==null?yn:typeof r=="object"?kt(r)?T0(r[0],r[1]):M0(r):Yg(r)}function Iu(r){if(!fo(r))return Ab(r);var o=[];for(var h in ae(r))ie.call(r,h)&&h!="constructor"&&o.push(h);return o}function Sw(r){if(!xe(r))return o2(r);var o=fo(r),h=[];for(var g in r)g=="constructor"&&(o||!ie.call(r,g))||h.push(g);return h}function Nu(r,o){return r<o}function E0(r,o){var h=-1,g=pn(r)?z(r.length):[];return qi(r,function(v,S,E){g[++h]=o(v,S,E)}),g}function M0(r){var o=Zu(r);return o.length==1&&o[0][2]?cg(o[0][0],o[0][1]):function(h){return h===r||Ru(h,r,o)}}function T0(r,o){return Qu(r)&&ag(o)?cg(fi(r),o):function(h){var g=lh(h,r);return g===n&&g===o?uh(h,r):lo(o,g,_|x)}}function Ba(r,o,h,g,v){r!==o&&Lu(o,function(S,E){if(v||(v=new Jn),xe(S))Cw(r,o,E,h,Ba,g,v);else{var D=g?g(eh(r,E),S,E+"",r,o,v):n;D===n&&(D=S),Mu(r,E,D)}},gn)}function Cw(r,o,h,g,v,S,E){var D=eh(r,h),N=eh(o,h),U=E.get(N);if(U){Mu(r,h,U);return}var j=S?S(D,N,h+"",r,o,E):n,X=j===n;if(X){var tt=kt(N),ct=!tt&&Zi(N),gt=!tt&&!ct&&us(N);j=N,tt||ct||gt?kt(D)?j=D:ke(D)?j=dn(D):ct?(X=!1,j=B0(N,!0)):gt?(X=!1,j=G0(N,!0)):j=[]:go(N)||br(N)?(j=D,br(D)?j=Wg(D):(!xe(D)||Ti(D))&&(j=og(N))):X=!1}X&&(E.set(N,j),v(j,N,g,S,E),E.delete(N)),Mu(r,h,j)}function L0(r,o){var h=r.length;if(h)return o+=o<0?h:0,Mi(o,h)?r[o]:n}function O0(r,o,h){o.length?o=ge(o,function(S){return kt(S)?function(E){return xr(E,S.length===1?S[0]:S)}:S}):o=[yn];var g=-1;o=ge(o,Cn(pt()));var v=E0(r,function(S,E,D){var N=ge(o,function(U){return U(S)});return{criteria:N,index:++g,value:S}});return Jv(v,function(S,E){return Ww(S,E,h)})}function Aw(r,o){return D0(r,o,function(h,g){return uh(r,g)})}function D0(r,o,h){for(var g=-1,v=o.length,S={};++g<v;){var E=o[g],D=xr(r,E);h(D,E)&&uo(S,Xi(E,r),D)}return S}function Ew(r){return function(o){return xr(o,r)}}function $u(r,o,h,g){var v=g?Zv:Qr,S=-1,E=o.length,D=r;for(r===o&&(o=dn(o)),h&&(D=ge(r,Cn(h)));++S<E;)for(var N=0,U=o[S],j=h?h(U):U;(N=v(D,j,N,g))>-1;)D!==r&&Oa.call(D,N,1),Oa.call(r,N,1);return r}function P0(r,o){for(var h=r?o.length:0,g=h-1;h--;){var v=o[h];if(h==g||v!==S){var S=v;Mi(v)?Oa.call(r,v,1):Bu(r,v)}}return r}function Wu(r,o){return r+Ra(d0()*(o-r+1))}function Mw(r,o,h,g){for(var v=-1,S=De(Pa((o-r)/(h||1)),0),E=z(S);S--;)E[g?S:++v]=r,r+=h;return E}function zu(r,o){var h="";if(!r||o<1||o>Gt)return h;do o%2&&(h+=r),o=Ra(o/2),o&&(r+=r);while(o);return h}function Ot(r,o){return nh(lg(r,o,yn),r+"")}function Tw(r){return y0(hs(r))}function Lw(r,o){var h=hs(r);return Za(h,mr(o,0,h.length))}function uo(r,o,h,g){if(!xe(r))return r;o=Xi(o,r);for(var v=-1,S=o.length,E=S-1,D=r;D!=null&&++v<S;){var N=fi(o[v]),U=h;if(N==="__proto__"||N==="constructor"||N==="prototype")return r;if(v!=E){var j=D[N];U=g?g(j,N,D):n,U===n&&(U=xe(j)?j:Mi(o[v+1])?[]:{})}oo(D,N,U),D=D[N]}return r}var R0=Ia?function(r,o){return Ia.set(r,o),r}:yn,Ow=Da?function(r,o){return Da(r,"toString",{configurable:!0,enumerable:!1,value:fh(o),writable:!0})}:yn;function Dw(r){return Za(hs(r))}function Bn(r,o,h){var g=-1,v=r.length;o<0&&(o=-o>v?0:v+o),h=h>v?v:h,h<0&&(h+=v),v=o>h?0:h-o>>>0,o>>>=0;for(var S=z(v);++g<v;)S[g]=r[g+o];return S}function Pw(r,o){var h;return qi(r,function(g,v,S){return h=o(g,v,S),!h}),!!h}function Ga(r,o,h){var g=0,v=r==null?g:r.length;if(typeof o=="number"&&o===o&&v<=xt){for(;g<v;){var S=g+v>>>1,E=r[S];E!==null&&!En(E)&&(h?E<=o:E<o)?g=S+1:v=S}return v}return Fu(r,o,yn,h)}function Fu(r,o,h,g){var v=0,S=r==null?0:r.length;if(S===0)return 0;o=h(o);for(var E=o!==o,D=o===null,N=En(o),U=o===n;v<S;){var j=Ra((v+S)/2),X=h(r[j]),tt=X!==n,ct=X===null,gt=X===X,Lt=En(X);if(E)var yt=g||gt;else U?yt=gt&&(g||tt):D?yt=gt&&tt&&(g||!ct):N?yt=gt&&tt&&!ct&&(g||!Lt):ct||Lt?yt=!1:yt=g?X<=o:X<o;yt?v=j+1:S=j}return Ke(S,ot)}function I0(r,o){for(var h=-1,g=r.length,v=0,S=[];++h<g;){var E=r[h],D=o?o(E):E;if(!h||!Qn(D,N)){var N=D;S[v++]=E===0?0:E}}return S}function N0(r){return typeof r=="number"?r:En(r)?et:+r}function An(r){if(typeof r=="string")return r;if(kt(r))return ge(r,An)+"";if(En(r))return p0?p0.call(r):"";var o=r+"";return o=="0"&&1/r==-Mt?"-0":o}function Yi(r,o,h){var g=-1,v=ba,S=r.length,E=!0,D=[],N=D;if(h)E=!1,v=gu;else if(S>=s){var U=o?null:Uw(r);if(U)return ka(U);E=!1,v=to,N=new yr}else N=o?[]:D;t:for(;++g<S;){var j=r[g],X=o?o(j):j;if(j=h||j!==0?j:0,E&&X===X){for(var tt=N.length;tt--;)if(N[tt]===X)continue t;o&&N.push(X),D.push(j)}else v(N,X,h)||(N!==D&&N.push(X),D.push(j))}return D}function Bu(r,o){return o=Xi(o,r),r=ug(r,o),r==null||delete r[fi(Gn(o))]}function $0(r,o,h,g){return uo(r,o,h(xr(r,o)),g)}function Ha(r,o,h,g){for(var v=r.length,S=g?v:-1;(g?S--:++S<v)&&o(r[S],S,r););return h?Bn(r,g?0:S,g?S+1:v):Bn(r,g?S+1:0,g?v:S)}function W0(r,o){var h=r;return h instanceof Ut&&(h=h.value()),yu(o,function(g,v){return v.func.apply(v.thisArg,Ui([g],v.args))},h)}function Gu(r,o,h){var g=r.length;if(g<2)return g?Yi(r[0]):[];for(var v=-1,S=z(g);++v<g;)for(var E=r[v],D=-1;++D<g;)D!=v&&(S[v]=ao(S[v]||E,r[D],o,h));return Yi(He(S,1),o,h)}function z0(r,o,h){for(var g=-1,v=r.length,S=o.length,E={};++g<v;){var D=g<S?o[g]:n;h(E,r[g],D)}return E}function Hu(r){return ke(r)?r:[]}function Uu(r){return typeof r=="function"?r:yn}function Xi(r,o){return kt(r)?r:Qu(r,o)?[r]:pg(te(r))}var Rw=Ot;function Ki(r,o,h){var g=r.length;return h=h===n?g:h,!o&&h>=g?r:Bn(r,o,h)}var F0=bb||function(r){return Ge.clearTimeout(r)};function B0(r,o){if(o)return r.slice();var h=r.length,g=c0?c0(h):new r.constructor(h);return r.copy(g),g}function Vu(r){var o=new r.constructor(r.byteLength);return new Ta(o).set(new Ta(r)),o}function Iw(r,o){var h=o?Vu(r.buffer):r.buffer;return new r.constructor(h,r.byteOffset,r.byteLength)}function Nw(r){var o=new r.constructor(r.source,mt.exec(r));return o.lastIndex=r.lastIndex,o}function $w(r){return so?ae(so.call(r)):{}}function G0(r,o){var h=o?Vu(r.buffer):r.buffer;return new r.constructor(h,r.byteOffset,r.length)}function H0(r,o){if(r!==o){var h=r!==n,g=r===null,v=r===r,S=En(r),E=o!==n,D=o===null,N=o===o,U=En(o);if(!D&&!U&&!S&&r>o||S&&E&&N&&!D&&!U||g&&E&&N||!h&&N||!v)return 1;if(!g&&!S&&!U&&r<o||U&&h&&v&&!g&&!S||D&&h&&v||!E&&v||!N)return-1}return 0}function Ww(r,o,h){for(var g=-1,v=r.criteria,S=o.criteria,E=v.length,D=h.length;++g<E;){var N=H0(v[g],S[g]);if(N){if(g>=D)return N;var U=h[g];return N*(U=="desc"?-1:1)}}return r.index-o.index}function U0(r,o,h,g){for(var v=-1,S=r.length,E=h.length,D=-1,N=o.length,U=De(S-E,0),j=z(N+U),X=!g;++D<N;)j[D]=o[D];for(;++v<E;)(X||v<S)&&(j[h[v]]=r[v]);for(;U--;)j[D++]=r[v++];return j}function V0(r,o,h,g){for(var v=-1,S=r.length,E=-1,D=h.length,N=-1,U=o.length,j=De(S-D,0),X=z(j+U),tt=!g;++v<j;)X[v]=r[v];for(var ct=v;++N<U;)X[ct+N]=o[N];for(;++E<D;)(tt||v<S)&&(X[ct+h[E]]=r[v++]);return X}function dn(r,o){var h=-1,g=r.length;for(o||(o=z(g));++h<g;)o[h]=r[h];return o}function hi(r,o,h,g){var v=!h;h||(h={});for(var S=-1,E=o.length;++S<E;){var D=o[S],N=g?g(h[D],r[D],D,h,r):n;N===n&&(N=r[D]),v?Ci(h,D,N):oo(h,D,N)}return h}function zw(r,o){return hi(r,Ju(r),o)}function Fw(r,o){return hi(r,rg(r),o)}function Ua(r,o){return function(h,g){var v=kt(h)?Vv:cw,S=o?o():{};return v(h,r,pt(g,2),S)}}function as(r){return Ot(function(o,h){var g=-1,v=h.length,S=v>1?h[v-1]:n,E=v>2?h[2]:n;for(S=r.length>3&&typeof S=="function"?(v--,S):n,E&&nn(h[0],h[1],E)&&(S=v<3?n:S,v=1),o=ae(o);++g<v;){var D=h[g];D&&r(o,D,g,S)}return o})}function j0(r,o){return function(h,g){if(h==null)return h;if(!pn(h))return r(h,g);for(var v=h.length,S=o?v:-1,E=ae(h);(o?S--:++S<v)&&g(E[S],S,E)!==!1;);return h}}function q0(r){return function(o,h,g){for(var v=-1,S=ae(o),E=g(o),D=E.length;D--;){var N=E[r?D:++v];if(h(S[N],N,S)===!1)break}return o}}function Bw(r,o,h){var g=o&b,v=ho(r);function S(){var E=this&&this!==Ge&&this instanceof S?v:r;return E.apply(g?h:this,arguments)}return S}function Y0(r){return function(o){o=te(o);var h=ts(o)?Zn(o):n,g=h?h[0]:o.charAt(0),v=h?Ki(h,1).join(""):o.slice(1);return g[r]()+v}}function cs(r){return function(o){return yu(jg(Vg(o).replace(Ov,"")),r,"")}}function ho(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var h=os(r.prototype),g=r.apply(h,o);return xe(g)?g:h}}function Gw(r,o,h){var g=ho(r);function v(){for(var S=arguments.length,E=z(S),D=S,N=ls(v);D--;)E[D]=arguments[D];var U=S<3&&E[0]!==N&&E[S-1]!==N?[]:Vi(E,N);if(S-=U.length,S<h)return Q0(r,o,Va,v.placeholder,n,E,U,n,n,h-S);var j=this&&this!==Ge&&this instanceof v?g:r;return Sn(j,this,E)}return v}function X0(r){return function(o,h,g){var v=ae(o);if(!pn(o)){var S=pt(h,3);o=$e(o),h=function(D){return S(v[D],D,v)}}var E=r(o,h,g);return E>-1?v[S?o[E]:E]:n}}function K0(r){return Ei(function(o){var h=o.length,g=h,v=zn.prototype.thru;for(r&&o.reverse();g--;){var S=o[g];if(typeof S!="function")throw new Wn(c);if(v&&!E&&Xa(S)=="wrapper")var E=new zn([],!0)}for(g=E?g:h;++g<h;){S=o[g];var D=Xa(S),N=D=="wrapper"?Ku(S):n;N&&th(N[0])&&N[1]==(O|A|P|W)&&!N[4].length&&N[9]==1?E=E[Xa(N[0])].apply(E,N[3]):E=S.length==1&&th(S)?E[D]():E.thru(S)}return function(){var U=arguments,j=U[0];if(E&&U.length==1&&kt(j))return E.plant(j).value();for(var X=0,tt=h?o[X].apply(this,U):j;++X<h;)tt=o[X].call(this,tt);return tt}})}function Va(r,o,h,g,v,S,E,D,N,U){var j=o&O,X=o&b,tt=o&w,ct=o&(A|L),gt=o&B,Lt=tt?n:ho(r);function yt(){for(var zt=arguments.length,jt=z(zt),Mn=zt;Mn--;)jt[Mn]=arguments[Mn];if(ct)var rn=ls(yt),Tn=tb(jt,rn);if(g&&(jt=U0(jt,g,v,ct)),S&&(jt=V0(jt,S,E,ct)),zt-=Tn,ct&&zt<U){var Se=Vi(jt,rn);return Q0(r,o,Va,yt.placeholder,h,jt,Se,D,N,U-zt)}var ti=X?h:this,Oi=tt?ti[r]:r;return zt=jt.length,D?jt=c2(jt,D):gt&&zt>1&&jt.reverse(),j&&N<zt&&(jt.length=N),this&&this!==Ge&&this instanceof yt&&(Oi=Lt||ho(Oi)),Oi.apply(ti,jt)}return yt}function Z0(r,o){return function(h,g){return yw(h,r,o(g),{})}}function ja(r,o){return function(h,g){var v;if(h===n&&g===n)return o;if(h!==n&&(v=h),g!==n){if(v===n)return g;typeof h=="string"||typeof g=="string"?(h=An(h),g=An(g)):(h=N0(h),g=N0(g)),v=r(h,g)}return v}}function ju(r){return Ei(function(o){return o=ge(o,Cn(pt())),Ot(function(h){var g=this;return r(o,function(v){return Sn(v,g,h)})})})}function qa(r,o){o=o===n?" ":An(o);var h=o.length;if(h<2)return h?zu(o,r):o;var g=zu(o,Pa(r/es(o)));return ts(o)?Ki(Zn(g),0,r).join(""):g.slice(0,r)}function Hw(r,o,h,g){var v=o&b,S=ho(r);function E(){for(var D=-1,N=arguments.length,U=-1,j=g.length,X=z(j+N),tt=this&&this!==Ge&&this instanceof E?S:r;++U<j;)X[U]=g[U];for(;N--;)X[U++]=arguments[++D];return Sn(tt,v?h:this,X)}return E}function J0(r){return function(o,h,g){return g&&typeof g!="number"&&nn(o,h,g)&&(h=g=n),o=Li(o),h===n?(h=o,o=0):h=Li(h),g=g===n?o<h?1:-1:Li(g),Mw(o,h,g,r)}}function Ya(r){return function(o,h){return typeof o=="string"&&typeof h=="string"||(o=Hn(o),h=Hn(h)),r(o,h)}}function Q0(r,o,h,g,v,S,E,D,N,U){var j=o&A,X=j?E:n,tt=j?n:E,ct=j?S:n,gt=j?n:S;o|=j?P:R,o&=~(j?R:P),o&C||(o&=~(b|w));var Lt=[r,o,v,ct,X,gt,tt,D,N,U],yt=h.apply(n,Lt);return th(r)&&hg(yt,Lt),yt.placeholder=g,fg(yt,r,o)}function qu(r){var o=Oe[r];return function(h,g){if(h=Hn(h),g=g==null?0:Ke(Et(g),292),g&&f0(h)){var v=(te(h)+"e").split("e"),S=o(v[0]+"e"+(+v[1]+g));return v=(te(S)+"e").split("e"),+(v[0]+"e"+(+v[1]-g))}return o(h)}}var Uw=rs&&1/ka(new rs([,-0]))[1]==Mt?function(r){return new rs(r)}:gh;function tg(r){return function(o){var h=Ze(o);return h==ce?ku(o):h==tn?ab(o):Qv(o,r(o))}}function Ai(r,o,h,g,v,S,E,D){var N=o&w;if(!N&&typeof r!="function")throw new Wn(c);var U=g?g.length:0;if(U||(o&=~(P|R),g=v=n),E=E===n?E:De(Et(E),0),D=D===n?D:Et(D),U-=v?v.length:0,o&R){var j=g,X=v;g=v=n}var tt=N?n:Ku(r),ct=[r,o,h,g,v,j,X,S,E,D];if(tt&&s2(ct,tt),r=ct[0],o=ct[1],h=ct[2],g=ct[3],v=ct[4],D=ct[9]=ct[9]===n?N?0:r.length:De(ct[9]-U,0),!D&&o&(A|L)&&(o&=~(A|L)),!o||o==b)var gt=Bw(r,o,h);else o==A||o==L?gt=Gw(r,o,D):(o==P||o==(b|P))&&!v.length?gt=Hw(r,o,h,g):gt=Va.apply(n,ct);var Lt=tt?R0:hg;return fg(Lt(gt,ct),r,o)}function eg(r,o,h,g){return r===n||Qn(r,is[h])&&!ie.call(g,h)?o:r}function ng(r,o,h,g,v,S){return xe(r)&&xe(o)&&(S.set(o,r),Ba(r,o,n,ng,S),S.delete(o)),r}function Vw(r){return go(r)?n:r}function ig(r,o,h,g,v,S){var E=h&_,D=r.length,N=o.length;if(D!=N&&!(E&&N>D))return!1;var U=S.get(r),j=S.get(o);if(U&&j)return U==o&&j==r;var X=-1,tt=!0,ct=h&x?new yr:n;for(S.set(r,o),S.set(o,r);++X<D;){var gt=r[X],Lt=o[X];if(g)var yt=E?g(Lt,gt,X,o,r,S):g(gt,Lt,X,r,o,S);if(yt!==n){if(yt)continue;tt=!1;break}if(ct){if(!mu(o,function(zt,jt){if(!to(ct,jt)&&(gt===zt||v(gt,zt,h,g,S)))return ct.push(jt)})){tt=!1;break}}else if(!(gt===Lt||v(gt,Lt,h,g,S))){tt=!1;break}}return S.delete(r),S.delete(o),tt}function jw(r,o,h,g,v,S,E){switch(h){case In:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Gi:return!(r.byteLength!=o.byteLength||!S(new Ta(r),new Ta(o)));case oe:case se:case wn:return Qn(+r,+o);case Ee:return r.name==o.name&&r.message==o.message;case kn:case vi:return r==o+"";case ce:var D=ku;case tn:var N=g&_;if(D||(D=ka),r.size!=o.size&&!N)return!1;var U=E.get(r);if(U)return U==o;g|=x,E.set(r,o);var j=ig(D(r),D(o),g,v,S,E);return E.delete(r),j;case Bi:if(so)return so.call(r)==so.call(o)}return!1}function qw(r,o,h,g,v,S){var E=h&_,D=Yu(r),N=D.length,U=Yu(o),j=U.length;if(N!=j&&!E)return!1;for(var X=N;X--;){var tt=D[X];if(!(E?tt in o:ie.call(o,tt)))return!1}var ct=S.get(r),gt=S.get(o);if(ct&&gt)return ct==o&&gt==r;var Lt=!0;S.set(r,o),S.set(o,r);for(var yt=E;++X<N;){tt=D[X];var zt=r[tt],jt=o[tt];if(g)var Mn=E?g(jt,zt,tt,o,r,S):g(zt,jt,tt,r,o,S);if(!(Mn===n?zt===jt||v(zt,jt,h,g,S):Mn)){Lt=!1;break}yt||(yt=tt=="constructor")}if(Lt&&!yt){var rn=r.constructor,Tn=o.constructor;rn!=Tn&&"constructor"in r&&"constructor"in o&&!(typeof rn=="function"&&rn instanceof rn&&typeof Tn=="function"&&Tn instanceof Tn)&&(Lt=!1)}return S.delete(r),S.delete(o),Lt}function Ei(r){return nh(lg(r,n,xg),r+"")}function Yu(r){return k0(r,$e,Ju)}function Xu(r){return k0(r,gn,rg)}var Ku=Ia?function(r){return Ia.get(r)}:gh;function Xa(r){for(var o=r.name+"",h=ss[o],g=ie.call(ss,o)?h.length:0;g--;){var v=h[g],S=v.func;if(S==null||S==r)return v.name}return o}function ls(r){var o=ie.call(k,"placeholder")?k:r;return o.placeholder}function pt(){var r=k.iteratee||dh;return r=r===dh?A0:r,arguments.length?r(arguments[0],arguments[1]):r}function Ka(r,o){var h=r.__data__;return e2(o)?h[typeof o=="string"?"string":"hash"]:h.map}function Zu(r){for(var o=$e(r),h=o.length;h--;){var g=o[h],v=r[g];o[h]=[g,v,ag(v)]}return o}function _r(r,o){var h=rb(r,o);return C0(h)?h:n}function Yw(r){var o=ie.call(r,pr),h=r[pr];try{r[pr]=n;var g=!0}catch{}var v=Ea.call(r);return g&&(o?r[pr]=h:delete r[pr]),v}var Ju=Cu?function(r){return r==null?[]:(r=ae(r),Hi(Cu(r),function(o){return u0.call(r,o)}))}:yh,rg=Cu?function(r){for(var o=[];r;)Ui(o,Ju(r)),r=La(r);return o}:yh,Ze=en;(Au&&Ze(new Au(new ArrayBuffer(1)))!=In||no&&Ze(new no)!=ce||Eu&&Ze(Eu.resolve())!=li||rs&&Ze(new rs)!=tn||io&&Ze(new io)!=hr)&&(Ze=function(r){var o=en(r),h=o==Me?r.constructor:n,g=h?vr(h):"";if(g)switch(g){case Lb:return In;case Ob:return ce;case Db:return li;case Pb:return tn;case Rb:return hr}return o});function Xw(r,o,h){for(var g=-1,v=h.length;++g<v;){var S=h[g],E=S.size;switch(S.type){case"drop":r+=E;break;case"dropRight":o-=E;break;case"take":o=Ke(o,r+E);break;case"takeRight":r=De(r,o-E);break}}return{start:r,end:o}}function Kw(r){var o=r.match(su);return o?o[1].split(ou):[]}function sg(r,o,h){o=Xi(o,r);for(var g=-1,v=o.length,S=!1;++g<v;){var E=fi(o[g]);if(!(S=r!=null&&h(r,E)))break;r=r[E]}return S||++g!=v?S:(v=r==null?0:r.length,!!v&&ic(v)&&Mi(E,v)&&(kt(r)||br(r)))}function Zw(r){var o=r.length,h=new r.constructor(o);return o&&typeof r[0]=="string"&&ie.call(r,"index")&&(h.index=r.index,h.input=r.input),h}function og(r){return typeof r.constructor=="function"&&!fo(r)?os(La(r)):{}}function Jw(r,o,h){var g=r.constructor;switch(o){case Gi:return Vu(r);case oe:case se:return new g(+r);case In:return Iw(r,h);case Bs:case Xr:case Kr:case fr:case Gs:case Hs:case Us:case Vs:case js:return G0(r,h);case ce:return new g;case wn:case vi:return new g(r);case kn:return Nw(r);case tn:return new g;case Bi:return $w(r)}}function Qw(r,o){var h=o.length;if(!h)return r;var g=h-1;return o[g]=(h>1?"& ":"")+o[g],o=o.join(h>2?", ":" "),r.replace(ru,`{
/* [wrapped with `+o+`] */
`)}function t2(r){return kt(r)||br(r)||!!(h0&&r&&r[h0])}function Mi(r,o){var h=typeof r;return o=o??Gt,!!o&&(h=="number"||h!="symbol"&&Be.test(r))&&r>-1&&r%1==0&&r<o}function nn(r,o,h){if(!xe(h))return!1;var g=typeof o;return(g=="number"?pn(h)&&Mi(o,h.length):g=="string"&&o in h)?Qn(h[o],r):!1}function Qu(r,o){if(kt(r))return!1;var h=typeof r;return h=="number"||h=="symbol"||h=="boolean"||r==null||En(r)?!0:ga.test(r)||!nu.test(r)||o!=null&&r in ae(o)}function e2(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function th(r){var o=Xa(r),h=k[o];if(typeof h!="function"||!(o in Ut.prototype))return!1;if(r===h)return!0;var g=Ku(h);return!!g&&r===g[0]}function n2(r){return!!a0&&a0 in r}var i2=Ca?Ti:mh;function fo(r){var o=r&&r.constructor,h=typeof o=="function"&&o.prototype||is;return r===h}function ag(r){return r===r&&!xe(r)}function cg(r,o){return function(h){return h==null?!1:h[r]===o&&(o!==n||r in ae(h))}}function r2(r){var o=ec(r,function(g){return h.size===f&&h.clear(),g}),h=o.cache;return o}function s2(r,o){var h=r[1],g=o[1],v=h|g,S=v<(b|w|O),E=g==O&&h==A||g==O&&h==W&&r[7].length<=o[8]||g==(O|W)&&o[7].length<=o[8]&&h==A;if(!(S||E))return r;g&b&&(r[2]=o[2],v|=h&b?0:C);var D=o[3];if(D){var N=r[3];r[3]=N?U0(N,D,o[4]):D,r[4]=N?Vi(r[3],d):o[4]}return D=o[5],D&&(N=r[5],r[5]=N?V0(N,D,o[6]):D,r[6]=N?Vi(r[5],d):o[6]),D=o[7],D&&(r[7]=D),g&O&&(r[8]=r[8]==null?o[8]:Ke(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=v,r}function o2(r){var o=[];if(r!=null)for(var h in ae(r))o.push(h);return o}function a2(r){return Ea.call(r)}function lg(r,o,h){return o=De(o===n?r.length-1:o,0),function(){for(var g=arguments,v=-1,S=De(g.length-o,0),E=z(S);++v<S;)E[v]=g[o+v];v=-1;for(var D=z(o+1);++v<o;)D[v]=g[v];return D[o]=h(E),Sn(r,this,D)}}function ug(r,o){return o.length<2?r:xr(r,Bn(o,0,-1))}function c2(r,o){for(var h=r.length,g=Ke(o.length,h),v=dn(r);g--;){var S=o[g];r[g]=Mi(S,h)?v[S]:n}return r}function eh(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var hg=dg(R0),po=kb||function(r,o){return Ge.setTimeout(r,o)},nh=dg(Ow);function fg(r,o,h){var g=o+"";return nh(r,Qw(g,l2(Kw(g),h)))}function dg(r){var o=0,h=0;return function(){var g=Eb(),v=at-(g-h);if(h=g,v>0){if(++o>=V)return arguments[0]}else o=0;return r.apply(n,arguments)}}function Za(r,o){var h=-1,g=r.length,v=g-1;for(o=o===n?g:o;++h<o;){var S=Wu(h,v),E=r[S];r[S]=r[h],r[h]=E}return r.length=o,r}var pg=r2(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(ya,function(h,g,v,S){o.push(v?S.replace(K,"$1"):g||h)}),o});function fi(r){if(typeof r=="string"||En(r))return r;var o=r+"";return o=="0"&&1/r==-Mt?"-0":o}function vr(r){if(r!=null){try{return Aa.call(r)}catch{}try{return r+""}catch{}}return""}function l2(r,o){return $n(Vt,function(h){var g="_."+h[0];o&h[1]&&!ba(r,g)&&r.push(g)}),r.sort()}function gg(r){if(r instanceof Ut)return r.clone();var o=new zn(r.__wrapped__,r.__chain__);return o.__actions__=dn(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function u2(r,o,h){(h?nn(r,o,h):o===n)?o=1:o=De(Et(o),0);var g=r==null?0:r.length;if(!g||o<1)return[];for(var v=0,S=0,E=z(Pa(g/o));v<g;)E[S++]=Bn(r,v,v+=o);return E}function h2(r){for(var o=-1,h=r==null?0:r.length,g=0,v=[];++o<h;){var S=r[o];S&&(v[g++]=S)}return v}function f2(){var r=arguments.length;if(!r)return[];for(var o=z(r-1),h=arguments[0],g=r;g--;)o[g-1]=arguments[g];return Ui(kt(h)?dn(h):[h],He(o,1))}var d2=Ot(function(r,o){return ke(r)?ao(r,He(o,1,ke,!0)):[]}),p2=Ot(function(r,o){var h=Gn(o);return ke(h)&&(h=n),ke(r)?ao(r,He(o,1,ke,!0),pt(h,2)):[]}),g2=Ot(function(r,o){var h=Gn(o);return ke(h)&&(h=n),ke(r)?ao(r,He(o,1,ke,!0),n,h):[]});function y2(r,o,h){var g=r==null?0:r.length;return g?(o=h||o===n?1:Et(o),Bn(r,o<0?0:o,g)):[]}function m2(r,o,h){var g=r==null?0:r.length;return g?(o=h||o===n?1:Et(o),o=g-o,Bn(r,0,o<0?0:o)):[]}function x2(r,o){return r&&r.length?Ha(r,pt(o,3),!0,!0):[]}function _2(r,o){return r&&r.length?Ha(r,pt(o,3),!0):[]}function v2(r,o,h,g){var v=r==null?0:r.length;return v?(h&&typeof h!="number"&&nn(r,o,h)&&(h=0,g=v),fw(r,o,h,g)):[]}function yg(r,o,h){var g=r==null?0:r.length;if(!g)return-1;var v=h==null?0:Et(h);return v<0&&(v=De(g+v,0)),wa(r,pt(o,3),v)}function mg(r,o,h){var g=r==null?0:r.length;if(!g)return-1;var v=g-1;return h!==n&&(v=Et(h),v=h<0?De(g+v,0):Ke(v,g-1)),wa(r,pt(o,3),v,!0)}function xg(r){var o=r==null?0:r.length;return o?He(r,1):[]}function b2(r){var o=r==null?0:r.length;return o?He(r,Mt):[]}function w2(r,o){var h=r==null?0:r.length;return h?(o=o===n?1:Et(o),He(r,o)):[]}function k2(r){for(var o=-1,h=r==null?0:r.length,g={};++o<h;){var v=r[o];g[v[0]]=v[1]}return g}function _g(r){return r&&r.length?r[0]:n}function S2(r,o,h){var g=r==null?0:r.length;if(!g)return-1;var v=h==null?0:Et(h);return v<0&&(v=De(g+v,0)),Qr(r,o,v)}function C2(r){var o=r==null?0:r.length;return o?Bn(r,0,-1):[]}var A2=Ot(function(r){var o=ge(r,Hu);return o.length&&o[0]===r[0]?Pu(o):[]}),E2=Ot(function(r){var o=Gn(r),h=ge(r,Hu);return o===Gn(h)?o=n:h.pop(),h.length&&h[0]===r[0]?Pu(h,pt(o,2)):[]}),M2=Ot(function(r){var o=Gn(r),h=ge(r,Hu);return o=typeof o=="function"?o:n,o&&h.pop(),h.length&&h[0]===r[0]?Pu(h,n,o):[]});function T2(r,o){return r==null?"":Cb.call(r,o)}function Gn(r){var o=r==null?0:r.length;return o?r[o-1]:n}function L2(r,o,h){var g=r==null?0:r.length;if(!g)return-1;var v=g;return h!==n&&(v=Et(h),v=v<0?De(g+v,0):Ke(v,g-1)),o===o?lb(r,o,v):wa(r,Qp,v,!0)}function O2(r,o){return r&&r.length?L0(r,Et(o)):n}var D2=Ot(vg);function vg(r,o){return r&&r.length&&o&&o.length?$u(r,o):r}function P2(r,o,h){return r&&r.length&&o&&o.length?$u(r,o,pt(h,2)):r}function R2(r,o,h){return r&&r.length&&o&&o.length?$u(r,o,n,h):r}var I2=Ei(function(r,o){var h=r==null?0:r.length,g=Tu(r,o);return P0(r,ge(o,function(v){return Mi(v,h)?+v:v}).sort(H0)),g});function N2(r,o){var h=[];if(!(r&&r.length))return h;var g=-1,v=[],S=r.length;for(o=pt(o,3);++g<S;){var E=r[g];o(E,g,r)&&(h.push(E),v.push(g))}return P0(r,v),h}function ih(r){return r==null?r:Tb.call(r)}function $2(r,o,h){var g=r==null?0:r.length;return g?(h&&typeof h!="number"&&nn(r,o,h)?(o=0,h=g):(o=o==null?0:Et(o),h=h===n?g:Et(h)),Bn(r,o,h)):[]}function W2(r,o){return Ga(r,o)}function z2(r,o,h){return Fu(r,o,pt(h,2))}function F2(r,o){var h=r==null?0:r.length;if(h){var g=Ga(r,o);if(g<h&&Qn(r[g],o))return g}return-1}function B2(r,o){return Ga(r,o,!0)}function G2(r,o,h){return Fu(r,o,pt(h,2),!0)}function H2(r,o){var h=r==null?0:r.length;if(h){var g=Ga(r,o,!0)-1;if(Qn(r[g],o))return g}return-1}function U2(r){return r&&r.length?I0(r):[]}function V2(r,o){return r&&r.length?I0(r,pt(o,2)):[]}function j2(r){var o=r==null?0:r.length;return o?Bn(r,1,o):[]}function q2(r,o,h){return r&&r.length?(o=h||o===n?1:Et(o),Bn(r,0,o<0?0:o)):[]}function Y2(r,o,h){var g=r==null?0:r.length;return g?(o=h||o===n?1:Et(o),o=g-o,Bn(r,o<0?0:o,g)):[]}function X2(r,o){return r&&r.length?Ha(r,pt(o,3),!1,!0):[]}function K2(r,o){return r&&r.length?Ha(r,pt(o,3)):[]}var Z2=Ot(function(r){return Yi(He(r,1,ke,!0))}),J2=Ot(function(r){var o=Gn(r);return ke(o)&&(o=n),Yi(He(r,1,ke,!0),pt(o,2))}),Q2=Ot(function(r){var o=Gn(r);return o=typeof o=="function"?o:n,Yi(He(r,1,ke,!0),n,o)});function tk(r){return r&&r.length?Yi(r):[]}function ek(r,o){return r&&r.length?Yi(r,pt(o,2)):[]}function nk(r,o){return o=typeof o=="function"?o:n,r&&r.length?Yi(r,n,o):[]}function rh(r){if(!(r&&r.length))return[];var o=0;return r=Hi(r,function(h){if(ke(h))return o=De(h.length,o),!0}),bu(o,function(h){return ge(r,xu(h))})}function bg(r,o){if(!(r&&r.length))return[];var h=rh(r);return o==null?h:ge(h,function(g){return Sn(o,n,g)})}var ik=Ot(function(r,o){return ke(r)?ao(r,o):[]}),rk=Ot(function(r){return Gu(Hi(r,ke))}),sk=Ot(function(r){var o=Gn(r);return ke(o)&&(o=n),Gu(Hi(r,ke),pt(o,2))}),ok=Ot(function(r){var o=Gn(r);return o=typeof o=="function"?o:n,Gu(Hi(r,ke),n,o)}),ak=Ot(rh);function ck(r,o){return z0(r||[],o||[],oo)}function lk(r,o){return z0(r||[],o||[],uo)}var uk=Ot(function(r){var o=r.length,h=o>1?r[o-1]:n;return h=typeof h=="function"?(r.pop(),h):n,bg(r,h)});function wg(r){var o=k(r);return o.__chain__=!0,o}function hk(r,o){return o(r),r}function Ja(r,o){return o(r)}var fk=Ei(function(r){var o=r.length,h=o?r[0]:0,g=this.__wrapped__,v=function(S){return Tu(S,r)};return o>1||this.__actions__.length||!(g instanceof Ut)||!Mi(h)?this.thru(v):(g=g.slice(h,+h+(o?1:0)),g.__actions__.push({func:Ja,args:[v],thisArg:n}),new zn(g,this.__chain__).thru(function(S){return o&&!S.length&&S.push(n),S}))});function dk(){return wg(this)}function pk(){return new zn(this.value(),this.__chain__)}function gk(){this.__values__===n&&(this.__values__=Ng(this.value()));var r=this.__index__>=this.__values__.length,o=r?n:this.__values__[this.__index__++];return{done:r,value:o}}function yk(){return this}function mk(r){for(var o,h=this;h instanceof $a;){var g=gg(h);g.__index__=0,g.__values__=n,o?v.__wrapped__=g:o=g;var v=g;h=h.__wrapped__}return v.__wrapped__=r,o}function xk(){var r=this.__wrapped__;if(r instanceof Ut){var o=r;return this.__actions__.length&&(o=new Ut(this)),o=o.reverse(),o.__actions__.push({func:Ja,args:[ih],thisArg:n}),new zn(o,this.__chain__)}return this.thru(ih)}function _k(){return W0(this.__wrapped__,this.__actions__)}var vk=Ua(function(r,o,h){ie.call(r,h)?++r[h]:Ci(r,h,1)});function bk(r,o,h){var g=kt(r)?Zp:hw;return h&&nn(r,o,h)&&(o=n),g(r,pt(o,3))}function wk(r,o){var h=kt(r)?Hi:b0;return h(r,pt(o,3))}var kk=X0(yg),Sk=X0(mg);function Ck(r,o){return He(Qa(r,o),1)}function Ak(r,o){return He(Qa(r,o),Mt)}function Ek(r,o,h){return h=h===n?1:Et(h),He(Qa(r,o),h)}function kg(r,o){var h=kt(r)?$n:qi;return h(r,pt(o,3))}function Sg(r,o){var h=kt(r)?jv:v0;return h(r,pt(o,3))}var Mk=Ua(function(r,o,h){ie.call(r,h)?r[h].push(o):Ci(r,h,[o])});function Tk(r,o,h,g){r=pn(r)?r:hs(r),h=h&&!g?Et(h):0;var v=r.length;return h<0&&(h=De(v+h,0)),rc(r)?h<=v&&r.indexOf(o,h)>-1:!!v&&Qr(r,o,h)>-1}var Lk=Ot(function(r,o,h){var g=-1,v=typeof o=="function",S=pn(r)?z(r.length):[];return qi(r,function(E){S[++g]=v?Sn(o,E,h):co(E,o,h)}),S}),Ok=Ua(function(r,o,h){Ci(r,h,o)});function Qa(r,o){var h=kt(r)?ge:E0;return h(r,pt(o,3))}function Dk(r,o,h,g){return r==null?[]:(kt(o)||(o=o==null?[]:[o]),h=g?n:h,kt(h)||(h=h==null?[]:[h]),O0(r,o,h))}var Pk=Ua(function(r,o,h){r[h?0:1].push(o)},function(){return[[],[]]});function Rk(r,o,h){var g=kt(r)?yu:e0,v=arguments.length<3;return g(r,pt(o,4),h,v,qi)}function Ik(r,o,h){var g=kt(r)?qv:e0,v=arguments.length<3;return g(r,pt(o,4),h,v,v0)}function Nk(r,o){var h=kt(r)?Hi:b0;return h(r,nc(pt(o,3)))}function $k(r){var o=kt(r)?y0:Tw;return o(r)}function Wk(r,o,h){(h?nn(r,o,h):o===n)?o=1:o=Et(o);var g=kt(r)?ow:Lw;return g(r,o)}function zk(r){var o=kt(r)?aw:Dw;return o(r)}function Fk(r){if(r==null)return 0;if(pn(r))return rc(r)?es(r):r.length;var o=Ze(r);return o==ce||o==tn?r.size:Iu(r).length}function Bk(r,o,h){var g=kt(r)?mu:Pw;return h&&nn(r,o,h)&&(o=n),g(r,pt(o,3))}var Gk=Ot(function(r,o){if(r==null)return[];var h=o.length;return h>1&&nn(r,o[0],o[1])?o=[]:h>2&&nn(o[0],o[1],o[2])&&(o=[o[0]]),O0(r,He(o,1),[])}),tc=wb||function(){return Ge.Date.now()};function Hk(r,o){if(typeof o!="function")throw new Wn(c);return r=Et(r),function(){if(--r<1)return o.apply(this,arguments)}}function Cg(r,o,h){return o=h?n:o,o=r&&o==null?r.length:o,Ai(r,O,n,n,n,n,o)}function Ag(r,o){var h;if(typeof o!="function")throw new Wn(c);return r=Et(r),function(){return--r>0&&(h=o.apply(this,arguments)),r<=1&&(o=n),h}}var sh=Ot(function(r,o,h){var g=b;if(h.length){var v=Vi(h,ls(sh));g|=P}return Ai(r,g,o,h,v)}),Eg=Ot(function(r,o,h){var g=b|w;if(h.length){var v=Vi(h,ls(Eg));g|=P}return Ai(o,g,r,h,v)});function Mg(r,o,h){o=h?n:o;var g=Ai(r,A,n,n,n,n,n,o);return g.placeholder=Mg.placeholder,g}function Tg(r,o,h){o=h?n:o;var g=Ai(r,L,n,n,n,n,n,o);return g.placeholder=Tg.placeholder,g}function Lg(r,o,h){var g,v,S,E,D,N,U=0,j=!1,X=!1,tt=!0;if(typeof r!="function")throw new Wn(c);o=Hn(o)||0,xe(h)&&(j=!!h.leading,X="maxWait"in h,S=X?De(Hn(h.maxWait)||0,o):S,tt="trailing"in h?!!h.trailing:tt);function ct(Se){var ti=g,Oi=v;return g=v=n,U=Se,E=r.apply(Oi,ti),E}function gt(Se){return U=Se,D=po(zt,o),j?ct(Se):E}function Lt(Se){var ti=Se-N,Oi=Se-U,Xg=o-ti;return X?Ke(Xg,S-Oi):Xg}function yt(Se){var ti=Se-N,Oi=Se-U;return N===n||ti>=o||ti<0||X&&Oi>=S}function zt(){var Se=tc();if(yt(Se))return jt(Se);D=po(zt,Lt(Se))}function jt(Se){return D=n,tt&&g?ct(Se):(g=v=n,E)}function Mn(){D!==n&&F0(D),U=0,g=N=v=D=n}function rn(){return D===n?E:jt(tc())}function Tn(){var Se=tc(),ti=yt(Se);if(g=arguments,v=this,N=Se,ti){if(D===n)return gt(N);if(X)return F0(D),D=po(zt,o),ct(N)}return D===n&&(D=po(zt,o)),E}return Tn.cancel=Mn,Tn.flush=rn,Tn}var Uk=Ot(function(r,o){return _0(r,1,o)}),Vk=Ot(function(r,o,h){return _0(r,Hn(o)||0,h)});function jk(r){return Ai(r,B)}function ec(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new Wn(c);var h=function(){var g=arguments,v=o?o.apply(this,g):g[0],S=h.cache;if(S.has(v))return S.get(v);var E=r.apply(this,g);return h.cache=S.set(v,E)||S,E};return h.cache=new(ec.Cache||Si),h}ec.Cache=Si;function nc(r){if(typeof r!="function")throw new Wn(c);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function qk(r){return Ag(2,r)}var Yk=Rw(function(r,o){o=o.length==1&&kt(o[0])?ge(o[0],Cn(pt())):ge(He(o,1),Cn(pt()));var h=o.length;return Ot(function(g){for(var v=-1,S=Ke(g.length,h);++v<S;)g[v]=o[v].call(this,g[v]);return Sn(r,this,g)})}),oh=Ot(function(r,o){var h=Vi(o,ls(oh));return Ai(r,P,n,o,h)}),Og=Ot(function(r,o){var h=Vi(o,ls(Og));return Ai(r,R,n,o,h)}),Xk=Ei(function(r,o){return Ai(r,W,n,n,n,o)});function Kk(r,o){if(typeof r!="function")throw new Wn(c);return o=o===n?o:Et(o),Ot(r,o)}function Zk(r,o){if(typeof r!="function")throw new Wn(c);return o=o==null?0:De(Et(o),0),Ot(function(h){var g=h[o],v=Ki(h,0,o);return g&&Ui(v,g),Sn(r,this,v)})}function Jk(r,o,h){var g=!0,v=!0;if(typeof r!="function")throw new Wn(c);return xe(h)&&(g="leading"in h?!!h.leading:g,v="trailing"in h?!!h.trailing:v),Lg(r,o,{leading:g,maxWait:o,trailing:v})}function Qk(r){return Cg(r,1)}function tS(r,o){return oh(Uu(o),r)}function eS(){if(!arguments.length)return[];var r=arguments[0];return kt(r)?r:[r]}function nS(r){return Fn(r,m)}function iS(r,o){return o=typeof o=="function"?o:n,Fn(r,m,o)}function rS(r){return Fn(r,p|m)}function sS(r,o){return o=typeof o=="function"?o:n,Fn(r,p|m,o)}function oS(r,o){return o==null||x0(r,o,$e(o))}function Qn(r,o){return r===o||r!==r&&o!==o}var aS=Ya(Du),cS=Ya(function(r,o){return r>=o}),br=S0(function(){return arguments}())?S0:function(r){return be(r)&&ie.call(r,"callee")&&!u0.call(r,"callee")},kt=z.isArray,lS=Vp?Cn(Vp):mw;function pn(r){return r!=null&&ic(r.length)&&!Ti(r)}function ke(r){return be(r)&&pn(r)}function uS(r){return r===!0||r===!1||be(r)&&en(r)==oe}var Zi=Sb||mh,hS=jp?Cn(jp):xw;function fS(r){return be(r)&&r.nodeType===1&&!go(r)}function dS(r){if(r==null)return!0;if(pn(r)&&(kt(r)||typeof r=="string"||typeof r.splice=="function"||Zi(r)||us(r)||br(r)))return!r.length;var o=Ze(r);if(o==ce||o==tn)return!r.size;if(fo(r))return!Iu(r).length;for(var h in r)if(ie.call(r,h))return!1;return!0}function pS(r,o){return lo(r,o)}function gS(r,o,h){h=typeof h=="function"?h:n;var g=h?h(r,o):n;return g===n?lo(r,o,n,h):!!g}function ah(r){if(!be(r))return!1;var o=en(r);return o==Ee||o==Le||typeof r.message=="string"&&typeof r.name=="string"&&!go(r)}function yS(r){return typeof r=="number"&&f0(r)}function Ti(r){if(!xe(r))return!1;var o=en(r);return o==ln||o==un||o==Xt||o==Yr}function Dg(r){return typeof r=="number"&&r==Et(r)}function ic(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Gt}function xe(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function be(r){return r!=null&&typeof r=="object"}var Pg=qp?Cn(qp):vw;function mS(r,o){return r===o||Ru(r,o,Zu(o))}function xS(r,o,h){return h=typeof h=="function"?h:n,Ru(r,o,Zu(o),h)}function _S(r){return Rg(r)&&r!=+r}function vS(r){if(i2(r))throw new bt(a);return C0(r)}function bS(r){return r===null}function wS(r){return r==null}function Rg(r){return typeof r=="number"||be(r)&&en(r)==wn}function go(r){if(!be(r)||en(r)!=Me)return!1;var o=La(r);if(o===null)return!0;var h=ie.call(o,"constructor")&&o.constructor;return typeof h=="function"&&h instanceof h&&Aa.call(h)==xb}var ch=Yp?Cn(Yp):bw;function kS(r){return Dg(r)&&r>=-Gt&&r<=Gt}var Ig=Xp?Cn(Xp):ww;function rc(r){return typeof r=="string"||!kt(r)&&be(r)&&en(r)==vi}function En(r){return typeof r=="symbol"||be(r)&&en(r)==Bi}var us=Kp?Cn(Kp):kw;function SS(r){return r===n}function CS(r){return be(r)&&Ze(r)==hr}function AS(r){return be(r)&&en(r)==da}var ES=Ya(Nu),MS=Ya(function(r,o){return r<=o});function Ng(r){if(!r)return[];if(pn(r))return rc(r)?Zn(r):dn(r);if(eo&&r[eo])return ob(r[eo]());var o=Ze(r),h=o==ce?ku:o==tn?ka:hs;return h(r)}function Li(r){if(!r)return r===0?r:0;if(r=Hn(r),r===Mt||r===-Mt){var o=r<0?-1:1;return o*Qt}return r===r?r:0}function Et(r){var o=Li(r),h=o%1;return o===o?h?o-h:o:0}function $g(r){return r?mr(Et(r),0,nt):0}function Hn(r){if(typeof r=="number")return r;if(En(r))return et;if(xe(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=xe(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=n0(r);var h=ne.test(r);return h||It.test(r)?Hv(r.slice(2),h?2:8):Kt.test(r)?et:+r}function Wg(r){return hi(r,gn(r))}function TS(r){return r?mr(Et(r),-Gt,Gt):r===0?r:0}function te(r){return r==null?"":An(r)}var LS=as(function(r,o){if(fo(o)||pn(o)){hi(o,$e(o),r);return}for(var h in o)ie.call(o,h)&&oo(r,h,o[h])}),zg=as(function(r,o){hi(o,gn(o),r)}),sc=as(function(r,o,h,g){hi(o,gn(o),r,g)}),OS=as(function(r,o,h,g){hi(o,$e(o),r,g)}),DS=Ei(Tu);function PS(r,o){var h=os(r);return o==null?h:m0(h,o)}var RS=Ot(function(r,o){r=ae(r);var h=-1,g=o.length,v=g>2?o[2]:n;for(v&&nn(o[0],o[1],v)&&(g=1);++h<g;)for(var S=o[h],E=gn(S),D=-1,N=E.length;++D<N;){var U=E[D],j=r[U];(j===n||Qn(j,is[U])&&!ie.call(r,U))&&(r[U]=S[U])}return r}),IS=Ot(function(r){return r.push(n,ng),Sn(Fg,n,r)});function NS(r,o){return Jp(r,pt(o,3),ui)}function $S(r,o){return Jp(r,pt(o,3),Ou)}function WS(r,o){return r==null?r:Lu(r,pt(o,3),gn)}function zS(r,o){return r==null?r:w0(r,pt(o,3),gn)}function FS(r,o){return r&&ui(r,pt(o,3))}function BS(r,o){return r&&Ou(r,pt(o,3))}function GS(r){return r==null?[]:Fa(r,$e(r))}function HS(r){return r==null?[]:Fa(r,gn(r))}function lh(r,o,h){var g=r==null?n:xr(r,o);return g===n?h:g}function US(r,o){return r!=null&&sg(r,o,dw)}function uh(r,o){return r!=null&&sg(r,o,pw)}var VS=Z0(function(r,o,h){o!=null&&typeof o.toString!="function"&&(o=Ea.call(o)),r[o]=h},fh(yn)),jS=Z0(function(r,o,h){o!=null&&typeof o.toString!="function"&&(o=Ea.call(o)),ie.call(r,o)?r[o].push(h):r[o]=[h]},pt),qS=Ot(co);function $e(r){return pn(r)?g0(r):Iu(r)}function gn(r){return pn(r)?g0(r,!0):Sw(r)}function YS(r,o){var h={};return o=pt(o,3),ui(r,function(g,v,S){Ci(h,o(g,v,S),g)}),h}function XS(r,o){var h={};return o=pt(o,3),ui(r,function(g,v,S){Ci(h,v,o(g,v,S))}),h}var KS=as(function(r,o,h){Ba(r,o,h)}),Fg=as(function(r,o,h,g){Ba(r,o,h,g)}),ZS=Ei(function(r,o){var h={};if(r==null)return h;var g=!1;o=ge(o,function(S){return S=Xi(S,r),g||(g=S.length>1),S}),hi(r,Xu(r),h),g&&(h=Fn(h,p|y|m,Vw));for(var v=o.length;v--;)Bu(h,o[v]);return h});function JS(r,o){return Bg(r,nc(pt(o)))}var QS=Ei(function(r,o){return r==null?{}:Aw(r,o)});function Bg(r,o){if(r==null)return{};var h=ge(Xu(r),function(g){return[g]});return o=pt(o),D0(r,h,function(g,v){return o(g,v[0])})}function t3(r,o,h){o=Xi(o,r);var g=-1,v=o.length;for(v||(v=1,r=n);++g<v;){var S=r==null?n:r[fi(o[g])];S===n&&(g=v,S=h),r=Ti(S)?S.call(r):S}return r}function e3(r,o,h){return r==null?r:uo(r,o,h)}function n3(r,o,h,g){return g=typeof g=="function"?g:n,r==null?r:uo(r,o,h,g)}var Gg=tg($e),Hg=tg(gn);function i3(r,o,h){var g=kt(r),v=g||Zi(r)||us(r);if(o=pt(o,4),h==null){var S=r&&r.constructor;v?h=g?new S:[]:xe(r)?h=Ti(S)?os(La(r)):{}:h={}}return(v?$n:ui)(r,function(E,D,N){return o(h,E,D,N)}),h}function r3(r,o){return r==null?!0:Bu(r,o)}function s3(r,o,h){return r==null?r:$0(r,o,Uu(h))}function o3(r,o,h,g){return g=typeof g=="function"?g:n,r==null?r:$0(r,o,Uu(h),g)}function hs(r){return r==null?[]:wu(r,$e(r))}function a3(r){return r==null?[]:wu(r,gn(r))}function c3(r,o,h){return h===n&&(h=o,o=n),h!==n&&(h=Hn(h),h=h===h?h:0),o!==n&&(o=Hn(o),o=o===o?o:0),mr(Hn(r),o,h)}function l3(r,o,h){return o=Li(o),h===n?(h=o,o=0):h=Li(h),r=Hn(r),gw(r,o,h)}function u3(r,o,h){if(h&&typeof h!="boolean"&&nn(r,o,h)&&(o=h=n),h===n&&(typeof o=="boolean"?(h=o,o=n):typeof r=="boolean"&&(h=r,r=n)),r===n&&o===n?(r=0,o=1):(r=Li(r),o===n?(o=r,r=0):o=Li(o)),r>o){var g=r;r=o,o=g}if(h||r%1||o%1){var v=d0();return Ke(r+v*(o-r+Gv("1e-"+((v+"").length-1))),o)}return Wu(r,o)}var h3=cs(function(r,o,h){return o=o.toLowerCase(),r+(h?Ug(o):o)});function Ug(r){return hh(te(r).toLowerCase())}function Vg(r){return r=te(r),r&&r.replace(me,eb).replace(Dv,"")}function f3(r,o,h){r=te(r),o=An(o);var g=r.length;h=h===n?g:mr(Et(h),0,g);var v=h;return h-=o.length,h>=0&&r.slice(h,v)==o}function d3(r){return r=te(r),r&&Ks.test(r)?r.replace(bi,nb):r}function p3(r){return r=te(r),r&&iu.test(r)?r.replace(Js,"\\$&"):r}var g3=cs(function(r,o,h){return r+(h?"-":"")+o.toLowerCase()}),y3=cs(function(r,o,h){return r+(h?" ":"")+o.toLowerCase()}),m3=Y0("toLowerCase");function x3(r,o,h){r=te(r),o=Et(o);var g=o?es(r):0;if(!o||g>=o)return r;var v=(o-g)/2;return qa(Ra(v),h)+r+qa(Pa(v),h)}function _3(r,o,h){r=te(r),o=Et(o);var g=o?es(r):0;return o&&g<o?r+qa(o-g,h):r}function v3(r,o,h){r=te(r),o=Et(o);var g=o?es(r):0;return o&&g<o?qa(o-g,h)+r:r}function b3(r,o,h){return h||o==null?o=0:o&&(o=+o),Mb(te(r).replace(Zr,""),o||0)}function w3(r,o,h){return(h?nn(r,o,h):o===n)?o=1:o=Et(o),zu(te(r),o)}function k3(){var r=arguments,o=te(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var S3=cs(function(r,o,h){return r+(h?"_":"")+o.toLowerCase()});function C3(r,o,h){return h&&typeof h!="number"&&nn(r,o,h)&&(o=h=n),h=h===n?nt:h>>>0,h?(r=te(r),r&&(typeof o=="string"||o!=null&&!ch(o))&&(o=An(o),!o&&ts(r))?Ki(Zn(r),0,h):r.split(o,h)):[]}var A3=cs(function(r,o,h){return r+(h?" ":"")+hh(o)});function E3(r,o,h){return r=te(r),h=h==null?0:mr(Et(h),0,r.length),o=An(o),r.slice(h,h+o.length)==o}function M3(r,o,h){var g=k.templateSettings;h&&nn(r,o,h)&&(o=n),r=te(r),o=sc({},o,g,eg);var v=sc({},o.imports,g.imports,eg),S=$e(v),E=wu(v,S),D,N,U=0,j=o.interpolate||fe,X="__p += '",tt=Su((o.escape||fe).source+"|"+j.source+"|"+(j===Zs?it:fe).source+"|"+(o.evaluate||fe).source+"|$","g"),ct="//# sourceURL="+(ie.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++$v+"]")+`
`;r.replace(tt,function(yt,zt,jt,Mn,rn,Tn){return jt||(jt=Mn),X+=r.slice(U,Tn).replace(fn,ib),zt&&(D=!0,X+=`' +
__e(`+zt+`) +
'`),rn&&(N=!0,X+=`';
`+rn+`;
__p += '`),jt&&(X+=`' +
((__t = (`+jt+`)) == null ? '' : __t) +
'`),U=Tn+yt.length,yt}),X+=`';
`;var gt=ie.call(o,"variable")&&o.variable;if(!gt)X=`with (obj) {
`+X+`
}
`;else if(T.test(gt))throw new bt(l);X=(N?X.replace(Ql,""):X).replace(tu,"$1").replace(qs,"$1;"),X="function("+(gt||"obj")+`) {
`+(gt?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(D?", __e = _.escape":"")+(N?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+X+`return __p
}`;var Lt=qg(function(){return Zt(S,ct+"return "+X).apply(n,E)});if(Lt.source=X,ah(Lt))throw Lt;return Lt}function T3(r){return te(r).toLowerCase()}function L3(r){return te(r).toUpperCase()}function O3(r,o,h){if(r=te(r),r&&(h||o===n))return n0(r);if(!r||!(o=An(o)))return r;var g=Zn(r),v=Zn(o),S=i0(g,v),E=r0(g,v)+1;return Ki(g,S,E).join("")}function D3(r,o,h){if(r=te(r),r&&(h||o===n))return r.slice(0,o0(r)+1);if(!r||!(o=An(o)))return r;var g=Zn(r),v=r0(g,Zn(o))+1;return Ki(g,0,v).join("")}function P3(r,o,h){if(r=te(r),r&&(h||o===n))return r.replace(Zr,"");if(!r||!(o=An(o)))return r;var g=Zn(r),v=i0(g,Zn(o));return Ki(g,v).join("")}function R3(r,o){var h=H,g=Y;if(xe(o)){var v="separator"in o?o.separator:v;h="length"in o?Et(o.length):h,g="omission"in o?An(o.omission):g}r=te(r);var S=r.length;if(ts(r)){var E=Zn(r);S=E.length}if(h>=S)return r;var D=h-es(g);if(D<1)return g;var N=E?Ki(E,0,D).join(""):r.slice(0,D);if(v===n)return N+g;if(E&&(D+=N.length-D),ch(v)){if(r.slice(D).search(v)){var U,j=N;for(v.global||(v=Su(v.source,te(mt.exec(v))+"g")),v.lastIndex=0;U=v.exec(j);)var X=U.index;N=N.slice(0,X===n?D:X)}}else if(r.indexOf(An(v),D)!=D){var tt=N.lastIndexOf(v);tt>-1&&(N=N.slice(0,tt))}return N+g}function I3(r){return r=te(r),r&&Xs.test(r)?r.replace(Ys,ub):r}var N3=cs(function(r,o,h){return r+(h?" ":"")+o.toUpperCase()}),hh=Y0("toUpperCase");function jg(r,o,h){return r=te(r),o=h?n:o,o===n?sb(r)?db(r):Kv(r):r.match(o)||[]}var qg=Ot(function(r,o){try{return Sn(r,n,o)}catch(h){return ah(h)?h:new bt(h)}}),$3=Ei(function(r,o){return $n(o,function(h){h=fi(h),Ci(r,h,sh(r[h],r))}),r});function W3(r){var o=r==null?0:r.length,h=pt();return r=o?ge(r,function(g){if(typeof g[1]!="function")throw new Wn(c);return[h(g[0]),g[1]]}):[],Ot(function(g){for(var v=-1;++v<o;){var S=r[v];if(Sn(S[0],this,g))return Sn(S[1],this,g)}})}function z3(r){return uw(Fn(r,p))}function fh(r){return function(){return r}}function F3(r,o){return r==null||r!==r?o:r}var B3=K0(),G3=K0(!0);function yn(r){return r}function dh(r){return A0(typeof r=="function"?r:Fn(r,p))}function H3(r){return M0(Fn(r,p))}function U3(r,o){return T0(r,Fn(o,p))}var V3=Ot(function(r,o){return function(h){return co(h,r,o)}}),j3=Ot(function(r,o){return function(h){return co(r,h,o)}});function ph(r,o,h){var g=$e(o),v=Fa(o,g);h==null&&!(xe(o)&&(v.length||!g.length))&&(h=o,o=r,r=this,v=Fa(o,$e(o)));var S=!(xe(h)&&"chain"in h)||!!h.chain,E=Ti(r);return $n(v,function(D){var N=o[D];r[D]=N,E&&(r.prototype[D]=function(){var U=this.__chain__;if(S||U){var j=r(this.__wrapped__),X=j.__actions__=dn(this.__actions__);return X.push({func:N,args:arguments,thisArg:r}),j.__chain__=U,j}return N.apply(r,Ui([this.value()],arguments))})}),r}function q3(){return Ge._===this&&(Ge._=_b),this}function gh(){}function Y3(r){return r=Et(r),Ot(function(o){return L0(o,r)})}var X3=ju(ge),K3=ju(Zp),Z3=ju(mu);function Yg(r){return Qu(r)?xu(fi(r)):Ew(r)}function J3(r){return function(o){return r==null?n:xr(r,o)}}var Q3=J0(),t5=J0(!0);function yh(){return[]}function mh(){return!1}function e5(){return{}}function n5(){return""}function i5(){return!0}function r5(r,o){if(r=Et(r),r<1||r>Gt)return[];var h=nt,g=Ke(r,nt);o=pt(o),r-=nt;for(var v=bu(g,o);++h<r;)o(h);return v}function s5(r){return kt(r)?ge(r,fi):En(r)?[r]:dn(pg(te(r)))}function o5(r){var o=++mb;return te(r)+o}var a5=ja(function(r,o){return r+o},0),c5=qu("ceil"),l5=ja(function(r,o){return r/o},1),u5=qu("floor");function h5(r){return r&&r.length?za(r,yn,Du):n}function f5(r,o){return r&&r.length?za(r,pt(o,2),Du):n}function d5(r){return t0(r,yn)}function p5(r,o){return t0(r,pt(o,2))}function g5(r){return r&&r.length?za(r,yn,Nu):n}function y5(r,o){return r&&r.length?za(r,pt(o,2),Nu):n}var m5=ja(function(r,o){return r*o},1),x5=qu("round"),_5=ja(function(r,o){return r-o},0);function v5(r){return r&&r.length?vu(r,yn):0}function b5(r,o){return r&&r.length?vu(r,pt(o,2)):0}return k.after=Hk,k.ary=Cg,k.assign=LS,k.assignIn=zg,k.assignInWith=sc,k.assignWith=OS,k.at=DS,k.before=Ag,k.bind=sh,k.bindAll=$3,k.bindKey=Eg,k.castArray=eS,k.chain=wg,k.chunk=u2,k.compact=h2,k.concat=f2,k.cond=W3,k.conforms=z3,k.constant=fh,k.countBy=vk,k.create=PS,k.curry=Mg,k.curryRight=Tg,k.debounce=Lg,k.defaults=RS,k.defaultsDeep=IS,k.defer=Uk,k.delay=Vk,k.difference=d2,k.differenceBy=p2,k.differenceWith=g2,k.drop=y2,k.dropRight=m2,k.dropRightWhile=x2,k.dropWhile=_2,k.fill=v2,k.filter=wk,k.flatMap=Ck,k.flatMapDeep=Ak,k.flatMapDepth=Ek,k.flatten=xg,k.flattenDeep=b2,k.flattenDepth=w2,k.flip=jk,k.flow=B3,k.flowRight=G3,k.fromPairs=k2,k.functions=GS,k.functionsIn=HS,k.groupBy=Mk,k.initial=C2,k.intersection=A2,k.intersectionBy=E2,k.intersectionWith=M2,k.invert=VS,k.invertBy=jS,k.invokeMap=Lk,k.iteratee=dh,k.keyBy=Ok,k.keys=$e,k.keysIn=gn,k.map=Qa,k.mapKeys=YS,k.mapValues=XS,k.matches=H3,k.matchesProperty=U3,k.memoize=ec,k.merge=KS,k.mergeWith=Fg,k.method=V3,k.methodOf=j3,k.mixin=ph,k.negate=nc,k.nthArg=Y3,k.omit=ZS,k.omitBy=JS,k.once=qk,k.orderBy=Dk,k.over=X3,k.overArgs=Yk,k.overEvery=K3,k.overSome=Z3,k.partial=oh,k.partialRight=Og,k.partition=Pk,k.pick=QS,k.pickBy=Bg,k.property=Yg,k.propertyOf=J3,k.pull=D2,k.pullAll=vg,k.pullAllBy=P2,k.pullAllWith=R2,k.pullAt=I2,k.range=Q3,k.rangeRight=t5,k.rearg=Xk,k.reject=Nk,k.remove=N2,k.rest=Kk,k.reverse=ih,k.sampleSize=Wk,k.set=e3,k.setWith=n3,k.shuffle=zk,k.slice=$2,k.sortBy=Gk,k.sortedUniq=U2,k.sortedUniqBy=V2,k.split=C3,k.spread=Zk,k.tail=j2,k.take=q2,k.takeRight=Y2,k.takeRightWhile=X2,k.takeWhile=K2,k.tap=hk,k.throttle=Jk,k.thru=Ja,k.toArray=Ng,k.toPairs=Gg,k.toPairsIn=Hg,k.toPath=s5,k.toPlainObject=Wg,k.transform=i3,k.unary=Qk,k.union=Z2,k.unionBy=J2,k.unionWith=Q2,k.uniq=tk,k.uniqBy=ek,k.uniqWith=nk,k.unset=r3,k.unzip=rh,k.unzipWith=bg,k.update=s3,k.updateWith=o3,k.values=hs,k.valuesIn=a3,k.without=ik,k.words=jg,k.wrap=tS,k.xor=rk,k.xorBy=sk,k.xorWith=ok,k.zip=ak,k.zipObject=ck,k.zipObjectDeep=lk,k.zipWith=uk,k.entries=Gg,k.entriesIn=Hg,k.extend=zg,k.extendWith=sc,ph(k,k),k.add=a5,k.attempt=qg,k.camelCase=h3,k.capitalize=Ug,k.ceil=c5,k.clamp=c3,k.clone=nS,k.cloneDeep=rS,k.cloneDeepWith=sS,k.cloneWith=iS,k.conformsTo=oS,k.deburr=Vg,k.defaultTo=F3,k.divide=l5,k.endsWith=f3,k.eq=Qn,k.escape=d3,k.escapeRegExp=p3,k.every=bk,k.find=kk,k.findIndex=yg,k.findKey=NS,k.findLast=Sk,k.findLastIndex=mg,k.findLastKey=$S,k.floor=u5,k.forEach=kg,k.forEachRight=Sg,k.forIn=WS,k.forInRight=zS,k.forOwn=FS,k.forOwnRight=BS,k.get=lh,k.gt=aS,k.gte=cS,k.has=US,k.hasIn=uh,k.head=_g,k.identity=yn,k.includes=Tk,k.indexOf=S2,k.inRange=l3,k.invoke=qS,k.isArguments=br,k.isArray=kt,k.isArrayBuffer=lS,k.isArrayLike=pn,k.isArrayLikeObject=ke,k.isBoolean=uS,k.isBuffer=Zi,k.isDate=hS,k.isElement=fS,k.isEmpty=dS,k.isEqual=pS,k.isEqualWith=gS,k.isError=ah,k.isFinite=yS,k.isFunction=Ti,k.isInteger=Dg,k.isLength=ic,k.isMap=Pg,k.isMatch=mS,k.isMatchWith=xS,k.isNaN=_S,k.isNative=vS,k.isNil=wS,k.isNull=bS,k.isNumber=Rg,k.isObject=xe,k.isObjectLike=be,k.isPlainObject=go,k.isRegExp=ch,k.isSafeInteger=kS,k.isSet=Ig,k.isString=rc,k.isSymbol=En,k.isTypedArray=us,k.isUndefined=SS,k.isWeakMap=CS,k.isWeakSet=AS,k.join=T2,k.kebabCase=g3,k.last=Gn,k.lastIndexOf=L2,k.lowerCase=y3,k.lowerFirst=m3,k.lt=ES,k.lte=MS,k.max=h5,k.maxBy=f5,k.mean=d5,k.meanBy=p5,k.min=g5,k.minBy=y5,k.stubArray=yh,k.stubFalse=mh,k.stubObject=e5,k.stubString=n5,k.stubTrue=i5,k.multiply=m5,k.nth=O2,k.noConflict=q3,k.noop=gh,k.now=tc,k.pad=x3,k.padEnd=_3,k.padStart=v3,k.parseInt=b3,k.random=u3,k.reduce=Rk,k.reduceRight=Ik,k.repeat=w3,k.replace=k3,k.result=t3,k.round=x5,k.runInContext=I,k.sample=$k,k.size=Fk,k.snakeCase=S3,k.some=Bk,k.sortedIndex=W2,k.sortedIndexBy=z2,k.sortedIndexOf=F2,k.sortedLastIndex=B2,k.sortedLastIndexBy=G2,k.sortedLastIndexOf=H2,k.startCase=A3,k.startsWith=E3,k.subtract=_5,k.sum=v5,k.sumBy=b5,k.template=M3,k.times=r5,k.toFinite=Li,k.toInteger=Et,k.toLength=$g,k.toLower=T3,k.toNumber=Hn,k.toSafeInteger=TS,k.toString=te,k.toUpper=L3,k.trim=O3,k.trimEnd=D3,k.trimStart=P3,k.truncate=R3,k.unescape=I3,k.uniqueId=o5,k.upperCase=N3,k.upperFirst=hh,k.each=kg,k.eachRight=Sg,k.first=_g,ph(k,function(){var r={};return ui(k,function(o,h){ie.call(k.prototype,h)||(r[h]=o)}),r}(),{chain:!1}),k.VERSION=i,$n(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){k[r].placeholder=k}),$n(["drop","take"],function(r,o){Ut.prototype[r]=function(h){h=h===n?1:De(Et(h),0);var g=this.__filtered__&&!o?new Ut(this):this.clone();return g.__filtered__?g.__takeCount__=Ke(h,g.__takeCount__):g.__views__.push({size:Ke(h,nt),type:r+(g.__dir__<0?"Right":"")}),g},Ut.prototype[r+"Right"]=function(h){return this.reverse()[r](h).reverse()}}),$n(["filter","map","takeWhile"],function(r,o){var h=o+1,g=h==rt||h==$t;Ut.prototype[r]=function(v){var S=this.clone();return S.__iteratees__.push({iteratee:pt(v,3),type:h}),S.__filtered__=S.__filtered__||g,S}}),$n(["head","last"],function(r,o){var h="take"+(o?"Right":"");Ut.prototype[r]=function(){return this[h](1).value()[0]}}),$n(["initial","tail"],function(r,o){var h="drop"+(o?"":"Right");Ut.prototype[r]=function(){return this.__filtered__?new Ut(this):this[h](1)}}),Ut.prototype.compact=function(){return this.filter(yn)},Ut.prototype.find=function(r){return this.filter(r).head()},Ut.prototype.findLast=function(r){return this.reverse().find(r)},Ut.prototype.invokeMap=Ot(function(r,o){return typeof r=="function"?new Ut(this):this.map(function(h){return co(h,r,o)})}),Ut.prototype.reject=function(r){return this.filter(nc(pt(r)))},Ut.prototype.slice=function(r,o){r=Et(r);var h=this;return h.__filtered__&&(r>0||o<0)?new Ut(h):(r<0?h=h.takeRight(-r):r&&(h=h.drop(r)),o!==n&&(o=Et(o),h=o<0?h.dropRight(-o):h.take(o-r)),h)},Ut.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},Ut.prototype.toArray=function(){return this.take(nt)},ui(Ut.prototype,function(r,o){var h=/^(?:filter|find|map|reject)|While$/.test(o),g=/^(?:head|last)$/.test(o),v=k[g?"take"+(o=="last"?"Right":""):o],S=g||/^find/.test(o);v&&(k.prototype[o]=function(){var E=this.__wrapped__,D=g?[1]:arguments,N=E instanceof Ut,U=D[0],j=N||kt(E),X=function(zt){var jt=v.apply(k,Ui([zt],D));return g&&tt?jt[0]:jt};j&&h&&typeof U=="function"&&U.length!=1&&(N=j=!1);var tt=this.__chain__,ct=!!this.__actions__.length,gt=S&&!tt,Lt=N&&!ct;if(!S&&j){E=Lt?E:new Ut(this);var yt=r.apply(E,D);return yt.__actions__.push({func:Ja,args:[X],thisArg:n}),new zn(yt,tt)}return gt&&Lt?r.apply(this,D):(yt=this.thru(X),gt?g?yt.value()[0]:yt.value():yt)})}),$n(["pop","push","shift","sort","splice","unshift"],function(r){var o=Sa[r],h=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",g=/^(?:pop|shift)$/.test(r);k.prototype[r]=function(){var v=arguments;if(g&&!this.__chain__){var S=this.value();return o.apply(kt(S)?S:[],v)}return this[h](function(E){return o.apply(kt(E)?E:[],v)})}}),ui(Ut.prototype,function(r,o){var h=k[o];if(h){var g=h.name+"";ie.call(ss,g)||(ss[g]=[]),ss[g].push({name:o,func:h})}}),ss[Va(n,w).name]=[{name:"wrapper",func:n}],Ut.prototype.clone=Ib,Ut.prototype.reverse=Nb,Ut.prototype.value=$b,k.prototype.at=fk,k.prototype.chain=dk,k.prototype.commit=pk,k.prototype.next=gk,k.prototype.plant=mk,k.prototype.reverse=xk,k.prototype.toJSON=k.prototype.valueOf=k.prototype.value=_k,k.prototype.first=k.prototype.head,eo&&(k.prototype[eo]=yk),k},ns=pb();dr?((dr.exports=ns)._=ns,du._=ns):Ge._=ns}).call(u1)}(Cs,Cs.exports)),Cs.exports}var We=h1();const lt=wr(We);var Ao,f1=new Uint8Array(16);function d1(){if(!Ao&&(Ao=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!Ao))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Ao(f1)}const p1=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function g1(e){return typeof e=="string"&&p1.test(e)}for(var ze=[],Fc=0;Fc<256;++Fc)ze.push((Fc+256).toString(16).substr(1));function y1(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=(ze[e[t+0]]+ze[e[t+1]]+ze[e[t+2]]+ze[e[t+3]]+"-"+ze[e[t+4]]+ze[e[t+5]]+"-"+ze[e[t+6]]+ze[e[t+7]]+"-"+ze[e[t+8]]+ze[e[t+9]]+"-"+ze[e[t+10]]+ze[e[t+11]]+ze[e[t+12]]+ze[e[t+13]]+ze[e[t+14]]+ze[e[t+15]]).toLowerCase();if(!g1(n))throw TypeError("Stringified UUID is invalid");return n}function Eo(e,t,n){e=e||{};var i=e.random||(e.rng||d1)();return i[6]=i[6]&15|64,i[8]=i[8]&63|128,y1(i)}var _n={};function Dn(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var of=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),Bc=function(){return Math.random().toString(36).substring(7).split("").join(".")},af={INIT:"@@redux/INIT"+Bc(),REPLACE:"@@redux/REPLACE"+Bc(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Bc()}};function m1(e){if(typeof e!="object"||e===null)return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function x1(e){if(e===void 0)return"undefined";if(e===null)return"null";var t=typeof e;switch(t){case"boolean":case"string":case"number":case"symbol":case"function":return t}if(Array.isArray(e))return"array";if(b1(e))return"date";if(v1(e))return"error";var n=_1(e);switch(n){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return n}return t.slice(8,-1).toLowerCase().replace(/\s/g,"")}function _1(e){return typeof e.constructor=="function"?e.constructor.name:null}function v1(e){return e instanceof Error||typeof e.message=="string"&&e.constructor&&typeof e.constructor.stackTraceLimit=="number"}function b1(e){return e instanceof Date?!0:typeof e.toDateString=="function"&&typeof e.getDate=="function"&&typeof e.setDate=="function"}function Or(e){var t=typeof e;return _n.NODE_ENV!=="production"&&(t=x1(e)),t}function cf(e,t,n){var i;if(typeof t=="function"&&typeof n=="function"||typeof n=="function"&&typeof arguments[3]=="function")throw new Error(_n.NODE_ENV==="production"?Dn(0):"It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.");if(typeof t=="function"&&typeof n>"u"&&(n=t,t=void 0),typeof n<"u"){if(typeof n!="function")throw new Error(_n.NODE_ENV==="production"?Dn(1):"Expected the enhancer to be a function. Instead, received: '"+Or(n)+"'");return n(cf)(e,t)}if(typeof e!="function")throw new Error(_n.NODE_ENV==="production"?Dn(2):"Expected the root reducer to be a function. Instead, received: '"+Or(e)+"'");var s=e,a=t,c=[],l=c,u=!1;function f(){l===c&&(l=c.slice())}function d(){if(u)throw new Error(_n.NODE_ENV==="production"?Dn(3):"You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");return a}function p(x){if(typeof x!="function")throw new Error(_n.NODE_ENV==="production"?Dn(4):"Expected the listener to be a function. Instead, received: '"+Or(x)+"'");if(u)throw new Error(_n.NODE_ENV==="production"?Dn(5):"You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.");var b=!0;return f(),l.push(x),function(){if(b){if(u)throw new Error(_n.NODE_ENV==="production"?Dn(6):"You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.");b=!1,f();var C=l.indexOf(x);l.splice(C,1),c=null}}}function y(x){if(!m1(x))throw new Error(_n.NODE_ENV==="production"?Dn(7):"Actions must be plain objects. Instead, the actual type was: '"+Or(x)+"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.");if(typeof x.type>"u")throw new Error(_n.NODE_ENV==="production"?Dn(8):'Actions may not have an undefined "type" property. You may have misspelled an action type string constant.');if(u)throw new Error(_n.NODE_ENV==="production"?Dn(9):"Reducers may not dispatch actions.");try{u=!0,a=s(a,x)}finally{u=!1}for(var b=c=l,w=0;w<b.length;w++){var C=b[w];C()}return x}function m(x){if(typeof x!="function")throw new Error(_n.NODE_ENV==="production"?Dn(10):"Expected the nextReducer to be a function. Instead, received: '"+Or(x));s=x,y({type:af.REPLACE})}function _(){var x,b=p;return x={subscribe:function(C){if(typeof C!="object"||C===null)throw new Error(_n.NODE_ENV==="production"?Dn(11):"Expected the observer to be an object. Instead, received: '"+Or(C)+"'");function A(){C.next&&C.next(d())}A();var L=b(A);return{unsubscribe:L}}},x[of]=function(){return this},x}return y({type:af.INIT}),i={dispatch:y,subscribe:p,getState:d,replaceReducer:m},i[of]=_,i}var Gc=1e-7;function Hc(e,t,n){n===void 0&&(n=-1);for(var i=e.length,s=0;s<i;++s)if(t(e[s],s,e))return s;return n}function lf(e,t,n){var i=Hc(e,t);return i>-1?e[i]:n}function w1(e){for(var t=e.length,n=0,i=t-1;i>=0;--i)n+=e[i];return n}function k1(e){for(var t=e.length,n=0,i=t-1;i>=0;--i)n+=e[i];return t?n/t:0}function uf(e,t){var n=t[0]-e[0],i=t[1]-e[1],s=Math.atan2(i,n);return s>=0?s:s+Math.PI*2}function S1(e){return[0,1].map(function(t){return k1(e.map(function(n){return n[t]}))})}function hf(e){var t=S1(e),n=uf(t,e[0]),i=uf(t,e[1]);return n<i&&i-n<Math.PI||n>i&&i-n<-Math.PI?1:-1}function ff(e,t){return Math.sqrt(Math.pow((t?t[0]:0)-e[0],2)+Math.pow((t?t[1]:0)-e[1],2))}function C1(e,t){var n=1/t;return Math.round(e/t)/n}/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */function A1(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var i=Array(e),s=0,t=0;t<n;t++)for(var a=arguments[t],c=0,l=a.length;c<l;c++,s++)i[s]=a[c];return i}function an(e){return C1(e,Gc)}function E1(e,t){return e.every(function(n,i){return an(n-t[i])===0})}function M1(e,t){return!an(e[0]-t[0])&&!an(e[1]-t[1])}function Mo(e){return e.length<3?0:Math.abs(w1(e.map(function(t,n){var i=e[n+1]||e[0];return t[0]*i[1]-i[0]*t[1]})))/2}function T1(e){var t=e.map(function(i){return i[0]}),n=e.map(function(i){return i[1]});return{minX:Math.min.apply(Math,t),minY:Math.min.apply(Math,n),maxX:Math.max.apply(Math,t),maxY:Math.max.apply(Math,n)}}function Uc(e,t,n){var i=e[0],s=e[1],a=T1(t),c=a.minX,l=a.maxX,u=[[c,s],[l,s]],f=To(u[0],u[1]),d=Vc(t),p=[];if(d.forEach(function(_){var x=To(_[0],_[1]),b=_[0];if(E1(f,x))p.push({pos:e,line:_,type:"line"});else{var w=pf(df(f,x),[u,_]);w.forEach(function(C){_.some(function(A){return M1(A,C)})?p.push({pos:C,line:_,type:"point"}):an(b[1]-s)!==0&&p.push({pos:C,line:_,type:"intersection"})})}}),lf(p,function(_){return _[0]===i}))return!0;var y=0,m={};return p.forEach(function(_){var x=_.pos,b=_.type,w=_.line;if(!(x[0]>i))if(b==="intersection")++y;else{if(b==="line")return;if(b==="point"){var C=lf(w,function(P){return P[1]!==s}),A=m[x[0]],L=C[1]>s?1:-1;A?A!==L&&++y:m[x[0]]=L}}}),y%2===1}function To(e,t){var n=e[0],i=e[1],s=t[0],a=t[1],c=s-n,l=a-i;Math.abs(c)<Gc&&(c=0),Math.abs(l)<Gc&&(l=0);var u=0,f=0,d=0;return c?l?(u=-l/c,f=1,d=-u*n-i):(f=1,d=-i):l&&(u=-1,d=n),[u,f,d]}function df(e,t){var n=e[0],i=e[1],s=e[2],a=t[0],c=t[1],l=t[2],u=n===0&&a===0,f=i===0&&c===0,d=[];if(u&&f)return[];if(u){var p=-s/i,y=-l/c;return p!==y?[]:[[-1/0,p],[1/0,p]]}else if(f){var m=-s/n,_=-l/a;return m!==_?[]:[[m,-1/0],[m,1/0]]}else if(n===0){var x=-s/i,b=-(c*x+l)/a;d=[[b,x]]}else if(a===0){var x=-l/c,b=-(i*x+s)/n;d=[[b,x]]}else if(i===0){var b=-s/n,x=-(a*b+l)/c;d=[[b,x]]}else if(c===0){var b=-l/a,x=-(n*b+s)/i;d=[[b,x]]}else{var b=(i*l-c*s)/(c*n-i*a),x=-(n*b+s)/i;d=[[b,x]]}return d.map(function(w){return[w[0],w[1]]})}function pf(e,t){var n=t.map(function(p){return[0,1].map(function(y){return[Math.min(p[0][y],p[1][y]),Math.max(p[0][y],p[1][y])]})}),i=[];if(e.length===2){var s=e[0],a=s[0],c=s[1];if(an(a-e[1][0])){if(!an(c-e[1][1])){var f=Math.max.apply(Math,n.map(function(p){return p[0][0]})),d=Math.min.apply(Math,n.map(function(p){return p[0][1]}));if(an(f-d)>0)return[];i=[[f,c],[d,c]]}}else{var l=Math.max.apply(Math,n.map(function(p){return p[1][0]})),u=Math.min.apply(Math,n.map(function(p){return p[1][1]}));if(an(l-u)>0)return[];i=[[a,l],[a,u]]}}return i.length||(i=e.filter(function(p){var y=p[0],m=p[1];return n.every(function(_){return 0<=an(y-_[0][0])&&0<=an(_[0][1]-y)&&0<=an(m-_[1][0])&&0<=an(_[1][1]-m)})})),i.map(function(p){return[an(p[0]),an(p[1])]})}function Vc(e){return A1(e.slice(1),[e[0]]).map(function(t,n){return[e[n],t]})}function L1(e,t){var n=e.slice(),i=t.slice();hf(n)===-1&&n.reverse(),hf(i)===-1&&i.reverse();var s=Vc(n),a=Vc(i),c=s.map(function(d){return To(d[0],d[1])}),l=a.map(function(d){return To(d[0],d[1])}),u=[];c.forEach(function(d,p){var y=s[p],m=[];l.forEach(function(_,x){var b=df(d,_),w=pf(b,[y,a[x]]);m.push.apply(m,w.map(function(C){return{index1:p,index2:x,pos:C,type:"intersection"}}))}),m.sort(function(_,x){return ff(y[0],_.pos)-ff(y[0],x.pos)}),u.push.apply(u,m),Uc(y[1],i)&&u.push({index1:p,index2:-1,pos:y[1],type:"inside"})}),a.forEach(function(d,p){if(Uc(d[1],n)){var y=!1,m=Hc(u,function(_){var x=_.index2;return x===p?(y=!0,!1):!!y});m===-1&&(y=!1,m=Hc(u,function(_){var x=_.index1,b=_.index2;return x===-1&&b+1===p?(y=!0,!1):!!y})),m===-1?u.push({index1:-1,index2:p,pos:d[1],type:"inside"}):u.splice(m,0,{index1:-1,index2:p,pos:d[1],type:"inside"})}});var f={};return u.filter(function(d){var p=d.pos,y=p[0]+"x"+p[1];return f[y]?!1:(f[y]=!0,!0)})}function O1(e,t){var n=L1(e,t);return n.map(function(i){var s=i.pos;return s})}function gf(e,t){var n=O1(e,t);return Mo(n)}const Dr=(e,t=0,n=1)=>qc(Yc(t,e),n),jc=e=>{e._clipped=!1,e._unclipped=e.slice(0);for(let t=0;t<=3;t++)t<3?((e[t]<0||e[t]>255)&&(e._clipped=!0),e[t]=Dr(e[t],0,255)):t===3&&(e[t]=Dr(e[t],0,1));return e},yf={};for(let e of["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"])yf[`[object ${e}]`]=e.toLowerCase();function Pt(e){return yf[Object.prototype.toString.call(e)]||"object"}const Rt=(e,t=null)=>e.length>=3?Array.prototype.slice.call(e):Pt(e[0])=="object"&&t?t.split("").filter(n=>e[0][n]!==void 0).map(n=>e[0][n]):e[0],Lo=e=>{if(e.length<2)return null;const t=e.length-1;return Pt(e[t])=="string"?e[t].toLowerCase():null},{PI:Oo,min:qc,max:Yc}=Math,pi=Oo*2,Xc=Oo/3,D1=Oo/180,P1=180/Oo,wt={format:{},autodetect:[]};class Z{constructor(...t){const n=this;if(Pt(t[0])==="object"&&t[0].constructor&&t[0].constructor===this.constructor)return t[0];let i=Lo(t),s=!1;if(!i){s=!0,wt.sorted||(wt.autodetect=wt.autodetect.sort((a,c)=>c.p-a.p),wt.sorted=!0);for(let a of wt.autodetect)if(i=a.test(...t),i)break}if(wt.format[i]){const a=wt.format[i].apply(null,s?t:t.slice(0,-1));n._rgb=jc(a)}else throw new Error("unknown format: "+t);n._rgb.length===3&&n._rgb.push(1)}toString(){return Pt(this.hex)=="function"?this.hex():`[${this._rgb.join(",")}]`}}const R1="2.6.0",St=(...e)=>new St.Color(...e);St.Color=Z,St.version=R1;const I1=(...e)=>{e=Rt(e,"cmyk");const[t,n,i,s]=e,a=e.length>4?e[4]:1;return s===1?[0,0,0,a]:[t>=1?0:255*(1-t)*(1-s),n>=1?0:255*(1-n)*(1-s),i>=1?0:255*(1-i)*(1-s),a]},{max:mf}=Math,N1=(...e)=>{let[t,n,i]=Rt(e,"rgb");t=t/255,n=n/255,i=i/255;const s=1-mf(t,mf(n,i)),a=s<1?1/(1-s):0,c=(1-t-s)*a,l=(1-n-s)*a,u=(1-i-s)*a;return[c,l,u,s]};Z.prototype.cmyk=function(){return N1(this._rgb)},St.cmyk=(...e)=>new Z(...e,"cmyk"),wt.format.cmyk=I1,wt.autodetect.push({p:2,test:(...e)=>{if(e=Rt(e,"cmyk"),Pt(e)==="array"&&e.length===4)return"cmyk"}});const Kc=e=>Math.round(e*100)/100,$1=(...e)=>{const t=Rt(e,"hsla");let n=Lo(e)||"lsa";return t[0]=Kc(t[0]||0),t[1]=Kc(t[1]*100)+"%",t[2]=Kc(t[2]*100)+"%",n==="hsla"||t.length>3&&t[3]<1?(t[3]=t.length>3?t[3]:1,n="hsla"):t.length=3,`${n}(${t.join(",")})`},xf=(...e)=>{e=Rt(e,"rgba");let[t,n,i]=e;t/=255,n/=255,i/=255;const s=qc(t,n,i),a=Yc(t,n,i),c=(a+s)/2;let l,u;return a===s?(l=0,u=Number.NaN):l=c<.5?(a-s)/(a+s):(a-s)/(2-a-s),t==a?u=(n-i)/(a-s):n==a?u=2+(i-t)/(a-s):i==a&&(u=4+(t-n)/(a-s)),u*=60,u<0&&(u+=360),e.length>3&&e[3]!==void 0?[u,l,c,e[3]]:[u,l,c]},{round:Zc}=Math,W1=(...e)=>{const t=Rt(e,"rgba");let n=Lo(e)||"rgb";return n.substr(0,3)=="hsl"?$1(xf(t),n):(t[0]=Zc(t[0]),t[1]=Zc(t[1]),t[2]=Zc(t[2]),(n==="rgba"||t.length>3&&t[3]<1)&&(t[3]=t.length>3?t[3]:1,n="rgba"),`${n}(${t.slice(0,n==="rgb"?3:4).join(",")})`)},{round:Jc}=Math,Qc=(...e)=>{e=Rt(e,"hsl");const[t,n,i]=e;let s,a,c;if(n===0)s=a=c=i*255;else{const l=[0,0,0],u=[0,0,0],f=i<.5?i*(1+n):i+n-i*n,d=2*i-f,p=t/360;l[0]=p+1/3,l[1]=p,l[2]=p-1/3;for(let y=0;y<3;y++)l[y]<0&&(l[y]+=1),l[y]>1&&(l[y]-=1),6*l[y]<1?u[y]=d+(f-d)*6*l[y]:2*l[y]<1?u[y]=f:3*l[y]<2?u[y]=d+(f-d)*(2/3-l[y])*6:u[y]=d;[s,a,c]=[Jc(u[0]*255),Jc(u[1]*255),Jc(u[2]*255)]}return e.length>3?[s,a,c,e[3]]:[s,a,c,1]},_f=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/,vf=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/,bf=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,wf=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,kf=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,Sf=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,{round:Cf}=Math,tl=e=>{e=e.toLowerCase().trim();let t;if(wt.format.named)try{return wt.format.named(e)}catch{}if(t=e.match(_f)){const n=t.slice(1,4);for(let i=0;i<3;i++)n[i]=+n[i];return n[3]=1,n}if(t=e.match(vf)){const n=t.slice(1,5);for(let i=0;i<4;i++)n[i]=+n[i];return n}if(t=e.match(bf)){const n=t.slice(1,4);for(let i=0;i<3;i++)n[i]=Cf(n[i]*2.55);return n[3]=1,n}if(t=e.match(wf)){const n=t.slice(1,5);for(let i=0;i<3;i++)n[i]=Cf(n[i]*2.55);return n[3]=+n[3],n}if(t=e.match(kf)){const n=t.slice(1,4);n[1]*=.01,n[2]*=.01;const i=Qc(n);return i[3]=1,i}if(t=e.match(Sf)){const n=t.slice(1,4);n[1]*=.01,n[2]*=.01;const i=Qc(n);return i[3]=+t[4],i}};tl.test=e=>_f.test(e)||vf.test(e)||bf.test(e)||wf.test(e)||kf.test(e)||Sf.test(e),Z.prototype.css=function(e){return W1(this._rgb,e)},St.css=(...e)=>new Z(...e,"css"),wt.format.css=tl,wt.autodetect.push({p:5,test:(e,...t)=>{if(!t.length&&Pt(e)==="string"&&tl.test(e))return"css"}}),wt.format.gl=(...e)=>{const t=Rt(e,"rgba");return t[0]*=255,t[1]*=255,t[2]*=255,t},St.gl=(...e)=>new Z(...e,"gl"),Z.prototype.gl=function(){const e=this._rgb;return[e[0]/255,e[1]/255,e[2]/255,e[3]]};const{floor:z1}=Math,F1=(...e)=>{e=Rt(e,"hcg");let[t,n,i]=e,s,a,c;i=i*255;const l=n*255;if(n===0)s=a=c=i;else{t===360&&(t=0),t>360&&(t-=360),t<0&&(t+=360),t/=60;const u=z1(t),f=t-u,d=i*(1-n),p=d+l*(1-f),y=d+l*f,m=d+l;switch(u){case 0:[s,a,c]=[m,y,d];break;case 1:[s,a,c]=[p,m,d];break;case 2:[s,a,c]=[d,m,y];break;case 3:[s,a,c]=[d,p,m];break;case 4:[s,a,c]=[y,d,m];break;case 5:[s,a,c]=[m,d,p];break}}return[s,a,c,e.length>3?e[3]:1]},B1=(...e)=>{const[t,n,i]=Rt(e,"rgb"),s=qc(t,n,i),a=Yc(t,n,i),c=a-s,l=c*100/255,u=s/(255-c)*100;let f;return c===0?f=Number.NaN:(t===a&&(f=(n-i)/c),n===a&&(f=2+(i-t)/c),i===a&&(f=4+(t-n)/c),f*=60,f<0&&(f+=360)),[f,l,u]};Z.prototype.hcg=function(){return B1(this._rgb)},St.hcg=(...e)=>new Z(...e,"hcg"),wt.format.hcg=F1,wt.autodetect.push({p:1,test:(...e)=>{if(e=Rt(e,"hcg"),Pt(e)==="array"&&e.length===3)return"hcg"}});const G1=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,H1=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,Af=e=>{if(e.match(G1)){(e.length===4||e.length===7)&&(e=e.substr(1)),e.length===3&&(e=e.split(""),e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);const t=parseInt(e,16),n=t>>16,i=t>>8&255,s=t&255;return[n,i,s,1]}if(e.match(H1)){(e.length===5||e.length===9)&&(e=e.substr(1)),e.length===4&&(e=e.split(""),e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);const t=parseInt(e,16),n=t>>24&255,i=t>>16&255,s=t>>8&255,a=Math.round((t&255)/255*100)/100;return[n,i,s,a]}throw new Error(`unknown hex color: ${e}`)},{round:Do}=Math,Ef=(...e)=>{let[t,n,i,s]=Rt(e,"rgba"),a=Lo(e)||"auto";s===void 0&&(s=1),a==="auto"&&(a=s<1?"rgba":"rgb"),t=Do(t),n=Do(n),i=Do(i);let l="000000"+(t<<16|n<<8|i).toString(16);l=l.substr(l.length-6);let u="0"+Do(s*255).toString(16);switch(u=u.substr(u.length-2),a.toLowerCase()){case"rgba":return`#${l}${u}`;case"argb":return`#${u}${l}`;default:return`#${l}`}};Z.prototype.hex=function(e){return Ef(this._rgb,e)},St.hex=(...e)=>new Z(...e,"hex"),wt.format.hex=Af,wt.autodetect.push({p:4,test:(e,...t)=>{if(!t.length&&Pt(e)==="string"&&[3,4,5,6,7,8,9].indexOf(e.length)>=0)return"hex"}});const{cos:Pr}=Math,U1=(...e)=>{e=Rt(e,"hsi");let[t,n,i]=e,s,a,c;return isNaN(t)&&(t=0),isNaN(n)&&(n=0),t>360&&(t-=360),t<0&&(t+=360),t/=360,t<1/3?(c=(1-n)/3,s=(1+n*Pr(pi*t)/Pr(Xc-pi*t))/3,a=1-(c+s)):t<2/3?(t-=1/3,s=(1-n)/3,a=(1+n*Pr(pi*t)/Pr(Xc-pi*t))/3,c=1-(s+a)):(t-=2/3,a=(1-n)/3,c=(1+n*Pr(pi*t)/Pr(Xc-pi*t))/3,s=1-(a+c)),s=Dr(i*s*3),a=Dr(i*a*3),c=Dr(i*c*3),[s*255,a*255,c*255,e.length>3?e[3]:1]},{min:V1,sqrt:j1,acos:q1}=Math,Y1=(...e)=>{let[t,n,i]=Rt(e,"rgb");t/=255,n/=255,i/=255;let s;const a=V1(t,n,i),c=(t+n+i)/3,l=c>0?1-a/c:0;return l===0?s=NaN:(s=(t-n+(t-i))/2,s/=j1((t-n)*(t-n)+(t-i)*(n-i)),s=q1(s),i>n&&(s=pi-s),s/=pi),[s*360,l,c]};Z.prototype.hsi=function(){return Y1(this._rgb)},St.hsi=(...e)=>new Z(...e,"hsi"),wt.format.hsi=U1,wt.autodetect.push({p:2,test:(...e)=>{if(e=Rt(e,"hsi"),Pt(e)==="array"&&e.length===3)return"hsi"}}),Z.prototype.hsl=function(){return xf(this._rgb)},St.hsl=(...e)=>new Z(...e,"hsl"),wt.format.hsl=Qc,wt.autodetect.push({p:2,test:(...e)=>{if(e=Rt(e,"hsl"),Pt(e)==="array"&&e.length===3)return"hsl"}});const{floor:X1}=Math,K1=(...e)=>{e=Rt(e,"hsv");let[t,n,i]=e,s,a,c;if(i*=255,n===0)s=a=c=i;else{t===360&&(t=0),t>360&&(t-=360),t<0&&(t+=360),t/=60;const l=X1(t),u=t-l,f=i*(1-n),d=i*(1-n*u),p=i*(1-n*(1-u));switch(l){case 0:[s,a,c]=[i,p,f];break;case 1:[s,a,c]=[d,i,f];break;case 2:[s,a,c]=[f,i,p];break;case 3:[s,a,c]=[f,d,i];break;case 4:[s,a,c]=[p,f,i];break;case 5:[s,a,c]=[i,f,d];break}}return[s,a,c,e.length>3?e[3]:1]},{min:Z1,max:J1}=Math,Q1=(...e)=>{e=Rt(e,"rgb");let[t,n,i]=e;const s=Z1(t,n,i),a=J1(t,n,i),c=a-s;let l,u,f;return f=a/255,a===0?(l=Number.NaN,u=0):(u=c/a,t===a&&(l=(n-i)/c),n===a&&(l=2+(i-t)/c),i===a&&(l=4+(t-n)/c),l*=60,l<0&&(l+=360)),[l,u,f]};Z.prototype.hsv=function(){return Q1(this._rgb)},St.hsv=(...e)=>new Z(...e,"hsv"),wt.format.hsv=K1,wt.autodetect.push({p:2,test:(...e)=>{if(e=Rt(e,"hsv"),Pt(e)==="array"&&e.length===3)return"hsv"}});const cn={Kn:18,Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452},{pow:tx}=Math,Mf=(...e)=>{e=Rt(e,"lab");const[t,n,i]=e;let s,a,c,l,u,f;return a=(t+16)/116,s=isNaN(n)?a:a+n/500,c=isNaN(i)?a:a-i/200,a=cn.Yn*nl(a),s=cn.Xn*nl(s),c=cn.Zn*nl(c),l=el(3.2404542*s-1.5371385*a-.4985314*c),u=el(-.969266*s+1.8760108*a+.041556*c),f=el(.0556434*s-.2040259*a+1.0572252*c),[l,u,f,e.length>3?e[3]:1]},el=e=>255*(e<=.00304?12.92*e:1.055*tx(e,1/2.4)-.055),nl=e=>e>cn.t1?e*e*e:cn.t2*(e-cn.t0),{pow:Tf}=Math,Lf=(...e)=>{const[t,n,i]=Rt(e,"rgb"),[s,a,c]=ex(t,n,i),l=116*a-16;return[l<0?0:l,500*(s-a),200*(a-c)]},il=e=>(e/=255)<=.04045?e/12.92:Tf((e+.055)/1.055,2.4),rl=e=>e>cn.t3?Tf(e,1/3):e/cn.t2+cn.t0,ex=(e,t,n)=>{e=il(e),t=il(t),n=il(n);const i=rl((.4124564*e+.3575761*t+.1804375*n)/cn.Xn),s=rl((.2126729*e+.7151522*t+.072175*n)/cn.Yn),a=rl((.0193339*e+.119192*t+.9503041*n)/cn.Zn);return[i,s,a]};Z.prototype.lab=function(){return Lf(this._rgb)},St.lab=(...e)=>new Z(...e,"lab"),wt.format.lab=Mf,wt.autodetect.push({p:2,test:(...e)=>{if(e=Rt(e,"lab"),Pt(e)==="array"&&e.length===3)return"lab"}});const{sin:nx,cos:ix}=Math,Of=(...e)=>{let[t,n,i]=Rt(e,"lch");return isNaN(i)&&(i=0),i=i*D1,[t,ix(i)*n,nx(i)*n]},Df=(...e)=>{e=Rt(e,"lch");const[t,n,i]=e,[s,a,c]=Of(t,n,i),[l,u,f]=Mf(s,a,c);return[l,u,f,e.length>3?e[3]:1]},rx=(...e)=>{const t=Rt(e,"hcl").reverse();return Df(...t)},{sqrt:sx,atan2:ox,round:ax}=Math,Pf=(...e)=>{const[t,n,i]=Rt(e,"lab"),s=sx(n*n+i*i);let a=(ox(i,n)*P1+360)%360;return ax(s*1e4)===0&&(a=Number.NaN),[t,s,a]},Rf=(...e)=>{const[t,n,i]=Rt(e,"rgb"),[s,a,c]=Lf(t,n,i);return Pf(s,a,c)};Z.prototype.lch=function(){return Rf(this._rgb)},Z.prototype.hcl=function(){return Rf(this._rgb).reverse()},St.lch=(...e)=>new Z(...e,"lch"),St.hcl=(...e)=>new Z(...e,"hcl"),wt.format.lch=Df,wt.format.hcl=rx,["lch","hcl"].forEach(e=>wt.autodetect.push({p:2,test:(...t)=>{if(t=Rt(t,e),Pt(t)==="array"&&t.length===3)return e}}));const Rr={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};Z.prototype.name=function(){const e=Ef(this._rgb,"rgb");for(let t of Object.keys(Rr))if(Rr[t]===e)return t.toLowerCase();return e},wt.format.named=e=>{if(e=e.toLowerCase(),Rr[e])return Af(Rr[e]);throw new Error("unknown color name: "+e)},wt.autodetect.push({p:5,test:(e,...t)=>{if(!t.length&&Pt(e)==="string"&&Rr[e.toLowerCase()])return"named"}});const cx=e=>{if(Pt(e)=="number"&&e>=0&&e<=16777215){const t=e>>16,n=e>>8&255,i=e&255;return[t,n,i,1]}throw new Error("unknown num color: "+e)},lx=(...e)=>{const[t,n,i]=Rt(e,"rgb");return(t<<16)+(n<<8)+i};Z.prototype.num=function(){return lx(this._rgb)},St.num=(...e)=>new Z(...e,"num"),wt.format.num=cx,wt.autodetect.push({p:5,test:(...e)=>{if(e.length===1&&Pt(e[0])==="number"&&e[0]>=0&&e[0]<=16777215)return"num"}});const{round:If}=Math;Z.prototype.rgb=function(e=!0){return e===!1?this._rgb.slice(0,3):this._rgb.slice(0,3).map(If)},Z.prototype.rgba=function(e=!0){return this._rgb.slice(0,4).map((t,n)=>n<3?e===!1?t:If(t):t)},St.rgb=(...e)=>new Z(...e,"rgb"),wt.format.rgb=(...e)=>{const t=Rt(e,"rgba");return t[3]===void 0&&(t[3]=1),t},wt.autodetect.push({p:3,test:(...e)=>{if(e=Rt(e,"rgba"),Pt(e)==="array"&&(e.length===3||e.length===4&&Pt(e[3])=="number"&&e[3]>=0&&e[3]<=1))return"rgb"}});const{log:Po}=Math,Nf=e=>{const t=e/100;let n,i,s;return t<66?(n=255,i=t<6?0:-155.25485562709179-.44596950469579133*(i=t-2)+104.49216199393888*Po(i),s=t<20?0:-254.76935184120902+.8274096064007395*(s=t-10)+115.67994401066147*Po(s)):(n=351.97690566805693+.114206453784165*(n=t-55)-40.25366309332127*Po(n),i=325.4494125711974+.07943456536662342*(i=t-50)-28.0852963507957*Po(i),s=255),[n,i,s,1]},{round:ux}=Math,hx=(...e)=>{const t=Rt(e,"rgb"),n=t[0],i=t[2];let s=1e3,a=4e4;const c=.4;let l;for(;a-s>c;){l=(a+s)*.5;const u=Nf(l);u[2]/u[0]>=i/n?a=l:s=l}return ux(l)};Z.prototype.temp=Z.prototype.kelvin=Z.prototype.temperature=function(){return hx(this._rgb)},St.temp=St.kelvin=St.temperature=(...e)=>new Z(...e,"temp"),wt.format.temp=wt.format.kelvin=wt.format.temperature=Nf;const{pow:Ro,sign:fx}=Math,$f=(...e)=>{e=Rt(e,"lab");const[t,n,i]=e,s=Ro(t+.3963377774*n+.2158037573*i,3),a=Ro(t-.1055613458*n-.0638541728*i,3),c=Ro(t-.0894841775*n-1.291485548*i,3);return[255*sl(4.0767416621*s-3.3077115913*a+.2309699292*c),255*sl(-1.2684380046*s+2.6097574011*a-.3413193965*c),255*sl(-.0041960863*s-.7034186147*a+1.707614701*c),e.length>3?e[3]:1]};function sl(e){const t=Math.abs(e);return t>.0031308?(fx(e)||1)*(1.055*Ro(t,1/2.4)-.055):e*12.92}const{cbrt:ol,pow:dx,sign:px}=Math,Wf=(...e)=>{const[t,n,i]=Rt(e,"rgb"),[s,a,c]=[al(t/255),al(n/255),al(i/255)],l=ol(.4122214708*s+.5363325363*a+.0514459929*c),u=ol(.2119034982*s+.6806995451*a+.1073969566*c),f=ol(.0883024619*s+.2817188376*a+.6299787005*c);return[.2104542553*l+.793617785*u-.0040720468*f,1.9779984951*l-2.428592205*u+.4505937099*f,.0259040371*l+.7827717662*u-.808675766*f]};function al(e){const t=Math.abs(e);return t<.04045?e/12.92:(px(e)||1)*dx((t+.055)/1.055,2.4)}Z.prototype.oklab=function(){return Wf(this._rgb)},St.oklab=(...e)=>new Z(...e,"oklab"),wt.format.oklab=$f,wt.autodetect.push({p:3,test:(...e)=>{if(e=Rt(e,"oklab"),Pt(e)==="array"&&e.length===3)return"oklab"}});const gx=(...e)=>{e=Rt(e,"lch");const[t,n,i]=e,[s,a,c]=Of(t,n,i),[l,u,f]=$f(s,a,c);return[l,u,f,e.length>3?e[3]:1]},yx=(...e)=>{const[t,n,i]=Rt(e,"rgb"),[s,a,c]=Wf(t,n,i);return Pf(s,a,c)};Z.prototype.oklch=function(){return yx(this._rgb)},St.oklch=(...e)=>new Z(...e,"oklch"),wt.format.oklch=gx,wt.autodetect.push({p:3,test:(...e)=>{if(e=Rt(e,"oklch"),Pt(e)==="array"&&e.length===3)return"oklch"}}),Z.prototype.alpha=function(e,t=!1){return e!==void 0&&Pt(e)==="number"?t?(this._rgb[3]=e,this):new Z([this._rgb[0],this._rgb[1],this._rgb[2],e],"rgb"):this._rgb[3]},Z.prototype.clipped=function(){return this._rgb._clipped||!1},Z.prototype.darken=function(e=1){const t=this,n=t.lab();return n[0]-=cn.Kn*e,new Z(n,"lab").alpha(t.alpha(),!0)},Z.prototype.brighten=function(e=1){return this.darken(-e)},Z.prototype.darker=Z.prototype.darken,Z.prototype.brighter=Z.prototype.brighten,Z.prototype.get=function(e){const[t,n]=e.split("."),i=this[t]();if(n){const s=t.indexOf(n)-(t.substr(0,2)==="ok"?2:0);if(s>-1)return i[s];throw new Error(`unknown channel ${n} in mode ${t}`)}else return i};const{pow:mx}=Math,xx=1e-7,_x=20;Z.prototype.luminance=function(e,t="rgb"){if(e!==void 0&&Pt(e)==="number"){if(e===0)return new Z([0,0,0,this._rgb[3]],"rgb");if(e===1)return new Z([255,255,255,this._rgb[3]],"rgb");let n=this.luminance(),i=_x;const s=(c,l)=>{const u=c.interpolate(l,.5,t),f=u.luminance();return Math.abs(e-f)<xx||!i--?u:f>e?s(c,u):s(u,l)},a=(n>e?s(new Z([0,0,0]),this):s(this,new Z([255,255,255]))).rgb();return new Z([...a,this._rgb[3]])}return vx(...this._rgb.slice(0,3))};const vx=(e,t,n)=>(e=cl(e),t=cl(t),n=cl(n),.2126*e+.7152*t+.0722*n),cl=e=>(e/=255,e<=.03928?e/12.92:mx((e+.055)/1.055,2.4)),je={},As=(e,t,n=.5,...i)=>{let s=i[0]||"lrgb";if(!je[s]&&!i.length&&(s=Object.keys(je)[0]),!je[s])throw new Error(`interpolation mode ${s} is not defined`);return Pt(e)!=="object"&&(e=new Z(e)),Pt(t)!=="object"&&(t=new Z(t)),je[s](e,t,n).alpha(e.alpha()+n*(t.alpha()-e.alpha()))};Z.prototype.mix=Z.prototype.interpolate=function(e,t=.5,...n){return As(this,e,t,...n)},Z.prototype.premultiply=function(e=!1){const t=this._rgb,n=t[3];return e?(this._rgb=[t[0]*n,t[1]*n,t[2]*n,n],this):new Z([t[0]*n,t[1]*n,t[2]*n,n],"rgb")},Z.prototype.saturate=function(e=1){const t=this,n=t.lch();return n[1]+=cn.Kn*e,n[1]<0&&(n[1]=0),new Z(n,"lch").alpha(t.alpha(),!0)},Z.prototype.desaturate=function(e=1){return this.saturate(-e)},Z.prototype.set=function(e,t,n=!1){const[i,s]=e.split("."),a=this[i]();if(s){const c=i.indexOf(s)-(i.substr(0,2)==="ok"?2:0);if(c>-1){if(Pt(t)=="string")switch(t.charAt(0)){case"+":a[c]+=+t;break;case"-":a[c]+=+t;break;case"*":a[c]*=+t.substr(1);break;case"/":a[c]/=+t.substr(1);break;default:a[c]=+t}else if(Pt(t)==="number")a[c]=t;else throw new Error("unsupported value for Color.set");const l=new Z(a,i);return n?(this._rgb=l._rgb,this):l}throw new Error(`unknown channel ${s} in mode ${i}`)}else return a},Z.prototype.tint=function(e=.5,...t){return As(this,"white",e,...t)},Z.prototype.shade=function(e=.5,...t){return As(this,"black",e,...t)};const bx=(e,t,n)=>{const i=e._rgb,s=t._rgb;return new Z(i[0]+n*(s[0]-i[0]),i[1]+n*(s[1]-i[1]),i[2]+n*(s[2]-i[2]),"rgb")};je.rgb=bx;const{sqrt:ll,pow:Ir}=Math,wx=(e,t,n)=>{const[i,s,a]=e._rgb,[c,l,u]=t._rgb;return new Z(ll(Ir(i,2)*(1-n)+Ir(c,2)*n),ll(Ir(s,2)*(1-n)+Ir(l,2)*n),ll(Ir(a,2)*(1-n)+Ir(u,2)*n),"rgb")};je.lrgb=wx;const kx=(e,t,n)=>{const i=e.lab(),s=t.lab();return new Z(i[0]+n*(s[0]-i[0]),i[1]+n*(s[1]-i[1]),i[2]+n*(s[2]-i[2]),"lab")};je.lab=kx;const Nr=(e,t,n,i)=>{let s,a;i==="hsl"?(s=e.hsl(),a=t.hsl()):i==="hsv"?(s=e.hsv(),a=t.hsv()):i==="hcg"?(s=e.hcg(),a=t.hcg()):i==="hsi"?(s=e.hsi(),a=t.hsi()):i==="lch"||i==="hcl"?(i="hcl",s=e.hcl(),a=t.hcl()):i==="oklch"&&(s=e.oklch().reverse(),a=t.oklch().reverse());let c,l,u,f,d,p;(i.substr(0,1)==="h"||i==="oklch")&&([c,u,d]=s,[l,f,p]=a);let y,m,_,x;return!isNaN(c)&&!isNaN(l)?(l>c&&l-c>180?x=l-(c+360):l<c&&c-l>180?x=l+360-c:x=l-c,m=c+n*x):isNaN(c)?isNaN(l)?m=Number.NaN:(m=l,(d==1||d==0)&&i!="hsv"&&(y=f)):(m=c,(p==1||p==0)&&i!="hsv"&&(y=u)),y===void 0&&(y=u+n*(f-u)),_=d+n*(p-d),i==="oklch"?new Z([_,y,m],i):new Z([m,y,_],i)},zf=(e,t,n)=>Nr(e,t,n,"lch");je.lch=zf,je.hcl=zf;const Sx=(e,t,n)=>{const i=e.num(),s=t.num();return new Z(i+n*(s-i),"num")};je.num=Sx;const Cx=(e,t,n)=>Nr(e,t,n,"hcg");je.hcg=Cx;const Ax=(e,t,n)=>Nr(e,t,n,"hsi");je.hsi=Ax;const Ex=(e,t,n)=>Nr(e,t,n,"hsl");je.hsl=Ex;const Mx=(e,t,n)=>Nr(e,t,n,"hsv");je.hsv=Mx;const Tx=(e,t,n)=>{const i=e.oklab(),s=t.oklab();return new Z(i[0]+n*(s[0]-i[0]),i[1]+n*(s[1]-i[1]),i[2]+n*(s[2]-i[2]),"oklab")};je.oklab=Tx;const Lx=(e,t,n)=>Nr(e,t,n,"oklch");je.oklch=Lx;const{pow:ul,sqrt:hl,PI:fl,cos:Ff,sin:Bf,atan2:Ox}=Math,Dx=(e,t="lrgb",n=null)=>{const i=e.length;n||(n=Array.from(new Array(i)).map(()=>1));const s=i/n.reduce(function(p,y){return p+y});if(n.forEach((p,y)=>{n[y]*=s}),e=e.map(p=>new Z(p)),t==="lrgb")return Px(e,n);const a=e.shift(),c=a.get(t),l=[];let u=0,f=0;for(let p=0;p<c.length;p++)if(c[p]=(c[p]||0)*n[0],l.push(isNaN(c[p])?0:n[0]),t.charAt(p)==="h"&&!isNaN(c[p])){const y=c[p]/180*fl;u+=Ff(y)*n[0],f+=Bf(y)*n[0]}let d=a.alpha()*n[0];e.forEach((p,y)=>{const m=p.get(t);d+=p.alpha()*n[y+1];for(let _=0;_<c.length;_++)if(!isNaN(m[_]))if(l[_]+=n[y+1],t.charAt(_)==="h"){const x=m[_]/180*fl;u+=Ff(x)*n[y+1],f+=Bf(x)*n[y+1]}else c[_]+=m[_]*n[y+1]});for(let p=0;p<c.length;p++)if(t.charAt(p)==="h"){let y=Ox(f/l[p],u/l[p])/fl*180;for(;y<0;)y+=360;for(;y>=360;)y-=360;c[p]=y}else c[p]=c[p]/l[p];return d/=i,new Z(c,t).alpha(d>.99999?1:d,!0)},Px=(e,t)=>{const n=e.length,i=[0,0,0,0];for(let s=0;s<e.length;s++){const a=e[s],c=t[s]/n,l=a._rgb;i[0]+=ul(l[0],2)*c,i[1]+=ul(l[1],2)*c,i[2]+=ul(l[2],2)*c,i[3]+=l[3]*c}return i[0]=hl(i[0]),i[1]=hl(i[1]),i[2]=hl(i[2]),i[3]>.9999999&&(i[3]=1),new Z(jc(i))},{pow:Rx}=Math;function Io(e){let t="rgb",n=St("#ccc"),i=0,s=[0,1],a=[],c=[0,0],l=!1,u=[],f=!1,d=0,p=1,y=!1,m={},_=!0,x=1;const b=function(O){if(O=O||["#fff","#000"],O&&Pt(O)==="string"&&St.brewer&&St.brewer[O.toLowerCase()]&&(O=St.brewer[O.toLowerCase()]),Pt(O)==="array"){O.length===1&&(O=[O[0],O[0]]),O=O.slice(0);for(let W=0;W<O.length;W++)O[W]=St(O[W]);a.length=0;for(let W=0;W<O.length;W++)a.push(W/(O.length-1))}return P(),u=O},w=function(O){if(l!=null){const W=l.length-1;let B=0;for(;B<W&&O>=l[B];)B++;return B-1}return 0};let C=O=>O,A=O=>O;const L=function(O,W){let B,H;if(W==null&&(W=!1),isNaN(O)||O===null)return n;W?H=O:l&&l.length>2?H=w(O)/(l.length-2):p!==d?H=(O-d)/(p-d):H=1,H=A(H),W||(H=C(H)),x!==1&&(H=Rx(H,x)),H=c[0]+H*(1-c[0]-c[1]),H=Dr(H,0,1);const Y=Math.floor(H*1e4);if(_&&m[Y])B=m[Y];else{if(Pt(u)==="array")for(let V=0;V<a.length;V++){const at=a[V];if(H<=at){B=u[V];break}if(H>=at&&V===a.length-1){B=u[V];break}if(H>at&&H<a[V+1]){H=(H-at)/(a[V+1]-at),B=St.interpolate(u[V],u[V+1],H,t);break}}else Pt(u)==="function"&&(B=u(H));_&&(m[Y]=B)}return B};var P=()=>m={};b(e);const R=function(O){const W=St(L(O));return f&&W[f]?W[f]():W};return R.classes=function(O){if(O!=null){if(Pt(O)==="array")l=O,s=[O[0],O[O.length-1]];else{const W=St.analyze(s);O===0?l=[W.min,W.max]:l=St.limits(W,"e",O)}return R}return l},R.domain=function(O){if(!arguments.length)return s;d=O[0],p=O[O.length-1],a=[];const W=u.length;if(O.length===W&&d!==p)for(let B of Array.from(O))a.push((B-d)/(p-d));else{for(let B=0;B<W;B++)a.push(B/(W-1));if(O.length>2){const B=O.map((Y,V)=>V/(O.length-1)),H=O.map(Y=>(Y-d)/(p-d));H.every((Y,V)=>B[V]===Y)||(A=Y=>{if(Y<=0||Y>=1)return Y;let V=0;for(;Y>=H[V+1];)V++;const at=(Y-H[V])/(H[V+1]-H[V]);return B[V]+at*(B[V+1]-B[V])})}}return s=[d,p],R},R.mode=function(O){return arguments.length?(t=O,P(),R):t},R.range=function(O,W){return b(O),R},R.out=function(O){return f=O,R},R.spread=function(O){return arguments.length?(i=O,R):i},R.correctLightness=function(O){return O==null&&(O=!0),y=O,P(),y?C=function(W){const B=L(0,!0).lab()[0],H=L(1,!0).lab()[0],Y=B>H;let V=L(W,!0).lab()[0];const at=B+(H-B)*W;let rt=V-at,ft=0,$t=1,Mt=20;for(;Math.abs(rt)>.01&&Mt-- >0;)(function(){return Y&&(rt*=-1),rt<0?(ft=W,W+=($t-W)*.5):($t=W,W+=(ft-W)*.5),V=L(W,!0).lab()[0],rt=V-at})();return W}:C=W=>W,R},R.padding=function(O){return O!=null?(Pt(O)==="number"&&(O=[O,O]),c=O,R):c},R.colors=function(O,W){arguments.length<2&&(W="hex");let B=[];if(arguments.length===0)B=u.slice(0);else if(O===1)B=[R(.5)];else if(O>1){const H=s[0],Y=s[1]-H;B=Ix(0,O).map(V=>R(H+V/(O-1)*Y))}else{e=[];let H=[];if(l&&l.length>2)for(let Y=1,V=l.length,at=1<=V;at?Y<V:Y>V;at?Y++:Y--)H.push((l[Y-1]+l[Y])*.5);else H=s;B=H.map(Y=>R(Y))}return St[W]&&(B=B.map(H=>H[W]())),B},R.cache=function(O){return O!=null?(_=O,R):_},R.gamma=function(O){return O!=null?(x=O,R):x},R.nodata=function(O){return O!=null?(n=St(O),R):n},R}function Ix(e,t,n){let i=[],s=e<t,a=t;for(let c=e;s?c<a:c>a;s?c++:c--)i.push(c);return i}const Nx=function(e){let t=[1,1];for(let n=1;n<e;n++){let i=[1];for(let s=1;s<=t.length;s++)i[s]=(t[s]||0)+t[s-1];t=i}return t},$x=function(e){let t,n,i,s;if(e=e.map(a=>new Z(a)),e.length===2)[n,i]=e.map(a=>a.lab()),t=function(a){const c=[0,1,2].map(l=>n[l]+a*(i[l]-n[l]));return new Z(c,"lab")};else if(e.length===3)[n,i,s]=e.map(a=>a.lab()),t=function(a){const c=[0,1,2].map(l=>(1-a)*(1-a)*n[l]+2*(1-a)*a*i[l]+a*a*s[l]);return new Z(c,"lab")};else if(e.length===4){let a;[n,i,s,a]=e.map(c=>c.lab()),t=function(c){const l=[0,1,2].map(u=>(1-c)*(1-c)*(1-c)*n[u]+3*(1-c)*(1-c)*c*i[u]+3*(1-c)*c*c*s[u]+c*c*c*a[u]);return new Z(l,"lab")}}else if(e.length>=5){let a,c,l;a=e.map(u=>u.lab()),l=e.length-1,c=Nx(l),t=function(u){const f=1-u,d=[0,1,2].map(p=>a.reduce((y,m,_)=>y+c[_]*f**(l-_)*u**_*m[p],0));return new Z(d,"lab")}}else throw new RangeError("No point in running bezier with only one color.");return t},Wx=e=>{const t=$x(e);return t.scale=()=>Io(t),t},jn=(e,t,n)=>{if(!jn[n])throw new Error("unknown blend mode "+n);return jn[n](e,t)},Ii=e=>(t,n)=>{const i=St(n).rgb(),s=St(t).rgb();return St.rgb(e(i,s))},Ni=e=>(t,n)=>{const i=[];return i[0]=e(t[0],n[0]),i[1]=e(t[1],n[1]),i[2]=e(t[2],n[2]),i},zx=e=>e,Fx=(e,t)=>e*t/255,Bx=(e,t)=>e>t?t:e,Gx=(e,t)=>e>t?e:t,Hx=(e,t)=>255*(1-(1-e/255)*(1-t/255)),Ux=(e,t)=>t<128?2*e*t/255:255*(1-2*(1-e/255)*(1-t/255)),Vx=(e,t)=>255*(1-(1-t/255)/(e/255)),jx=(e,t)=>e===255?255:(e=255*(t/255)/(1-e/255),e>255?255:e);jn.normal=Ii(Ni(zx)),jn.multiply=Ii(Ni(Fx)),jn.screen=Ii(Ni(Hx)),jn.overlay=Ii(Ni(Ux)),jn.darken=Ii(Ni(Bx)),jn.lighten=Ii(Ni(Gx)),jn.dodge=Ii(Ni(jx)),jn.burn=Ii(Ni(Vx));const{pow:qx,sin:Yx,cos:Xx}=Math;function Kx(e=300,t=-1.5,n=1,i=1,s=[0,1]){let a=0,c;Pt(s)==="array"?c=s[1]-s[0]:(c=0,s=[s,s]);const l=function(u){const f=pi*((e+120)/360+t*u),d=qx(s[0]+c*u,i),y=(a!==0?n[0]+u*a:n)*d*(1-d)/2,m=Xx(f),_=Yx(f),x=d+y*(-.14861*m+1.78277*_),b=d+y*(-.29227*m-.90649*_),w=d+y*(1.97294*m);return St(jc([x*255,b*255,w*255,1]))};return l.start=function(u){return u==null?e:(e=u,l)},l.rotations=function(u){return u==null?t:(t=u,l)},l.gamma=function(u){return u==null?i:(i=u,l)},l.hue=function(u){return u==null?n:(n=u,Pt(n)==="array"?(a=n[1]-n[0],a===0&&(n=n[1])):a=0,l)},l.lightness=function(u){return u==null?s:(Pt(u)==="array"?(s=u,c=u[1]-u[0]):(s=[u,u],c=0),l)},l.scale=()=>St.scale(l),l.hue(n),l}const Zx="0123456789abcdef",{floor:Jx,random:Qx}=Math,t_=()=>{let e="#";for(let t=0;t<6;t++)e+=Zx.charAt(Jx(Qx()*16));return new Z(e,"hex")},{log:Gf,pow:e_,floor:n_,abs:i_}=Math;function Hf(e,t=null){const n={min:Number.MAX_VALUE,max:Number.MAX_VALUE*-1,sum:0,values:[],count:0};return Pt(e)==="object"&&(e=Object.values(e)),e.forEach(i=>{t&&Pt(i)==="object"&&(i=i[t]),i!=null&&!isNaN(i)&&(n.values.push(i),n.sum+=i,i<n.min&&(n.min=i),i>n.max&&(n.max=i),n.count+=1)}),n.domain=[n.min,n.max],n.limits=(i,s)=>Uf(n,i,s),n}function Uf(e,t="equal",n=7){Pt(e)=="array"&&(e=Hf(e));const{min:i,max:s}=e,a=e.values.sort((l,u)=>l-u);if(n===1)return[i,s];const c=[];if(t.substr(0,1)==="c"&&(c.push(i),c.push(s)),t.substr(0,1)==="e"){c.push(i);for(let l=1;l<n;l++)c.push(i+l/n*(s-i));c.push(s)}else if(t.substr(0,1)==="l"){if(i<=0)throw new Error("Logarithmic scales are only possible for values > 0");const l=Math.LOG10E*Gf(i),u=Math.LOG10E*Gf(s);c.push(i);for(let f=1;f<n;f++)c.push(e_(10,l+f/n*(u-l)));c.push(s)}else if(t.substr(0,1)==="q"){c.push(i);for(let l=1;l<n;l++){const u=(a.length-1)*l/n,f=n_(u);if(f===u)c.push(a[f]);else{const d=u-f;c.push(a[f]*(1-d)+a[f+1]*d)}}c.push(s)}else if(t.substr(0,1)==="k"){let l;const u=a.length,f=new Array(u),d=new Array(n);let p=!0,y=0,m=null;m=[],m.push(i);for(let b=1;b<n;b++)m.push(i+b/n*(s-i));for(m.push(s);p;){for(let w=0;w<n;w++)d[w]=0;for(let w=0;w<u;w++){const C=a[w];let A=Number.MAX_VALUE,L;for(let P=0;P<n;P++){const R=i_(m[P]-C);R<A&&(A=R,L=P),d[L]++,f[w]=L}}const b=new Array(n);for(let w=0;w<n;w++)b[w]=null;for(let w=0;w<u;w++)l=f[w],b[l]===null?b[l]=a[w]:b[l]+=a[w];for(let w=0;w<n;w++)b[w]*=1/d[w];p=!1;for(let w=0;w<n;w++)if(b[w]!==m[w]){p=!0;break}m=b,y++,y>200&&(p=!1)}const _={};for(let b=0;b<n;b++)_[b]=[];for(let b=0;b<u;b++)l=f[b],_[l].push(a[b]);let x=[];for(let b=0;b<n;b++)x.push(_[b][0]),x.push(_[b][_[b].length-1]);x=x.sort((b,w)=>b-w),c.push(x[0]);for(let b=1;b<x.length;b+=2){const w=x[b];!isNaN(w)&&c.indexOf(w)===-1&&c.push(w)}}return c}const r_=(e,t)=>{e=new Z(e),t=new Z(t);const n=e.luminance(),i=t.luminance();return n>i?(n+.05)/(i+.05):(i+.05)/(n+.05)},{sqrt:gi,pow:Ce,min:s_,max:o_,atan2:Vf,abs:jf,cos:No,sin:qf,exp:a_,PI:Yf}=Math;function c_(e,t,n=1,i=1,s=1){var a=function(xt){return 360*xt/(2*Yf)},c=function(xt){return 2*Yf*xt/360};e=new Z(e),t=new Z(t);const[l,u,f]=Array.from(e.lab()),[d,p,y]=Array.from(t.lab()),m=(l+d)/2,_=gi(Ce(u,2)+Ce(f,2)),x=gi(Ce(p,2)+Ce(y,2)),b=(_+x)/2,w=.5*(1-gi(Ce(b,7)/(Ce(b,7)+Ce(25,7)))),C=u*(1+w),A=p*(1+w),L=gi(Ce(C,2)+Ce(f,2)),P=gi(Ce(A,2)+Ce(y,2)),R=(L+P)/2,O=a(Vf(f,C)),W=a(Vf(y,A)),B=O>=0?O:O+360,H=W>=0?W:W+360,Y=jf(B-H)>180?(B+H+360)/2:(B+H)/2,V=1-.17*No(c(Y-30))+.24*No(c(2*Y))+.32*No(c(3*Y+6))-.2*No(c(4*Y-63));let at=H-B;at=jf(at)<=180?at:H<=B?at+360:at-360,at=2*gi(L*P)*qf(c(at)/2);const rt=d-l,ft=P-L,$t=1+.015*Ce(m-50,2)/gi(20+Ce(m-50,2)),Mt=1+.045*R,Gt=1+.015*R*V,Qt=30*a_(-Ce((Y-275)/25,2)),nt=-(2*gi(Ce(R,7)/(Ce(R,7)+Ce(25,7))))*qf(2*c(Qt)),ot=gi(Ce(rt/(n*$t),2)+Ce(ft/(i*Mt),2)+Ce(at/(s*Gt),2)+nt*(ft/(i*Mt))*(at/(s*Gt)));return o_(0,s_(100,ot))}function l_(e,t,n="lab"){e=new Z(e),t=new Z(t);const i=e.get(n),s=t.get(n);let a=0;for(let c in i){const l=(i[c]||0)-(s[c]||0);a+=l*l}return Math.sqrt(a)}const u_=(...e)=>{try{return new Z(...e),!0}catch{return!1}},h_={cool(){return Io([St.hsl(180,1,.9),St.hsl(250,.7,.4)])},hot(){return Io(["#000","#f00","#ff0","#fff"]).mode("rgb")}},$o={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]};for(let e of Object.keys($o))$o[e.toLowerCase()]=$o[e];Object.assign(St,{average:Dx,bezier:Wx,blend:jn,cubehelix:Kx,mix:As,interpolate:As,random:t_,scale:Io,analyze:Hf,contrast:r_,deltaE:c_,distance:l_,limits:Uf,valid:u_,scales:h_,input:wt,colors:Rr,brewer:$o});const Wo=e=>(e.changedTouches&&([e]=e.changedTouches),{x:e.clientX,y:e.clientY});class f_{constructor(t,n){M(this,"_isMoving",!1);t.remember("_draggable",this),this.el=t,this.drag=this.drag.bind(this),this.startDrag=this.startDrag.bind(this),this.endDrag=this.endDrag.bind(this),this.enabled=n.enabled??!0,this.relative=n.relative??!1,this.init()}init(){this.enabled?(this.el.on("mousedown.drag",this.startDrag),this.el.on("touchstart.drag",this.startDrag,{passive:!1})):(this.el.off("mousedown.drag"),this.el.off("touchstart.drag"))}startDrag(t){const n=!t.type.indexOf("mouse");if(n&&t.which!==1&&t.buttons!==0||this.el.dispatch("beforedrag",{event:t,handler:this}).defaultPrevented)return;t.preventDefault(),t.stopPropagation(),this.enabled=!1,this.init(),this.box=this.el.bbox(),this.relative?this.lastClick=this.el.point(Wo(t)):this.lastClick=Wo(t);const i=(n?"mouseup":"touchend")+".drag";Ie(window,(n?"mousemove":"touchmove")+".drag",this.drag,this,{passive:!1}),Ie(window,i,this.endDrag,this,{passive:!1}),this.el.fire("dragstart",{event:t,handler:this,box:this.box})}drag(t){const{box:n,lastClick:i}=this;let s;s=this.relative?this.el.point(Wo(t)):Wo(t);const a=s.x-i.x,c=s.y-i.y;if(!a&&!c)return n;this._isMoving=!0;const l=n.x+a,u=n.y+c;this.box=new Re(l,u,n.w,n.h),this.lastClick=s,this.el.dispatch("dragmove",{event:t,handler:this,box:this.box}).defaultPrevented||this.move(l,u)}move(t,n){this.el.type==="svg"?So.prototype.move.call(this.el,t,n):this.el.move(t,n)}endDrag(t){this.drag(t),this._isMoving&&(this.el.fire("dragend",{event:t,handler:this,box:this.box}),this._isMoving=!1),On(window,"mousemove.drag"),On(window,"touchmove.drag"),On(window,"mouseup.drag"),On(window,"touchend.drag"),this.enabled=!0,this.init()}}Dt(Un,{draggable(e={}){return this.remember("_draggable")||new f_(this,e),this}});const qe="SET_ROOT_ACTION",Xf="ALIGNMENT_LINES_ADD",Kf="ALIGNMENT_LINES_DELETE",Zf="ALIGNMENT_LINES_SORT",Jf="ALIGNMENT_LINES_CLEAR",dl="SET_PREV_SELECTED_GROUP",ir="COMMON",Es="SIGMA_RECTANGLE_SHAPE",zo="SIGMA_BLOCK_SHAPE",Fo="SIGMA_TEXTLABEL_SHAPE",pl="SIGMA_ICON_SHAPE",Bo="SIGMA_IMAGE_SHAPE",Go="SIGMA_AREA_SHAPE",Ho="SIGMA_AUTOSCALING_SHAPE",Uo="SIGMA_AVAILABILITYZONE_SHAPE",rr="SIGMA_PRODUCT_SHAPE",pe="SIGMA_LINE_SHAPE",$r="SIGMA_CIRCLE_SHAPE",Vo="SIGMA_SECURITY_GROUP_SHAPE",jo="SIGMA_SUBNET_SHAPE",qo="SIGMA_VPC_SHAPE",Yo="SIGMA_SECURITY_SHAPE",Xo="SIGMA_BASE_GROUP_SHAPE",sr="SIGMA_REMARK_NOTE_SHAPE",Ko="SIGMA_CCN_SHAPE",Pn="SIGMA_TKE_SHAPE",vn=16,Ye=9.25,gl=18.5,or=64,$i=37,yl=.578125,At=22.5,ye=45,Fe=90,Wi="RENDER_CREATE",ml="RENDER_UPDATE",Qf="RENDER_REMOVE",ni=1.415,ii=[.707,.409,-.707,.409,0,0],qn="#006eff",xl="#006eff",td="#0052D9",ar="#0052D9",ee="SIGMA_GRAPH_MODE_2D",Q="SIGMA_GRAPH_MODE_3D",Zo="movable-points",cr="movable-tip",Wr="url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/5080ef5b-8d3a-4bc9-a8a8-6e22a1018447.svg), auto",zr=["component","alignmentLines","resizePoints","controlPoints","renderComponent","stickyUnits"],_l=["t0","t1","t2","t3","r0","r1","r2","r3","b0","b1","b2","b3","l0","l1","l2","l3"];function ed(){return _l.reduce((e,t)=>(e[t]={x:0,y:0},e),{})}const nd=[jo,qo,Vo,Xo],Qe=[...nd,Go,Ho,Uo,Ko,Pn],vl=[$r,pe],d_=[...vl,Es],p_=[...vl,Es],id=[...Qe],Fr={duration:150,when:"now",swing:!0},rd=`<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g class="tke-group-empty-icon-g">
<circle cx="40" cy="40" r="40" fill="#EBEFF5"/>
<rect opacity="0.01" x="16" y="16" width="48" height="48" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40 36.8774L60.999 49.0024V30.0024L40 17.8774L18.999 30.0024V49.0024L40 36.8774Z" fill="#C1CCDD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40 42.1226L60.999 29.9976V49.9976L40 62.1226L18.999 49.9976V29.9976L40 42.1226Z" fill="white"/>
<rect opacity="0.01" x="48.3335" y="44" width="20.2222" height="20" fill="white"/>
<ellipse cx="58.4445" cy="54" rx="7.07777" ry="7" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M58.4448 46C53.9778 46 50.356 49.582 50.356 54C50.356 58.418 53.9778 62 58.4448 62C62.9119 62 66.5337 58.418 66.5337 54C66.5337 49.582 62.9119 46 58.4448 46ZM57.4337 58H59.4559V56H57.4337V58ZM59.4559 55H57.4337V50H59.4559V55Z" fill="#006EFF"/>
</g>
</svg>
`,bl=["b2"],g_=[zo,rr],ri=".sigma",_e=7.5,Ae=30,y_={root:null,mode:localStorage.getItem("sigma_mode")||Q,scale:1,minScale:.1,maxScale:5,step:.1,w:0,h:0,doc:null,g:null,defs:null,viewBox:{x:0,y:0,w:0,h:0},container:{},core:null,alignments:{lines:new Map,sortX:[],sortY:[]},uneditable:!1,prevSelectdGroup:null,willClearWhenGroupDeleted:!1,connectionNumber:8},$=cf(function(e=y_,t){switch(t.type){case qe:return{...e,...t.value};case Xf:{const{value:n}=t,{alignments:i,...s}=e;return i.lines.set(n[0],n[1]),{...s,alignments:i}}case Kf:{const{alignments:n,...i}=e;return n.lines.delete(t.value),{...i,alignments:n}}case Zf:{const{alignments:n,...i}=e,{x:s,y:a}=t.value;return n.sortX=s,n.sortY=a,{...i,alignments:n}}case Jf:{const{alignments:n,...i}=e;return n.lines.clear(),n.sortX=[],n.sortY=[],{...i,alignments:n}}case dl:{const{value:n}=t,{prevSelectdGroup:i,...s}=e;return{...s,prevSelectdGroup:n}}default:return e}});function Yn(e=[]){let t="";for(let n=0;n<e.length;n++){const{x:i,y:s}=e[n];if(n===e.length-1){t+=`${i} ${s}`;break}t+=`${i} ${s}, `}return t}function ve(e,t={}){const{offsetX:n=0,offsetY:i=0,step:s=1}=t,a=3+s,c=Ye*s,l=4*c,u={x:e.x,y:e.y},f=e.x+8>>a,d=e.y%l,p=Math.round(d/c),y=(f-p)%2==0?0:-1,m=Math.floor((p+y)*c);return u.x=(f<<a)+n,u.y=~~(e.y/l)*l+m+i,{...e,...u}}function Te(e,t={}){const{offsetX:n=0,offsetY:i=0,step:s=1}=t,a=At*s,c=2*a,l={x:e.x,y:e.y},u=~~(e.x/c),f=e.x%c,d=Math.floor(Math.round(f/a)*a),p=~~(e.y/c),y=e.y%c,m=Math.floor(Math.round(y/a)*a);return l.x=u*c+d+n,l.y=p*c+m+i,l.x=Math.round(l.x/At)*At,l.y=Math.round(l.y/At)*At,{...e,...l}}function _t(e,t=!1,n=0,i=0){const{x:s,y:a}=e,c=s+a/yl,l=a-yl*s;return{x:(t?c/32*At:Math.floor(Math.round(c/32)*At))+n,y:(t?l/gl*At:Math.floor(Math.round(l/gl)*At))+i}}function dt(e,t=!1,n=0,i=0){const{x:s,y:a}=e,c=t?a/At:Math.round(a/At),l=t?s/At:Math.round(s/At);return{x:(l-c)*vn+n,y:(l+c)*Ye+i}}function wl(e=!1,t=!0,n="e",i={x:0,y:0}){const{x:s,y:a}=i,c=`${t&&t?e?"matrix(0.707, 0.409, 0, 0.816, 0, 0)":"matrix(0.707 0.409 -0.707 0.409 0 0)":""} translate(${-s} ${-a})`,l={e:"",s:"rotate(270)",n:"rotate(180)",w:"rotate(90)"};return l[n]===void 0&&console.error(`转换值错误，请检查设置的值: ${n}`),`${c} ${l[n]}`}function q(e={}){return e.value===null?e.default:e.value}function m_(e,t=0){const n={},i={},s={},a={};let c=-1/0,l=1/0,u=-1/0,f=1/0;const d=-.578125,p=yl;for(let _=0;_<e.length;_++){const x=e[_];for(let b=0;b<x.length;b++){const{x:w,y:C}=x[b],A=C-d*w,L=C-p*w;c=c<A?A:c,l=l>L?L:l,u=u<L?L:u,f=f>A?A:f}}const y=t*or*2,m=t*$i*2;return n.x=(l-c)/-1.15625,n.y=n.x*d+c,i.x=(f-l)/1.15625,i.y=i.x*p+l,s.x=(c-u)/1.15625,s.y=s.x*p+u,a.x=(u-f)/-1.15625,a.y=a.x*d+f,[{x:i.x,y:i.y-m},{x:n.x+y,y:n.y},{x:s.x,y:s.y+m},{x:a.x-y,y:a.y}].map(_=>ve(_))}function x_(e,t=0){let n=1/0,i=1/0,s=-1/0,a=-1/0;for(let l=0;l<e.length;l++){const u=e[l];for(let f=0;f<u.length;f++){const{x:d,y:p}=u[f];d<n&&(n=d),d>s&&(s=d),p<i&&(i=p),p>a&&(a=p)}}const c=ye*t*2;return n-=c,i-=c,s+=c,a+=c,Number.isFinite(n)&&Number.isFinite(i)&&Number.isFinite(s)&&Number.isFinite(a)?[Te({x:n,y:i}),Te({x:s,y:i}),Te({x:s,y:a}),Te({x:n,y:a})]:[]}function sd(e){const t=document.createElement("canvas");return t.width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0,e.width,e.height),t.toDataURL("image/png")}function od(e,t,n,i){const s={t0:{x:e,y:t,w:Ae,h:Ae},t2:{x:e+n/4*2,y:t,isMid:!0,w:_e,h:Ae},r0:{x:e+n,y:t,w:Ae,h:Ae},r2:{x:e+n,y:t+i/4*2,isMid:!0,w:Ae,h:_e},b0:{x:e+n,y:t+i,w:Ae,h:Ae},b2:{x:e+n/4*2,y:t+i,isMid:!0,w:_e,h:Ae},l0:{x:e,y:t+i,w:Ae,h:Ae},l2:{x:e,y:t+i/4*2,isMid:!0,w:Ae,h:_e}},a=n/2,c=i/2,l=Math.floor(a/_e)-1,u=Math.floor(c/_e)-1;for(let f=1;f<=l;f++){const d={x:f*_e+e,y:t,w:_e,h:Ae};s[`t0-${f}-t2`]=d}for(let f=1;f<=l;f++){const d={x:n+e-(f*_e+e),y:t,w:_e,h:Ae};s[`r0-${f}-t2`]=d}for(let f=1;f<=l;f++){const d={x:f*_e+e,y:t+i,w:_e,h:Ae};s[`l0-${f}-b2`]=d}for(let f=1;f<=l;f++){const d={x:n+e-(f*_e+e),y:t+i,w:_e,h:Ae};s[`b0-${f}-b2`]=d}for(let f=1;f<=u;f++){const d={x:e,y:f*_e+t,w:Ae,h:_e};s[`t0-${f}-l2`]=d}for(let f=1;f<=u;f++){const d={x:e,y:i+t-(f*_e+t),w:Ae,h:_e};s[`l0-${f}-l2`]=d}for(let f=1;f<=u;f++){const d={x:n+e,y:f*_e+t,w:Ae,h:_e};s[`r0-${f}-r2`]=d}for(let f=1;f<=u;f++){const d={x:n+e,y:i+t-(f*_e+t),w:Ae,h:_e};s[`b0-${f}-r2`]=d}return s}function Br(e,t,n,i){var d,p,y,m,_;const{width:s,height:a}=e;if(!s||!a)return;i||(i={x:-s/2,y:-a});const{x:c,y:l}=i;if(e.d2_connection=od(c,l,s,a),e.sticky){const{shapes:x}=$.getState().core.data,b=x[e.sticky];b!=null&&b.height&&bl.forEach(w=>{e.d2_connection[w].y+=b.height})}let u={};const{d2_connection:f}=e;if(t)if(n){const x=Object.keys(f);for(let b=0;b<x.length;b++)u[x[b]]={...dt(f["b"+b%4],!0),w:(d=f[x[b]])==null?void 0:d.w,h:(p=f[x[b]])==null?void 0:p.h,isMid:(y=f[x[b]])==null?void 0:y.isMid}}else for(const x in f)u[x]={...dt(f[x],!0),w:f[x].w,h:(m=f[x])==null?void 0:m.h,isMid:(_=f[x])==null?void 0:_.isMid};else u=f;e.connection=u}function kl(e){return!e.default&&!e.value?"":`<svg ${q(e).split("<svg ").pop()}`}function Gr(e){return e.reduce((t,{x:n,y:i})=>(t.push([n,i]),t),[])}function Ms(e){return!(vl.includes(e.type)||e.attach)}function Sl(e,t){var i;if(!e.attach||e.type!==Fo)return!1;const{shapes:n}=t.data;return((i=n[e.attach])==null?void 0:i.type)===pe}function ad(e,t){const{x:n,y:i,w:s,h:a}=e,{x:c,y:l,w:u,h:f}=t;return c>=n&&l>=i&&c+u<=n+s&&l+f<=i+a}const ur=class ur{constructor(){M(this,"col",4);M(this,"counter",[]);M(this,"instance",null)}setCol(t){typeof t=="number"&&(this.col=t)}getPosition(){return{col:Math.floor(this.counter.length/this.col),row:this.counter.length%this.col}}clear(){this.counter=[]}set(t){this.counter=[...t]}add(t){const n=this.counter.indexOf(null);if(p_.includes(t.type))return{col:0,row:0};let i;if(n===-1)i={col:Math.floor(this.counter.length/this.col),row:this.counter.length%this.col},this.counter.push(t.key);else{let s;this.counter.find((a,c)=>{c>n&&typeof a=="string"&&!s&&(s=a)}),i={col:Math.floor(n/this.col),row:n%this.col,__index:s},this.counter[n]=t.key}return i}remove(t){for(let n=0;n<t.length;n++){const i=this.counter.indexOf(t[n]);i!==-1&&(this.counter[i]=null)}}};M(ur,"getManager",()=>{if(ur.instance)return ur.instance;const t=new ur;return ur.instance=t,t});let si=ur;const __=si.getManager();class zi{constructor(){M(this,"key",null);M(this,"type",null);M(this,"category",null);M(this,"name",null);M(this,"cName",null);M(this,"label",null);M(this,"description",null);M(this,"lock",!1);M(this,"isAreaShape",!1);M(this,"is3DShape",!0);M(this,"isChecked",!0);M(this,"position",null);M(this,"positionOffsetX",0);M(this,"positionOffsetY",0);M(this,"zIndex",0);M(this,"gIndex",0);M(this,"editable",[]);M(this,"groups",{});M(this,"sticky",null);M(this,"styles",{});M(this,"connection",{});M(this,"d2_connection",{});M(this,"connectable",!1);M(this,"checkedable",!0);M(this,"linkable",!1);M(this,"component",null);M(this,"customize",{});M(this,"forever",!1);M(this,"width",0);M(this,"height",0);M(this,"_create",(t=void 0)=>{const{viewBox:{x:n,y:i},mode:s}=$.getState(),a=t??Eo(),{col:c,row:l,__index:u}=__.add({key:a,type:this.type});let f=this.position||{};return s===Q?(f.x=f.x||384+(c-l)*or+n,f.y=f.y||222+(c+l)*$i+i,f=ve(f)):(f.x=f.x||(1+c)*ye*2+n,f.y=f.y||(2+l)*ye*2+i,f=Te(f)),{key:a,type:this.type,category:this.category,name:this.name,cName:this.cName,label:this.label,sticky:this.sticky,description:this.description,groups:this.groups,lock:this.lock,editable:this.editable,isAreaShape:this.isAreaShape,is3DShape:this.is3DShape,isChecked:this.isChecked,position:f,positionOffsetX:this.positionOffsetX,positionOffsetY:this.positionOffsetY,positionSide:"center",zIndex:this.zIndex,gIndex:this.gIndex,styles:this.styles,connectable:this.connectable,connection:this.connection,d2_connection:this.d2_connection,checkedable:this.checkedable,linkable:this.linkable,customize:this.customize,component:null,width:this.width,height:this.height,forever:this.forever,__index:u}})}}class cd extends zi{constructor(){super(...arguments);M(this,"type",zo);M(this,"category",ir);M(this,"name","BLOCK");M(this,"cName","基础元素");M(this,"editable",["fill","width","height","depth"]);M(this,"gIndex",8);M(this,"styles",{staticStrokeColor:xl,fill:{name:"填充",type:"color",default:"#ececed",value:null},width:{name:"宽",type:"number",default:2,value:null},height:{name:"高",type:"number",default:1,value:null},depth:{name:"长",type:"number",default:2,value:null}});M(this,"connectable",!0);M(this,"linkable",!0);M(this,"data",{sideTop:[{x:0,y:0},{x:32,y:19},{x:0,y:37},{x:-32,y:19}],sideLeft:[{x:-32,y:56},{x:0,y:74},{x:0,y:37},{x:-32,y:19}],sideRight:[{x:0,y:74},{x:32,y:56},{x:32,y:19},{x:0,y:37}],sideOutline:[{x:0,y:0},{x:-32,y:19},{x:-32,y:56},{x:0,y:74},{x:32,y:56},{x:32,y:19}]});M(this,"create",()=>{const n=this._create();return n.data=this.data,lt.cloneDeep(n)})}}class Cl extends zi{constructor(){super(...arguments);M(this,"type",Fo);M(this,"name","TEXT LABEL");M(this,"cName","文本");M(this,"category",ir);M(this,"label","Click to Edit Text");M(this,"data",{});M(this,"gIndex",11);M(this,"editable",["label","outline","xAlign","fill","fontSize","is3d","route"]);M(this,"styles",{label:{name:"内容",type:"textarea",default:"Click to Edit Text",value:null},outline:{name:"描边",type:"boolean",default:!0,value:null},xAlign:{name:"水平对齐",type:"select",default:"center",value:null},yAlign:{name:"垂直对齐",type:"select",default:"middle",value:null},fill:{name:"颜色",type:"color",default:"#000000",value:null},fontSize:{name:"字号",type:"number",default:24,value:null},is3d:{name:"3D",type:"boolean",default:!0,value:null},isFlat:{name:"垂直",type:"boolean",default:!1,value:null},route:{name:"旋转",type:"select",default:"e",value:null}});M(this,"attach",null);M(this,"width",Fe);M(this,"height",ye);M(this,"connectable",!0);M(this,"positionPercent",NaN);M(this,"positionSide","center");M(this,"create",()=>{const n=this._create();return n.label=this.label,n.attach=this.attach,lt.cloneDeep(n)});M(this,"createLinkNode",({name:n,key:i})=>{const s=this._create();return s.styles.label.default=n,s.attach=i,s.positionPercent=this.positionPercent,lt.cloneDeep(s)})}}class ld extends zi{constructor(){super(...arguments);M(this,"type",pl);M(this,"category",ir);M(this,"name","PRODUCT ICON");M(this,"cName","产品图标");M(this,"description","");M(this,"connectable",!0);M(this,"linkable",!0);M(this,"gIndex",10);M(this,"data",{w:48,h:48});M(this,"editable",["icon","scale","is3d","isFlat","route"]);M(this,"styles",{icon:{name:"图标",type:"productIcon",default:`<svg  width="48" height="48" viewBox="0 0 48 48">
<g fill="#0052D9" fill-rule="evenodd">
  <path d="M36 28v2h-2v4h-4v2h-2v-2h-3v2h-2v-2h-3v2h-2l.2-2H14v-4h-2v-2h2v-3.033L12 25v-2l2 .033V20h-2v-2h2v-4h4v-2h2v2h3.033L23 12h2l-.033 2H28v-2h2v2h4v4h2v2h-2v3.033L36 23v2l-2-.033V28h2zm-4-12H16v16h16V16zm-3 13H19V19h10v10zm-2-8h-6v6h6v-6z"/>
  <path fill-rule="nonzero" d="M24.202 45C12.572 45 3.11 35.579 3.11 24S12.572 3 24.202 3c10.604 0 19.382 7.84 20.851 18H47.1C45.624 9.716 35.938 1 24.202 1 11.443 1 1.1 11.297 1.1 24s10.342 23 23.1 23c11.737 0 21.423-8.716 22.9-20h-2.047c-1.471 10.16-10.248 18-20.852 18z"/>
</g>
</svg>`,value:null},scale:{name:"缩放",type:"number",default:1,value:null},is3d:{name:"3D",type:"boolean",default:!0,value:null},isFlat:{name:"垂直",type:"boolean",default:!1,value:null},route:{name:"旋转",type:"select",default:"e",value:null}});M(this,"create",()=>{const n=this._create();return lt.cloneDeep(n)})}}class ud extends zi{constructor(){super(...arguments);M(this,"name","IMAGE");M(this,"cName","图片");M(this,"type",Bo);M(this,"data",{});M(this,"editable",["image","scale","is3d","isFlat","route"]);M(this,"gIndex",9);M(this,"styles",{scale:{name:"缩放",type:"number",default:1,value:null},route:{name:"旋转",type:"select",default:"e",value:null},image:{name:"图片",type:"file",default:"https://3dserver.diagram.woa.com/assets/statics/images/d90d3332-cbf1-4740-90fe-af6872dede6b.png",value:null},is3d:{name:"3D",type:"boolean",default:!0,value:null},isFlat:{name:"垂直",type:"boolean",default:!1,value:null}});M(this,"category",ir);M(this,"connectable",!0);M(this,"linkable",!0);M(this,"connection",null);M(this,"create",()=>{const n=this._create();return n.data=this.data,lt.cloneDeep(n)})}}class Al extends zi{constructor(){super(...arguments);M(this,"name","LINE");M(this,"cName","连线");M(this,"type",pe);M(this,"category",ir);M(this,"editable",["lineColor","line","lineWidth","lineStart","lineEnd","lineType"]);M(this,"gIndex",6);M(this,"styles",{lineColor:{name:"颜色",type:"color",default:"#888",value:null},line:{name:"线条",type:"select",default:"solid",value:null},lineStart:{name:"起点",type:"select",default:"line",value:null},lineEnd:{name:"终点",type:"select",default:"arrow",value:null},lineWidth:{name:"线宽",type:"number",default:2,value:null},lineType:{name:"类型",type:"select",default:"straight",value:null}});M(this,"data",{start:{type:"vnode",vkey:null,dir:"c"},end:{type:"mouse",dir:"c"},arrow:[{x:0,y:1},{x:-11,y:24},{x:0,y:18},{x:11,y:24}]});M(this,"create",(n,i)=>{const s=this._create();return s.isChecked=!1,s.data=this.data,s.data.start.vkey=n.key,s.data.end.type="mouse",s.data.end.x=i.x,s.data.end.y=i.y,lt.cloneDeep(s)});M(this,"createByVnode",({startKey:n,endKey:i,startDir:s,endDir:a,arrowType:c="end"})=>{const l=this._create();switch(l.isChecked=!1,l.data=this.data,l.data.start.vkey=n,l.data.end.type="vnode",l.data.end.vkey=i,c){case"none":l.styles.lineStart.value="line",l.styles.lineEnd.value="line";break;case"start":l.styles.lineStart.value="arrow",l.styles.lineEnd.value="line";break;case"end":l.styles.lineStart.value="line",l.styles.lineEnd.value="arrow";break;case"both":l.styles.lineStart.value="arrow",l.styles.lineEnd.value="arrow"}return s&&_l.includes(s)&&(l.data.start.dir=s,l.data.start.isFixed=!0),a&&_l.includes(a)&&(l.data.end.dir=a,l.data.end.isFixed=!0),lt.cloneDeep(l)})}}let El=null;const hd=(e,t)=>{const{root:n,uneditable:i,scale:s,root:a,viewBox:{x:c,y:l},mode:u}=$.getState(),f=navigator.userAgent.toLowerCase().indexOf("firefox")>-1;let d=document.getElementById("sigma-c-box");d||(d=document.createElement("div"),d.id="sigma-c-box",d.style="height: 0px; position: fixed; top: 0px; left: 0px; width: 100%; z-index: 901;",n.appendChild(d)),d.innerHTML="";const p=document.getElementById(t.key);if(!p)return;const y=p.getBoundingClientRect(),m=new Al,_=document.createElement("div");if(_.id="sigma-c-container",_.style.position="absolute",_.style.marginLeft="36px",f){const{x:C,y:A}=a.getBoundingClientRect();_.style.left=u===Q?`${C+(t.position.x-c)/s+y.width}px`:`${C+(t.position.x-c+t.width+30)/s}px`,_.style.top=u===Q?`${A+(t.position.y-l+60)/s}px`:`${A+(t.position.y-l+t.height/2)/s}px`,_.style.transform="translate(-50%, -50%)",Qe.includes(t.type)&&u===Q&&(_.style.left=`${C+(t.position.x-c)/s}px`)}else _.style.left=u===ee?`${y.x+y.width}px`:y.x+y.width-54/s+"px",_.style.top=i?y.y+y.height/2-30+"px":`${y.y+y.height/2}px`;const x=document.createElement("span");x.style="display: flex; align-items: center; transform: scale(1.2); justify-content: center; width: 36px; height:36px; cursor: pointer; background-color: #0052d9; border-radius: 100%; box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;",x.innerHTML=`<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="3" cy="3" r="3" transform="matrix(0.707107 0.707107 0.707107 -0.707107 9.27148 30.4853)" fill="white"/>
<path d="M31.1927 12.8076L13.7676 30.2327" stroke="white"/>
<path d="M32.6072 11.3934L29.0717 19.1716L28.1625 15.8381L24.829 14.9289L32.6072 11.3934Z" fill="white"/>
</svg>`,x.classList.add("sigma-c-arrow"),x.onclick=function(C){const{callbacks:A={}}=e.options;if(A.onCreateArrowLine){const Y=A.onCreateArrowLine(C,t);if(re(),!Y)return}C.stopPropagation();const L=m.create(t,t.component.point(C.pageX,C.pageY)),P=$.getState().mode===Q,{position:{x:R,y:O},connection:W,d2_connection:B}=t,H=P?W.r0:B.r2;L.data.start.x=R+H.x,L.data.start.y=O+H.y,e.add(L,{_historical:!1}),d.innerHTML="",e._setShapeChecked(L,!0,!1)};const b=document.createElement("span");b.style="margin-top: 20px; display: flex; align-items: center; transform: scale(1.2); justify-content: center; width: 36px; height:36px; cursor: pointer; background-color: #0052d9; border-radius: 100%; box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;",b.innerHTML=`<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30.8947 14.1026L23.5 24.117L20.579 24.117L14.5 32" stroke="white"/>
<circle cx="3" cy="3" r="3" transform="matrix(0.819152 0.573576 0.573576 -0.819152 10.5352 32.5349)" fill="white"/>
<path d="M32.3533 11.7324L30.1746 19.9939L28.7184 16.8605L25.2795 16.5243L32.3533 11.7324Z" fill="white"/>
</svg>`,b.classList.add("sigma-c-polyline"),b.onclick=function(C){const{callbacks:A={}}=e.options;if(A.onCreatePolyline){const Y=A.onCreatePolyline(C,t);if(re(),!Y)return}C.stopPropagation();const L=m.create(t,t.component.point(C.pageX,C.pageY));L.styles.lineType.value="polyline";const P=$.getState().mode===Q,{position:{x:R,y:O},connection:W,d2_connection:B}=t,H=P?W.r0:B.r2;L.data.start.x=R+H.x,L.data.start.y=O+H.y,e.add(L,{_historical:!1}),d.innerHTML="",e._setShapeChecked(L,!0,!1)};let w=[];if(i||(w=[x,b]),t.linkable){const C=document.createElement("span");C.style="display: flex; align-items: center; transform: scale(1.2); justify-content: center; width: 36px; height: 36px; cursor: pointer;margin-top: 20px; background-color: #0052d9; border-radius: 100%; box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;",C.innerHTML=`<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 1025 1024">
      <path fill="#ffffff" d="M984.38 219.267l-95.092 95.091-183.414-177.794 97.902-97.901a127.704 127.704 0 1 1 180.603 180.604zM833.6 369.982L248.936 954.645 0 1024l68.27-249.895 582.49-582.491L833.6 369.982z" />
    </svg>
    `,C.classList.add("sigma-c-label"),C.onclick=function(A){const{callbacks:L={}}=e.options;if(L.onCreateLabel){const P=L.onCreateLabel(A,t);if(re(),!P)return}if(A.stopPropagation(),t.sticky){const P=e.data.shapes[t.sticky];P&&(e.onlyShapeChecked(P),d.innerHTML="")}else e._createNodeLabel(t),d.innerHTML=""},w.push(C)}d.appendChild(_),_.append(...w),El=t},re=()=>{const e=document.getElementById("sigma-c-box");e&&e.innerHTML!==""&&(e.innerHTML=""),El=null};function v_(e,t,n){const{shapes:i}=e.data,{relations:s={},styles:a}=t,c=q(a.padding),l=[],{x:u,y:f}=function(y,m){const _=ye*y*2,x={x:_,y:_};return m?dt(x):x}(c,n),d=n?m_:x_,p=Object.keys(s);if(p.length>0)for(const y of p)i[y]&&l.push(Ne.getflat(i[y]));if(!p.length||!l.length){const{x:y,y:m}=t.position;l.push([...new Array(4)].map(()=>({x:y+u,y:m+f})))}return d(l,c)}function fd(e,t,n){const i=v_(e,t,n),{width:s,height:a}=function(c,l){const[u,,f]=c,d=f.x-u.x,p=f.y-u.y;if(l){const{x:y,y:m}=_t({x:d,y:p});return{width:y,height:m}}return{width:d,height:p}}(i,n);t.position.x=i[0].x,t.position.y=i[0].y,t.width=Math.max(s,180),t.height=Math.max(a,180)}function dd(e,t,n){if(Object.keys(t.groups).length===0)return!1;for(const i in t.groups)if(i===n.key||dd(e,e[i],n))return!0;return!1}function b_(e,t){if(!e||!t)return!1;const n=Object.keys(e.groups),i=Object.keys(t.groups);return n.length===0&&i.length===0||n.length!==0&&i.length!==0&&n.some(s=>i.includes(s))}const pd="resize-points",Fi=["top","top-right","right","right-bottom","bottom","bottom-left","left","left-top"];function Ml(e,t,n,i={x:0,y:0}){const{x:s,y:a}=i,c={[Fi[0]]:{position:{x:e/2,y:0},cursor:{[ee]:"n-resize",[Q]:"ne-resize"}},[Fi[1]]:{position:{x:e,y:0},cursor:{[ee]:"ne-resize",[Q]:"e-resize"}},[Fi[2]]:{position:{x:e,y:t/2},cursor:{[ee]:"e-resize",[Q]:"se-resize"}},[Fi[3]]:{position:{x:e,y:t},cursor:{[ee]:"se-resize",[Q]:"s-resize"}},[Fi[4]]:{position:{x:e/2,y:t},cursor:{[ee]:"s-resize",[Q]:"sw-resize"}},[Fi[5]]:{position:{x:0,y:t},cursor:{[ee]:"sw-resize",[Q]:"w-resize"}},[Fi[6]]:{position:{x:0,y:t/2},cursor:{[ee]:"w-resize",[Q]:"nw-resize"}},[Fi[7]]:{position:{x:0,y:0},cursor:{[ee]:"nw-resize",[Q]:"n-resize"}}};for(const l in c){const u=c[l];u.position.x+=s,u.position.y+=a,n&&(u.position=dt(u.position,!0))}return c}function gd(e){const t=$.getState().mode===Q,{data:n,styles:i}=e,{points:s}=n,a=q(i.borderRadius),c={"left-top":[{x:0,y:a},{x:a,y:0}],"top-right":[{x:-a,y:0},{x:0,y:a}],"right-bottom":[{x:0,y:-a},{x:-a,y:0}],"bottom-left":[{x:a,y:0},{x:0,y:-a}]};return Object.entries(s).reduce((l,[u,{position:{x:f,y:d}}])=>{if(c[u]){const[p,y]=c[u],m=t?dt(p,!0):p,_=t?dt(y,!0):y;l+=`${f+m.x},${d+m.y} ${f+_.x},${d+_.y} `}return l},"")}class yd{constructor(t){M(this,"copyHandle",t=>{const n=window.getSelection().toString(),i=this.textdiv.innerText||n;t.preventDefault(),t.clipboardData.setData("text/plain",i)});M(this,"handle",t=>{this.textdiv.contains(t.target)||(this.onblur(this.textdiv),document.removeEventListener("mousedown",this.handle),this.textdiv.setAttribute("contentEditable","false"),this.textdiv.removeEventListener("mousemove",this.mouseHandle),this.textdiv.removeEventListener("mousedown",this.mouseHandle),this.textdiv.removeEventListener("mouseup",this.mouseHandle),this.textdiv.removeEventListener("copy",this.copyHandle))});M(this,"mouseHandle",t=>{t.stopPropagation()});M(this,"dblHandle",()=>{if(!$.getState().uneditable){if(this.isSafari&&document.addEventListener("mousedown",this.handle),!$.getState().uneditable){this.textdiv.setAttribute("contentEditable","true");const t=document.createRange();t.selectNodeContents(this.textdiv);const n=window.getSelection();n.removeAllRanges(),n.addRange(t)}this.ondblclick&&this.ondblclick(this.textdiv),this.isInline&&(this.textdiv.style.width="auto"),this.textdiv.addEventListener("mousemove",this.mouseHandle),this.textdiv.addEventListener("mousedown",this.mouseHandle),this.textdiv.addEventListener("mouseup",this.mouseHandle),this.textdiv.addEventListener("copy",this.copyHandle)}});this.myforeign=null,this.textdiv=null,this.wrapper=null,this.startEditFlag=!1,this.init(t)}init(t){const{maxLength:n,wrapper:i,text:s,innerHTML:a,width:c,height:l,initStyles:u={},initForeignStyles:f={},initAttrs:d={},ondblclick:p,onblur:y,dblClickDom:m,isInline:_=!1,isHidden:x=!1,isEnterSubmit:b=!1,onSave:w}=t,C=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);this.isSafari=C,this.myforeign=i.foreignObject(c,l),this.textdiv=document.createElement("div"),this.ondblclick=p,this.isInline=_,this.onblur=y,this.onSave=w,s&&(this.textdiv.innerText=s,this.textdiv.title=s),a&&(this.textdiv.innerHTML=a,this.textdiv.title=a),this.myforeign.node.appendChild(this.textdiv),i.node.appendChild(this.myforeign.node),this.wrapper=i,this.myforeign.node.style.overflow=x?"hidden":"visible",this.myforeign.node.style.position="relative",this.textdiv.setAttribute("contentEditable","false"),this.textdiv.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),this.textdiv.style.whiteSpace="nowrap",this.textdiv.style.outline="none",this.setTextStyle(u),this.setforeignAttr(d),this.setforeignStyle({yAlign:u==null?void 0:u.yAlign,...f}),this.dblClickDom=m,this.textdiv.addEventListener("keydown",A=>{A.stopPropagation(),A.key==="Enter"&&b&&(y(this.textdiv),this.textdiv.setAttribute("contentEditable","false"),this.textdiv.removeEventListener("mousemove",this.mouseHandle),this.textdiv.removeEventListener("mousedown",this.mouseHandle),this.textdiv.removeEventListener("mouseup",this.mouseHandle),this.textdiv.removeEventListener("copy",this.copyHandle),this.onSave(),this.dblClickDom&&this.dblClickDom.fire("end-edit"))}),this.textdiv.onmouseenter=()=>{$.getState().uneditable&&this.textdiv.setAttribute("contentEditable","false")},this.dblClickDom?this.dblClickDom.on("dblclick",this.dblHandle):this.textdiv.ondblclick=this.dblHandle,this.textdiv.oninput=()=>{n&&this.textdiv.innerText.length>=n&&(y(this.textdiv),this.textdiv.setAttribute("contentEditable","false"))},this.textdiv.onblur=()=>{this.startEditFlag||(y(this.textdiv),this.textdiv.title=this.textdiv.innerText,this.textdiv.setAttribute("contentEditable","false"),this.textdiv.removeEventListener("mousemove",this.mouseHandle),this.textdiv.removeEventListener("mousedown",this.mouseHandle),this.textdiv.removeEventListener("mouseup",this.mouseHandle),this.textdiv.removeEventListener("copy",this.copyHandle),this.onSave(),this.dblClickDom&&this.dblClickDom.fire("end-edit"))}}setTextStyle(t){Object.keys(t).forEach(n=>{this.textdiv.style[n]=t[n]})}setforeignAttr(t){Object.keys(t).forEach(n=>{this.myforeign.node.setAttribute(n,t[n])})}setforeignStyle(t){const{yAlign:n,...i}=t;Object.keys(i).forEach(s=>{this.myforeign.node.style[s]=i[s]}),n&&!this.isSafari&&this.setTextYAlign({value:n})}getText(){return this.textdiv.innerText}setText(t){this.textdiv.innerText=t}setInnerHtml(t){this.textdiv.innerHTML=t}setTextYAlign(t){if(this.isSafari)return;const{value:n}=t;this.textdiv.style.position="absolute",this.textdiv.style.removeProperty("bottom"),this.textdiv.style.removeProperty("top"),this.textdiv.style.removeProperty("transform"),n==="top"&&(this.textdiv.style.height="auto",this.textdiv.style.top="0%"),n==="bottom"&&(this.textdiv.style.height="auto",this.textdiv.style.bottom="0%"),n==="center"&&(this.textdiv.style.height="auto",this.textdiv.style.top="50%",this.textdiv.style.transform="translateY(-50%)")}startEdit(){if(/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&document.addEventListener("mousedown",this.handle),!$.getState().uneditable){this.textdiv.setAttribute("contentEditable","true");const t=document.createRange();t.selectNodeContents(this.textdiv);const n=window.getSelection();n.removeAllRanges(),n.addRange(t)}ondblclick&&ondblclick(),this.textdiv.addEventListener("mousemove",this.mouseHandle),this.textdiv.addEventListener("mousedown",this.mouseHandle),this.textdiv.addEventListener("mouseup",this.mouseHandle),this.textdiv.addEventListener("copy",this.copyHandle),this.textdiv.focus(),this.startEditFlag=!0,setTimeout(()=>{this.startEditFlag=!1,this.textdiv.focus();const t=document.createRange();t.selectNodeContents(this.textdiv),t.collapse(!1);const n=window.getSelection();n.removeAllRanges(),n.addRange(t)},500)}}class w_{constructor(t){this.vnode=t,this.elements={},this.editTextLabel=null,this.core=$.getState().core,this.resetText="",this.textLabel=null,this._initStyles()}_initStyles(){const{styles:t}=this.vnode,n=kl(t.icon),i=q(t.iconSize),s=q(t.label),a=q(t.xAlign),c=q(t.yAlign),l=q(t.location),u=q(t.position),f=q(t.fontSize),d=q(t.color);this._styles={icon:n,iconSize:i,label:s,xAlign:a,yAlign:c,location:l,position:u,fontSize:f,color:d}}create(t,n){const{elements:i}=this;this.elements.svg=t,i.head=new ht().attr({class:"head",style:"overflow: auto;"}),t.add(i.head),i.divide=this._createDivide(),i.head.add(i.divide),i.headWrapper=i.head.group().attr({class:"head-wrapper"}),this._styles.icon&&(i.icon=this._createIcon(),i.headWrapper.add(i.icon)),this._createLabel(n),this.resetText=this._styles.label.replace(/\n/g,`
`),queueMicrotask(()=>{this._setHeadPosition()})}_createIcon(){const{icon:t,iconSize:n}=this._styles;return new ht(t).attr({class:"icon",width:n,height:n})}_createLabel(t){var p,y;const{label:n,iconSize:i,icon:s,fontSize:a,color:c,xAlign:l,yAlign:u}=this._styles,f={height:"100%",width:"100%",display:"inline-block",fontSize:`${a}px`,color:c,fontWeight:"bold",lineHeight:`${i}px`,textAlign:l,verticalAlign:"super",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",yAlign:u||"top"},d={display:"block",height:"max-content",width:"100%",fontSize:`${a}px`,color:c,fontWeight:"bold",lineHeight:1.3,overflow:"hidden",whiteSpace:"pre-wrap",wordWrap:"break-word",textAlign:l,yAlign:u||"top"};if(!this.textLabel){let m=n==null?void 0:n.replace(/\n/g,`
`);t!=null&&t.isExport&&(m=(y=(p=n==null?void 0:n.replace(/\n/g,`
`))==null?void 0:p.replace(/\s+/g," "))==null?void 0:y.replace(/nbsp;+/g," "));const _=new yd({wrapper:this.elements.headWrapper,width:t==null?void 0:t.boxWidth,height:t==null?void 0:t.boxHeight,innerHTML:m,initStyles:t.isBlock?d:f,maxLength:t.isBlock?void 0:128,isInline:!t.isBlock,isHidden:t.isBlock,onblur:x=>{var b;x.innerText.length<1?(this.core.setStyles({label:{value:this.resetText}},this.vnode.key),x.innerText=this.resetText):this.core.setStyles({label:{value:(b=x.innerText)==null?void 0:b.replace(/\n/g,`
`)}},this.vnode.key),t.isBlock||(x.style.width="100%")},onSave:()=>{var x,b,w,C;(C=(w=(b=(x=this.core)==null?void 0:x.options)==null?void 0:b.callbacks)==null?void 0:w.onDbClickSaveText)==null||C.call(w,this.vnode)}});this.textLabel=_,s&&this.textLabel.setforeignAttr(this._filterWidthIcon(s,{x:i+8}))}}_createDivide(){return new Mr({stroke:"#fff","stroke-width":4,opacity:0})}_setHeadPosition(){const{mode:t}=$.getState(),n=t===Q,{elements:i,vnode:s,_styles:a}=this,{iconSize:c,xAlign:l,location:u,position:f}=a;n&&i.headWrapper.attr({transform:""});const{w:d,h:p}=i.headWrapper.bbox();f==="border"?i.divide.attr({x1:-4,y1:p/2,x2:d+4,y2:p/2,opacity:1,transform:n?`matrix(${ii.join(" ")})`:""}):i.divide.attr({opacity:0});const{x:y,y:m,transform:_}=function({boxW:x,boxH:b,childW:w,childH:C,xAlign:A="left",position:L="inside",location:P="top",is3D:R}){let O,W,B="";switch(A){case"left":default:O=ci;break;case"center":O=(x-w)/2;break;case"right":O=x-ci-w}switch(P){case"top":switch(W=ci,L){case"inside":case"default":W=ci;break;case"border":W=-C/2;break;case"outside":W=-C-ci}break;case"bottom":switch(W=b-ci-C,L){case"inside":W=b-ci-C;break;case"border":W=b-C/2;break;case"outside":W=b+ci;break;case"default":W=ci}break;default:W=ci}if(R){const H=dt({x:O,y:W},!0);O=H.x,W=H.y,B+=`matrix(${ii.join(" ")}) `}return{x:O,y:W,transform:B}}({boxW:s.width,boxH:s.height,childW:d,childH:p,iconSize:c,xAlign:l,position:f,location:u,is3D:n});i.head.x(y).y(m),i.headWrapper.attr({transform:_})}_filterWidthIcon(t,n){return t?n:{}}update(t={},n){var c,l;const{_styles:i,elements:s}=this,{icon:a}=s;if(t.iconSize&&(i.iconSize=q(t.iconSize),i.icon&&(s.icon.attr({width:i.iconSize,height:i.iconSize}),this.textLabel.setforeignAttr(this._filterWidthIcon(a,{height:i.iconSize,x:i.iconSize+8})),this.textLabel.setTextStyle({lineHeight:`${i.iconSize}px`}))),t.fontSize){const u=q(t.fontSize);this.textLabel.setTextStyle({fontSize:`${u}px`,lineHeight:`${u}px`}),a&&s.icon.attr({width:u,height:u}),this.textLabel.setforeignAttr(this._filterWidthIcon(a,{height:u,x:u+8})),this.vnode.styles.iconSize.value=u}if(t.color&&this.textLabel.setTextStyle({color:q(t.color)}),t.icon)if(i.icon=kl(t.icon),(c=s.icon)==null||c.remove(),i.icon){const u=this._createIcon();u.insertBefore(this.textLabel.myforeign),s.icon=u,this.textLabel.setforeignAttr(this._filterWidthIcon(a,{x:i.iconSize+8}))}else s.icon=null,this.textLabel.setforeignAttr(this._filterWidthIcon(a,{x:8}));t.label&&(i.label=q(t.label),this._createLabel(n),this.textLabel.setInnerHtml((l=i.label)==null?void 0:l.replace(/\n/g,`
`))),t.xAlign&&(i.xAlign=q(t.xAlign),n&&this.textLabel.setTextStyle({textAlign:i.xAlign})),t!=null&&t.yAlign&&(i.yAlign=q(t.yAlign),n&&this.textLabel.setTextYAlign(t.yAlign)),t.location&&(i.location=q(t.location)),t.position&&(i.position=q(t.position)),n&&!t.fontSize&&this.textLabel.setforeignAttr({width:n.boxWidth,height:n.boxHeight}),this._setHeadPosition()}}const k_=si.getManager(),md=(e,t,n)=>{const{key:i,position:{x:s,y:a},width:c,height:l}=t,u=Q===n;let f,d,p;const y=new ht().group().attr({key:i,id:i}),m={x:0,y:0},_=new w_(t);return y.on("style-changed",x=>{var at,rt,ft,$t,Mt,Gt,Qt,et;const{styles:b,data:w,initial:C,pure:A,isExport:L}=x.detail;if(!w&&!C)return;const P=q(b.fill),R=q(b.stroke),O=q(b.strokeWidth),W=q(b.strokeStyle),B=q(b.showTips),H=80*c/180,Y=80*l/180,V={x:(c-H)/2,y:(l-Y)/2};if(C){t.data.points=Ml(c,l,u);const nt=new ht("<svg></svg>").attr({x:s,y:a,overflow:"visible",class:"position"}),ot=nt.group().attr({class:"body"});if(ot.polygon(gd(t)).attr({class:"main"}),p=ot,(t==null?void 0:t.type)===Pn&&B){f=new ht(rd).attr({class:"state-icon",width:H,height:Y,x:u?dt(V).x:V.x,y:u?dt(V).y:V.y}),f.findOne(".tke-group-empty-icon-g").attr({transform:u?`matrix(${ii.join(" ")})`:""}),d=ot.text("未绑定集群").attr({color:"#6E829C",fontSize:"14px",fontWeight:700,transform:u?`matrix(${ii.join(" ")})`:""});const xt=d.bbox().width,Vt=d.bbox().height,Tt=(c-xt)/2,Wt=(l-Vt)/2+Y/2+15;d.move(Tt,Wt),ot.add(f)}if(t.type===sr)_.create(nt,{boxWidth:t.width-12,boxHeight:t.height-12,isBlock:!0,isExport:L});else{const xt=((rt=(at=t==null?void 0:t.styles)==null?void 0:at.iconSize)==null?void 0:rt.value)||(($t=(ft=t==null?void 0:t.styles)==null?void 0:ft.iconSize)==null?void 0:$t.default);_.create(nt,{boxWidth:t.width-xt-8-8-6,boxHeight:xt,isBlock:!1,isExport:L})}nt.group().attr({class:"rect-movable"}),y.add(nt),y.fire("size-change",{initial:!0,data:w,pure:A})}else{if(t.type===sr)_.update(w,{boxWidth:t.width-12,boxHeight:t.height-12,isBlock:!0});else{const nt=((Gt=(Mt=t==null?void 0:t.styles)==null?void 0:Mt.iconSize)==null?void 0:Gt.value)||((et=(Qt=t==null?void 0:t.styles)==null?void 0:Qt.iconSize)==null?void 0:et.default);_.update(w,{boxWidth:t.width-nt-8-8-6,boxHeight:nt,isBlock:!1})}if((t==null?void 0:t.type)===Pn)if(B){if(f&&d)return;f=new ht(rd).attr({x:u?dt(V).x:V.x,y:u?dt(V).y:V.y,width:H,height:Y,class:"state-icon"}),f.findOne(".tke-group-empty-icon-g").attr({transform:u?`matrix(${ii.join(" ")})`:""}),d=p.text("未绑定集群").attr({color:"#6E829C",fontSize:"14px",fontWeight:700,transform:u?`matrix(${ii.join(" ")})`:""});const nt=d.bbox().width,ot=d.bbox().height,xt=(c-nt)/2,Vt=(l-ot)/2+Y/2+15;d.move(xt,Vt),p.add(f)}else f==null||f.remove(),d==null||d.remove(),f=void 0,d=void 0}y.findOne(".main").attr({fill:P,stroke:R,"stroke-width":O,"stroke-dasharray":W==="solid"?null:"4,4"})}),y.on("size-change",x=>{var P,R,O,W;const{mode:b}=$.getState(),w=Q===b,{initial:C=!1,willUpdate:A=!0,animating:L=!1}=x.detail||{};if(Br(t,w,!1,m),e.lines.add(t,w,!1,m),!C){const B=y.findOne(".main"),{width:H,height:Y}=t;if(t.data.points=Ml(H,Y,w),(L?B.animate(Fr):B).attr({points:gd(t)}),f&&d){const V=80*H/180,at=80*Y/180,rt={x:(H-V)/2,y:(Y-at)/2};f.attr({x:w?dt(rt).x:rt.x,y:w?dt(rt).y:rt.y,width:V,height:at});const ft=(H-d.bbox().width)/2,$t=(Y-d.bbox().height)/2+at/2+15;d.move(ft,$t)}if(e.lines.draw(t.key),t.type===sr)_.update(void 0,{boxWidth:t.width-12,boxHeight:t.height-12,isBlock:!0});else{const V=((R=(P=t==null?void 0:t.styles)==null?void 0:P.iconSize)==null?void 0:R.value)||((W=(O=t==null?void 0:t.styles)==null?void 0:O.iconSize)==null?void 0:W.default);_.update(void 0,{boxWidth:t.width-V-8-8-6,boxHeight:V,isBlock:!1})}}ua(t,C,e),!C&&A&&e.update([t])}),y.on("editable-change",()=>{ua(t,void 0,e)}),y.on("position-change",x=>{k_.remove([t.key]);const{animating:b=!1}=x.detail||{},{position:w}=t,{x:C,y:A}=w,{mode:L}=$.getState(),P=Q===L,R=y.findOne(".position");(b?R.animate(Fr):R).attr({x:C,y:A}),e.lines.add(t,P,!1,m),e.lines.draw(t.key)}),y.on("checked-change",()=>{var w,C;const{isChecked:x,data:b}=t;x&&b.canResize?(w=t.resizePoints)==null||w.show():(C=t.resizePoints)==null||C.hide(),y.findOne(".main").css({filter:x?"drop-shadow( 0 0 6px hsla(0, 0%, 0%, 0.2))":"none"})}),t.component=y,y};var xd={render:md,rerender:()=>{},exports:async(e,t,n)=>md(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e),getflat:e=>{const{resizePoints:t,position:{x:n,y:i}}=e,{map:s}=t,a=[];for(const c in s){if(!/-/.test(c))continue;const{position:l}=s[c];a.push({x:n+l.x,y:i+l.y})}return a}};function Ft(e,t=!1){if(t)return Object.keys(e).reduce((i,s)=>(i[s]=Ft(e[s]),i),{});if(Array.isArray(e))return e.map(i=>Ft(i));const n={};for(const i in e)zr.includes(i)||(n[i]=e[i]);return We.cloneDeep(n)}const _d=(e,t,n)=>{const i=n===Q,{styles:s}=t,a=q(s.geometry);t.relations||(t.relations={}),a==="manual"&&(t.data.canResize=!0),a==="rectangular"&&(t.data.canResize=!1,fd(e,t,i));const c=xd.render(e,t,n);return c.on("style-changed",l=>{const{initial:u=!1,data:f={},styles:d}=l.detail,p=q(d.geometry);u?(t.resizePoints=null,setTimeout(()=>{ua(t,void 0,e)},0)):(f.geometry&&(p==="manual"&&(t.data.canResize=!0),p==="rectangular"&&(t.data.canResize=!1,c.fire("relations-change",{isEnd:!1})),setTimeout(()=>{ua(t,void 0,e)},0)),f.padding&&p==="rectangular"&&c.fire("relations-change",{isEnd:!1}))}),c.on("relations-change",l=>{var d,p;const{vnodesPrevSnapshots:u=[],isEnd:f=!0}=l.detail||{};if(u.length===0&&u.push(Ft(t)),q(t.styles.geometry)==="rectangular"){const{mode:y}=$.getState(),m=y===Q,{component:_}=t;fd(e,t,m),_.fire("position-change"),_.fire("size-change",{animating:!0,isEnd:f}),e.lines.clear();const x=e._getRelationShapes(t);for(const b of x){const w=["style-changed.relations","attach-change.relations"];(d=b.component)==null||d.off(w),(p=b.component)==null||p.on(w,()=>{_.fire("relations-change",{isEnd:!1})})}}f&&Nt.saveAction({updateType:["size-change","position-change"],data:[t],oldData:u,isSub:!0})}),c.on("rank-change",()=>{const{shapes:l}=e.data,u=c.position();for(const f in t.relations){const d=l[f];d!=null&&d.component&&Qe.includes(d.type)&&(d.component.position()<u&&d.component.insertAfter(c),d.component.fire("rank-change"))}}),c.on("pre-select",l=>{const{key:u}=l.detail,f=c.findOne(".main");if(u&&u===t.key)f.attr({"stroke-width":4,stroke:xl});else{const{styles:d}=t,p=q(d.stroke),y=q(d.strokeWidth);f.attr({"stroke-width":y,stroke:p})}}),c.on("size-change",l=>{var m;const{isEnd:u=!1,pure:f=!1,vnodeCopyBeforeUpdate:d}=l.detail||{};if(f)return;const{groups:p={}}=t,{shapes:y}=e.data;for(const _ in p){const x=y[_];(m=x==null?void 0:x.component)==null||m.fire("relations-change",{isEnd:u!==!1})}q(t.styles.geometry)==="manual"&&u&&(function(_){var A;const{shapes:x}=$.getState().core.data,{relations:b}=_,w={};for(const L in b){const P=x[L];P&&(w[L]={vnode:P,status:1})}const C=Gr(Ne.getflat(_));for(const L in x){if(L===_.key)continue;const P=x[L],{groups:R,type:O}=P;if(Object.keys(R).length!==0&&!b[L]&&!b_(_,P)||!Ms(P)||Qe.includes(O)&&dd(x,_,P))continue;const W=Gr(Ne.getflat(P)),B=Mo(W);if(gf(C,W)>B/2){if(((A=w[L])==null?void 0:A.status)===1)continue;w[L]={vnode:P,status:2}}else w[L]&&(w[L]={vnode:P,status:0})}for(const L in w){const{vnode:P,status:R}=w[L];R===2&&(_.relations[P.key]=P.type,P.groups[_.key]=_.type,_.component.fire("rank-change")),R===0&&(delete _.relations[P.key],delete P.groups[_.key])}}(t),t.component.fire("relations-change",{vnodesPrevSnapshots:d?[d]:[],isEnd:u}))}),c.on("deleted",l=>{const{willClearWhenGroupDeleted:u}=$.getState();if(!u)return;const{shapes:f}=e.data,d=[];for(const p in t.relations){const y=f[p];y&&d.push(y)}d.length>0&&e.remove(d,{...l.detail,isSub:!0})}),c};var oi={render:_d,rerender:()=>{},exports:async(e,t,n)=>_d(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const vd=(e,t,n)=>oi.render(e,t,n);var S_={render:vd,rerender:()=>{},exports:async(e,t,n)=>vd(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const bd=(e,t,n)=>oi.render(e,t,n);var C_={render:bd,rerender:()=>{},exports:async(e,t,n)=>bd(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const wd=(e,t,n)=>oi.render(e,t,n);var A_={render:wd,rerender:()=>{},exports:async(e,t,n)=>wd(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const kd={t0:{x:0,y:0},t1:{x:At,y:0},t2:{x:45,y:0},t3:{x:67.5,y:0},l0:{x:0,y:90},l1:{x:0,y:67.5},l2:{x:0,y:45},l3:{x:0,y:At},b0:{x:90,y:Fe},b1:{x:67.5,y:Fe},b2:{x:45,y:Fe},b3:{x:At,y:Fe},r0:{x:Fe,y:0},r1:{x:Fe,y:At},r2:{x:Fe,y:45},r3:{x:Fe,y:67.5}};function Tl(e,t){const{mode:n}=$.getState(),i=n===Q,{d2_connection:s,connection:a,type:c,attach:l}=e,u=i?a:s;if(t==="polyline"){const f={};return["t0","t2","r0","r2","b0","b2","l0","l2"].forEach(d=>{f[d]=u[d]}),f}if(c===rr){const f=["l0-1-b2","l0-2-b2","l0-3-b2","l0-4-b2","l0-5-b2","b0-1-b2","b0-2-b2","b0-3-b2","b0-4-b2","b0-5-b2"],d=We.cloneDeep(u);return f.forEach(p=>{delete d[p]}),d}return[...Qe,sr].includes(c),u}function yi(e,t,n){const i=q(n.height)-1,s=q(n.depth)-1,a=q(n.width)-1,c=lt.cloneDeep(e);for(let l=0;l<c.length;l++){if(t==="top"){if(a>0&&l===1||a>0&&l===2){const{x:u,y:f}=c[l];c[l]=ve({x:u+vn*a*2,y:f+Ye*a*2})}if(i>0){const{x:u,y:f}=c[l];c[l]=ve({x:u,y:f-Ye*i*4})}if(s>0&&l===2||s>0&&l===3){const{x:u,y:f}=c[l];c[l]=ve({x:u-vn*s*2,y:f+Ye*s*2})}}if(t==="left"){if(a>0&&l===1||a>0&&l===2){const{x:u,y:f}=c[l];c[l]=ve({x:u+vn*a*2,y:f+Ye*a*2})}if(i>0&&l===2||i>0&&l===3){const{x:u,y:f}=c[l];c[l]=ve({x:u,y:f-Ye*i*4})}if(s>0){const{x:u,y:f}=c[l];c[l]=ve({x:u-vn*s*2,y:f+Ye*s*2})}}if(t==="right"){if(a>0){const{x:u,y:f}=c[l];c[l]=ve({x:u+vn*a*2,y:f+Ye*a*2})}if(i>0&&l===2||i>0&&l===3){const{x:u,y:f}=c[l];c[l]=ve({x:u,y:f-Ye*i*4})}if(s>0&&l===0||s>0&&l===3){const{x:u,y:f}=c[l];c[l]=ve({x:u-vn*s*2,y:f+Ye*s*2})}}if(t==="outline"){if(a>0&&l!==0&&l!==1&&l!==2){const{x:u,y:f}=c[l];c[l]=ve({x:u+vn*a*2,y:f+Ye*a*2})}if(i>0&&l!==2&&l!==3&&l!==4){const{x:u,y:f}=c[l];c[l]=ve({x:u,y:f-Ye*i*4})}if(s>0&&l!==0&&l!==4&&l!==5){const{x:u,y:f}=c[l];c[l]=ve({x:u-vn*s*2,y:f+Ye*s*2})}}}return c}function Sd(e){const{styles:t}=e,n=q(t.depth),i=q(t.width);for(const s in kd){e.d2_connection[s]={};const a=kd[s];e.d2_connection[s].x=i/2*a.x,e.d2_connection[s].y=n/2*a.y}if(e.sticky){const{shapes:s}=$.getState().core.data,a=s[e.sticky];a!=null&&a.height&&bl.forEach(c=>{e.d2_connection[c].y+=a.height})}for(const s in e.d2_connection){const a=e.d2_connection[s];e.connection[s]=dt(a,!0),e.connection[s].y+=$i}}const Ts=(e,t=1,n=!1)=>{e=e==="transparent"?"#ffffff":e;const i=St(n?"#ffffff":"#000000");return St.mix(i,e,t,"rgb").hex()};class E_{constructor({vnode:t}){M(this,"_render",null);M(this,"_setSize",null);M(this,"_setColor",null);M(this,"_is3D",null);M(this,"_group",null);M(this,"_style",null);M(this,"_element",{});this.vnode=t,this._resetMode(),this._init()}_init(){const{vnode:t}=this,{key:n,position:{x:i,y:s}}=t,a=new ht().group().attr({key:n,id:n}),c=new ht("<svg></svg>").attr({x:i,y:s,overflow:"visible",class:"position"});a.add(c),this._group=c.group(),this._render(),t.component=a}styleChange({isSizeChange:t,isColorChange:n}){this._resetMode(),t&&this._setSize(),n&&this._setColor()}checkedChange(){this._resetMode(),this._setColor()}_3dRender(){const{data:t,styles:n,isChecked:i}=this.vnode,{staticStrokeColor:s,fill:a,leftFill:c,rightFill:l,soldeStroke:u}=this._style,f=yi(t.sideTop,"top",n),d=yi(t.sideLeft,"left",n),p=yi(t.sideRight,"right",n),y=yi(t.sideOutline,"outline",n);this._element.sideTop=this._group.polygon(Yn(f)).attr({fill:a,class:"sideTop"}),this._element.sideLeft=this._group.polygon(Yn(d)).attr({fill:c,stroke:u,class:"sideLeft"}),this._element.sideRight=this._group.polygon(Yn(p)).attr({fill:l,stroke:u,class:"sideRight"}),this._element.outline=this._group.polygon(Yn(y)).attr({fill:"none",stroke:i?s:"#000000",class:"outline"})}_2dRender(){const{isChecked:t}=this.vnode,{staticStrokeColor:n,fill:i,depth:s,width:a}=this._style;this._element.rect=this._group.rect(a*ye,s*ye).attr({fill:i,"stroke-width":t?2:null,stroke:t?n:null,class:"rect"})}_3dSetSize(){const{styles:t,data:n}=this.vnode,{sideTop:i,sideLeft:s,sideRight:a,outline:c}=this._element,l=yi(n.sideTop,"top",t),u=yi(n.sideLeft,"left",t),f=yi(n.sideRight,"right",t),d=yi(n.sideOutline,"outline",t);i.attr({points:Yn(l)}),s.attr({points:Yn(u)}),a.attr({points:Yn(f)}),c.attr({points:Yn(d)})}_2dSetSize(){const{depth:t,width:n}=this._style;this._element.rect.attr({width:n*ye,height:t*ye})}_3dSetColor(){const{isChecked:t}=this.vnode,{staticStrokeColor:n,fill:i,leftFill:s,rightFill:a,soldeStroke:c}=this._style,{sideTop:l,sideLeft:u,sideRight:f,outline:d}=this._element;l.attr({fill:i}),u.attr({fill:s,stroke:c}),f.attr({fill:a,stroke:c}),d.attr({stroke:t?n:"#000000"})}_2dSetColor(){const{isChecked:t}=this.vnode,{staticStrokeColor:n,fill:i}=this._style;this._element.rect.attr({"stroke-width":t?2:null,stroke:t?n:null,fill:i})}_resetMode(){this._is3D=$.getState().mode===Q,this._render=this._is3D?this._3dRender:this._2dRender,this._setSize=this._is3D?this._3dSetSize:this._2dSetSize,this._setColor=this._is3D?this._3dSetColor:this._2dSetColor,this._resetStyle()}_resetStyle(){const{styles:t,isChecked:n}=this.vnode,{staticStrokeColor:i}=t;if(this._is3D){const s=n?.2:0,a=q(t.fill),c=Ts(a,.98+s),l=Ts(c,.88),u=Ts(c,.78),f=n?i:Ts(c,.5);this._style={staticStrokeColor:i,fill:c,leftFill:l,rightFill:u,soldeStroke:f}}else{const s=Ts(q(t.fill),.9),a=q(t.depth),c=q(t.width);this._style={staticStrokeColor:i,fill:s,depth:a,width:c}}}}const M_=si.getManager(),Cd=e=>e?{x:45,y:45}:{x:0,y:0},Ad=(e,t)=>{t.renderComponent=null,t.renderComponent=new E_({vnode:t});const{component:n}=t;return n.on("style-changed",i=>{const{initial:s,data:a={}}=i.detail||{},c=s||a.width||a.height||a.depth,l=s||a.fill;if(c){const u=$.getState().mode===Q;(function(d){const{styles:p}=d,y=q(p.depth),m=q(p.width);d.width=ye*m,d.height=ye*y})(t),Sd(t);const f=Cd(u);e.lines.add(t,u,!1,f)}t.renderComponent.styleChange({isSizeChange:c,isColorChange:l})}),n.on("attach-change",()=>{Sd(t)}),n.on("position-change",()=>{M_.remove([t.key]);const i=$.getState().mode===Q,{position:{x:s,y:a}}=t;n.findOne(".position").attr({x:s,y:a});const c=Cd(i);e.lines.add(t,i,!1,c),e.lines.draw(t.key)}),n.on("checked-change",i=>{const{initial:s}=i.detail||{};!s&&t.renderComponent.checkedChange()}),n.on("mouseover",()=>{const{mode:i}=$.getState();i===Q&&n.attr({opacity:.6})}),n.on("mouseout",()=>{n.attr({opacity:1})}),t.component=n,n};var T_={render:Ad,rerender:()=>{},exports:async(e,t,n)=>Ad(e,t),transform:e=>(e.position=_t(e.position,!1,ye,ye),e),reduction:e=>(e.position=dt({x:e.position.x-ye,y:e.position.y-ye}),e),getflat:(e,t)=>{const{position:{x:n,y:i},data:s,styles:a}=e,c=q(a.height);if(t===Q)return yi(s.sideTop,"top",a).map(f=>({x:f.x+n,y:f.y+i+c*$i}));const l=q(a.width)*ye,u=q(a.depth)*ye;return[{x:n,y:i},{x:n,y:i+u},{x:n+l,y:i+u},{x:n+l,y:i}]}};const Ed=(e,t,n)=>{(function(x){const{connection:b,d2_connection:w}=x,C=A=>A&&typeof A=="object"&&!Array.isArray(A)&&Object.keys(A).length===16;C(b)&&C(w)||(x.connection=ed(),x.d2_connection=ed())})(t);const{key:i,isChecked:s,position:a,styles:c={}}=t,l=q(c.color)||"#000000",u=q(c.opacity)||1,f=q(c.radius)||4,d=q(c.hide)||!1,p=new ht().group().attr({key:i,id:i}),y=p.group(),{x:m,y:_}=n===Q?ve(a):Te(a);return y.circle().attr({cx:m,cy:_,class:"c1",r:f,fill:s?qn:l,opacity:d?0:u}),y.circle().attr({cx:m,cy:_,r:18,class:"c2",opacity:s?.1:0}),p.on("dblclick",()=>function(x,b){x.remove([b])}(e,t)),t.component=p,p};var L_={render:Ed,rerender:(e,t,n)=>{const{isChecked:i,position:s,component:a,styles:c={}}=t,l=q(c.color)||"#000000",u=q(c.opacity)||1,f=q(c.radius)||4,d=q(c.hide)||!1,p=a.findOne(".c1"),y=a.findOne(".c2"),{x:m,y:_}=n===Q?ve(s):Te(s);p.attr({cx:m,cy:_,fill:i?qn:l,r:f,opacity:d?0:u});const x={cx:m,cy:_};x.opacity=i?.1:0,y.attr(x)},exports:async(e,t,n)=>Ed(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e),getflat:e=>[e.position]};const Md=271.68;function Jo(e,t=!1){const{core:n,mode:i}=$.getState(),{styles:s}=e,a=i!==ee&&q(s.is3d),c=q(s.isFlat);Br(e,a,c),t||n.lines.add(e,a,c)}function Td(e){const{core:t,mode:n}=$.getState(),{styles:i,component:s}=e,a=n!==ee&&q(i.is3d),c=q(i.isFlat),l=q(i.route),u=q(i.image),f=q(i.scale),d=s.findOne(".g1");let p=s.findOne(".main");const y=s.findOne(".image-rect"),m=s.findOne(".image"),_=new Image;return _.src=u,_.setAttribute("crossOrigin","anonymous"),new Promise((x,b)=>{_.onload=function(){const w=_.width,C=_.height;p?p.attr({x:-1*w/2,y:-1*C/2}):(p=new ht("<svg></svg>").attr({x:-1*w/2,y:-1*C/2,overflow:"visible",class:"main"}),d.add(p)),m?m.attr({href:u,width:w,height:C}):p.image(u).attr({class:"image",width:w,height:C}),e.data={w,h:C,cache:sd(_)},e.isChecked&&(y?y.attr({x:-1*w/2,y:-1*C/2,width:w,height:C}):d.rect().attr({x:-1*w/2,y:-1*C/2,width:w,height:C,stroke:"#006eff",fill:"none","stroke-width":"4",class:"image-rect"})),e.width=f*ni*w,e.height=f*ni*C;const A=wl(c,a,l,{x:0,y:e.height/2});d.attr({transform:`${A} scale(${f*ni})`}),Jo(e),x()},_.onerror=function(){console.error("上传的图片加载失败"),t.remove([e]),re(),b()}})}function Qo(e){const{styles:t}=e;return{is3D:$.getState().mode!==ee&&q(t.is3d),isFlat:q(t.isFlat)}}const O_=si.getManager(),Ld=async(e,t,n)=>{const{key:i,position:{x:s,y:a},styles:c}=t,l=q(c.isFlat),u=new ht().group().attr({key:i,id:i}),f=new ht("<svg></svg>").attr({x:s,y:a,overflow:"visible",class:"position"});return f.group().attr({class:"g1"}),u.add(f),function(d){d.width&&d.height||(d.width=Md,d.height=Md),Jo(d,!0)}(t),t.component=u,await Td(t),u.on("style-changed",d=>{const{initial:p,styles:y,data:m}=d.detail;if(p)Jo(t);else if(m!=null&&m.image)Td(t);else{const _=n!==ee&&q(y.is3d),x=q(y.isFlat),b=q(y.route),w=q(y.scale),C=u.findOne(".g1"),A=u.findOne(".image"),L=A.attr("width"),P=A.attr("height");t.width=w*ni*L,t.height=w*ni*P;const R=wl(x,_,b,{x:0,y:t.height/2});C.attr({transform:`${R} scale(${w*ni})`}),Jo(t)}}),u.on("position-change",()=>{O_.remove([t.key]);const d=n!==ee&&q(t.styles.is3d),{x:p,y}=t.position;u.findOne(".position").attr({x:p,y}),e.lines.add(t,d,l),e.lines.draw(t.key)}),u.on("checked-change",()=>{const{component:d,styles:p,isChecked:y}=t,m=q(p.image),_=d.findOne(".g1"),x=d.findOne(".image-rect"),b=d.findOne(".image");if(m===(b==null?void 0:b.attr("href"))){const w=b.attr("width"),C=b.attr("height");!y&&x?x.remove():!x&&y&&_.rect().attr({x:-1*w/2,y:-1*C/2,width:w,height:C,stroke:"#006eff",fill:"none","stroke-width":"4",class:"image-rect"})}}),u.on("attach-change",()=>{const{is3D:d,isFlat:p}=Qo(t);Br(t,d,p)}),u};var D_={render:Ld,rerender:()=>{},exports:async(e,t,n)=>await Ld(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e),getflat:(e,t)=>{const{position:{x:n,y:i},data:{w:s,h:a}}=e;if(t===ee){const _=-s/2,x=-a/2;return[{x:n+_,y:i+x},{x:n+_,y:i+x+a},{x:n+_+s,y:i+x+a},{x:n+_+s,y:i+x}].map(b=>Te(b))}let[c,l,u,f]=ii;c*=ni,l*=ni,u*=ni,f*=ni;const d=s/2,p=a/2,y=c*d+u*p,m=l*d+f*p;return[{x:n-y,y:i-m},{x:n+c*s-y,y:i+l*s-m},{x:n+y,y:i+m},{x:n+u*a-y,y:i+f*a-m}].map(_=>ve(_))}};class P_{constructor({vnode:t}){M(this,"_is3D",null);M(this,"_style",null);M(this,"_element",{});this.vnode=t,this._reset(),this._init()}_init(){const{vnode:t}=this,{key:n,position:{x:i,y:s}}=t,a=new ht().group().attr({key:n,id:n}),c=new ht("<svg></svg>").attr({x:i,y:s,overflow:"visible",class:"position"});a.add(c),this._element.svg=c,t.component=a,this._render()}_render(){this._reset();const{vnode:t,_style:n,_element:i}=this,{isChecked:s}=t,{width:a,height:c,scale:l,transform:u}=n,{main:f,svg:d}=i;f.attr({x:-c/2,y:-a/2});const p=d.group().attr({transform:`${u} scale(${l})`,class:"g1"}),y=p.group().attr("transform","scale(0.8)"),m=y.circle(c).attr({cx:0,cy:0,fill:"transparent",class:"circle"});y.add(f);const _=p.rect(a,c).attr({x:-a/2,y:-c/2,stroke:ar,fill:"none",class:"rect","stroke-with":4,opacity:s?1:0});i.g1=p,i.circle=m,i.rect=_}styleChange(t={}){this._reset({initial:!1,resetIcon:!!t.icon});const{_element:n,_style:i,vnode:s}=this,{isChecked:a}=s,{width:c,height:l,scale:u,transform:f}=i,{main:d,g1:p,circle:y,rect:m}=n;d.attr({x:-l/2,y:-c/2}),y.attr({r:l/2}),m.attr({width:c,height:l,x:-c/2,y:-l/2,opacity:a?1:0}),p.attr({transform:`${f} scale(${u})`})}checkedChange(){this._element.rect.attr({opacity:this.vnode.isChecked?1:0})}_reset(t={}){const{styles:n,component:i}=this.vnode;this._is3D=$.getState().mode!==ee&&q(n.is3d);const{initial:s=!0,resetIcon:a=!1}=t,c=q(n.isFlat),l=q(n.route),u=q(n.scale),f=kl(n.icon),d=wl(c,this._is3D,l,{x:0,y:90*u/2});let p;if(s)p=new ht(f).attr({class:"main"});else if(a){const x=i.findOne(".main");p=new ht(f).attr({class:"main",data:f}),p.insertAfter(x),x.remove()}else p=i.findOne(".main");const y=parseInt(p.attr("height")||48,10),m=parseInt(p.attr("width")||48,10),_=91/y*u;this.vnode.width=m*_,this.vnode.height=y*_,this._element.main=p,this._style={width:m,height:y,scale:_,transform:d}}}const R_=si.getManager(),Od=(e,t)=>{t.renderComponent=null,t.renderComponent=new P_({vnode:t});const{component:n}=t;return n.on("style-changed",i=>{const{initial:s=!1,data:a}=i.detail||{};s||t.renderComponent.styleChange(a);const{is3D:c,isFlat:l}=Qo(t);Br(t,c,l),e.lines.add(t,c,l)}),n.on("position-change",()=>{R_.remove([t.key]);const{position:{x:i,y:s}}=t,{is3D:a,isFlat:c}=Qo(t);n.findOne(".position").attr({x:i,y:s}),e.lines.add(t,a,c),e.lines.draw(t.key)}),n.on("checked-change",()=>{t.renderComponent.checkedChange()}),n.on("attach-change",()=>{const{is3D:i,isFlat:s}=Qo(t);Br(t,i,s)}),n};var I_={render:Od,rerender:()=>{},exports:async(e,t,n)=>Od(e,t),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};let ta=null;const Dd=(e,t,n)=>{var m,_,x,b;const i=e._getMovingLineShape();if(!i)return;let s;n?(ta=t,s=ta):s=ta||t;const{container:{gCache:a}}=$.getState(),{connectable:c,position:{x:l,y:u}}=s;if(Ll(),!c)return;const f=new ht().attr({x:l,y:u,overflow:"visible",class:Zo});a.add(f),a.remember(Zo,f);const d=Tl(s,(_=(m=i==null?void 0:i.styles)==null?void 0:m.lineType)==null?void 0:_.value),p=10,y=[[d.t0.x-p,d.t0.y-p],[d.r0.x+p,d.r0.y-p],[d.b0.x+p,d.b0.y+p],[d.l0.x-p,d.l0.y+p]];f.polygon(y).attr({fill:"none","stroke-width":20,stroke:"transparent"});for(const w in d){const C=d[w],A=new N_({key:s.key,position:C,className:`c-${w}`,direction:w,isPolyline:((b=(x=i==null?void 0:i.styles)==null?void 0:x.lineType)==null?void 0:b.value)==="polyline"});f.add(A.element)}},Ll=()=>{const{container:{gCache:e}}=$.getState();ta=null;const t=e.remember(Zo);t&&(t==null||t.remove(),e.forget(Zo))};class N_{constructor({key:t,position:n,className:i,direction:s,maxWidth:a,maxHeight:c,isPolyline:l}){this.pkey=t,this.position=n,this.className=i,this.direction=s,this.maxHeight=c,this.maxWidth=a,this.isPolyline=l,this._render(),this.tkeLineInfo={}}_render(){const{x:t,y:n,w:i,h:s}=this.position,a=new ht().group().attr({class:this.className});this.isPolyline&&a.circle().attr({cx:t,cy:n,r:4.5,fill:qn,opacity:.3}),this.p2=a.rect(i,s).attr({x:t-i/2,y:n-s/2,opacity:0,class:"p2"}),this.element=a,this._bindMouseEvent(),this._bindDraggingEvent(),this._bindMouseOutEvent()}rerender(t){const{x:n,y:i,w:s,h:a}=t;this.position=t,this.p2.attr({x:n-s/2,y:i-a/2})}_bindMouseEvent(){const{core:t}=$.getState(),{shapes:n}=t.data;this.p2.off("mouseover.mouse"),this.p2.on("mouseover.mouse",()=>{t._getMouseLineShape()&&(this._initTip(),this.p2.on("mousedown.mouse",i=>{this.p2.off("mousedown.mouse");const s=t._getMouseLineShape();if(!s)return;i.stopPropagation(),this._removeTip();const{data:a}=s,c=n[this.pkey],{position:l}=c;a.start.isFixed=!0,a.end.isFixed=!0,a.end.dir=this.direction,a.end.x=l.x,a.end.y=l.y,s.component.fire("line-complete",{key:this.pkey})}))})}_bindDraggingEvent(){const{core:t}=$.getState(),{shapes:n}=t.data;this.p2.off("mouseover.movable"),this.p2.on("mouseover.movable",()=>{const i=t._getDraggingLineShape();i!=null&&i.isTkeSubVode&&(this.tkeLineInfo={start:i.data.start.vkey,end:i.data.end.vkey}),i&&(this._initTip(),this.p2.on("mouseup.movable",()=>{this.p2.off("mouseup.movable"),this._removeTip();const{data:s}=i;let a;if(s.start.type==="dragging"&&(a="start"),s.end.type==="dragging"&&(a="end"),(i==null?void 0:i.isTkeSubVode)&&this.tkeLineInfo[a]!==this.pkey)return void(this.tkeLineInfo={});const c=n[this.pkey],{position:l}=c;s[a].isFixed=!0,s[a].dir=this.direction,s[a].vkey=this.pkey,s[a].x=l.x,s[a].y=l.y,ia(i,a,"vnode"),i.component.fire("update-position",{side:a})}))})}_bindMouseOutEvent(){this.p2.off("mouseout.movable"),this.p2.on("mouseout.movable",()=>{this._removeTip()})}_initTip(){const{container:{gCache:t},core:n}=$.getState(),i=n.data.shapes[this.pkey],s=i.position.x+this.position.x,a=i.position.y+this.position.y;let c=t.remember(cr);c?this.position.isMid?c.attr({x:s,y:a}):c.attr({x:s,y:a,opacity:.6}):this.position.isMid&&!this.isPolyline?(c=new ht().attr({x:s,y:a,"pointer-events":"none",class:cr}),c.circle().attr({r:8,fill:qn,opacity:1}),c.circle().attr({r:6,fill:"#fff",opacity:1}),t.add(c),t.remember(cr,c)):(c=new ht().attr({x:s,y:a,"pointer-events":"none",class:cr}),c.circle().attr({r:4.5,fill:qn}),t.add(c),t.remember(cr,c))}_removeTip(){const{container:{gCache:t}}=$.getState(),n=t.remember(cr);n&&(n.remove(),this.position.isMid&&this.p2.attr({width:this.position.w,height:this.position.h,x:this.position.x-this.position.w/2,y:this.position.y-this.position.h/2,opacity:0}),t.forget(cr))}}function Pd(e,t,n){const i=n===Q,{angle:s}=((a,c)=>{const l=Math.atan2(a.y-c.y,a.x-c.x);return{radian:l,angle:180/Math.PI*l}})(e,t);if(i)switch(!0){case(s>-150&&s<=-90):return s-150+30*Math.cos((s+150)*Math.PI/180);case(s>-90&&s<=-30):return s-150+15*Math.sin((s-150)*Math.PI/180);case(s>-30&&s<=30):return s-150-30*Math.sin((s-150)*Math.PI/180);case(s>30&&s<=90):return s-150-30*Math.cos((s+150)*Math.PI/180);case(s>90&&s<=150):return s-150-15*Math.sin((s-150)*Math.PI/180);default:return s-150+30*Math.sin((s-150)*Math.PI/180)}return s-90}function Rd(e,t,n){const{core:i}=$.getState(),{shapes:s}=i.data,{data:a,styles:c}=e,l=q(c.lineType),u=l==="polyline"||a.points.length<=3,f=t==="start"?"end":"start",d=a[t].isFixed,p=a[f].isFixed,y=Hr(e,t);if(y){const{x:m,y:_}=a[t];mi(a,l,t,m,_)}else d&&function(m,_,x,b,w){const{vkey:C,dir:A}=x[b],{position:{x:L,y:P},connection:R,d2_connection:O}=m[C],W=w?R:O;if(x[b].x=L,x[b].y=P,W[A])mi(x,_,b,W[A].x+L,W[A].y+P);else{const B=A.split("-")[2];mi(x,_,b,W[B].x+L,W[B].y+P)}}(s,l,a,t,n);if(!(u&&p&&y))if(u&&!p&&y){const{position:{x:m,y:_},direction:x}=ea(e,l,n,f);mi(a,l,f,m,_,x)}else{if(u){if(d&&p)return;if(d){const{position:{x:L,y:P},direction:R}=ea(e,l,n,f);return void mi(a,l,f,L,P,R)}if(p){const{position:{x:L,y:P},direction:R}=ea(e,l,n,t);return void mi(a,l,t,L,P,R)}const m=function(L,P,R){const{start:O,end:W}=P.data,{shapes:B}=L.data,H=B[O.vkey],Y=B[W.vkey];if(!H||!Y)return;const{position:V}=H,{position:at}=Y,{sd:rt,ed:ft}=function(et,nt,ot){const{position:xt,d2_connection:Vt,connection:Tt,width:Wt,height:Xt}=et,{position:oe,d2_connection:se,connection:Le,width:Ee,height:ln}=nt,un=ot?Tt:Vt,ce=ot?Le:se,wn={x:xt.x+(un.t0.x+un.b0.x)/2,y:xt.y+(un.t0.y+un.b0.y)/2},hn={x:oe.x+(ce.t0.x+ce.b0.x)/2,y:oe.y+(ce.t0.y+ce.b0.y)/2},Me={x:hn.x-wn.x,y:hn.y-wn.y},li=ot?_t(Me):Me;return Math.abs(li.x)<=Math.max(Wt,Ee)/2+1?{sd:["b2","t2"],ed:["b2","t2"]}:Math.abs(li.y)<=Math.max(Xt,ln)/2+1?{sd:["l2","r2"],ed:["l2","r2"]}:{sd:["l2","r2"],ed:["b2","t2"]}}(H,Y,R),$t=R?H.connection:H.d2_connection,Mt=R?Y.connection:Y.d2_connection,Gt=rt.map(et=>{const{x:nt,y:ot}=$t[et];return{x:nt+V.x,y:ot+V.y}}),Qt=ft.map(et=>{const{x:nt,y:ot}=Mt[et];return{x:nt+at.x,y:ot+at.y}});return function(et,nt,ot){let xt=1/0;const Vt=[];for(const Tt in et){const Wt=et[Tt];for(const Xt in nt){const oe=nt[Xt],se=na(Wt,oe,ot);xt>se&&(xt=se,Vt[0]={direction:Tt,position:Wt},Vt[1]={direction:Xt,position:oe})}}return Vt}(Gt,Qt,R)}(i,e,n);if(!m)return;const[{position:{x:_,y:x},direction:b},{position:{x:w,y:C},direction:A}]=m;return mi(a,l,"start",_,x,b),void mi(a,l,"end",w,C,A)}if(!d){const{position:{x:m,y:_},direction:x}=ea(e,l,n,t);mi(a,l,t,m,_,x)}}}function mi(e,t,n,i,s,a){a&&(e[n].dir=a);const c=t==="straight";if(n==="start")return e.points[0].x=i,e.points[0].y=s,void(c&&(e.points[1].x=(e.points[2].x+i)/2,e.points[1].y=(e.points[2].y+s)/2));if(n==="end"){const{length:l}=e.points;e.points[l-1].x=i,e.points[l-1].y=s,c&&(e.points[l-2].x=(e.points[l-3].x+i)/2,e.points[l-2].y=(e.points[l-3].y+s)/2)}}function ea(e,t,n,i="start"){const{shapes:s}=$.getState().core.data,{data:a}=e,{points:c}=a,{length:l}=c,u=s[a[i].vkey],{position:f}=u,d=Tl(u),p=t==="straight",y=Object.entries(d).reduce((x,[b,{x:w,y:C}])=>(x[b]={x:w+f.x,y:C+f.y},x),{});let m;m=p?i==="end"?c[l-3]:c[2]:i==="end"?c[0]:c[l-1];const _=function(x,b,w){let C,A,L=1/0;const P=function(R,O,W){const{x:B,y:H}=O,{t2:Y,r2:V,b2:at,l2:rt}=R,ft={x:B-Y.x,y:H-Y.y},$t={x:B-V.x,y:H-V.y},Mt={x:B-at.x,y:H-at.y},Gt={x:B-rt.x,y:H-rt.y},Qt=W?_t(ft):ft,et=W?_t($t):$t,nt=W?_t(Mt):Mt;return(W?_t(Gt):Gt).x>=0&&et.x<=0?["t2","b2"]:Qt.y>=0&&nt.y<=0?["l2","r2"]:["l2","r2"]}(x,b,w);for(const R of P){const O=x[R],W=na(O,b,w);L>W&&(L=W,C=O,A=R)}return{position:C,direction:A}}(y,m,n);return{vnode:u,..._}}function na(e,t,n){if(n){const{x:i,y:s}=_t({x:e.x-t.x,y:e.y-t.y},!0);return(i**2+s**2)**.5}return((e.x-t.x)**2+(e.y-t.y)**2)**.5}const Id=(e,t)=>{const n=t?e+90:e,i=8*Math.sin(n*Math.PI/180),s=8*Math.cos(n*Math.PI/180);return{x:t?-(.707*i-.707*s):-i,y:t?.409*i+.409*s:s}};function Hr(e,t){if(!e)return;const{type:n,data:i}=e;return n===pe&&(t?["mouse","dragging"].includes(i[t].type):i.end.type==="mouse"||i.start.type==="dragging"||i.end.type==="dragging")}function ia(e,t,n){const{component:i}=e;if(t&&n&&e.data[t].type!==n&&(e.data[t].type=n,n==="vnode"&&Ll(),n==="dragging")){const{core:a}=$.getState(),{shapes:c}=a.data,l=c[e.data[t].vkey];l&&Dd(a,l)}const s=Hr(e);return i==null||i.attr({"pointer-events":s?"none":"auto"}),s}function Ur(e){const{isChecked:t,renderComponent:n,data:i}=e,{end:s,start:a}=i,{uneditable:c}=$.getState();c||s!=null&&s.isMove||a!=null&&a.isMove?n.updateChecked(!1):n.updateChecked(t)}function Nd(e){return["style-changed","position-change","attach-change","size-change"].map(t=>`${t}.line-${e}`)}function $d(e,t,n,i){var u;const{key:s,data:a}=t;i&&(a[n].prevKey=null);const{vkey:c}=a[n],{prevKey:l}=a[n];if(i||l!==c){const{shapes:f}=e.data,d=f[c],p=Nd(s),y=f[l];(u=y==null?void 0:y.component)==null||u.off(p),d==null||d.component.on(p,m=>{t.component.fire("update-position",{...m.detail,side:n})}),a[n].prevKey=c}}const Wd="arrows",zd="lines",Fd="8, 6";function Ls(e,t,n,i=!1){let{length:s}=e;i&&s>2&&(s=2);const a=s===0?0:-.55*(s-1);for(let c=0;c<s;c++){const l=n.tspan(e[c]).attr({x:t,y:"50%",dy:`${a+1.1*c}em`});i&&l.attr({textLength:"135px"})}}function Os(e){const{core:t,mode:n}=$.getState(),i=n===Q,{shapes:s}=t.data,{attach:a}=e,c=s[a];if(!c)return;const{position:{x:l,y:u},data:{points:f}}=c;if(c.type===pe){const[p]=Bd(e.positionPercent||.5,f);e.position=p}else{const p=i?c.connection:c.d2_connection,y=i?e.connection:e.d2_connection,m=y.b2.x-y.t0.x,_=y.b2.y-y.t0.y;e.position={x:l+p.b2.x-m,y:u+p.b2.y-_}}const{component:d}=e;d==null||d.fire("position-change",{position:e.position})}function Bd(e,t){const n=t.reduce((s,a,c,l)=>{if(c===l.length-1)return s;const u=l[c+1],f=u.x-a.x,d=u.y-a.y;return s+Math.sqrt(f*f+d*d)},0)*e;let i=0;for(let s=0;s<t.length-1;s++){const a=t[s],c=t[s+1],{x:l,y:u}=a,{x:f,y:d}=c,p=f-l,y=d-u,m=Math.sqrt(p*p+y*y),_=n-i;if(m>=_){const x=_/m;return[{x:l+x*p,y:u+x*y},t[s]]}i+=m}return[t[t.length-1],t[t.length-2]]}function Gd(e,t){const n=Hd(t),[i]=function(s,a){const[c,l]=Ud(s,a),u=[],f=[{...c}];for(let d=0;d<=l;d++)u.push(a[d]);u.push(c);for(let d=l;d<=a.length;d++)f.push(a[d+1]);return[u,f]}(e,t);return Hd(i)/n}function Hd(e){let t=0;for(let n=0;n<e.length-1;n++){const i=e[n+1].x-e[n].x,s=e[n+1].y-e[n].y;t+=Math.sqrt(i*i+s*s)}return t}function Ud(e,t){const{x:n,y:i}=e;let s=1/0,a=null,c=0;for(let l=0;l<t.length-1;l++){const u=t[l],f=t[l+1],{x:d,y:p}=u,{x:y,y:m}=f,_=y-d,x=m-p,b=((n-d)*_+(i-p)*x)/(_*_+x*x);let w,C;b<0?(w=d,C=p):b>1?(w=y,C=m):(w=d+b*_,C=p+b*x);const A=(n-w)*(n-w)+(i-C)*(i-C);A<s&&(s=A,a={x:w,y:C},c=l)}return[a,c]}function Vd({point:e,labelNode:t,polyline:n,is3D:i}){if(e){const[l,u,f]=function(m,_,x=10){const[b,w]=Ud(m,_),C=m.x-b.x,A=m.y-b.y,L=Math.sqrt(C*C+A*A);if(L<x)return[b,b,w];const P=x/L;return[{x:b.x+P*C,y:b.y+P*A},b,w]}(e,n),d=Gd(u,n),p=n[f],y=l===u?"center":function(m,_,x){const b={x:_.x-m.x,y:_.y-m.y},w={x:x.x-m.x,y:x.y-m.y};return b.x*w.y-b.y*w.x>0?"left":"right"}(l,u,p);return[jd(l,u,t,i),d,y]}const s=t.positionSide,[a,c]=Bd(t.positionPercent,n);return[jd(s==="center"?a:function(l,u,f){const d={x:u.x-l.x,y:u.y-l.y},p=Math.sqrt(d.x*d.x+d.y*d.y);d.x/=p,d.y/=p;const y=f==="left"?{x:d.y,y:-d.x}:{x:-d.y,y:d.x};return y.x*=10,y.y*=10,{x:u.x+y.x,y:u.y+y.y}}(c,a,s),a,t,i)]}function jd(e,t,n,i){const s={...e},{width:a,height:c}=n;if(i){const l={center:ra(a/2,c/2),rightTop:ra(a,0),rightBottom:ra(a,c),leftBottom:ra(0,c)},u=Math.atan(.409/.707)*(180/Math.PI),f=e.y-t.y,d=e.x-t.x,p=Math.atan2(f,d)*(180/Math.PI)-u;return d===0&&f===0?(s.x-=n.width/2,s.y-=n.height/2,s.x-=l.center.x,s.y-=l.center.y):p<-150||p>90?(s.x-=a,s.x-=l.rightTop.x,s.y-=l.rightTop.y):p<-90?(s.x-=a,s.y-=c,s.x-=l.rightBottom.x,s.y-=l.rightBottom.y):p>-90&&p<30&&(s.y-=c,s.x-=l.leftBottom.x,s.y-=l.leftBottom.y),s}return e.x>t.x?e.y>t.y||(e.y===t.y?s.y-=n.height/2:s.y-=n.height):e.x===t.x?e.y>t.y?s.x-=n.width/2:e.y===t.y?(s.x-=n.width/2,s.y-=n.height/2):(s.x-=n.width/2,s.y-=n.height):e.y>t.y?s.x-=n.width:e.y===t.y?(s.x-=n.width,s.y-=n.height/2):(s.x-=n.width,s.y-=n.height),s}function ra(e,t,n=[.707,.409,-.707,.409,0,0]){const[i,s,a,c,l,u]=n;return{x:i*e+a*t+l-e,y:s*e+c*t+u-t}}class qd{constructor({vnode:t}){M(this,"_wrapper");M(this,"_attrs",{overflow:"visible",viewBox:"0 0 24 24",width:14,height:14});this.vnode=t,this.isStartArrow=!1,this.isEndArrow=!1,this.start=null,this.end=null,this.startDegree=null,this.endDegree=null,this.is3D=!1,this._initWrapper(),this.render()}render(){const{styles:t}=this.vnode;this.isStartArrow=q(t.lineStart)==="arrow",this.isEndArrow=q(t.lineEnd)==="arrow",this.is3D=$.getState().mode===Q,this._calcDegree(),this._renderStart(),this._renderEnd()}_renderStart(){var a;const{data:t,styles:n}=this.vnode,i=q(n.lineColor),s=Yn(t.arrow);if(this.start=this._wrapper.findOne(".start"),this.isStartArrow){const{x:c,y:l}=t.points[0];this.start?this.start.attr({x:c,y:l}):(this.start=new ht().attr({x:c,y:l,class:"start",fill:i,...this._attrs}),this._wrapper.add(this.start),this.start.group().attr({class:"main"}).polygon(s)),this.start.findOne(".main").attr({transform:this.is3D?`matrix(${ii.join(" ")}) rotate(${this.startDegree+180})`:`rotate(${this.startDegree+180})`})}else(a=this.start)==null||a.remove(),this.start=null}_renderEnd(){var a;const{data:t,styles:n}=this.vnode,i=q(n.lineColor),s=Yn(t.arrow);if(this.end=this._wrapper.findOne(".end"),this.isEndArrow){const{length:c}=t.points,{x:l,y:u}=t.points[c-1];this.end?this.end.attr({x:l,y:u}):(this.end=new ht().attr({x:l,y:u,class:"end",fill:i,...this._attrs}),this._wrapper.add(this.end),this.end.group().attr({class:"main"}).polygon(s)),this.end.findOne(".main").attr({transform:this.is3D?`matrix(${ii.join(" ")}) rotate(${this.endDegree})`:`rotate(${this.endDegree})`})}else(a=this.end)==null||a.remove(),this.end=null}_calcDegree(){const{mode:t}=$.getState(),{points:n}=this.vnode.data;if(this.isStartArrow&&(this.startDegree=Pd(n[0],n[1],t)),this.isEndArrow){const{length:i}=n;this.endDegree=Pd(n[i-2],n[i-1],t)}}_initWrapper(){const{component:t}=this.vnode;this._wrapper=t.findOne(`.${Wd}`),this._wrapper?this._wrapper.clear():this._wrapper=t.group().attr({class:Wd})}}class Yd{constructor({vnode:t,arrow:n}){this.vnode=t,this.arrow=n,this._initWrapper(),this._init()}_init(){const{vnode:t}=this,{styles:n}=t,i=q(n.line),s=q(n.lineWidth),a=q(n.lineColor),c=this._formatPoints();this.l1=this.element.polyline(c).attr({"stroke-width":s||2,"stroke-linecap":i==="dashed"?"butt":null,"stroke-dasharray":i==="dashed"?Fd:null,stroke:a,fill:"none",class:"l1"}),this.l2=this.element.polyline(c).attr({"stroke-width":20,stroke:"transparent",fill:"none",class:"l2"})}_initWrapper(){const{component:t}=this.vnode;this.element=t.findOne(`.${zd}`),this.element?this.element.clear():this.element=t.group().attr({class:zd})}_formatPoints(){const{isStartArrow:t,isEndArrow:n,startDegree:i,endDegree:s,is3D:a}=this.arrow,{points:c}=this.vnode.data;return c.reduce((l,{x:u,y:f},d)=>{if(d===0&&t){const{x:p,y}=Id(i+180,a);return l.push([u+p,f+y]),l}if(d===c.length-1&&n){const{x:p,y}=Id(s,a);return l.push([u+p,f+y]),l}return l.push([u,f]),l},[])}updatePath({animating:t=!1}={}){const n=this._formatPoints(),i=t?this.l1.animate(Fr):this.l1,s=t?this.l2.animate(Fr):this.l2;i.plot(n),s.plot(n)}updateStyle(){var c,l;const{vnode:t}=this,{styles:n}=t,i=q(n.line),s=q(n.lineWidth),a=q(n.lineColor);this.l1.attr({"stroke-width":s||2,"stroke-linecap":i==="dashed"?"butt":null,"stroke-dasharray":i==="dashed"?Fd:null,stroke:a}),(c=this.arrow.start)==null||c.attr({fill:a}),(l=this.arrow.end)==null||l.attr({fill:a})}}const sa="points",Xd="point";function Kd(e,t){const n=(t?ve:Te)(e);return na(n,e,t)<=8?n:e}let $_=class{constructor({position:e,parent:t,vnode:n,side:i="start"}){M(this,"_is3D",!1);this.position=e,this.parent=t,this.vnode=n,this.side=i,this._init()}_init(){const{x:e,y:t}=this.position;this.element=new ht().group().attr({class:Xd}),this.c0=this.element.circle().attr({cx:e,cy:t,r:8,fill:qn,opacity:0,"pointer-events":"none"}),this.c1=this.element.circle().attr({cx:e,cy:t,r:4,"stroke-width":2,fill:qn,stroke:"#fff"}),this.c2=this.element.circle().attr({cx:e,cy:t,r:12,fill:"transparent"}),this.element.draggable({relative:!0}),this.element.on("dragstart",this._startDrag.bind(this)),this.element.on("dragmove",this._drag.bind(this)),this.element.on("dragend",this._endDrag.bind(this)),this.element.on("mouseover",this._mouseover.bind(this)),this.element.on("mouseout",this._mouseout.bind(this))}_startDrag(){this._is3D=$.getState().mode===Q,this._makeSnapshot()}_drag(e){e.preventDefault(),this._setCursor("move");const t=this._getStandard(e);this.parent.pointChange(this.side,t)}_endDrag(e){const t=this._getStandard(e);this.parent.pointChange(this.side,t,!0),this._makeHistory(),this._setCursor("auto")}_getStandard(e){const{box:t}=e.detail,{cx:n,cy:i}=t;return{x:n,y:i}}_mouseover(){this._setCursor("pointer")}_mouseout(){const{doc:e}=$.getState();e.attr("cursor")!=="move"&&this._setCursor("auto")}update({x:e,y:t}){this.position={x:e,y:t},this.c0.attr({cx:e,cy:t}),this.c1.attr({cx:e,cy:t}),this.c2.attr({cx:e,cy:t})}_setCursor(e){const{doc:t,dropzone:n}=$.getState();t.attr({cursor:e}),n.css("cursor",e==="auto"?Wr:e)}_makeSnapshot(){this.vnodePrevSnapshot=Ft(this.vnode)}_makeHistory(){Nt.saveAction({updateType:"rerender-line",data:[this.vnode],oldData:[this.vnodePrevSnapshot]})}};const{floor:Zd}=Math,{min:W_}=Math,Rn=function(e,t){return e<t?-1:e>t?1:0},z_=function(e,t,n,i,s){let a;if(n==null&&(n=0),s==null&&(s=Rn),n<0)throw new Error("lo must be non-negative");for(i==null&&(i=e.length);n<i;)a=Zd((n+i)/2),s(t,e[a])<0?i=a:n=a+1;return[].splice.apply(e,[n,n-n].concat(t)),t},Jd=function(e,t,n){return n==null&&(n=Rn),e.push(t),Pl(e,0,e.length-1,n)},Ol=function(e,t){let n;t==null&&(t=Rn);const i=e.pop();return e.length?(n=e[0],e[0]=i,Ds(e,0,t)):n=i,n},Qd=function(e,t,n){n==null&&(n=Rn);const i=e[0];return e[0]=t,Ds(e,0,n),i},Dl=function(e,t,n){let i;return n==null&&(n=Rn),e.length&&n(e[0],t)<0&&(i=[e[0],t],t=i[0],e[0]=i[1],Ds(e,0,n)),t},oa=function(e,t){let n,i,s,a;t==null&&(t=Rn);const c=(function(){a=[];for(let u=0,f=Zd(e.length/2);0<=f?u<f:u>f;0<=f?u++:u--)a.push(u);return a}).apply(this).reverse(),l=[];for(i=0,s=c.length;i<s;i++)n=c[i],l.push(Ds(e,n,t));return l},tp=function(e,t,n){n==null&&(n=Rn);const i=e.indexOf(t);if(i!==-1)return Pl(e,0,i,n),Ds(e,i,n)},Pl=function(e,t,n,i){let s,a;i==null&&(i=Rn);const c=e[n];for(;n>t&&(a=n-1>>1,s=e[a],i(c,s)<0);)e[n]=s,n=a;return e[n]=c},Ds=function(e,t,n){let i,s;n==null&&(n=Rn);const a=e.length,c=t,l=e[t];for(i=2*t+1;i<a;)s=i+1,s<a&&!(n(e[i],e[s])<0)&&(i=s),e[t]=e[i],i=2*(t=i)+1;return e[t]=l,Pl(e,c,t,n)};function Bt(e){this.cmp=e??Rn,this.nodes=[]}function ep(e,t,n){this.x=e,this.y=t,this.walkable=n===void 0||n}Bt.prototype.push=function(e){return Jd(this.nodes,e,this.cmp)},Bt.prototype.pop=function(){return Ol(this.nodes,this.cmp)},Bt.prototype.peek=function(){return this.nodes[0]},Bt.prototype.contains=function(e){return this.nodes.indexOf(e)!==-1},Bt.prototype.replace=function(e){return Qd(this.nodes,e,this.cmp)},Bt.prototype.pushpop=function(e){return Dl(this.nodes,e,this.cmp)},Bt.prototype.heapify=function(){return oa(this.nodes,this.cmp)},Bt.prototype.updateItem=function(e){return tp(this.nodes,e,this.cmp)},Bt.prototype.clear=function(){return this.nodes=[]},Bt.prototype.empty=function(){return this.nodes.length===0},Bt.prototype.size=function(){return this.nodes.length},Bt.prototype.clone=function(){const e=new Bt;return e.nodes=this.nodes.slice(0),e},Bt.prototype.toArray=function(){return this.nodes.slice(0)},Bt.prototype.insert=Bt.prototype.push,Bt.prototype.top=Bt.prototype.peek,Bt.prototype.front=Bt.prototype.peek,Bt.prototype.has=Bt.prototype.contains,Bt.prototype.copy=Bt.prototype.clone,Bt.push=Jd,Bt.pop=Ol,Bt.replace=Qd,Bt.pushpop=Dl,Bt.heapify=oa,Bt.updateItem=tp,Bt.nlargest=function(e,t,n){let i,s,a;n==null&&(n=Rn);const c=e.slice(0,t);if(!c.length)return c;oa(c,n);const l=e.slice(t);for(s=0,a=l.length;s<a;s++)i=l[s],Dl(c,i,n);return c.sort(n).reverse()},Bt.nsmallest=function(e,t,n){let i,s,a,c,l,u,f,d;if(n==null&&(n=Rn),10*t<=e.length){if(a=e.slice(0,t).sort(n),!a.length)return a;for(s=a[a.length-1],f=e.slice(t),c=0,u=f.length;c<u;c++)i=f[c],n(i,s)<0&&(z_(a,i,0,null,n),a.pop(),s=a[a.length-1]);return a}oa(e,n);const p=[];for(l=0,d=W_(t,e.length);0<=d?l<d:l>d;0<=d?++l:--l)p.push(Ol(e,n));return p};const np=1,lr=2,aa=3,ca=4;function xi(e,t,n){let i;typeof e!="object"?i=e:(t=e.length,i=e[0].length,n=e),this.width=i,this.height=t,this.nodes=this._buildNodes(i,t,n)}function la(e){const t=[[e.x,e.y]];for(;e.parent;)e=e.parent,t.push([e.x,e.y]);return t.reverse()}function ip(e,t){const n=la(e),i=la(t);return n.concat(i.reverse())}function F_(e,t,n,i){const{abs:s}=Math,a=[];let c,l;const u=s(n-e),f=s(i-t),d=e<n?1:-1,p=t<i?1:-1;for(c=u-f;a.push([e,t]),e!==n||t!==i;)l=2*c,l>-f&&(c-=f,e+=d),l<u&&(c+=u,t+=p);return a}function B_(e){const t=[],n=e.length;let i,s,a,c,l,u;if(n<2)return t;for(l=0;l<n-1;++l)for(i=e[l],s=e[l+1],a=F_(i[0],i[1],s[0],s[1]),c=a.length,u=0;u<c-1;++u)t.push(a[u]);return t.push(e[n-1]),t}xi.prototype._buildNodes=function(e,t,n){let i,s;const a=new Array(t);for(i=0;i<t;++i)for(a[i]=new Array(e),s=0;s<e;++s)a[i][s]=new ep(s,i);if(n===void 0)return a;if(n.length!==t||n[0].length!==e)throw new Error("Matrix size does not fit");for(i=0;i<t;++i)for(s=0;s<e;++s)n[i][s]&&(a[i][s].walkable=!1);return a},xi.prototype.getNodeAt=function(e,t){return this.nodes[t][e]},xi.prototype.isWalkableAt=function(e,t){return this.isInside(e,t)&&this.nodes[t][e].walkable},xi.prototype.isInside=function(e,t){return e>=0&&e<this.width&&t>=0&&t<this.height},xi.prototype.setWalkableAt=function(e,t,n){this.nodes[t][e].walkable=n},xi.prototype.getNeighbors=function(e,t){const{x:n}=e,{y:i}=e,s=[];let a=!1,c=!1,l=!1,u=!1,f=!1,d=!1,p=!1,y=!1;const{nodes:m}=this;if(this.isWalkableAt(n,i-1)&&(s.push(m[i-1][n]),a=!0),this.isWalkableAt(n+1,i)&&(s.push(m[i][n+1]),l=!0),this.isWalkableAt(n,i+1)&&(s.push(m[i+1][n]),f=!0),this.isWalkableAt(n-1,i)&&(s.push(m[i][n-1]),p=!0),t===lr)return s;if(t===ca)c=p&&a,u=a&&l,d=l&&f,y=f&&p;else if(t===aa)c=p||a,u=a||l,d=l||f,y=f||p;else{if(t!==np)throw new Error("Incorrect value of diagonalMovement");c=!0,u=!0,d=!0,y=!0}return c&&this.isWalkableAt(n-1,i-1)&&s.push(m[i-1][n-1]),u&&this.isWalkableAt(n+1,i-1)&&s.push(m[i-1][n+1]),d&&this.isWalkableAt(n+1,i+1)&&s.push(m[i+1][n+1]),y&&this.isWalkableAt(n-1,i+1)&&s.push(m[i+1][n-1]),s},xi.prototype.clone=function(){let e,t;const{width:n}=this,{height:i}=this,s=this.nodes,a=new xi(n,i),c=new Array(i);for(e=0;e<i;++e)for(c[e]=new Array(n),t=0;t<n;++t)c[e][t]=new ep(t,e,s[e][t].walkable);return a.nodes=c,a};var _i={manhattan:(e,t)=>e+t,euclidean:(e,t)=>Math.sqrt(e*e+t*t),octile(e,t){const n=Math.SQRT2-1;return e<t?n*e+t:n*t+e},chebyshev:(e,t)=>Math.max(e,t)};function Vr(e){e=e||{},this.allowDiagonal=e.allowDiagonal,this.dontCrossCorners=e.dontCrossCorners,this.heuristic=e.heuristic||_i.manhattan,this.weight=e.weight||1,this.diagonalMovement=e.diagonalMovement,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=ca:this.diagonalMovement=aa:this.diagonalMovement=lr),this.diagonalMovement===lr?this.heuristic=e.heuristic||_i.manhattan:this.heuristic=e.heuristic||_i.octile}function G_(e){const{parent:t}=e;if(!t)return 0;const n=t.parent;if(!n)return 0;let i=0;return n.x===t.x&&e.x!==t.x&&(i+=1),n.y===t.y&&e.y!==t.y&&(i+=1),i}function Rl(e){Vr.call(this,e);const t=this.heuristic;this.heuristic=function(n,i){return 1e6*t(n,i)}}function Il(e){Vr.call(this,e),this.heuristic=function(){return 0}}function Ps(e){e=e||{},this.allowDiagonal=e.allowDiagonal,this.dontCrossCorners=e.dontCrossCorners,this.diagonalMovement=e.diagonalMovement,this.heuristic=e.heuristic||_i.manhattan,this.weight=e.weight||1,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=ca:this.diagonalMovement=aa:this.diagonalMovement=lr),this.diagonalMovement===lr?this.heuristic=e.heuristic||_i.manhattan:this.heuristic=e.heuristic||_i.octile}function Nl(e){Ps.call(this,e);const t=this.heuristic;this.heuristic=function(n,i){return 1e6*t(n,i)}}function $l(e){Ps.call(this,e),this.heuristic=function(){return 0}}function ai(e){e=e||{},this.heuristic=e.heuristic||_i.manhattan,this.trackJumpRecursion=e.trackJumpRecursion||!1}function Rs(e){ai.call(this,e)}function Is(e){ai.call(this,e)}function Ns(e){ai.call(this,e)}function $s(e){ai.call(this,e)}Vr.prototype.findPath=function(e,t,n,i,s){const a=new Bt((R,O)=>R.f-O.f),c=s.getNodeAt(e,t),l=s.getNodeAt(n,i),{heuristic:u}=this,{diagonalMovement:f}=this,{weight:d}=this,{abs:p}=Math,{SQRT2:y}=Math;let m,_,x,b,w,C,A,L,P=0;for(c.g=0,c.f=0,c.t=0,a.push(c),c.opened=!0;!a.empty();){if(m=a.pop(),m.closed=!0,m===l)return la(l);for(_=s.getNeighbors(m,f),b=0,w=_.length;b<w;++b)x=_[b],x.closed||(C=x.x,A=x.y,L=m.g+(C-m.x==0||A-m.y==0?1:y),(!x.opened||L<x.g)&&(x.parent=m,x.g=L,x.h=x.h||d*u(p(C-n),p(A-i)),x.t=m.t+G_(x),x.t!==0&&(P+=.01,x.t+=P),x.f=x.g+x.h+x.t,x.opened?a.updateItem(x):(a.push(x),x.opened=!0)))}return[]},Rl.prototype=new Vr,Rl.prototype.constructor=Rl,Il.prototype=new Vr,Il.prototype.constructor=Il,Ps.prototype.findPath=function(e,t,n,i,s){const a=function(O,W){return O.f-W.f},c=new Bt(a),l=new Bt(a),u=s.getNodeAt(e,t),f=s.getNodeAt(n,i),{heuristic:d}=this,{diagonalMovement:p}=this,{weight:y}=this,{abs:m}=Math,{SQRT2:_}=Math;let x,b,w,C,A,L,P,R;for(u.g=0,u.f=0,c.push(u),u.opened=1,f.g=0,f.f=0,l.push(f),f.opened=2;!c.empty()&&!l.empty();){for(x=c.pop(),x.closed=!0,b=s.getNeighbors(x,p),C=0,A=b.length;C<A;++C)if(w=b[C],!w.closed){if(w.opened===2)return ip(x,w);L=w.x,P=w.y,R=x.g+(L-x.x==0||P-x.y==0?1:_),(!w.opened||R<w.g)&&(w.g=R,w.h=w.h||y*d(m(L-n),m(P-i)),w.f=w.g+w.h,w.parent=x,w.opened?c.updateItem(w):(c.push(w),w.opened=1))}for(x=l.pop(),x.closed=!0,b=s.getNeighbors(x,p),C=0,A=b.length;C<A;++C)if(w=b[C],!w.closed){if(w.opened===1)return ip(w,x);L=w.x,P=w.y,R=x.g+(L-x.x==0||P-x.y==0?1:_),(!w.opened||R<w.g)&&(w.g=R,w.h=w.h||y*d(m(L-e),m(P-t)),w.f=w.g+w.h,w.parent=x,w.opened?l.updateItem(w):(l.push(w),w.opened=2))}}return[]},Nl.prototype=new Ps,Nl.prototype.constructor=Nl,$l.prototype=new Ps,$l.prototype.constructor=$l,ai.prototype.findPath=function(e,t,n,i,s){this.openList=new Bt((f,d)=>f.f-d.f),this.startNode=s.getNodeAt(e,t),this.endNode=s.getNodeAt(n,i);const{openList:a}=this,{startNode:c}=this,{endNode:l}=this;let u;for(this.grid=s,c.g=0,c.f=0,a.push(c),c.opened=!0;!a.empty();){if(u=a.pop(),u.closed=!0,u===l)return B_(la(l));this._identifySuccessors(u)}return[]},ai.prototype._identifySuccessors=function(e){const{grid:t}=this,{heuristic:n}=this,{openList:i}=this,s=this.endNode.x,a=this.endNode.y;let c,l,u,f;const{x:d}=e,{y:p}=e;let y,m,_,x,b;const{abs:w}=Math,C=this._findNeighbors(e);for(u=0,f=C.length;u<f;++u)if(c=C[u],l=this._jump(c[0],c[1],d,p),l){if(y=l[0],m=l[1],b=t.getNodeAt(y,m),b.closed)continue;_=_i.octile(w(y-d),w(m-p)),x=e.g+_,(!b.opened||x<b.g)&&(b.g=x,b.h=b.h||n(w(y-s),w(m-a)),b.f=b.g+b.h,b.parent=e,b.opened?i.updateItem(b):(i.push(b),b.opened=!0))}},Rs.prototype=new ai,Rs.prototype.constructor=Rs,Rs.prototype._jump=function(e,t,n,i){const{grid:s}=this,a=e-n,c=t-i;if(!s.isWalkableAt(e,t))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(e,t).tested=!0),s.getNodeAt(e,t)===this.endNode)return[e,t];if(a!==0){if(s.isWalkableAt(e,t-1)&&!s.isWalkableAt(e-a,t-1)||s.isWalkableAt(e,t+1)&&!s.isWalkableAt(e-a,t+1))return[e,t]}else{if(c===0)throw new Error("Only horizontal and vertical movements are allowed");if(s.isWalkableAt(e-1,t)&&!s.isWalkableAt(e-1,t-c)||s.isWalkableAt(e+1,t)&&!s.isWalkableAt(e+1,t-c))return[e,t];if(this._jump(e+1,t,e,t)||this._jump(e-1,t,e,t))return[e,t]}return this._jump(e+a,t+c,e,t)},Rs.prototype._findNeighbors=function(e){const{parent:t}=e,{x:n}=e,{y:i}=e,{grid:s}=this;let a,c,l,u;const f=[];let d,p,y,m;if(t)a=t.x,c=t.y,l=(n-a)/Math.max(Math.abs(n-a),1),u=(i-c)/Math.max(Math.abs(i-c),1),l!==0?(s.isWalkableAt(n,i-1)&&f.push([n,i-1]),s.isWalkableAt(n,i+1)&&f.push([n,i+1]),s.isWalkableAt(n+l,i)&&f.push([n+l,i])):u!==0&&(s.isWalkableAt(n-1,i)&&f.push([n-1,i]),s.isWalkableAt(n+1,i)&&f.push([n+1,i]),s.isWalkableAt(n,i+u)&&f.push([n,i+u]));else for(d=s.getNeighbors(e,lr),y=0,m=d.length;y<m;++y)p=d[y],f.push([p.x,p.y]);return f},Is.prototype=new ai,Is.prototype.constructor=Is,Is.prototype._jump=function(e,t,n,i){const{grid:s}=this,a=e-n,c=t-i;if(!s.isWalkableAt(e,t))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(e,t).tested=!0),s.getNodeAt(e,t)===this.endNode)return[e,t];if(a!==0&&c!==0){if(s.isWalkableAt(e-a,t+c)&&!s.isWalkableAt(e-a,t)||s.isWalkableAt(e+a,t-c)&&!s.isWalkableAt(e,t-c))return[e,t];if(this._jump(e+a,t,e,t)||this._jump(e,t+c,e,t))return[e,t]}else if(a!==0){if(s.isWalkableAt(e+a,t+1)&&!s.isWalkableAt(e,t+1)||s.isWalkableAt(e+a,t-1)&&!s.isWalkableAt(e,t-1))return[e,t]}else if(s.isWalkableAt(e+1,t+c)&&!s.isWalkableAt(e+1,t)||s.isWalkableAt(e-1,t+c)&&!s.isWalkableAt(e-1,t))return[e,t];return this._jump(e+a,t+c,e,t)},Is.prototype._findNeighbors=function(e){const{parent:t}=e,{x:n}=e,{y:i}=e,{grid:s}=this;let a,c,l,u;const f=[];let d,p,y,m;if(t)a=t.x,c=t.y,l=(n-a)/Math.max(Math.abs(n-a),1),u=(i-c)/Math.max(Math.abs(i-c),1),l!==0&&u!==0?(s.isWalkableAt(n,i+u)&&f.push([n,i+u]),s.isWalkableAt(n+l,i)&&f.push([n+l,i]),s.isWalkableAt(n+l,i+u)&&f.push([n+l,i+u]),s.isWalkableAt(n-l,i)||f.push([n-l,i+u]),s.isWalkableAt(n,i-u)||f.push([n+l,i-u])):l===0?(s.isWalkableAt(n,i+u)&&f.push([n,i+u]),s.isWalkableAt(n+1,i)||f.push([n+1,i+u]),s.isWalkableAt(n-1,i)||f.push([n-1,i+u])):(s.isWalkableAt(n+l,i)&&f.push([n+l,i]),s.isWalkableAt(n,i+1)||f.push([n+l,i+1]),s.isWalkableAt(n,i-1)||f.push([n+l,i-1]));else for(d=s.getNeighbors(e,np),y=0,m=d.length;y<m;++y)p=d[y],f.push([p.x,p.y]);return f},Ns.prototype=new ai,Ns.prototype.constructor=Ns,Ns.prototype._jump=function(e,t,n,i){const{grid:s}=this,a=e-n,c=t-i;if(!s.isWalkableAt(e,t))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(e,t).tested=!0),s.getNodeAt(e,t)===this.endNode)return[e,t];if(a!==0&&c!==0){if(this._jump(e+a,t,e,t)||this._jump(e,t+c,e,t))return[e,t]}else if(a!==0){if(s.isWalkableAt(e,t-1)&&!s.isWalkableAt(e-a,t-1)||s.isWalkableAt(e,t+1)&&!s.isWalkableAt(e-a,t+1))return[e,t]}else if(c!==0&&(s.isWalkableAt(e-1,t)&&!s.isWalkableAt(e-1,t-c)||s.isWalkableAt(e+1,t)&&!s.isWalkableAt(e+1,t-c)))return[e,t];return s.isWalkableAt(e+a,t)&&s.isWalkableAt(e,t+c)?this._jump(e+a,t+c,e,t):null},Ns.prototype._findNeighbors=function(e){const{parent:t}=e,{x:n}=e,{y:i}=e,{grid:s}=this;let a,c,l,u;const f=[];let d,p,y,m;if(t)if(a=t.x,c=t.y,l=(n-a)/Math.max(Math.abs(n-a),1),u=(i-c)/Math.max(Math.abs(i-c),1),l!==0&&u!==0)s.isWalkableAt(n,i+u)&&f.push([n,i+u]),s.isWalkableAt(n+l,i)&&f.push([n+l,i]),s.isWalkableAt(n,i+u)&&s.isWalkableAt(n+l,i)&&f.push([n+l,i+u]);else{let _;if(l!==0){_=s.isWalkableAt(n+l,i);const x=s.isWalkableAt(n,i+1),b=s.isWalkableAt(n,i-1);_&&(f.push([n+l,i]),x&&f.push([n+l,i+1]),b&&f.push([n+l,i-1])),x&&f.push([n,i+1]),b&&f.push([n,i-1])}else if(u!==0){_=s.isWalkableAt(n,i+u);const x=s.isWalkableAt(n+1,i),b=s.isWalkableAt(n-1,i);_&&(f.push([n,i+u]),x&&f.push([n+1,i+u]),b&&f.push([n-1,i+u])),x&&f.push([n+1,i]),b&&f.push([n-1,i])}}else for(d=s.getNeighbors(e,ca),y=0,m=d.length;y<m;++y)p=d[y],f.push([p.x,p.y]);return f},$s.prototype=new ai,$s.prototype.constructor=$s,$s.prototype._jump=function(e,t,n,i){const{grid:s}=this,a=e-n,c=t-i;if(!s.isWalkableAt(e,t))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(e,t).tested=!0),s.getNodeAt(e,t)===this.endNode)return[e,t];if(a!==0&&c!==0){if(s.isWalkableAt(e-a,t+c)&&!s.isWalkableAt(e-a,t)||s.isWalkableAt(e+a,t-c)&&!s.isWalkableAt(e,t-c))return[e,t];if(this._jump(e+a,t,e,t)||this._jump(e,t+c,e,t))return[e,t]}else if(a!==0){if(s.isWalkableAt(e+a,t+1)&&!s.isWalkableAt(e,t+1)||s.isWalkableAt(e+a,t-1)&&!s.isWalkableAt(e,t-1))return[e,t]}else if(s.isWalkableAt(e+1,t+c)&&!s.isWalkableAt(e+1,t)||s.isWalkableAt(e-1,t+c)&&!s.isWalkableAt(e-1,t))return[e,t];return s.isWalkableAt(e+a,t)||s.isWalkableAt(e,t+c)?this._jump(e+a,t+c,e,t):null},$s.prototype._findNeighbors=function(e){const{parent:t}=e,{x:n}=e,{y:i}=e,{grid:s}=this;let a,c,l,u;const f=[];let d,p,y,m;if(t)a=t.x,c=t.y,l=(n-a)/Math.max(Math.abs(n-a),1),u=(i-c)/Math.max(Math.abs(i-c),1),l!==0&&u!==0?(s.isWalkableAt(n,i+u)&&f.push([n,i+u]),s.isWalkableAt(n+l,i)&&f.push([n+l,i]),(s.isWalkableAt(n,i+u)||s.isWalkableAt(n+l,i))&&f.push([n+l,i+u]),!s.isWalkableAt(n-l,i)&&s.isWalkableAt(n,i+u)&&f.push([n-l,i+u]),!s.isWalkableAt(n,i-u)&&s.isWalkableAt(n+l,i)&&f.push([n+l,i-u])):l===0?s.isWalkableAt(n,i+u)&&(f.push([n,i+u]),s.isWalkableAt(n+1,i)||f.push([n+1,i+u]),s.isWalkableAt(n-1,i)||f.push([n-1,i+u])):s.isWalkableAt(n+l,i)&&(f.push([n+l,i]),s.isWalkableAt(n,i+1)||f.push([n+l,i+1]),s.isWalkableAt(n,i-1)||f.push([n+l,i-1]));else for(d=s.getNeighbors(e,aa),y=0,m=d.length;y<m;++y)p=d[y],f.push([p.x,p.y]);return f};class H_{constructor({start:t,end:n}){M(this,"_grids",null);M(this,"_w",null);M(this,"_h",null);M(this,"_ox",null);M(this,"_oy",null);const{mode:i,core:s}=$.getState();this.is3D=i===Q;const{shapes:a}=s.data;this.shapes=a,this.startPoint=this.is3D?_t(t):t,this.endPoint=this.is3D?_t(n):n,this.startPoint=Te(this.startPoint),this.endPoint=Te(this.endPoint)}run(){this._setView(),this._setTarget("start"),this._setTarget("end"),this._grids=new xi(this._w,this._h),this._resetGrid(),this._find()}_find(){const{start:[t,n],end:[i,s]}=this,a=new Vr({heuristic:_i.manhattan,diagonalMovement:lr}).findPath(t,n,i,s,this._grids);a.length<2?this.polyline=[this._revert(this.start),this._revert(this.end)]:this.polyline=function(c){if(c.length<3)return c;const l=[],u=c[0][0],f=c[0][1];let d,p,y,m,_,x,b=c[1][0],w=c[1][1],C=b-u,A=w-f;for(_=Math.sqrt(C*C+A*A),C/=_,A/=_,l.push([u,f]),x=2;x<c.length;x++)d=b,p=w,y=C,m=A,b=c[x][0],w=c[x][1],C=b-d,A=w-p,_=Math.sqrt(C*C+A*A),C/=_,A/=_,C===y&&A===m||l.push([d,p]);return l.push([b,w]),l}(a).map(c=>this._revert(c))}_resetGrid(){const{shapes:t,_format:n,_ox:i,_oy:s,is3D:a}=this;for(const c in t){if(!Ms(t[c]))continue;const l=a?Ne.transform(Ft(t[c])):t[c],{position:{x:u,y:f}}=l,{width:d,height:p}=this._getVnodeSize(l);if(!d||!p)continue;const y=n(u-i),m=n(f-s),_=n(d),x=n(p);if(!this._isInside(y,m,_,x))for(let b=0;b<=x;b++)for(let w=0;w<=_;w++){const C=[y+w,m+b];this._isSameSide(C)||this._grids.setWalkableAt(C[0],C[1],!1)}}}_getVnodeSize(t){const{d2_connection:{t0:n,t2:i,r0:s,b2:a}}=t;return{width:s.x-n.x,height:a.y-i.y}}_isInside(t,n,i,s){const{start:a,end:c}=this;return a[0]>t&&a[0]<t+i&&a[1]>n&&a[1]<n+s?"start":c[0]>t&&c[0]<t+i&&c[1]>n&&c[1]<n+s?"end":null}_isSameSide(t){const{start:n,end:i}=this,s=n[0]===t[0]&&n[1]===t[1],a=i[0]===t[0]&&i[1]===t[1];return s?"start":a?"end":null}_setTarget(t){const{_ox:n,_oy:i,_format:s}=this,{x:a,y:c}=this[`${t}Point`],l=s(a-n),u=s(c-i);this[t]=[l,u]}_setView(){const{shapes:t,is3D:n,_format:i,startPoint:s,endPoint:a}=this,c={x:1/0,y:1/0},l={x:-1/0,y:-1/0};for(const u in t){if(!Ms(t[u]))continue;const f=n?Ne.transform(Ft(t[u])):t[u],{position:{x:d,y:p},width:y,height:m}=f;c.x>d&&(c.x=d),c.y>p&&(c.y=p),l.x<d+y&&(l.x=d+y),l.y<p+m&&(l.y=p+m)}[s,a].forEach(({x:u,y:f})=>{c.x>u&&(c.x=u),c.y>f&&(c.y=f),l.x<u&&(l.x=u),l.y<f&&(l.y=f)}),this._ox=c.x-90,this._oy=c.y-90,this._w=i(l.x-c.x+180),this._h=i(l.y-c.y+180)}_format(t){return Math.round(t/At)}_revert([t,n]){const{_ox:i,_oy:s,is3D:a}=this,c={x:i+t*At,y:s+n*At};return a?dt(c):c}}class U_{constructor({vnode:t}){M(this,"_points",[]);M(this,"_polyline",null);M(this,"_arrow",null);M(this,"_pointWrapper",null);M(this,"_startUpdate",!1);M(this,"_start",{x:0,y:0});this.vnode=t,this._init()}_init(){const{vnode:t}=this;this._findPoints(),this._arrow=new qd({vnode:t}),this._polyline=new Yd({vnode:t,arrow:this._arrow}),this._initPointWrapper(),this._initPoints()}_initPoints(){const{vnode:t}=this,{points:n}=t.data,{length:i}=n,s={start:n[0],end:n[i-1]};this._points=["start","end"].map(a=>{const c=new $_({vnode:t,parent:this,position:s[a],side:a});return this._pointWrapper.add(c.element),c})}_initPointWrapper(){const{component:t}=this.vnode;this._pointWrapper=t.findOne(`.${sa}`),this._pointWrapper?this._pointWrapper.clear():this._pointWrapper=t.group().attr({class:sa})}pointChange(t,n,i){this.vnode.component.fire("side-change",{side:t,point:n,isEnd:i})}_findPoints(){const t=sessionStorage.getItem("init")==="true",{data:n}=this.vnode,{length:i}=n.points,s=n.points[0],a=n.points[i-1],c=this.vnode.hasDragged||t?n.points:function(l,u){const f=new H_({start:l,end:u});return f.run(),f.polyline}(s,a);n.points=c,this._updatePoints()}_updatePoints(){var i,s;if(this._startUpdate)return;this._startUpdate=!0;const{points:t}=this.vnode.data,{length:n}=t;(i=this._points[0])==null||i.update(t[0]),(s=this._points[1])==null||s.update(t[n-1]),this._startUpdate=!1}updatePath(){this._findPoints(),this._arrow.render(),this._polyline.updatePath()}updateStyle(){this._polyline.updateStyle()}updateChecked(t){var s,a;const n=q(this.vnode.styles.lineColor),i=t?xl:n;this._polyline.l1.attr({stroke:i}),(s=this._polyline.arrow.start)==null||s.attr({fill:i}),(a=this._polyline.arrow.end)==null||a.attr({fill:i}),t?(this.removeDragPoint(),this.initDragPoint()):this.removeDragPoint();for(const c of this._points)c.element[t?"show":"hide"]()}initDragPoint(){var l;const{vnode:t}=this,n=$.getState().mode==="SIGMA_GRAPH_MODE_3D",{points:i}=this.vnode.data;if(i.length<4)return;(l=t.dragPoints)==null||l.forEach(u=>{u.remove()}),t.dragPoints=[];const s=[];for(let u=1;u<i.length-2;u++){let f="";const d=n?_t(i[u]):i[u],p=n?_t(i[u+1]):i[u+1];f=Math.abs(d.x-p.x)<1?"x":"y";const y=i[u],m=i[u+1],_={x:(y.x+m.x)/2,y:(y.y+m.y)/2,direction:f,index:u};s.push(_)}const{doc:a}=$.getState(),c=[];s==null||s.forEach(u=>{const f=a.group();f.c0=f.rect(6,6).fill("white").stroke({color:"blue",width:2}).move(u.x-3,u.y-3),f.direction=u.direction,f.index=u.index,f.draggable(),f.on("dragstart",this._startDrag.bind(this)),f.on("dragmove",d=>this._drag({e:d,element:f})),f.on("dragend",this._endDrag.bind(this)),f.on("mouseover",()=>this._mouseover()),f.on("mouseout",()=>this._mouseout()),c.push(f)}),t.dragPoints=[...t.dragPoints,...c]}removeDragPoint(){var n;const{vnode:t}=this;(n=t.dragPoints)==null||n.forEach(i=>{i.remove()}),t.dragPoints=[]}resetDragPoint(){this.removeDragPoint(),this.initDragPoint()}_startDrag(t){const{box:n}=t.detail,{cx:i,cy:s}=n;this._start.x=i,this._start.y=s,this._makeSnapshot()}_drag(t){this._setCursor("move");const{e:n,element:i}=t,{core:s,scale:a,mode:c}=$.getState(),{box:l}=n.detail,{cx:u,cy:f}=l,d={x:this._start.x+(u-this._start.x)*a,y:this._start.y+(f-this._start.y)*a},{vnode:p}=this,{data:y}=p,{index:m,direction:_}=i,x=c==="SIGMA_GRAPH_MODE_3D",b=x?_t(d):d;n.preventDefault();const w=s.__getStandardPosition(b);[m,m+1].forEach(x?C=>{const A=_t(y.points[C]);A[_]=w[_];const L=dt(A);y.points[C].x=L.x,y.points[C].y=L.y}:C=>{y.points[C][_]=w[_]}),this.vnode.hasDragged=!0,this._polyline.updatePath(),this.removeDragPoint(),this.initDragPoint()}_endDrag(){this._makeHistory(),this._setCursor("auto")}_mouseover(){this._setCursor("pointer")}_mouseout(){const{doc:t}=$.getState();t.attr("cursor")!=="move"&&this._setCursor("auto")}_setCursor(t){const{doc:n,dropzone:i}=$.getState();n.attr({cursor:t}),i.css("cursor",t==="auto"?Wr:t)}_getStandard(t,n){const{scale:i}=$.getState(),{box:s}=t.detail,{cx:a,cy:c}=s,{x:l,y:u}=this._start,{x:f,y:d}=n,p={x:f+i*(a-l),y:d+i*(c-u)},y=Kd(p,$.getState().mode==="SIGMA_GRAPH_MODE_3D");return this._start.x=a+(y.x-p.x)/i,this._start.y=c+(y.y-p.y)/i,y}_makeSnapshot(){this.vnodePrevSnapshot=Ft(this.vnode)}_makeHistory(){Nt.saveAction({updateType:"rerender-line",data:[this.vnode],oldData:[this.vnodePrevSnapshot]})}}class Wl{constructor({position:t,parent:n,vnode:i,status:s="raw",index:a,isStart:c=!1,isEnd:l=!1}){M(this,"_start",{x:0,y:0});M(this,"_is3D",!1);M(this,"_sideVnode",null);this.position=t,this.parent=n,this.vnode=i,this.status=s,this.index=a,this.isStart=c,this.isEnd=l,this._isDragging=!1,this._init()}_init(){const{x:t,y:n}=this.position;this.element=new ht().group().attr({class:Xd}),this.c0=this.element.circle().attr({cx:t,cy:n,r:8,fill:qn,opacity:0,"pointer-events":"none"}),this.c1=this.element.circle().attr({cx:t,cy:n,r:4,"stroke-width":2,...this._getCircleStyles()}),this.c2=this.element.circle().attr({cx:t,cy:n,r:12,fill:"transparent"}),this.element.draggable(),this.element.on("mouseover",this._mouseover.bind(this)),this.element.on("mouseout",this._mouseout.bind(this)),this.element.on("dragstart",this._startDrag.bind(this)),this.element.on("dragmove",this._drag.bind(this)),this.element.on("dragend",this._endDrag.bind(this)),this.element.on("dblclick",this._dblclick.bind(this))}_getCircleStyles(){return this.status==="raw"?{stroke:qn,fill:"#fff"}:this.status==="mature"?{fill:qn,stroke:"#fff"}:void 0}_startDrag(t){this._is3D=$.getState().mode===Q;const{box:n}=t.detail,{cx:i,cy:s}=n;this._start.x=i,this._start.y=s,this._setSideVnode(),this._makeSnapshot(),this._isDragging=!0}_drag(t){if(t.preventDefault(),this.isStart&&(this.vnode.data.start.isMove=!0),this.isEnd&&(this.vnode.data.end.isMove=!0),Ur(this.vnode),this.status==="raw"){const{data:i}=this.vnode;this.status="mature",i.points[this.index].status="mature",this.parent.addRawPoints(this.index)}this._setCursor("move");const n=this._getStandard(t);this.parent.pointChange(this.index,n)}_endDrag(t){this.isStart&&(this.vnode.data.start.isMove=!1),this.isEnd&&(this.vnode.data.end.isMove=!1);const n=this._getStandard(t);this.parent.pointChange(this.index,n,!0),this._makeHistory(),this._setCursor("auto"),Ur(this.vnode),this._isDragging=!1}_getStandard(t){const{scale:n}=$.getState(),{box:i}=t.detail,{cx:s,cy:a}=i,{x:c,y:l}=this._start,{x:u,y:f}=this.position,d={x:u+n*(s-c),y:f+n*(a-l)};if(this._sideVnode){const y=function(m,_,x){const{position:{x:b,y:w}}=m,C=Tl(m);for(const A in C){const L=C[A],P={x:b+L.x,y:w+L.y};if(na(P,_,x)<=8)return P}}(this._sideVnode,d,this._is3D);return y?(this._start.x=s+(y.x-d.x)/n,this._start.y=a+(y.y-d.y)/n,y):(this._start.x=s,this._start.y=a,d)}const p=Kd(d,this._is3D);return this._start.x=s+(p.x-d.x)/n,this._start.y=a+(p.y-d.y)/n,p}_setSideVnode(){const{core:t}=$.getState(),{shapes:n}=t.data,{index:i,vnode:s}=this,{start:a,end:c,points:l}=s.data,{length:u}=l;this._sideVnode=i===0?n[a.vkey]:i===u-1?n[c.vkey]:null}update({x:t,y:n}){this.position={x:t,y:n},this.c0.attr({cx:t,cy:n}),this.c1.attr({cx:t,cy:n,...this._getCircleStyles()}),this.c2.attr({cx:t,cy:n})}_mouseover(){var i,s,a;const t=(a=(s=(i=this.parent)==null?void 0:i.vnode)==null?void 0:s.data)==null?void 0:a.points,n=localStorage.getItem("USER_USE_DELETE_POINT")==="true";if(this.status==="mature"&&!this.tip&&this.index!==0&&this.index!==t.length-1&&!n&&!this._isDragging){const{doc:c}=$.getState(),{position:l}=this,u=c.group().opacity(0),f=c.text("双击删除").font({size:15,family:"Arial",weight:200}).attr({stroke:"#fff"}),d=f.bbox(),p=c.rect(d.width+20,d.height+20).stroke({width:1,color:"#202020"}).radius(4);f.move(10,10),u.add(p),u.add(f),u.animate(500).opacity(.8),u.move(l.x-40,l.y-50),this.tip=u}this.c0.animate(Fr).attr({opacity:.2}),this._setCursor("pointer")}_mouseout(){this.tip&&(this.tip.remove(),this.tip=null),this.c0.animate(Fr).attr({opacity:0});const{doc:t}=$.getState();t.attr("cursor")!=="move"&&this._setCursor("auto")}_setCursor(t){const{doc:n,dropzone:i}=$.getState();n.attr({cursor:t}),i.css("cursor",t==="auto"?Wr:t)}_dblclick(t){t.stopPropagation(),this._makeSnapshot(),this.parent.delete(this.index),this._makeHistory(),localStorage.setItem("USER_USE_DELETE_POINT","true"),this.tip&&(this.tip.remove(),this.tip=null)}_makeSnapshot(){this.vnodePrevSnapshot=Ft(this.vnode)}_makeHistory(){Nt.saveAction({updateType:"rerender-line",data:[this.vnode],oldData:[this.vnodePrevSnapshot]})}}class V_{constructor({vnode:t}){M(this,"_points",[]);M(this,"_pointWrapper",null);M(this,"_staightLine",null);M(this,"_arrow",null);this.vnode=t,this._init()}_init(){const{vnode:t}=this;this._points=[],this._arrow=new qd({vnode:t}),this._staightLine=new Yd({vnode:t,arrow:this._arrow}),this._initPointWrapper();const{points:n}=t.data;for(let i=0;i<n.length;i++){const s=n[i],{x:a,y:c,status:l}=s,u=new Wl({vnode:t,parent:this,position:{x:a,y:c},status:l,index:i,isStart:i===0,isEnd:i===n.length-1});this._points.push(u),this._pointWrapper.add(u.element)}}addRawPoints(t){const{vnode:n}=this,{data:i}=n,s=i.points[t],a=i.points[t-1],c=i.points[t+1];let l=0;const u="raw";if(a){const f={x:(a.x+s.x)/2,y:(a.y+s.y)/2};i.points.splice(t,0,{...f,status:u});const d=new Wl({vnode:n,parent:this,position:f,status:u});this._points.splice(t,0,d),this._pointWrapper.add(d.element),l++}if(c){const f={x:(c.x+s.x)/2,y:(c.y+s.y)/2},d=t+1+l;i.points.splice(d,0,{...f,status:u});const p=new Wl({vnode:n,parent:this,position:f,status:u});this._points.splice(d,0,p),this._pointWrapper.add(p.element)}for(let f=t;f<i.points.length;f++)this._points[f].index=f;this._staightLine.updatePath()}pointChange(t,n,i){const{vnode:s}=this,{data:a}=s;if(t===0)return void s.component.fire("side-change",{side:"start",point:n,isEnd:i});if(t===a.points.length-1)return void s.component.fire("side-change",{side:"end",point:n,isEnd:i});const c=t-2,l=t-1,u=t+1,f=t+2;a.points[t].x=n.x,a.points[t].y=n.y,a.points[l].x=(a.points[c].x+a.points[t].x)/2,a.points[l].y=(a.points[c].y+a.points[t].y)/2,a.points[u].x=(a.points[f].x+a.points[t].x)/2,a.points[u].y=(a.points[f].y+a.points[t].y)/2;for(let d=l;d<=u;d++)this._points[d].update(a.points[d]);!this._checkSideEvent(t)&&this._staightLine.updatePath()}_initPointWrapper(){const{component:t}=this.vnode;this._pointWrapper=t.findOne(`.${sa}`),this._pointWrapper?this._pointWrapper.clear():this._pointWrapper=t.group().attr({class:sa})}_initPoints(){const{data:t}=this.vnode;if(!Array.isArray(t.points)||t.points.length<=2){const{start:{x:n,y:i},end:{x:s,y:a}}=t;t.points=[{x:n,y:i,status:"mature"},{x:(n+s)/2,y:(i+a)/2,start:"raw"},{x:s,y:a,status:"mature"}]}}updateChecked(t){for(const n of this._points)n.element[t?"show":"hide"]()}updatePath(t={}){if(!t.justArrow){const{points:n}=this.vnode.data;for(let i=0;i<n.length;i++)this._points[i].update(n[i])}this._arrow.render(),this._staightLine.updatePath()}delete(t){const{data:n}=this.vnode;if(t===0||t===n.points.length-1||this._points[t].status==="raw")return;const i={x:(n.points[t-2].x+n.points[t+2].x)/2,y:(n.points[t-2].y+n.points[t+2].y)/2,status:"raw"};n.points[t-1]=i,this._points[t-1].update(i),[t,t+1].forEach(s=>{this._points[s].element.remove()}),n.points.splice(t,2),this._points.splice(t,2);for(let s=t;s<=n.points.length-1;s++)this._points[s].index=s;!this._checkSideEvent(t-1)&&this._staightLine.updatePath({animating:!0})}_checkSideEvent(t){const{data:n,component:i}=this.vnode,{length:s}=n.points;let a=!1;return t<=2&&(i.fire("update-position",{side:"start"}),a=!0),t>=s-3&&(i.fire("update-position",{side:"end"}),a=!0),a}updateStyle(){this._staightLine.updateStyle()}}class j_{constructor({vnode:t}){M(this,"_render",null);this.vnode=t,this.updateType()}updateType(){var c,l;const t=$.getState().mode===Q,{vnode:n}=this,{styles:i,component:s}=n,a=q(i.lineType);this._render=null,s==null||s.clear(),this._initPoints(),Rd(n,"end",t),(l=(c=this._render)==null?void 0:c.removeDragPoint)==null||l.call(c),a==="straight"&&(this._render=new V_({vnode:n})),a==="polyline"&&(this._render=new U_({vnode:n}))}_initPoints(){const{data:t,styles:n}=this.vnode,i=q(n.lineType),s=Array.isArray(t.points),{length:a}=(t==null?void 0:t.points)||[];if(i==="straight"&&(!s||a<=2)){const c={...this._getSidePoint(t,"start"),status:"mature"},l={...this._getSidePoint(t,"end"),status:"mature"};t.points=[c,{x:(c.x+l.x)/2,y:(c.y+l.y)/2,start:"raw"},l]}i==="polyline"&&(!s||a<=1)&&(t.points=[this._getSidePoint(t,"start"),this._getSidePoint(t,"end")])}_getSidePoint(t,n){const{core:i,mode:s}=$.getState(),a=s===Q,{shapes:c}=i.data,{type:l,vkey:u,isFixed:f,dir:d}=t[n];if(["mouse","dragging"].includes(l))return{x:t[n].x,y:t[n].y};const{position:{x:p,y},connection:m,d2_connection:_}=c[u],x=a?m:_;return f?{x:x[d].x+p,y:x[d].y+y}:{x:p,y}}updateStyle(){this._render.updateStyle()}updatePath(t){this._render.updatePath(t)}updateChecked(t){this._render.updateChecked(t)}resetDragPoint(){var t,n;(n=(t=this._render)==null?void 0:t.resetDragPoint)==null||n.call(t)}removeDragPoint(){var t,n;(n=(t=this._render)==null?void 0:t.removeDragPoint)==null||n.call(t)}}const rp=(e,t)=>{const{key:n}=t,i=new ht().group().attr({key:n,id:n});function s(){window.clearMouseLineEvents();const a=[];for(const c in e.data.shapes){const l=e.data.shapes[c];Hr(l)&&a.push(l)}a.length&&e.remove(a),Ll()}return t.component=i,i.on("style-changed",a=>{const{data:c={},initial:l}=a.detail,{component:u}=t;l&&(t.renderComponent=new j_({vnode:t}),ia(t),Ur(t)),l||(c.lineType&&(t.data.points=null,t.renderComponent.updateType()),(c.line||c.lineWidth||c.lineColor)&&t.renderComponent.updateStyle(),(c.lineStart||c.lineEnd)&&t.renderComponent.updatePath({justArrow:!0})),l&&u.fire("update-position",a.detail)}),i.on("update-position",a=>{const{initial:c=!1,side:l,isExport:u=!1}=a.detail||{},f=$.getState().mode===Q;c||(Rd(t,l,f),t.renderComponent.updatePath()),u||($d(e,t,"start",c),$d(e,t,"end",c),t.isChecked&&t.renderComponent.resetDragPoint())}),i.on("side-change",a=>{const{side:c,point:l,isEnd:u}=a.detail||{},{data:f}=t;if(t.hasDragged=!1,!u||f[c].type!=="vnode"){if(u){const d=e.data.shapes[f[c].vkey];f[c].x=d.position.x,f[c].y=d.position.y}else f[c].x=l.x,f[c].y=l.y;ia(t,c,u?"vnode":"dragging"),i.fire("update-position",{side:c})}}),i.on("position-change",a=>{const{diff:c,pure:l}=a.detail||{};if(l)return void t.renderComponent.updatePath();if(!c)return;const{x:u,y:f}=c,{data:d}=t;for(let p=1;p<d.points.length-1;p++)d.points[p].x+=u,d.points[p].y+=f;t.renderComponent.updatePath(),t.isChecked&&t.renderComponent.resetDragPoint()}),i.on("checked-change",()=>{Ur(t)}),i.on("editable-change",()=>{Ur(t)}),i.on("line-complete",a=>{const{key:c}=a.detail||{},{data:l}=t;c&&c!==t.key&&c!==l.start.vkey?(window.clearMouseLineEvents(),l.end.vkey=c,l.end.isMove=!1,ia(t,"end","vnode"),i.fire("update-position",{side:"end"}),Nt.saveAction({type:"add",data:[t]}),re()):s()}),i.on("deleted",()=>{var y,m,_;const{key:a}=t,{shapes:c}=e.data,{start:l,end:u}=t.data,f=c[l.vkey],d=c[u.vkey],p=Nd(a);(y=f==null?void 0:f.component)==null||y.off(p),(m=d==null?void 0:d.component)==null||m.off(p),(_=t.renderComponent)==null||_.removeDragPoint()}),i.on("rerender-line",()=>{t.renderComponent.updateType()}),i.on("dblclick",a=>{const c=i.point(a.pageX,a.pageY),{points:l}=t.data,u=Gd(c,l);e._createNodeLabel(t,"双击编辑",{position:c,positionPercent:u})}),t.data.end.type==="mouse"&&(Ie(window,"mousemove.line",function(a){a.stopPropagation();const{dropzone:c}=$.getState(),{x:l,y:u}=c.point(a.pageX,a.pageY);t.data.end.x=l,t.data.end.y=u,t.data.end.isMove=!0,Ur(t),t.component.fire("update-position",{side:"end"})}),Ie(window,"contextmenu.line",s),Ie(window,"keydown.line",function(a){a.keyCode===27&&s()}),Ie(window,"mousedown.line",function(a){a.which!==3&&s()})),window.clearMouseLineEvents=function(){On(window,".line")},i};var q_={render:rp,rerender:()=>{},exports:async(e,t,n)=>rp(e,t),transform:e=>{const{start:t,end:n,points:i}=e.data,{x:s,y:a}=_t({x:t.x,y:t.y}),{x:c,y:l}=_t({x:n.x,y:n.y});return e.data.start.x=s,e.data.start.y=a,e.data.end.x=c,e.data.end.y=l,e.data.points=i.map(({x:u,y:f,status:d})=>({..._t({x:u,y:f},!0),status:d})),e},reduction:e=>{const{start:t,end:n,points:i}=e.data,{x:s,y:a}=dt({x:t.x,y:t.y}),{x:c,y:l}=dt({x:n.x,y:n.y});return e.data.start.x=s,e.data.start.y=a,e.data.end.x=c,e.data.end.y=l,e.data.points=i.map(({x:u,y:f,status:d})=>({...dt({x:u,y:f},!0),status:d})),e},getflat:e=>[e.position]};function Y_(e){return`<svg width="128" height="128" viewBox="0 0 128 128">
    <g fill="none" fill-rule="evenodd" transform="scale(1.28)">
      <path class="checked-base" fill="transparent" pointer-events="none" fill-rule="evenodd" clip-rule="evenodd" d="M150 71.2088L50.3317 129.128L-50 71.2025L49.6662 13.2812L150 71.2088Z" />
    
      <path class="fill-light base-bg-path" d="M98.001 68.003L50.078 95.852 1.836 68l47.922-27.85z"/>
      <path class="base-fill outline" fill-rule="nonzero" d="M49.754 39.332L99.413 68 50.076 96.674.416 68l49.338-28.668zm.324 56.521L98 68 49.758 40.15 1.836 68l48.242 27.852v.001z"/>
      <path class="base-stroke outline" d="M50.042 96.66l-.01 3.265L.419 71.257l.009-3.264z"/>
      <path class="base-stroke outline" d="M99.448 67.998l.009 3.257-49.448 28.667L50 96.665z"/>

      ${e}
    </g>
</svg>`}function zl(e,t,n=!1,i){const{staticCheckedFill:s}=t,a=q(t.pattern);t.fillDark.default=a==="gary"?"#E2E6EC":"#D2F0FF";const c=q(t.dark),l=q(t.fillLight),u=q(t.fillDark),f=q(t.baseFill),d=q(t.baseBgColor),p=q(t.baseStroke),y=q(t.baseBorderStroke),m=e.find(".fill-light"),_=e.find(".base-bg-path"),x=e.find(".fill-dark"),b=e.find(".base-fill"),w=e.find(".base-stroke"),C=e.find(".dark"),A=e.find(".outline"),L=e.find(".stroke-light"),P=e.find(".stroke-dark"),R=e.find(".stroke-outline");C.attr({fill:c}),m.attr({fill:l}),x.attr({fill:i===void 0?u:i?"#006EFF":"#E2E6EC"}),b.attr({fill:f}),d&&_.attr({fill:d}),p?(w.attr({fill:y}),b.attr({fill:y})):w.attr({fill:p}),L.attr({stroke:l}),P.attr({stroke:c});const O=e.find(".checked-base");n?(A.attr({fill:s}),R.attr({stroke:s}),O.attr({fill:"#006eff",opacity:.1})):O.attr({fill:"transparent",opacity:0})}const jr=90,Fl=112.5,Bl=At;function sp(e,t){return e.replaceAll(new RegExp(td,"ig"),t)}const op={width:60,height:60,x:15,y:15,class:"main"};function ap(e){(function(t){if(t.d2_connection=We.cloneDeep(od(0,0,90,90)),t.sticky){const{shapes:n}=$.getState().core.data,i=n[t.sticky];i!=null&&i.height&&bl.forEach(s=>{t.d2_connection[s].y+=i.height})}})(e),function(t){var i,s,a;const{d2_connection:n}=t;t.connection={};for(const c in n){const l=dt(n[c],!0);t.connection[c]={x:l.x+or,y:l.y+55.5,w:(i=n[c])==null?void 0:i.w,h:(s=n[c])==null?void 0:s.h,isMid:(a=n[c])==null?void 0:a.isMid}}}(e)}const X_=si.getManager(),cp=(e,t,n)=>{const i=n===Q,s=i?K_(t):Z_(t),a=i?{x:Fl,y:Bl}:{x:0,y:0};return t.width&&t.height||(t.width=jr,t.height=jr),s.on("style-changed",()=>{const c=$.getState().mode===Q,{styles:l,isChecked:u,customize:f={}}=t,d=q(l.opacity)??1,p=f==null?void 0:f.hasResource;if(c)zl(s,l,u,p);else{const y=q(l.baseStroke),m=q(l.baseBorderStroke),_=q(l.baseBgColor),x=s.findOne(".rect");x.attr({fill:_||y,stroke:m||y});const b=p===void 0?q(l.fillDark):p?"#006EFF":"#888888",w=s.findOne(".main"),C=sp(t.data.d2,b);new ht(C).attr(op).insertAfter(w),w.remove(),u&&x.attr({"stroke-width":2,stroke:ar})}s.attr({opacity:d}),ap(t),e.lines.add(t,c,!1,a)}),s.on("attach-change",()=>{ap(t)}),s.on("checked-change",()=>{const c=$.getState().mode===Q,{isChecked:l,styles:u,customize:f={}}=t,d=f==null?void 0:f.hasResource;if(c)zl(s,u,l,d);else{const p=s.findOne(".rect");if(!p)return;if(l)p.attr({"stroke-width":2,stroke:ar});else{const y=q(u.baseBorderStroke);p.attr({"stroke-width":2,stroke:y||"#C1C6C8"})}}}),s.on("position-change",c=>{var x;X_.remove([t.key]);const{shapes:l}=e.data,{movingLineKeys:u=[]}=c.detail,f=(x=Object.keys(l))==null?void 0:x.filter(b=>{var w,C,A,L,P,R,O,W;return!(((w=l[b])==null?void 0:w.type)!=="SIGMA_LINE_SHAPE"||((L=(A=(C=l[b])==null?void 0:C.data)==null?void 0:A.start)==null?void 0:L.vkey)!==(t==null?void 0:t.key)&&((O=(R=(P=l[b])==null?void 0:P.data)==null?void 0:R.end)==null?void 0:O.vkey)!==(t==null?void 0:t.key)||l[b].isChecked||u!=null&&u.includes((W=l[b])==null?void 0:W.key))});(f==null?void 0:f.length)>0&&(f==null||f.forEach(b=>{l[b].hasDragged=!1}));const d=$.getState().mode===Q,p=s.findOne(".position"),{position:{x:y,y:m},positionOffsetY:_=0}=t;p.attr(d?{x:y,y:m+_}:{x:y,y:m}),e.lines.add(t,d,!1,a),e.lines.draw(t.key)}),t.component=s,s},K_=e=>{var p;const{key:t,position:{x:n,y:i},positionOffsetY:s=0,data:a,isChecked:c,styles:l}=e;l.fillDark.default="#E2E6EC";const u=new ht().group().attr({key:t,id:t}),f=new ht(Y_(a.d3));u.add(f),f.attr({x:n,y:i+s,class:"position"});const d=(p=e==null?void 0:e.customize)==null?void 0:p.hasResource;return zl(u,l,c,d),e.component=u,u},Z_=e=>{const{key:t,position:{x:n,y:i},data:s,isChecked:a,styles:c}=e;c.fillDark.default=ar;const l=q(c.fillDark),u=q(c.baseStroke),f=new ht().group().attr({key:t,id:t}),d=new ht("<svg></svg>").attr({x:n,y:i,overflow:"visible",class:"position",width:jr,height:jr}),p=d.group().attr({x:-.5,y:-.5}),y=sp(s.d2,l),m=new ht(y).attr(op),_=p.rect(90,90).attr({class:"rect",fill:u,"stroke-width":2,stroke:"#C1C6C8"});return f.add(d),p.add(_),p.add(m),a&&_.attr({"stroke-width":2,stroke:ar}),f};var J_={render:cp,rerender:()=>{},exports:async(e,t,n)=>cp(e,t,n),transform:e=>(e.position=_t(e.position,!1,Fl,Bl),e),reduction:e=>(e.position=dt({x:e.position.x-Fl,y:e.position.y-Bl}),e),getflat:(e,t)=>{const{position:{x:n}}=e;let{position:{y:i}}=e;return t===Q?(i=i+74+vn,[{x:n,y:i},{x:n+or,y:i-$i},{x:n+128,y:i},{x:n+or,y:i+$i}]):[{x:n,y:i},{x:n,y:i+90},{x:n+90,y:i+90},{x:n+90,y:i}]}};const lp=(e,t,n)=>oi.render(e,t,n);var Q_={render:lp,rerender:()=>{},exports:async(e,t,n)=>lp(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const up=(e,t,n)=>oi.render(e,t,n);var tv={render:up,rerender:()=>{},exports:async(e,t,n)=>up(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const ev=e=>{const{key:t,position:{x:n,y:i},data:s,isChecked:a}=e,c=new ht().group().attr({key:t,id:t}),l=new ht(s.d3).attr("overflow","visible");return c.add(l),l.attr({x:n,y:i,class:"main"}),a&&l.attr({filter:"url(#bright75)"}),c},hp=e=>{const{key:t,position:{x:n,y:i},data:s,isChecked:a}=e,c=new ht().group().attr({key:t,id:t}),l=new ht("<svg></svg>").attr({x:n,y:i,overflow:"visible",class:"position",width:90,height:90}),u=l.group().attr({x:-.5,y:-.5}),f=new ht(s.d2).attr({width:60,height:60,x:15,y:15}),d=u.rect(91,91).attr({fill:"#E2E6EC",class:"rect"});return c.add(l),u.add(d),u.add(f),e.d2_connection=[{x:ye,y:0},{x:0,y:ye},{x:ye,y:90},{x:90,y:ye}],a&&d.attr({"stroke-width":4,stroke:ar}),c},nv=e=>{const{component:t,isChecked:n,position:{x:i,y:s}}=e,a=t.findOne(".position"),c=t.findOne(".rect");c&&(c.fill("#E2E6EC"),n?c.attr({"stroke-width":2,stroke:ar}):c.attr({"stroke-width":2,stroke:"none"})),a.animate(128,0,"now").attr({x:i,y:s})};var iv={render:(e,t,n)=>{const i=n===Q?ev(t):hp(t);return t.component=i,i},rerender:(e,t,n)=>{if(n===Q){const{position:{x:i,y:s},isChecked:a,component:c}=t,l=c.findOne(".main");a?l.attr({filter:"url(#bright75)"}):l.attr("filter",null),l.animate(128,0,"now").attr({x:i,y:s})}else nv(t)},exports:async(e,t,n)=>{const{key:i,position:{x:s,y:a},data:c}=t;let l;if(n===Q){l=new ht().group().attr({key:i,id:i});const u=new ht(c.d3);l.add(u),u.attr({x:s,y:a,class:"main"});const f=u.findOne("image"),d=f.attr("href"),{base64:p}=await function(y){return new Promise((m,_)=>{let x="";const b=new Image;b.src=`${y}?stamp=${Date.now()}`,b.setAttribute("crossOrigin","anonymous"),b.onload=function(){x=sd(b),m({base64:x,width:b.width,height:b.height})},b.onerror=function(){_(new Error(`failed load image ${y}`))}})}(d);f.attr("href",p)}else l=hp(t);return l},transform:e=>(e.position=_t(e.position,112.5,At),e),reduction:e=>(e.position=dt({x:e.position.x-112.5,y:e.position.y-At}),e),getflat:(e,t)=>{const{position:{x:n}}=e;let{position:{y:i}}=e;return t===Q?(i=i+74+vn,[{x:n,y:i},{x:n+or,y:i-$i},{x:n+128,y:i},{x:n+or,y:i+$i}]):[{x:n,y:i},{x:n,y:i+90},{x:n+90,y:i+90},{x:n+90,y:i}]}};const rv=si.getManager(),fp=(e,t,n)=>{const{key:i,position:{x:s,y:a}}=t;(function(m){const{core:_}=$.getState(),{key:x,linkNodeKey:b}=m;if(b){const{shapes:w}=_.data,C=w[b];if(!C||!C.linkTextLabel||C.linkTextLabel!==x)return void delete m.linkNodeKey;m.attach=C.key,C.sticky=x,delete m.linkNodeKey,delete C.linkTextLabel}})(t);const c=new ht().group().attr({key:i,id:i}),l=new ht("<svg></svg>").attr({x:s,y:a,overflow:"visible",class:"position"});c.add(l);const u={x:0,y:0};let f=null,d=null,p=null,y="";return c.on("style-changed",m=>{const{styles:_,initial:x,isExport:b=!1}=m.detail||{};x&&!b&&function(et){var Xt,oe,se,Le,Ee;const{core:nt}=$.getState(),{shapes:ot}=nt.data,{key:xt,attach:Vt}=et,Tt=ot[Vt];if(!Tt)return;const Wt=`.text-${xt}`;(Xt=Tt.component)==null||Xt.off(Wt),(oe=Tt.component)==null||oe.on(`style-changed${Wt}`,()=>{Os(et)}),(se=Tt.component)==null||se.on(`position-change${Wt}`,()=>{Os(et)}),(Le=Tt.component)==null||Le.on(`update-position${Wt}`,()=>{Os(et)}),Tt.component.on(`attach-change${Wt}`,ln=>{const{type:un}=ln.detail||{};un!=="deleted"&&Os(et)}),(Ee=Tt.component)==null||Ee.on(`deleted${Wt}`,ln=>{nt.remove(et,ln.detail)})}(t);const{isChecked:w}=t,C=n!==ee&&q(_.is3d);let A=q(_.label);const L=q(_.fill),P=q(_.fontSize),R=q(_.isFlat),O=q(_.route),W=q(_.outline),B=q(_.xAlign),{tspanX:H,textAnchor:Y}=function(et){switch(et){case"left":return{tspanX:"0",textAnchor:"start"};case"center":default:return{tspanX:"50%",textAnchor:"middle"};case"right":return{tspanX:"100%",textAnchor:"end"}}}(B),V=function(et,nt){return{"font-weight":"bold","font-family":"'pingfang SC', 'helvetica neue', arial, 'hiragino sans gb',  'microsoft yahei ui', 'microsoft yahei', simsun, sans-serif","dominant-baseline":"central","font-size":`${et}px`,"text-anchor":nt}}(P,Y),at=function(et=!1,nt=!0,ot="e"){if(nt)if(et)switch(ot){case"e":case"n":return"matrix(0.707, 0.409, 0, 0.816, 0, 0) translate(26 0)";case"s":case"w":return"matrix(0.707, -0.409, 0, 0.816, 0, 0) translate(26 0)";default:console.error(`转换值错误，请检查设置的值: ${ot}`)}else switch(ot){case"e":return"matrix(0.707 0.409 -0.707 0.409 0 0)";case"s":return"matrix(0.707 -0.409 0.707 0.409 0 0)";case"n":return"matrix(-0.707 -0.409 0.707 -0.409 0 0)";case"w":return"matrix(-0.707 0.409 -0.707 -0.409 0 0)";default:console.error(`转换值错误，请检查设置的值: ${ot}`)}switch(ot){case"e":return"";case"s":return"rotate(270)";case"n":return"rotate(180)";case"w":return"rotate(90)";default:console.error(`转换值错误，请检查设置的值: ${ot}`)}}(R,C,O);t!=null&&t.attach&&(A=A==null?void 0:A.replace(/\n/g," "));let rt=A.split(/[\n]+/);rt.length===1&&(t!=null&&t.attach)&&(rt=function(nt,ot,xt,Vt){const Tt=[];let Wt="";const Xt=document.createElement("canvas").getContext("2d");Xt.font=`bold ${Vt} ${xt}`;for(let oe=0;oe<nt.length;oe++){const se=nt[oe];Wt+=se;const Le=Xt.measureText(Wt),{width:Ee}=Le;Ee>ot&&(Tt.push(Wt.trim()),Wt=se)}return Tt.push(Wt.trim()),Tt}(rt[0],135,V["font-family"],V["font-size"]));let ft=c.findOne(".g1");if(ft){const et=c.findOne(".text-outline"),nt=c.findOne(".text");if(ft.attr({transform:at}),W)if(et)f=et.text(ot=>{Ls(rt,H,ot,!!(t!=null&&t.attach))}).attr({fill:L,"font-size":`${P}px`,"text-anchor":Y});else{const ot=new ht().text(xt=>{Ls(rt,H,xt,!!(t!=null&&t.attach))}).attr({stroke:"#ffffff",class:"text-outline","stroke-width":4,...V});ot.insertBefore(nt),f=ot}b&&!(t!=null&&t.attach)&&nt.text(ot=>{Ls(rt,H,ot,!!(t!=null&&t.attach))}).attr({fill:L,"font-size":`${P}px`,"text-anchor":Y}),p&&p.setText(A)}else{ft=l.group().attr({transform:at,class:"g1"});const et=rt==null?void 0:rt.map(nt=>nt.replace(/\s+/g,"　"));W&&(f=ft.text(nt=>{Ls(b?et:rt,H,nt,!!(t!=null&&t.attach))}).attr({stroke:"#fff",class:"text-outline","stroke-width":4,...V})),b&&!(t!=null&&t.attach)&&ft.text(nt=>{Ls(et,H,nt,!!(t!=null&&t.attach))}).attr({fill:L,class:"text",...V}),y=A}const $t=c.findOne(".text-outline").bbox();let Mt=t!=null&&t.attach?Math.ceil($t.w):Math.ceil($t.w/ye)*ye;const Gt=t!=null&&t.attach?Math.ceil($t.h):Math.ceil($t.h/At)*At;Sl(t,e)&&(Mt=135),l.width(Mt).height(Gt);let Qt=c.findOne(".rect");if(Qt?Qt.attr({width:Mt,height:Gt}):(Qt=ft.rect(Mt,Gt).attr({class:"rect","stroke-width":4,stroke:w?"#006eff":"",fill:"none"}),ft.put(Qt,0)),d=Qt,t.width=Mt,t.height=Gt,Br(t,C,R,u),Os(t),t.attach||e.lines.add(t,C,R,u),!p){if(b&&!(t!=null&&t.attach))return;f.node.style.visibility="hidden";const{w:et,h:nt}=d.bbox(),ot={...V,display:"block",width:"100%",height:"max-content",lineHeight:V["font-size"],color:L,textAlign:B,overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box","-webkit-line-clamp":2,"-webkit-box-orient":"vertical",whiteSpace:"pre-wrap",wordWrap:"break-word",paddingTop:"3px"},xt={...V,width:"100%",height:"100%",lineHeight:V["font-size"],display:"inline-flex",alignItems:"center",justifyContent:"center",flexWrap:"wrap",color:L,textAlign:B};p=new yd({wrapper:ft,text:A,width:et,height:nt,maxLength:128,initStyles:t!=null&&t.attach?ot:xt,isEnterSubmit:!!(t!=null&&t.attach),initForeignStyles:{textAlign:"center",textShadow:"-1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff"},dblClickDom:c,onblur:Vt=>{d.node.style.visibility="initial",t!=null&&t.attach&&p.setTextStyle({overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box","-webkit-line-clamp":2,"-webkit-box-orient":"vertical"}),Vt.innerText.length<1?(e.setStyles({label:{value:y}},t.key),Vt.innerText=y):e.setStyles({label:{value:Vt.innerText}},t.key)},ondblclick:Vt=>{d.node.style.visibility="hidden",t!=null&&t.attach&&(Vt.style.removeProperty("overflow"),Vt.style.removeProperty("textOverflow"),Vt.style.removeProperty("display"),Vt.style.removeProperty("-webkit-line-clamp"),Vt.style.removeProperty("-webkit-box-orient"))},onSave:()=>{var Vt,Tt,Wt;(Wt=(Tt=(Vt=e==null?void 0:e.options)==null?void 0:Vt.callbacks)==null?void 0:Tt.onDbClickSaveText)==null||Wt.call(Tt,t)}})}if(p&&!b){const{w:et,h:nt}=d.bbox();p.setforeignAttr({width:et,height:nt}),p.setTextStyle({...V,lineHeight:V["font-size"],textAlign:B,color:L})}}),c.on("position-change",m=>{rv.remove([t.key]);const{component:_,styles:x,attach:b,position:{x:w,y:C}}=t,A=_.findOne(".position");if(Sl(t,e)){const{mousePosition:L}=m.detail,P=e.data.shapes[t.attach],{points:R}=P.data,O=t,W=n!==ee;let B;if(L){const[H,Y,V]=Vd({point:L,labelNode:O,polyline:R,is3D:W});t.positionSide=V,t.positionPercent=Y,B=H}else{const[H]=Vd({labelNode:O,polyline:R,is3D:W});B=H}t.position=B,A.attr(B)}else A.attr({x:w,y:C});if(!b){const L=n!==ee&&q(x.is3d),P=q(x.isFlat);e.lines.add(t,L,P,u),e.lines.draw(t.key)}}),c.on("checked-change",()=>{const{isChecked:m}=t;c.findOne(".rect").attr({stroke:m?"#006eff":""})}),c.on("deleted",()=>{const{shapes:m}=e.data,{attach:_}=t,x=m[_];x&&delete x.sticky}),c.on("start-edit",()=>{p==null||p.startEdit()}),t.component=c,c};var sv={render:fp,rerender:()=>{},exports:async(e,t,n)=>fp(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e),getflat:(e,t)=>{const{position:{x:n,y:i},component:s,width:a,height:c}=e;if(s){if(t===Q){const[l,u,f,d]=ii;return[{x:n,y:i},{x:n+l*a,y:i+u*a},{x:n+l*a+f*c,y:i+u*a+d*c},{x:n+f*c,y:i+d*c}]}return[{x:n,y:i},{x:n,y:i+c},{x:n+a,y:i+c},{x:n+a,y:i}]}return[{x:0,y:0},{x:0,y:0},{x:0,y:0},{x:0,y:0}]}};const dp=(e,t,n)=>oi.render(e,t,n);var ov={render:dp,rerender:()=>{},exports:async(e,t,n)=>dp(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const pp=(e,t,n)=>oi.render(e,t,n);var av={render:pp,rerender:()=>{},exports:async(e,t,n)=>pp(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const gp=(e,t,n)=>oi.render(e,t,n);var cv={render:gp,rerender:()=>{},exports:async(e,t,n)=>gp(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const yp=(e,t,n)=>oi.render(e,t,n);var lv={render:yp,rerender:()=>{},exports:async(e,t,n)=>yp(e,t,n),transform:e=>(e.position=_t(e.position),e),reduction:e=>(e.position=dt(e.position),e)};const bn=function(){this.instance=null,this.map=new Map,this._init()};bn.prototype._init=function(){this.register(Es,xd),this.register(Go,S_),this.register(Ho,C_),this.register(Uo,A_),this.register(zo,T_),this.register($r,L_),this.register(Bo,D_),this.register(pl,I_),this.register(pe,q_),this.register(rr,J_),this.register(Vo,Q_),this.register(jo,tv),this.register(Yo,iv),this.register(Fo,sv),this.register(qo,ov),this.register(Xo,oi),this.register(sr,av),this.register(Ko,cv),this.register(Pn,lv)},bn.getRender=function(){return this.instance?this.instance:this.instance=new bn},bn.prototype.register=function(e,t){if(!this.map.has(e))return this.map.set(e,t),!0;throw new Error("已存在相同类型的基础元素, 检查是否重复或使用别的类型名称")},bn.prototype.deregister=function(e){this.map.delete(e)},bn.prototype.render=function(e,t){const{mode:n,container:i}=$.getState(),{type:s}=t,a=this.map.get(s);if(a){let c,l=a.render(e,t,n);if([Bo].includes(s)&&(l=t.component),t.attach){const{shapes:u}=e.data;if(c=u[t.attach].component,c)return{component:l,container:c};delete t.attach}return c=i[this._getGIndex(t)],{component:l,container:c}}throw new Error(`render: 元素数据错误, 请检查程序。vnode: ${t}`)},bn.prototype.rerender=function(e,t){const{mode:n}=$.getState(),i=this.map.get(t.type);if(!i)throw new Error(`rerender: 元素数据错误, 请检查程序。vnode: ${t}`);i.rerender(e,t,n)},bn.prototype.transform=function(e){const t=this.map.get(e.type);if(t)return t.transform(e)},bn.prototype.reduction=function(e){const t=this.map.get(e.type);if(t)return t.reduction(e)},bn.prototype.exports=async function(e,t){const{mode:n}=$.getState(),i=this.map.get(t.type);if(i){const s=this._getGIndex(t);return[await i.exports(e,t,n),s]}throw new Error(`exports: 元素数据错误, 请检查程序。vnode: ${t}`)},bn.prototype.getflat=function(e){if(!e)return void console.warn("Function getFlat: vnode is undefind!");const{connection:t,d2_connection:n,position:{x:i,y:s}}=e,a=$.getState().mode===Q?t:n;if(!a){const{mode:u}=$.getState(),f=this.map.get(e.type);if(f)return f.getflat(e,u)}const c=[],l=["t0","r0","b0","l0"];for(const u of l){const f=a[u]||{};c.push({x:i+f.x,y:s+f.y})}return c},bn.prototype._getGIndex=function(e){switch(e.type){case Ho:case Go:case Uo:case Vo:case qo:case jo:case Xo:case Ko:case Pn:return 4;case Es:return 5;case pe:return 6;case $r:return 7;case zo:case rr:case Yo:return 8;case Bo:return 9;case pl:return 10;case Fo:case sr:return 11;default:throw new Error(`数据错误: ${e}`)}};var Ne=bn.getRender(),Nt=new class{constructor(){M(this,"undoList",[]);M(this,"redoList",[]);M(this,"listeners",{});M(this,"cmdGroupCache",{type:"groupCmd",cmdGroup:[]});M(this,"cmdGroupFlag",!1);M(this,"maxCmdGroupCache",{type:"groupCmd",cmdGroup:[]});M(this,"maxCmdGroupFlag",!1);M(this,"tbVnode",null);M(this,"onCmdListChange",()=>{const e=this.getUndoStatus();Object.values(this.listeners).forEach(t=>{t(e)})});M(this,"pushUndo",(e,t={})=>{const{groupBegin:n,groupEnd:i,isSub:s,maxGroupBegin:a,maxGroupEnd:c}=t,{undoList:l,redoList:u}=this;if(!this.cmdGroupFlag&&s&&l.length>0){let f={};const d=l.pop();return f=d.type==="groupCmd"?d:{type:"groupCmd",cmdGroup:[d]},f.cmdGroup.push(e),l.push(f),void(u.length=0)}if(a)this.maxCmdGroupFlag=!0;else{if(n===!0&&(this.cmdGroupFlag=!0),this.cmdGroupFlag!==!0||this.maxCmdGroupFlag||this.cmdGroupCache.cmdGroup.push(e),i===!0&&!this.maxCmdGroupFlag)return this.cmdGroupFlag=!1,l.push(lt.clone(this.cmdGroupCache)),this.cmdGroupCache={type:"groupCmd",cmdGroup:[]},u.length=0,void this.onCmdListChange();if(i===!0&&this.maxCmdGroupFlag&&(this.cmdGroupCache.cmdGroup.push(e),this.cmdGroupFlag=!1),this.maxCmdGroupFlag&&(this.cmdGroupFlag?this.cmdGroupCache.cmdGroup.push(e):this.cmdGroupCache.cmdGroup.length>0?(this.maxCmdGroupCache.cmdGroup.push(this.cmdGroupCache),this.cmdGroupCache={type:"groupCmd",cmdGroup:[]},u.length=0):this.maxCmdGroupCache.cmdGroup.push(e)),c===!0)return this.maxCmdGroupFlag=!1,l.push(lt.cloneDeep(this.maxCmdGroupCache)),this.maxCmdGroupCache={type:"groupCmd",cmdGroup:[]},u.length=0,void this.onCmdListChange();this.cmdGroupFlag!==!0&&this.maxCmdGroupFlag!==!0&&(l.push(e),u.length=0,this.onCmdListChange())}});M(this,"saveUndoableState",(e,t,n)=>{const i={type:"undoableState",data:e,oldData:t,setState:n};this.pushUndo(i)});M(this,"saveUndoableCmd",e=>{const t={type:"undoableCmd",cmd:e};this.pushUndo(t)});M(this,"getUndo",()=>{const{undoList:e,redoList:t}=this;if((e==null?void 0:e.length)>0){const n=e.pop();return t.push(n),this.onCmdListChange(),lt.cloneDeep(n)}});M(this,"getRedo",()=>{const{undoList:e,redoList:t}=this;if(t.length>0){const n=t.pop();return e.push(n),this.onCmdListChange(),lt.cloneDeep(n)}});M(this,"redo",e=>{const{mode:t,uneditable:n}=$.getState();if(n)return;const i=a=>{var _;const{type:c,updateType:l,data:u,oldData:f,setState:d,cmd:p,cmdGroup:y}=a,m=t===Q;switch(e.allShapeNotChecked(),c){case"add":if(f.length===1){const A=m?u:e.transform(u);e.replace(A[0],{_historical:!1,needCheckType:!1}),e._setShapeChecked(A,!0),A==null||A.forEach(L=>{L!=null&&L.groups&&Object.keys(L==null?void 0:L.groups).forEach(P=>{var O,W,B;const R=(W=(O=e==null?void 0:e.data)==null?void 0:O.shapes)==null?void 0:W[P];(B=R==null?void 0:R.component)==null||B.fire("rank-change")})})}else if(u.length>0){const A=m?u:e.transform(u);this.checkParentSticky(e,A),e.add(A,{_historical:!1,isHistoryAction:!0}),e._setShapeChecked(A,!0),A==null||A.forEach(L=>{L!=null&&L.groups&&Object.keys(L==null?void 0:L.groups).forEach(P=>{var O,W,B;const R=(W=(O=e==null?void 0:e.data)==null?void 0:O.shapes)==null?void 0:W[P];(B=R==null?void 0:R.component)==null||B.fire("rank-change")})})}break;case"update":e._updateNodes(u,l),u==null||u.forEach(A=>{A!=null&&A.groups&&Object.keys(A==null?void 0:A.groups).forEach(L=>{var R,O,W;const P=(O=(R=e==null?void 0:e.data)==null?void 0:R.shapes)==null?void 0:O[L];(W=P==null?void 0:P.component)==null||W.fire("rank-change")})});break;case"delete":const x=e._linkUndoNodesInData(u);e.remove(x,{_historical:!1});break;case"setStyles":const{keys:b,style:w}=u[0];e.setStyles(w,b,{_historical:!1});const C=b.map(A=>e.data.shapes[A]);e._setShapeChecked(C,!0);break;case"undoableState":d(u);break;case"undoableCmd":(_=p==null?void 0:p.execute)==null||_.call(p);break;case"groupCmd":y.forEach(A=>i(A))}},s=this.getRedo();s&&(window.clearMouseLineEvents&&window.clearMouseLineEvents(),re(),i(s),e.lines.clear())});M(this,"undo",e=>{const{mode:t,uneditable:n}=$.getState();if(n)return;const i=a=>{var _;const{type:c,updateType:l,data:u,oldData:f,setState:d,cmd:p,cmdGroup:y}=a,m=t===Q;switch(e.allShapeNotChecked(),c){case"add":if(f.length===1){const A=m?f:e.transform(f);e.replace(A[0],{_historical:!1,needCheckType:!1}),e._setShapeChecked(A,!0)}else if(u.length>0){const A=e._linkUndoNodesInData(u);e.remove(A,{_historical:!1})}break;case"update":e._updateNodes(f,l);break;case"delete":const x=m?u:e.transform(u);this.checkParentSticky(e,x),e.add(x,{_historical:!1}),e._setShapeChecked(x,!0),this.isShowToolBox(x)&&hd(e,this.tbVnode),setTimeout(()=>{x==null||x.forEach(A=>{A!=null&&A.groups&&Object.keys(A==null?void 0:A.groups).forEach(L=>{var R,O,W;const P=(O=(R=e==null?void 0:e.data)==null?void 0:R.shapes)==null?void 0:O[L];(W=P==null?void 0:P.component)==null||W.fire("rank-change")})})},0);break;case"setStyles":const{keys:b,style:w}=f[0];e.setStyles(w,b,{_historical:!1});const C=b.map(A=>e.data.shapes[A]);e._setShapeChecked(C,!0);break;case"undoableState":d(f);break;case"undoableCmd":(_=p==null?void 0:p.undo)==null||_.call(p);break;case"groupCmd":y.reverse().forEach(A=>i(A))}},s=this.getUndo();s&&(window.clearMouseLineEvents&&window.clearMouseLineEvents(),re(),i(s),e.lines.clear())});M(this,"saveAction",({type:e="update",updateType:t,data:n=[],oldData:i=[],...s})=>{if(e==="setStyles"){const d={type:e,updateType:t,data:n,oldData:i};return void this.pushUndo(lt.cloneDeep(d),s)}const{mode:a}=$.getState(),c=a===Q,l=[],u=[];for(const d of n){const p=lt.cloneDeep(d);zr.forEach(y=>{delete p[y]}),p.isChecked=!1,l.push(c?p:Ne.reduction(p))}for(const d of i){const p=lt.cloneDeep(d);zr.forEach(y=>{delete p[y]}),p.isChecked=!1,u.push(c?p:Ne.reduction(d))}i=null;const f={type:e,updateType:t,data:l,oldData:u};this.pushUndo(lt.cloneDeep(f),s)});M(this,"processDeleteCmd",e=>{const{undoList:t}=this,n=t[t.length-1];if(n&&n.type==="groupCmd"&&n.cmdGroup.find(i=>i.type==="delete")){const i=[],s=[];n.cmdGroup.forEach(a=>{a.type==="delete"?i.push(...a.data):s.push(a)}),i.length>0&&(s.push({type:"delete",data:e._getSortedShapes(i,"history")}),i.length=0),n.cmdGroup=s}});M(this,"clearCmd",()=>{this.undoList=[],this.redoList=[],this.onCmdListChange()});M(this,"undoableSetState",e=>t=>{e(n=>(this.saveUndoableState(t,n,e),t))});M(this,"pushCmd",e=>{if(!lt.isObject(e))throw new Error("cmd 必须是对象");if(!["execute","undo"].every(t=>lt.isFunction(e[t])))throw new Error("cmd 必须定义execute、undo方法");return this.saveUndoableCmd(e),this.onCmdListChange(),e.execute()});M(this,"getUndoStatus",()=>{const e=this.undoList.length,t=this.redoList.length;return{undoSteps:e,redoSteps:t,hasUndo:e>0,hasRedo:t>0}});M(this,"index",0);M(this,"addListener",e=>{if(lt.isFunction(e)){const t=`l${this.index}`;return this.index+=1,this.listeners[t]=e,()=>{this.listeners[t]=null,delete this.listeners[t]}}throw new Error("sigma undo addListener 的参数必须是function")})}isShowToolBox(e){const t=Array.isArray(e)?e:[e];return this.tbVnode&&t.some(({key:n})=>n===this.tbVnode.key)}checkParentSticky(e,t){const n=Array.isArray(t)?t:[t],{shapes:i}=e.data;for(const{attach:s,key:a}of n){if(!s)continue;const c=i[s];c&&(c.sticky||(c.sticky=a))}}};const zs=class zs{constructor({direction:t,position:n,cursor:i,vnode:s,minWidth:a,minHeight:c,shapes:l}){M(this,"_start",{x:0,y:0});M(this,"_is3D",!1);this.direction=t,this.position=n,this.cursor=i,this.vnode=s,this.vnodeCopyBeforeUpdate=Ft(s),this.minWidth=a,this.minHeight=c,l&&(zs.shapes=l),this.subVnodes=[],this.init(),this.canDrag=!0}init(){const{x:t,y:n}=this.position;this.element=new ht().group().attr({class:this.direction}),this.c1=this.element.circle().attr({cx:t,cy:n,r:4,cursor:this.cursor,fill:"#98A3B7"}),this.c2=this.element.circle().attr({cx:t,cy:n,r:10,cursor:this.cursor,fill:"transparent"}),this.element.draggable(),this.element.on("dragstart",this._startDrag.bind(this)),this.element.on("dragmove",this._drag.bind(this),50),this.element.on("dragend",this._endDrag.bind(this))}update(t){this.position=t;const{x:n,y:i}=t;this.c1.attr({cx:n,cy:i}),this.c2.attr({cx:n,cy:i})}_startDrag(t){var a,c;this._is3D=$.getState().mode===Q;const{box:n}=t.detail,{cx:i,cy:s}=n;this._start.x=i,this._start.y=s,this.vnodeCopyBeforeUpdate=Ft(this.vnode),((a=this.vnode)==null?void 0:a.type)===Pn&&Object.keys((c=this.vnode)==null?void 0:c.relations).forEach(l=>{var f;const u=(f=zs.shapes)==null?void 0:f[l];if(u){const{position:d,width:p,height:y}=u;let m=this._is3D?_t(d):d;this._is3D&&(m.x=m.x+112.5,m.y=m.y+22.5,m=Te(m)),this.subVnodes.push({...m,w:p,h:y})}}),re()}_drag(t){t.preventDefault();const{scale:n}=$.getState(),{box:i}=t.detail,{cx:s,cy:a}=i,c={x:(s-this._start.x)*n,y:(a-this._start.y)*n},l={cx:s,cy:a};this._sizeChange(c,"draging",l)}_endDrag(t){const{scale:n}=$.getState(),{box:i}=t.detail,{cx:s,cy:a}=i,c=this.canDrag?{x:(s-this._start.x)*n,y:(a-this._start.y)*n}:{x:0,y:0};this._sizeChange(c,"end"),this.subVnodes=[],this.canDrag=!0}_sizeChange(t,n="draging",i={}){const{vnode:s}=this,a=n==="end",c=this.direction.split("-"),{x:l,y:u}=this._is3D?_t(t,!0):t,f={...t,dx:l,dy:u},d=this.direction==="left-top"?this._leftTopResize(f):c.reduce((A,L)=>({...this[`_${L}Resize`](f),...A}),{}),{width:p,height:y,x:m,y:_}=d;let x=!0;if((s==null?void 0:s.type)===Pn){const{position:A,width:L,height:P}=s,R={...A,w:L,h:P};if(p&&(R.w=p),y&&(R.h=y),m&&(R.x=m),_&&(R.y=_),this._is3D){let O=_t(R);O=Te(O),R.x=O.x,R.y=O.y}if(x=function(O,W){const{x:B,y:H,w:Y,h:V}=O,at=B+Y,rt=H+V;return W.every(ft=>{const{x:$t,y:Mt,w:Gt,h:Qt}=ft,et=$t+Gt,nt=Mt+Qt;return($t-At>=B||Math.abs($t-At-B)<10)&&(et+At<=at||Math.abs(et+At-at)<10)&&(Mt-At>=H||Math.abs(Mt-At-H)<10)&&(nt+At<=rt||Math.abs(nt+At-rt)<10)})}(R,this.subVnodes),this.canDrag=x,!a&&!x&&(p<s.width||y<s.height))return}const{cx:b,cy:w}=i;if(b&&w&&(this._start.x=b,this._start.y=w),p&&(s.width=p),y&&(s.height=y),a){const{x:A,y:L}=Te({x:s.width,y:s.height},{step:1});s.width=Math.ceil(A/At)*At,s.height=Math.ceil(L/At)*At}const C=m!==void 0&&_!==void 0;if(C)if(a){const A=this._is3D?ve:Te,{x:L,y:P}=A({x:m,y:_},{step:1});s.position.x=L,s.position.y=P}else s.position.x=m,s.position.y=_;a&&Nt.saveAction({groupBegin:!0}),s.component.fire("size-change",{isEnd:a,vnodeCopyBeforeUpdate:this.vnodeCopyBeforeUpdate}),C&&s.component.fire("position-change",{position:s.position,animating:!1}),a&&Nt.saveAction({updateType:["size-change","position-change"],data:[s],oldData:[this.vnodeCopyBeforeUpdate],groupEnd:!0})}_leftTopResize(t){const{x:n,y:i}=this.vnode.position,{x:s,y:a}=t;return{x:n+s,y:i+a,width:this._leftResize(t).width,height:this._topResize(t).height}}_topResize({y:t,dy:n}){const{height:i}=this.vnode,{x:s,y:a}=this.vnode.position,c=this._is3D?dt({x:0,y:n},!0):{x:0,y:t};return{x:c.x+s,y:c.y+a,height:Math.max(i-n,this.minHeight)}}_bottomResize({dy:t}){const{height:n}=this.vnode;return{height:Math.max(n+t,this.minHeight)}}_leftResize({x:t,dx:n}){const{width:i}=this.vnode,{x:s,y:a}=this.vnode.position,c=this._is3D?dt({x:n,y:0},!0):{x:t,y:0};return{x:c.x+s,y:c.y+a,width:Math.max(i-n,this.minWidth)}}_rightResize({dx:t}){const{width:n}=this.vnode;return{width:Math.max(n+t,this.minWidth)}}};M(zs,"shapes",{});let Gl=zs;const Fs=class Fs{constructor({vnode:t,shapes:n,container:i=".position",minWidth:s=22.5,minHeight:a=22.5}){M(this,"_wrapper",null);if(!t||!t.component)return;this.vnode=t,n&&(Fs.shapes=n);const{component:c}=t;this.container=c.findOne(i),this.minWidth=s,this.minHeight=a,this.map={},this._init()}_init(){const{mode:t}=$.getState(),{vnode:n,minWidth:i,minHeight:s}=this,{points:a}=n.data;this._initWrapper();for(const c in a){const l=a[c],{position:u,cursor:f}=l,d=new Gl({position:u,cursor:f[t],direction:c,vnode:n,minWidth:i,minHeight:s,shapes:Fs.shapes});this._wrapper.add(d.element),this.map[c]=d}}_initWrapper(){const{component:t}=this.vnode;this._wrapper=t.findOne(`.${pd}`),this._wrapper?this._wrapper.clear():this._wrapper=this.container.group().attr({class:pd})}update(t){const n=$.getState().mode===Q,{width:i,height:s}=this.vnode,a=t||Ml(i,s,n);for(const c in a){const l=a[c];this.map[c].update(l.position)}}show(){for(const t of Object.values(this.map))t.element.show()}hide(){for(const t of Object.values(this.map))t.element.hide()}};M(Fs,"shapes",{});let Hl=Fs;const ci=6;function ua(e,t,n){setTimeout(()=>{var c,l;const{uneditable:i}=$.getState(),{points:s,canResize:a}=e.data;a&&!i?(t||!e.resizePoints?(e.resizePoints=null,e.resizePoints=new Hl({vnode:e,shapes:(c=n==null?void 0:n.data)==null?void 0:c.shapes})):e.resizePoints.update(s),e.isChecked?e.resizePoints.show():e.resizePoints.hide()):((l=e.resizePoints)==null||l.hide(),e.resizePoints=null)},0)}class ha extends zi{constructor(){super(...arguments);M(this,"type",Es);M(this,"category",ir);M(this,"name","RECTANGLE");M(this,"cName","矩形");M(this,"gIndex",5);M(this,"width",180);M(this,"height",Fe);M(this,"data",{canResize:!0});M(this,"editable",["icon","iconSize","label","xAlign","location","position","fill","stroke","strokeWidth","strokeStyle","borderRadius","fontSize","color"]);M(this,"styles",{fill:{name:"背景色",type:"color",default:"transparent",value:null},stroke:{name:"边框色",type:"color",default:"#666",value:null},strokeWidth:{name:"边框宽度",type:"number",default:1,value:null},strokeStyle:{name:"边框样式",type:"select",default:"solid",value:null},icon:{name:"图标",type:"productIcon",default:null,value:null},iconSize:{name:"图标大小",type:"number",default:32,value:null},label:{name:"标题",type:"string",default:"",value:null},xAlign:{name:"标题对齐方式",type:"select",default:"left",value:null},location:{name:"标题定位",type:"select",default:"top",value:null},position:{name:"标题位置",type:"select",default:"inside",value:null},borderRadius:{name:"圆角",type:"number",default:0,value:null},fontSize:{name:"字号",type:"number",default:14,value:null},color:{name:"字体颜色",type:"color",default:"#242A35",value:null}});M(this,"connectable",!0)}create(n){const i=this._create(n);return i.data=this.data,lt.cloneDeep(i)}}class uv extends ha{constructor(){super();M(this,"type",sr);M(this,"category",ir);M(this,"name","REMARK");M(this,"cName","备注");M(this,"gIndex",4);M(this,"zIndex",1);M(this,"relations",{});M(this,"create",()=>{const n=super.create();return n.relations=this.relations,n});Object.assign(this.styles,{geometry:{name:"边框形状",type:"select",default:"rectangular",value:null},padding:{name:"边距",type:"number",default:1,value:null}}),this.styles.label.default="Double click to edit",this.styles.icon.default=null,this.styles.strokeWidth.default=2,this.styles.stroke.default="#FFF3A3",this.styles.fill.default="#FFF3A3",this.styles.yAlign={name:"标题垂直对齐方式",type:"select",default:"top",value:null},this.editable.push("geometry","padding")}}const hv={staticCheckedFill:qn,opacity:{name:"透明度",type:"number",default:1,value:null},dark:{name:"主边框颜色",type:"color",default:"#334966",value:null},fillLight:{name:"浅色填充色",type:"color",default:"#ffffff",value:null},fillDark:{name:"深色填充色",type:"color",default:td,value:null},baseFill:{name:"底座颜色",type:"color",default:"#E2E6EC",value:null},baseBgColor:{name:"底座背景颜色",type:"color",default:"#E2E6EC",value:null},baseBorderStroke:{name:"底座边框颜色",type:"color",default:"rgb(193, 198, 200)",value:null},baseStroke:{name:"底座边框",type:"color",default:"#D3D3D3",value:null},pattern:{name:"模型",type:"select",default:"gary",value:null}};class mp extends zi{constructor({name:n,category:i,description:s,cName:a,d3:c,d2:l}={}){super();M(this,"name","CVM");M(this,"type",rr);M(this,"category","COMPUTE");M(this,"description","Cloud virtual machine");M(this,"connectable",!0);M(this,"linkable",!0);M(this,"gIndex",8);M(this,"data",{});M(this,"positionOffsetY",1);M(this,"editable",["fillDark","baseStroke"]);M(this,"styles",hv);M(this,"width",jr);M(this,"height",jr);M(this,"create",n=>{const i=this._create(n);return i.data=this.data,lt.cloneDeep(i)});this.name=n,this.cName=a,i&&(this.category=i),this.description=s,this.data.d3=c,this.data.d2=l}}class Xn extends ha{constructor(){super();M(this,"type",Xo);M(this,"category","GROUP");M(this,"name","BASE GROUP");M(this,"cName","基础组");M(this,"gIndex",4);M(this,"zIndex",1);M(this,"relations",{});M(this,"create",n=>{const i=super.create(n);return i.relations=this.relations,i});Object.assign(this.styles,{geometry:{name:"边框形状",type:"select",default:"rectangular",value:null},padding:{name:"边距",type:"number",default:.5,value:null}}),this.styles.label.default="基础组 Base Group",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_863_1748)">
<mask id="mask0_863_1748" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<path d="M16 0H0V16H16V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_863_1748)">
<path d="M8 0.842285L12 3.17562V6.42529L15 8.17562V12.8244L11 15.1577L8 13.4073L5 15.1577L1 12.8244V8.17562L4 6.42529V3.17562L8 0.842285ZM5 8.17562L3 9.32299V11.676L5 12.842L7 11.675V10.8673V10V9.5V9.32299L5 8.17562ZM11 8.17562L9 9.32299V11.676L11 12.842L13 11.675V10.8673V10V9.32299L12.312 8.92229L11 8.17562ZM8 3.15699L6 4.32299V6.42529L8 7.59128L10 6.42529V5.86728V5.5V4.32299L9.312 3.92229L8 3.15699Z" fill="#334966"/>
</g>
</g>
<defs>
<clipPath id="clip0_863_1748">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>`,this.styles.iconSize.default=16,this.styles.strokeWidth.default=2,this.styles.borderRadius.default=5,this.styles.stroke.default="#60708A",this.styles.fill.default="#F3F4F7",this.editable.push("geometry","padding")}}class Ul extends Xn{constructor(){super();M(this,"name","AREA");M(this,"cName","区域");M(this,"type",Go);this.styles.label.default="地域 Area",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 8C2 7.686 2.032 7.379 2.078 7.078L6 11V12H8V14C4.691 14 2 11.309 2 8ZM14 8C14 9.093 13.701 10.116 13.188 11H12L10 9H5V7H8V4H10V2.35C12.327 3.176 14 5.394 14 8ZM8 0C3.582 0 0 3.582 0 8C0 12.418 3.582 16 8 16C12.418 16 16 12.418 16 8C16 3.582 12.418 0 8 0Z" fill="#18609C"/>
</svg>
`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#18609C",this.styles.borderRadius.default=5,this.styles.fill.default="transparent"}}class Vl extends Xn{constructor(){super();M(this,"name","AUTO SCALING");M(this,"cName","弹性伸缩");M(this,"type",Ho);this.styles.label.default="未命名弹性伸缩",this.styles.icon.value=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="server">
<path id="stroke3" d="M2.66602 9.33333H7.33268L8.66602 10.6667H13.3327L11.9993 7L13.3327 3.33333H8.66602L7.33268 2H2.66602V5.66667V9.33333ZM2.66602 9.33333V14.3333" stroke="#2A86FF" stroke-width="2" stroke-linecap="square"/>
</g>
</svg>`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#2A86FF",this.styles.strokeStyle.default="dashed"}}class jl extends Xn{constructor(){super();M(this,"name","ZONE");M(this,"cName","可用区");M(this,"type",Uo);this.styles.label.default="可用区 Zone",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.333 0L1 5.5L8.333 11L15.666 5.5L8.333 0ZM4.333 5.5L8.333 2.5L12.333 5.5L8.333 8.501L4.333 5.5ZM8.333 13.5L2.667 9.25L1 10.5L8.333 16L15.666 10.5L13.999 9.25L8.333 13.5Z" fill="#3178AF"/>
</svg>
`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#3178AF",this.styles.borderRadius.default=5,this.styles.fill.default="#F5F9FF"}}class ql extends Xn{constructor(){super();M(this,"name","SECURITY GROUP");M(this,"cName","安全组");M(this,"type",Vo);this.styles.label.default="安全组 Security Group",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.49963 9.46438L11.9497 5.01431L10.5355 3.6001L7.49975 6.63583L5.70731 4.84306L4.29297 6.25715L7.49963 9.46438Z" fill="#06774A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15 0H1V7.4C1 7.4 1.054 13.359 8 15C14.946 13.359 15 7.4 15 7.4V0ZM13 7.362C12.987 7.802 12.732 11.589 8 12.934C3.268 11.589 3.013 7.803 3 7.4V2H13V7.362Z" fill="#06774A"/>
</svg>`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#06774A",this.styles.strokeStyle.default="dashed",this.styles.borderRadius.default=5,this.styles.fill.default="transparent"}}class Yl extends Xn{constructor(){super();M(this,"name","SUBNET");M(this,"cName","子网");M(this,"type",jo);this.styles.label.default="子网 Subnet",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_863_1775)">
<mask id="mask0_863_1775" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<path d="M16 0H0V16H16V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_863_1775)">
<path d="M8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16ZM2.08296 9C2.55904 11.8377 5.027 14 8 14C10.973 14 13.441 11.8377 13.917 9H2.08296ZM2.08296 7H13.917C13.441 4.16229 10.973 2 8 2C5.027 2 2.55904 4.16229 2.08296 7ZM8.64256 12.8552C9.17713 11.6333 9.5 9.88855 9.5 8C9.5 6.11145 9.17713 4.36666 8.64256 3.14478C8.43061 2.66034 8.20314 2.30218 8 2.09422C7.79686 2.30218 7.56939 2.66034 7.35744 3.14478C6.82287 4.36666 6.5 6.11145 6.5 8C6.5 9.88855 6.82287 11.6333 7.35744 12.8552C7.56939 13.3397 7.79686 13.6978 8 13.9058C8.20314 13.6978 8.43061 13.3397 8.64256 12.8552ZM8 16C6.067 16 4.5 12.4183 4.5 8C4.5 3.58172 6.067 0 8 0C9.933 0 11.5 3.58172 11.5 8C11.5 12.4183 9.933 16 8 16Z" fill="#5865B4"/>
</g>
</g>
<defs>
<clipPath id="clip0_863_1775">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#5865B4",this.styles.strokeStyle.default="dashed",this.styles.borderRadius.default=5,this.styles.fill.default="transparent"}}class Xl extends Xn{constructor(){super();M(this,"name","VPC");M(this,"cName","虚拟私有网络");M(this,"type",qo);this.styles.label.default="虚拟私有网络 VPC",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 12H9V9H7V12ZM3 13H13V8H3V13ZM4 6C4 3.794 5.794 2 8 2C10.206 2 12 3.794 12 6H4ZM14 6C14 2.691 11.309 0 8 0C4.691 0 2 2.691 2 6H1V15H15V6H14Z" fill="#354D7F"/>
</svg>
`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#354D7F",this.styles.strokeStyle.default="dashed",this.styles.borderRadius.default=5,this.styles.fill.default="transparent"}}class xp extends Xn{constructor(){super();M(this,"name","CCN");M(this,"cName","云联网 CCN");M(this,"type",Ko);this.styles.label.default="云联网 CCN",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.999 6.02149C13.999 6.01449 14 6.00749 14 6.00049C14 2.68649 11.313 0.000488281 8 0.000488281C4.686 0.000488281 2 2.68649 2 6.00049C2 6.00749 2.001 6.01449 2.001 6.02149C0.793 6.93449 0 8.36949 0 10.0005C0 12.7615 2.238 15.0005 5 15.0005V13.0005C3.346 13.0005 2 11.6545 2 10.0005C2 9.06449 2.439 8.19649 3.206 7.61749L4.001 7.01749V6.02149V5.93449C4.035 3.75949 5.816 2.00049 8 2.00049C10.185 2.00049 11.966 3.76049 12 5.93749L11.999 5.94549V6.02149V7.01749L12.794 7.61749C13.561 8.19649 14 9.06449 14 10.0005C14 11.6545 12.654 13.0005 11 13.0005H9V10.0005H10V6.00049H6V10.0005H7V15.0005H11C13.762 15.0005 16 12.7615 16 10.0005C16 8.36949 15.207 6.93449 13.999 6.02149Z" fill="#354D7F"/>
</svg>`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#354D7F",this.styles.strokeStyle.default="dashed",this.styles.borderRadius.default=5,this.styles.fill.default="transparent"}}class _p extends Xn{constructor(){super();M(this,"name","TKE GROUP");M(this,"cName","TKE 集群 TKE Cluster");M(this,"type",Pn);Object.assign(this.styles,{showTips:{name:"是否显示状态提示",type:"boolean",default:!0,value:null}}),this.styles.label.default="TKE 集群 TKE Cluster",this.styles.icon.default=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 9V16L1 12.8933V9H7ZM15 9V12.8933L9 16V9H15ZM5 11H3V11.9878L5 13V11ZM13 11H11V13L13 11.9878V11ZM7 0V7H1V3.10668L7 0ZM9 0L15 3.10668V7H9V0ZM5 3L3 4.01219V5H5V3ZM11 3V5H13V4.01219L11 3Z" fill="#006EFF"/>
</svg>
`,this.styles.strokeWidth.default=2,this.styles.stroke.default="#006EFF",this.styles.borderRadius.default=5,this.styles.fill.default="#D5E7FE"}}class vp extends zi{constructor({name:n,cName:i,category:s,description:a,connection:c,d3:l,d2:u,positionOffsetY:f=0,positionOffsetX:d=0}={}){super();M(this,"type",Yo);M(this,"category","SECURITY");M(this,"connectable",!0);M(this,"linkable",!0);M(this,"connection",[]);M(this,"data",{});M(this,"gIndex",8);M(this,"create",()=>{const n=this._create();return n.data=this.data,lt.cloneDeep(n)});this.name=n,this.cName=i,this.description=a,this.connection=c,s&&(this.category=s),this.positionOffsetY=f,this.positionOffsetX=d,this.data={d2:u,d3:l}}}function bp(e,t){const{x:n,y:i}=e,{x:s,y:a}=t;if(s-n==0)return{k:1/0,b:n};const c=(a-i)/(s-n);return{k:c,b:i-c*n}}function wp(e,t,n,i){const[s,a]=e;if(i==="x"){const c=s.x*t+n,l=a.x*t+n;return[{x:s.x,y:c},{x:a.x,y:l}]}if(i==="y"){if(t===1/0)return[{x:n,y:s.y},{x:n,y:a.y}];const c=(s.y-n)/t,l=(a.y-n)/t;return[{x:c,y:s.y},{x:l,y:a.y}]}}function kp(e,t,n,i){const s=e.line[0],{length:a}=t;function c(f,d){return i==="x"?s.x*f+d-s.y:i==="y"?d-e.b:void 0}const l=function(){let f=0,d=a-1;for(;f<=d;){const p=~~((f+d)/2),y=t[p];if(c(y.k,y.b)<-n){if(p===a-1)return;const m=t[p+1],_=c(m.k,m.b);if(Math.abs(_)<=n)return p+1;f=p+1}else d=p-1}}(),u=function(){let f=0,d=a-1;for(;f<=d;){const p=~~((f+d)/2),y=t[p];if(c(y.k,y.b)>n){if(p===0)return;const m=t[p-1],_=c(m.k,m.b);if(Math.abs(_)<=n)return p-1;d=p-1}else f=p+1}}();return l===void 0&&u===void 0?null:l!==void 0&&u!==void 0&&l<=u?[l,u]:l!==void 0?[l,a-1]:u!==void 0?[0,u]:void 0}class fv{constructor(){this.min=5,this.className="aligment-line"}add(t,n,i,s){const{width:a,height:c}=t,l=this.getPlaneAlignmentLines(t.position,a,c,n,i,s);$.dispatch({type:Xf,value:[t.key,l]})}sort(t){if(!(t&&t.length>0))return;const n=t.map(({key:u})=>u),{alignments:i}=$.getState();function s(u,f,d){f.forEach(p=>{const{k:y,b:m}=p,_=`${y}${m}`,x=u[d][_];if(x){let b=p.line;b=[...new Set([...x.line.map(w=>JSON.stringify(w)),...p.line.map(w=>JSON.stringify(w))])].map(w=>JSON.parse(w)),b.sort((w,C)=>w[d]-C[d]),x.line=b}else u[d][_]=p})}const a=[...i.lines.entries()].reduce((u,[f,{x:d,y:p}])=>(n.includes(f)||(s(u,d,"x"),s(u,p,"y")),u),{x:{},y:{}}),c=Object.values(a.x),l=Object.values(a.y);c.sort((u,f)=>u.b-f.b),l.sort((u,f)=>u.b-f.b),$.dispatch({type:Zf,value:{x:c,y:l}})}getPlaneAlignmentLines(t,n,i,s,a,c={x:-n/2,y:-i}){const{mode:l}=$.getState(),{x:u,y:f}=l===ee?t:_t(t);let d,{x:p,y}=c;p+=u,y+=f;const m=[{x:p,y},{x:p+n/2,y},{x:p+n,y},{x:p+n,y:y+i/2},{x:p+n,y:y+i},{x:p+n/2,y:y+i},{x:p,y:y+i},{x:p,y:y+i/2}],_={x:[[m[0],m[2]],[m[7],m[3]],[m[6],m[4]]],y:[[m[0],m[6]],[m[1],m[5]],[m[2],m[4]]]},x={x:_.x.map(b=>b.map(w=>dt(w))),y:_.y.map(b=>b.map(w=>dt(w)))};return d=s?a?{x:[x.x[2]],y:x.y}:x:_,{x:d.x.map(b=>{const{k:w,b:C}=bp(...b);return{line:b,k:w,b:C}}),y:d.y.map(b=>{const{k:w,b:C}=bp(...b);return{line:b,k:w,b:C}})}}getRelationLines(t){const{alignments:n}=$.getState(),i=n.lines.get(t),{sortX:s,sortY:a}=n,c=s.filter(({k:d})=>d===i.x[0].k),l=a.filter(({k:d})=>d===i.y[0].k),u=[],f=[];for(let d=0;d<i.x.length;d++){const p=i.x[d],y=kp(p,c,this.min,"x");if(!y)continue;const[m,_]=y;for(let x=m;x<=_;x++){const b=c[x],{line:w,k:C,b:A}=b,[L,P]=wp(p.line,C,A,"x"),R=[L,P,...w].sort((O,W)=>O.x-W.x);u.push(R)}}for(let d=0;d<i.y.length;d++){const p=i.y[d],y=kp(p,l,this.min,"y");if(!y)continue;const[m,_]=y;for(let x=m;x<=_;x++){const b=l[x],{line:w,k:C,b:A}=b,[L,P]=wp(p.line,C,A,"y"),R=[L,P,...w].sort((O,W)=>O.y-W.y);f.push(R)}}return u.concat(f)}draw(t){const n=this.getRelationLines(t);this.clear();const i=new ht().group({class:this.className});n.forEach(s=>{const{length:a}=s,c=i.group();c.line(s[0].x,s[0].y,s[a-1].x,s[a-1].y).attr({"stroke-width":1,stroke:"#999","stroke-dasharray":"4,2"}),s.forEach(({x:u,y:f})=>{c.line(u-2,f-2,u+2,f+2).attr({"stroke-width":1,stroke:"#999"}),c.line(u+2,f-2,u-2,f+2).attr({"stroke-width":1,stroke:"#999"})});const{container:{gCache:l}}=$.getState();l.add(i)})}clear(){const{container:{gCache:t}}=$.getState(),n=t.findOne(`.${this.className}`);n==null||n.remove()}}function fa(e,t){const n=Object.keys((t==null?void 0:t.relations)||{});if(!n.length)return[];const i=[];for(const s of n){const a=e[s];a&&Qe.includes(a.type)&&(i.push(s),i.push(...fa(e,a)))}return i}function dv(e,t,n){if(!t)return;const i=Gr(Ne.getflat(t)),s=Mo(i);let a;for(const c of e){if(c.key===t.key||n.includes(c.key))continue;const l=Gr(Ne.getflat(c)),u=gf(l,i);if(u>=s/2||u>=Mo(l)/2){a=c;break}}return a}function Sp(e){const{core:t}=$.getState(),n={},i=[];for(const s of e)if(Qe.includes(s.type)){const a=t._getRelationShapes(s);for(const{key:c}of a)n[c]=!0}for(const s of e)n[s.key]||Ms(s)&&i.push(s);return i}function pv(e,t){!t||t.attach||id.includes(t.type)||function(n,i){const{initial:s=!1}=n.detail||{},{key:a,isChecked:c,component:l}=i,u="checked-shapes",{container:{gCache:f}}=$.getState();let d=f.findOne(`.${u}`);if(d||(d=f.group({class:u})),c){const p=new ht().group().attr({key:a});p.insertAfter(l),l.remember("ghost",p),l.toParent(d)}else{if(s)return;const p=l.remember("ghost");p==null||p.replace(l),l.forget("ghost")}l.off("deleted.checked"),l.on("deleted.checked",()=>{var p;(p=l==null?void 0:l.remember("ghost"))==null||p.remove()})}(e,t)}function Ws(e){const t=$.getState().mode===Q,{position:n}=e;return t?_t(n):n}function gv(e,t){t!=null&&t.component&&(function(n,i){const{component:s}=i;s.on("style-changed",a=>{var l;const{initial:c}=a.detail||{};if(i.attach){const{shapes:u}=n.data,f=u[i.attach];(l=f==null?void 0:f.component)==null||l.fire("attach-change",{type:c?"create":"changed"})}}),s.on("deleted",()=>{var a;if(i.attach){const{shapes:c}=n.data,l=c[i.attach];(a=l==null?void 0:l.component)==null||a.fire("attach-change",{type:"deleted"})}$.dispatch({type:Kf,value:i.key})})}(e,t),function(n,i){const{type:s,component:a}=i;id.includes(s)&&(a.on("start-move",()=>{const{shapes:c}=n.data,l=fa(c,i);for(const u of[i.key].concat(l)){const{component:f}=c[u];if(!f)continue;if(f.remember("ghost")){f.front();continue}const d=new ht().group().attr({key:u});d.insertAfter(f),f.remember("ghost",d),f.front(),f.off("deleted.group-queue"),f.on("deleted.group-queue",()=>{var p;(p=f.remember("ghost"))==null||p.remove()})}}),a.on("end-move",()=>{const{shapes:c}=n.data,l=fa(c,i);for(const u of[i.key].concat(l)){const{component:f}=c[u];if(!f)continue;const d=f.remember("ghost");d==null||d.replace(f),f.forget("ghost")}}))}(e,t),function(n){const{mode:i,core:s}=$.getState();if(i!==Q)return;const{component:a}=n;a.on(["initial","end-move"],()=>{if(!g_.includes(n.type))return;const{shapes:c}=s.data,l=Ws(n),u=a.remember("ghost")||a;let f=u.prev();for(;f;){const p=Ws(c[f.attr("key")]);l.y<p.y||l.y===p.y&&l.x<p.x?(u.insertBefore(f),f=u.prev()):f=null}let d=u.next();for(;d;){const p=Ws(c[d.attr("key")]);l.y>p.y||l.y===p.y&&l.x>p.x?(u.insertAfter(d),d=u.next()):d=null}})}(t))}let Kl=null,Zl=null;const qr={},yv=si.getManager();function F(e={}){this.instance=null,this.data={id:Eo(),shapes:{},cached:[]},this.options=e,this.lines=new fv,this.cmdStack=Nt,this.allStyles=[new ha,new cd,new Cl,new ud,new ld,new Xn,new Ul,new Vl,new jl,new ql,new Xl,new Yl,new mp,new vp,new Al].reduce((t,{type:n,styles:i})=>(t[n]=i,t),{}),this.isDoubleTouch=!1,this._init()}F.prototype._init=function(){const{mode:e}=$.getState();document.oncontextmenu=function(){return!1},e===ee&&(this.data.shapes=this.transform(this.data.shapes),re()),this._initRender(),this._initWindowFunction(),this._initAssistFunction(),this.cmdStack.clearCmd()},F.prototype._toggleMode=function(){const{mode:e}=$.getState();e===ee?(this.data.shapes=this.transform(this.data.shapes),re()):e===Q&&(this.data.shapes=this.reduction(this.data.shapes)),this.forceRender()},F.prototype.add=function(e,t={_historical:!0}){re();const{shapes:n={}}=this.data,i=lt.isArray(e)?e:[e],s=this._vnodeVerify(i);if(s.length){for(let a=0;a<s.length;a++){const c=s[a];n[c.key]=c}this._setData({vnodes:s,action:Wi},t,"add"),t._historical&&Nt.saveAction({type:"add",data:s})}},F.prototype.update=function(e,t={}){const{shapes:n}=this.data;for(let i=0;i<e.length;i++){const{key:s}=e[i];if(n[s]){const a=n[s],c=e[i];for(const l in c)zr.includes(l)||(a[l]=We.cloneDeep(c[l]))}}this._setData({vnodes:e,action:ml}),this._relateUpdate(e,t)},F.prototype.remove=function(e,t={}){var d,p,y;const{canRemoveLocked:n=!1,_historical:i=!0,isSub:s=!1}=t,a=lt.isArray(e)?e:[e],c=Ft(a),{uneditable:l}=$.getState();if(l)return;const{shapes:u}=this.data,f=n?a:a.filter(({lock:m})=>!m);i&&!s&&Nt.saveAction({type:"delete",data:[]}),Nt.tbVnode=El;for(let m=0;m<f.length;m++){const _=f[m],{type:x,key:b,groups:w={},component:C}=_;if(u[b]){if(delete u[_.key],C.fire("deleted",{...t,isSub:!0,_historical:i}),i&&Nt.saveAction({type:"delete",data:[c[m]],isSub:!0}),Qe.includes(x)){const{relations:A={}}=_;for(const L in A){const P=u[L],R=Ft(P);P&&((d=P.groups)==null||delete d[b]),P&&i&&Nt.saveAction({data:[P],oldData:[R],isSub:!0})}for(const L in w){const P=u[L];for(const R in A){const O=u[R];O!=null&&O.groups&&(O.groups[L]=P==null?void 0:P.type)}P!=null&&P.relations&&(P.relations={...P.relations,...A})}}for(const A in w){const L=u[A];if(L){const P=Ft(L);(p=L.relations)==null||delete p[b],(y=L.component)==null||y.fire("relations-change",{vnodesPrevSnapshots:[P],isEnd:i})}}}}this._setData({vnodes:f,action:Qf},{...t,_historical:i},"delete"),re(),yv.remove(f.map(m=>m.key)),this._relateRemove(f,t),i&&!s&&Nt.processDeleteCmd(this)},F.prototype._relateUpdate=function(e){this.__whenShapeUpdate(e)},F.prototype._relateRemove=function(e,t){const{callbacks:n={}}=this.options;this.__whenShapeDelete(e,t),n.onShapeDelete&&n.onShapeDelete(e)},F.prototype._setData=function({vnodes:e,action:t},n){this.render(e,t,n),t===Wi&&this.onlyShapeChecked(e.filter(({type:i,attach:s})=>![pe].includes(i)&&!s)),this._autoLocalSave(),this.__onGraphChange()},F.prototype.render=function(e,t,n){for(let i=0;i<e.length;i++){const s=e[i];switch(t){case Wi:this._addComponent(s,n),this.__checkShapeLocation(s,Wi);break;case ml:this._updateComponent(s),this.__checkShapeLocation(s,ml);break;case Qf:this._removeComponent(s);break;default:throw new Error(`错误的渲染模式，检查渲染行为 ${t}, ${e}`)}}},F.prototype.forceRender=function(){const{container:{gText:e,gImage:t,gIcon:n,gProduct:i,gRect:s,gLine:a,gCircle:c,gNetwork:l,gCache:u}}=$.getState();e.clear(),t.clear(),n.clear(),i.clear(),s.clear(),a.clear(),c.clear(),l.clear(),u.clear(),console.time("force render");const{shapes:f}=this.data;for(const m in f)d=this,p=f[m],function(_,x){const{type:b,styles:w={}}=x,C=_.allStyles[b]||{};w.borderRadius&&(w.borderRadius.type="number"),x.styles={...C,...w}}(d,p);var d,p;const y=this._getSortedShapes(Object.values(f));for(const m of y)this._addComponent(m),this.__checkShapeLocation(m,Wi);console.timeEnd("force render")},F.prototype.rerender=function(){const{shapes:e}=this.data,t=[];for(const n in e)t.push(e[n]);this.update(t)},F.prototype._getSortedShapes=function(e,t="data"){const{shapes:n}=this.data,i=[],s=[],a=[],c=[],l=[];function u(d,p){if(!d||!Qe.includes(d.type))return;i[p]||(i[p]=[]),i[p].push(d);const{key:y,relations:m={}}=d;for(const _ in m){const x=n[_];x&&x.groups[y]?u(n[_],p+1):delete d.relations[_]}}for(const d of e)if(Qe.includes(d.type)){if(t==="history")i.push(d);else{const{groups:p={}}=d;Object.keys(p).length===0&&u(d,0)}s.push(d)}else d.attach?c.push(d):d.type!==pe?l.push(d):a.push(d);const f=[...new Set(i.flat())];for(const d of s)f.find(({key:p})=>p===d.key)||(d.groups={},d.relations={},l.push(d));return l.sort((d,p)=>{const y=Ws(d),m=Ws(p);return y.y<m.y||y.y===m.y&&y.x<m.x?-1:1}).concat(f).concat(a).concat(c)},F.prototype._initRender=function(){const{shapes:e}=this.data,t=this._getSortedShapes(Object.values(e));for(const n of t)this.__checkShapeLocation(n,Wi)},F.prototype._initRenderWithStringData=function(e){const{mode:t}=$.getState(),n=JSON.parse(e);this.data.shapes=n,this.data.cached=[],$.dispatch({type:Jf}),t===ee&&(this.data.shapes=this.transform(this.data.shapes),re()),this.forceRender(),this._autoLocalSave()},F.prototype._pureRender=async function(e,{onlySelection:t}){const{shapes:n}=this.data,i=Ft(this._getSortedShapes(Object.values(n)));for(const s of i)s.component=null,t&&s.isChecked&&(this._setShapeChecked(s,!1,!1),await this._renderPureComponent(e,s)),t||(this._setShapeChecked(s,!1,!1),await this._renderPureComponent(e,s)),s.component&&this.__checkShapeLocation(s,Wi)},F.prototype.transform=function(e){if(lt.isArray(e)){const n=We.cloneDeep(e);for(const i of n)i.component=null,Ne.transform(i);return n}const t=We.cloneDeep(e,!0);for(const n in t){const i=t[n];i.component=null,Ne.transform(i)}return t},F.prototype.reduction=function(e){if(lt.isArray(e)){const n=We.cloneDeep(e);for(const i of n)i.component=null,Ne.reduction(i);return n}const t=We.cloneDeep(e,!0);for(const n in t){const i=t[n];i.component=null,Ne.reduction(i)}return t},F.prototype._addComponent=function(e,t={}){const{component:n,container:i}=Ne.render(this,e);let s,a,c=!1;const l=this;setTimeout(()=>{n.on("mousedown",u=>this.__onmousedown(u,e)),n.on("mouseup",u=>this.__onmouseup(u,e)),n.on("mouseover",u=>this.__onmouseover(u,e)),n.on("mouseout",u=>this.__onmouseout(u,e)),n.on("dblclick",u=>this.__ondoubleclick(u,e)),n.on("contextmenu",u=>this.__onrightclick(u,e)),n.on("checked-change",u=>pv(u,e)),n.on("touchstart",u=>{setTimeout(()=>{if(l.isDoubleTouch)return;const f=u.targetTouches[0];qr.x=f.clientX,qr.y=f.clientY;const{root:d,viewBox:p,scale:y,dropzone:m,doc:_}=$.getState(),x=d.classList.contains("isRotated"),{callbacks:b}=this.options;n.on("touchmove",w=>{if(l.isDoubleTouch)return;const C=w.targetTouches[0];w.preventDefault();const{x:A,y:L,w:P,h:R}=p;let O,W;x?(W=C.clientX-qr.x,O=qr.y-C.clientY):(O=qr.x-C.clientX,W=qr.y-C.clientY);const B=m.node.width.baseVal.value,H=m.node.height.baseVal.value,Y=m.node.x.baseVal.value,V=m.node.y.baseVal.value;if(s=lt.round(A+O*y,0),a=lt.round(L+W*y,0),c=!1,V>=0&&a>=0){if(a<=V)return void(a=V);if(a-V+R>=H)return void(a=H-R+V)}if(V<0&&a>=0&&Math.abs(V)+a+R>=H)return void(a=H-Math.abs(V)-R);if(V<0&&a<0){if(a<=V)return void(a=V);if(Math.abs(V)-Math.abs(a)+R>=H)return void(a=-(H-Math.abs(V)-R))}if(Y>=0&&s>=0){if(s<=Y)return void(s=Y);if(s-Y+P>=B)return void(s=B-P+Y)}if(Y<0&&s>=0&&Math.abs(Y)+s+P>=B)return void(s=B-Math.abs(Y)-P);if(Y<0&&s<0){if(s<=Y)return void(s=Y);if(Math.abs(Y)-Math.abs(s)+P>=B)return void(s=-(B-Math.abs(Y)-P))}c=!0;const at=`${s} ${a} ${p.w} ${p.h}`;_.attr({viewBox:at}),b.onDocMove&&b.onDocMove()})},0)}),n.on("touchend",()=>{setTimeout(()=>{const{viewBox:u}=$.getState();s!==void 0&&a!==void 0&&c&&($.dispatch({type:qe,value:{viewBox:{x:s,y:a,w:u.w,h:u.h}}}),s=void 0,a=void 0)},0)})},0),gv(this,e),i.add(n),n.fire("style-changed",{styles:e.styles,initial:!0,pure:!t._historical}),n.fire("checked-change",{initial:!0}),this.__onShapeInit(e)},F.prototype._updateComponent=function(e){Ne.rerender(this,e)},F.prototype._removeComponent=function(e){var t;(t=e.component)==null||t.remove()},F.prototype._renderPureComponent=async function(e,t){const[n,i]=await Ne.exports(this,t),s={initial:!0,styles:t.styles,isExport:!0};if(n.fire("style-changed",s),!lt.isNumber(i))throw new Error(`容器不存在，请检查数据: ${t}`);e[i].add(n)},F.prototype.__onShapeInit=function(e){const{callbacks:t={}}=this.options;t.onShapeInit&&t.onShapeInit(e)},F.prototype._initWindowFunction=function(){const{root:e,doc:t}=$.getState();Ie(e,`keydown${ri}`,i=>{const s=i.key,a=i.ctrlKey||i.metaKey,{shiftKey:c}=i;switch(!0){case(a&&(s==="c"||s==="C")):this._keyPressCtrlAndC();break;case(a&&(s==="v"||s==="V")):this._keyPressCtrlAndV();break;case(a&&(s==="d"||s==="D")):i.preventDefault(),this._keyPressCtrlAndD();break;case(a&&(s==="y"||s==="Y")):case(a&&c&&(s==="z"||s==="Z")):i.preventDefault(),this._keyPressCtrlAndY();break;case(a&&(s==="z"||s==="Z")):i.preventDefault(),this._keyPressCtrlAndZ();break;case(a&&(s==="x"||s==="X")):this._keyPressCtrlAndX();break;case(a&&(s==="s"||s==="S")):this._keyPressCtrlAndS();break;case(a&&(s==="l"||s==="L")):this._keyPressCtrlAndL();break;case(a&&(s==="g"||s==="G")):i.preventDefault(),this._keyPressCtrlAndG();break;case((s==="ArrowUp"||s==="W"||s==="w")&&!a):re(),this._keyPressUp();break;case((s==="ArrowDown"||s==="S"||s==="s")&&!a):re(),this._keyPressDown();break;case((s==="ArrowRight"||s==="D"||s==="d")&&!a):re(),this._keyPressRight();break;case((s==="ArrowLeft"||s==="A"||s==="a")&&!a):re(),this._keyPressLeft();break;case(a&&s==="ArrowLeft"):i.preventDefault(),this._keyPressCtrlAndLeft();break;case(a&&s==="ArrowRight"):i.preventDefault(),this._keyPressCtrlAndRight();break;case(a&&s==="ArrowUp"):i.preventDefault(),this._keyPressCtrlAndUp();break;case(a&&s==="ArrowDown"):i.preventDefault(),this._keyPressCtrlAndDown()}});const{callbacks:n={}}=this.options;e.addEventListener("keydown",i=>{const s=i.key,a=i.ctrlKey||i.metaKey;s!=="Backspace"&&s!=="Delete"||this._keyPressDelete(),!a||s!=="a"&&s!=="A"||(re(),this._keyPressCtrlAndA())}),t.on("wheel",function(i){i.preventDefault();const{ctrlKey:s,metaKey:a,deltaX:c,deltaY:l}=i;if(s||a)return;const{viewBox:u,scale:f,doc:d,dropzone:p}=$.getState(),y=p.node.width.baseVal.value,m=p.node.height.baseVal.value,_=p.node.x.baseVal.value,x=p.node.y.baseVal.value,{x:b,y:w,w:C,h:A}=u,L=b+f*c,P=w+f*l;if(x>=0&&P>=0&&(P<=x||P-x+A>=m)||x<0&&P>=0&&Math.abs(x)+P+A>=m||x<0&&P<0&&(P<=x||Math.abs(x)-Math.abs(P)+A>=m)||_>=0&&L>=0&&(L<=_||L-_+C>=y)||_<0&&L>=0&&Math.abs(_)+L+C>=y||_<0&&L<0&&(L<=_||Math.abs(_)-Math.abs(L)+C>=y))return;const R=`${L} ${P} ${C} ${A}`;$.dispatch({type:qe,value:{viewBox:{x:L,y:P,w:C,h:A}}}),d.attr({viewBox:R}),re(),n.onDocMove&&n.onDocMove()})},F.prototype._initAssistFunction=function(){const{dropzone:e,container:{gCache:t}}=$.getState(),n=this;let i;const s=new ht().polygon().attr({class:"selection"});function a(u){u.ctrlKey||u.metaKey||(i=e.point(u.clientX,u.clientY),window.addEventListener("mousemove",c),window.addEventListener("mouseup",l)),n.allShapeNotChecked(),re()}Ie(window,`keydown${ri}`,u=>{u.code==="Space"&&e.off("mousedown",a)}),Ie(window,`keyup${ri}`,u=>{sessionStorage.getItem("sigmaIsGrab")==="true"||u.code!=="Space"||e.on("mousedown",a)}),e.on("mousedown",a);const c=u=>{const{mode:f}=$.getState(),d=e.point(u.clientX,u.clientY),p=f===Q?function(_,x){const b=x.x-_.x,w=x.y-_.y,C=Math.abs(b),A=Math.abs(w)-C*Math.tan(30/180*Math.PI),L=A*Math.sin(30/180*Math.PI),P=A*Math.cos(30/180*Math.PI),R={},O={};return b>=0?w>=0?(R.x=x.x+P,R.y=x.y-L,O.x=_.x-P,O.y=_.y+L):(R.x=x.x+P,R.y=x.y+L,O.x=_.x-P,O.y=_.y-L):w>=0?(R.x=_.x+P,R.y=_.y+L,O.x=x.x-P,O.y=x.y-L):(R.x=_.x+P,R.y=_.y-L,O.x=x.x-P,O.y=x.y+L),[_,O,x,R]}(i,d):[y=i,{x:(m=d).x,y:y.y},m,{x:y.x,y:m.y}];var y,m;s.attr({points:Yn(p),fill:"#4080FF","fill-opacity":.15,stroke:"#4080FF","stroke-width":2,cursor:Wr}),t.findOne(".selection")||t.add(s),n.__rangSelectionNodes(p)},l=()=>{this.__clearSelection(),window.removeEventListener("mouseup",l),window.removeEventListener("mousemove",c)}},F.prototype.__clearSelection=function(){const{container:{gCache:e}}=$.getState(),t=e.findOne(".selection");t==null||t.remove()},F.prototype._setShapeChecked=function(e,t,n=!0){var s;let i=(Array.isArray(e)?e:[e]).filter(a=>(a==null?void 0:a.isChecked)!==t);if(i=i.filter(a=>!!a),!(i.length<=0)){for(let a=0;a<i.length;a++){const c=i[a];c&&(c.isChecked=t,(s=c.component)==null||s.fire("checked-change"))}n&&this.update(i,{cachemanager:!0}),this.__onCheckedChange()}},F.prototype.onlyShapeChecked=function(e){const t=Array.isArray(e)?e:[e];if(!t.length)return;const{shapes:n}=this.data,i=t.map(c=>c.key),s=[],a=[];for(const c in n){const l=n[c];i.includes(c)?l.isChecked||s.push(l):l.isChecked&&a.push(l)}this._setShapeChecked(s,!0),this._setShapeChecked(a,!1)},F.prototype._toggleShapeChecked=function(e){const t=Array.isArray(e)?e:[e],n=t.every(({isChecked:i})=>i===!0);this._setShapeChecked(t,!n)},F.prototype.allShapeChecked=function(){const{shapes:e}=this.data;this._setShapeChecked(Object.values(e),!0)},F.prototype.allShapeNotChecked=function(){const{shapes:e}=this.data;this._setShapeChecked(Object.values(e),!1)},F.prototype.allShapeTypeChecked=function(e){const{type:t}=e,{shapes:n}=this.data;this._setShapeChecked(Object.values(n).filter(({type:i})=>i===t),!0)},F.prototype.setStyles=function(e={},t,n={}){let i=[];if(t){const l=Array.isArray(t)?t:[t];i=this._getShapesByKeys(l)}else i=this._getCheckedShapes();const s=Ft(i),{_historical:a=!0}=n,c={};for(let l=0;l<i.length;l++){const u=i[l];Object.keys(e).forEach(f=>{c[f]=u.styles[f]});for(const f in e)u.styles[f]=Object.assign({},u.styles[f],We.cloneDeep(e[f]))}if(a&&Nt.saveAction({data:i,oldData:s,groupBegin:!0}),this._changeNodesStyles(i,We.cloneDeep(e)),a){const l=i.map(u=>u.key);Nt.saveAction({type:"setStyles",data:[{keys:l,style:e}],oldData:[{keys:l,style:c}],groupEnd:!0})}},F.prototype._changeNodesStyles=function(e,t){this.update(e),e.forEach(n=>{var i;(i=n.component)==null||i.fire("style-changed",{styles:n.styles,data:t})})},F.prototype.setBaseGroup=function(e=null){return this.setCommonGroup(Xn,e)},F.prototype.setSecurityGroup=function(e=null){return this.setCommonGroup(ql,e)},F.prototype.setSubnetGroup=function(e=null){return this.setCommonGroup(Yl,e)},F.prototype.setVPCGroup=function(e=null){return this.setCommonGroup(Xl,e)},F.prototype.setAreaGroup=function(e=null){return this.setCommonGroup(Ul,e)},F.prototype.setAvailabilityZoneGroup=function(e=null){return this.setCommonGroup(jl,e)},F.prototype.setCcnGroup=function(e=null){return this.setCommonGroup(xp,e)},F.prototype.setTkeGroup=function(e=null){return this.setCommonGroup(_p,e)},F.prototype.setAutoScalingGroup=function(e=null){this.setCommonGroup(Vl,e)},F.prototype.setCommonGroup=function(e,t=null){var y;let n=this._getCheckedShapes().filter(m=>Ms(m));n=Sp(n);const{shapes:i}=this.data;if(!n.length)return;let s,a=!1;if(Nt.saveAction({groupBegin:!0}),t){const{shapes:m}=this.data,_=m[t];if(!_)return void console.error("设置了错误的基础组");a=!0,s=_}else s=new e().create(),this.add([s]);const c=(n==null?void 0:n[0])||{},l=(c==null?void 0:c.groups)||{},u=(y=Object.keys(l))==null?void 0:y[0],f=Ft(n),d=Ft([s]);for(let m=0;m<n.length;m++){const _=n[m];_!=null&&_.groups&&(Object.keys(_.groups).forEach(x=>{var w;const b=i[x];b!=null&&b.relations&&((w=b.relations)==null||delete w[_==null?void 0:_.key])}),_.groups={}),_.groups[s.key]=s.type,s.relations[_.key]=_.type}if(u){const m=i[u];m&&(m!=null&&m.relations||(m.relations={}),m.relations={...m.relations,[s.key]:s.type},s.groups={[m.key]:m.type})}Nt.saveAction({data:Ft(n),oldData:f}),Nt.saveAction({groupEnd:!0,data:Ft([s]),oldData:d}),this.update([s]),s.component.fire("relations-change",{type:a?"add":"initial"});const p=(s==null?void 0:s.groups)||{};return Object.keys(p).forEach(m=>{const _=i[m];_&&_.component.fire("rank-change")}),t||s.key},F.prototype.clone=function(){this.copy(),this.paste()},F.prototype.cut=function(){const e=this._getCheckedShapes();e.length&&(this.copy(),this.remove(e,{canRemoveLocked:!0}),this.__onCheckedChange())},F.prototype.copy=function(){var s;const{uneditable:e}=$.getState();if(e)return;const t=$.getState().mode===Q,n=(s=this._getCheckedShapes({excludeType:pe,checkedable:!0}))==null?void 0:s.filter(a=>!(a!=null&&a.isTkeSubVode));if(!n.length)return void(this.data.cached={});let i=[...n];i.push(...this._getRelationShapesWithoutTke(n)),i.push(...this._getRelationLines(i)),i=Ft(this._deDuplicateShapes(i)),this.data.cached=t?this.transform(i):i},F.prototype.paste=function(){const{uneditable:e}=$.getState();if(e)return;const{mode:t}=$.getState(),n=t===Q,{cached:i,shapes:s}=this.data;if(!i.length)return;const a=Ft(i),c=[],l=[],u={};for(let d=0;d<a.length;d++){const p=a[d],y=Eo();u[p.key]=y,p.component=null,p.key=y,p.type===Pn&&(p.relations={},p.styles.showTips.value=!0,p.position.x+=p.width,p.height=180,p.width=180)}for(let d=0;d<a.length;d++){const p=a[d],y={x:Fe,y:0};p.position.x+=y.x,p.position.y+=y.y,p.sticky=u[p.sticky]||null,p.attach=u[p.attach]||null;for(const m in p.groups)if(u[m])p.groups[u[m]]=p.groups[m],l.push(u[m]),delete p.groups[m];else{const _=s[m];_?(_.relations[p.key]=p.type,c.push(_),l.push(m)):delete p.groups[m]}if(p.type===pe){const{data:m}=p,{start:_,end:x,points:b}=m;m.start.vkey=u[_.vkey],m.end.vkey=u[x.vkey];for(let w=0;w<b.length;w++){const{x:C,y:A}=b[w];m.points[w].x=C+y.x,m.points[w].y=A+y.y}}if(Qe.includes(p.type)&&p.type!==Pn){const{relations:m={}}=p;for(const _ in m)u[_]&&(m[u[_]]=m[_],delete m[_])}}this.allShapeNotChecked(),this.add(n?this.reduction(a):a),c.forEach(d=>{var p;(p=d.component)==null||p.fire("relations-change")}),l.forEach(d=>{var y;const p=s[d];p&&((y=p.component)==null||y.fire("rank-change"))});const{callbacks:f={}}=this.options;f.onShapesPaste&&f.onShapesPaste(a)},F.prototype.__setLock=function(e){const t=this._getCheckedShapes(),n=Ft(t);for(let i=0;i<t.length;i++)t[i].lock=e;Nt.saveAction({data:t,oldData:n}),this.__onCheckedChange()},F.prototype.lock=function(){this.__setLock(!0)},F.prototype.unlock=function(){this.__setLock(!1)},F.prototype.toggleLock=function(){const e=this._getCheckedShapes().every(({lock:t})=>t===!0);this.__setLock(!e)},F.prototype.delete=function(){var n;const e=this._getCheckedShapes();if(!e.length)return;const t=(n=e.filter(({forever:i})=>!i))==null?void 0:n.filter(({isTkeSubVode:i})=>!i);this.remove(t),this.__onCheckedChange()},F.prototype.descript=function(e){const t=this._getCheckedShapes();if(!t.length)return;const n=Ft(t);for(let i=0;i<t.length;i++)t[i].descript=e;Nt.saveAction({data:t,oldData:n})},F.prototype.raise=function(){const e=this._getCheckedShapes();if(e.length!==1)return;const t=e[0],{max:n}=this.__getShapesMaxAndMinZIndex(),i=Ft(e);t.zIndex=n+1,Nt.saveAction({data:e,oldData:i})},F.prototype.lower=function(){const e=this._getCheckedShapes();if(e.length!==1)return;const t=e[0],{min:n}=this.__getShapesMaxAndMinZIndex(),i=Ft(e);t.zIndex=n-1,Nt.saveAction({data:e,oldData:i})},F.prototype.replace=function(e,t={}){const{_historical:n=!0,needCheckType:i=!0}=t;let s=[];if(i?s=this._getCheckedShapes().filter(f=>f.type===e.type):s=[this.data.shapes[e.key]],s.length!==1)return;const a=Ft(s),{key:c,position:l}=s[0];Object.assign(e,{key:c,position:l}),s[0].component.remove();const u=n?{_historical:!0,vnodesPrevSnapshots:a}:{_historical:!1};this.add([e],u)},F.prototype.move=function(e=0,t=0){const{uneditable:n}=$.getState();if(n)return;const i=[],s=this._getCheckedShapes(),a=this._deDuplicateShapes(s.concat(this._getRelationShapes(s)).filter(u=>!u.attach&&!u.lock));if(!(a!=null&&a.length))return;const c=a.map(u=>Ft(u));for(let u=0;u<a.length;u++){const f=a[u];if(f.attach)continue;c.push(Ft(f));const{x:d,y:p}=f.position;f.position={x:d+e,y:p+t},i.push(f)}const l=i.filter(u=>{var f,d;return((d=(f=u.styles)==null?void 0:f.lineType)==null?void 0:d.value)==="polyline"}).map(u=>u.key);this._getRelationLines(i).forEach(u=>{u.component.fire("position-change",{diff:{x:e,y:t}})}),i.forEach(u=>{(u==null?void 0:u.type)===pe||u.component.fire("position-change",{position:u.position,type:"move",movingLineKeys:l})}),Nt.saveAction({updateType:"position-change",data:i,oldData:c}),this.lines.clear(),this.update(i),this._updateVnodeGroups(i)},F.prototype._updateVnodeGroups=function(e){const t=Array.isArray(e)?e:[e],{shapes:n}=this.data,i={};for(const s of t){const{groups:a={}}=s;for(const c in a)if(!i[c]){const l=n[c];l&&(i[c]=l)}}Object.values(i).forEach(s=>{var a;(a=s.component)==null||a.fire("relations-change")})},F.prototype.setPosition=function(e,t){const n=this._getShapesByKeys([t]);if(n.length>0){const i=this.__getStandardPosition({...n[0].position,...e});n[0].position=i,this.update(n)}},F.prototype.setChildrenPosition=function(e,t){const{relations:n={}}=t,i=Object.keys(n),s=[];if(i.length>0){const a=this._getShapesByKeys(i);a==null||a.forEach(c=>{this.setChildrenPosition(e,c);const{position:l}=c,u={x:l.x+e.x,y:l.y+e.y};c.position=u,c.component.fire("position-change",{position:t.position}),s.push(c)})}return s},F.prototype.align=function(e,t){const{mode:n}=$.getState(),{shapes:i}=this.data,s=Object.keys(i).map(u=>i[u]),a=[],c=this._getCheckedSubstanceShape(t),l=s.map(u=>We.cloneDeep(u));if(!(c.length<2)){if(n===Q&&c.forEach(u=>{u.position=_t(u.position)}),e==="top"){const u=Math.min(...c.map(f=>f.position.y));c.forEach(f=>{const d={x:0,y:u-f.position.y},p=this.setChildrenPosition(d,f);a.push(...p),f.position.y=u,n===Q&&(f.position=dt(f.position)),a.push(f)})}if(e==="bottom"){const u=Math.max(...c.map(f=>f.position.y+f.height));c.forEach(f=>{const d={x:0,y:u-f.position.y-f.height},p=this.setChildrenPosition(d,f);a.push(...p),f.position.y=u-f.height,n===Q&&(f.position=dt(f.position)),a.push(f)})}if(e==="left"){const u=Math.min(...c.map(f=>f.position.x));c.forEach(f=>{const d={x:u-f.position.x,y:0},p=this.setChildrenPosition(d,f);a.push(...p),f.position.x=u,n===Q&&(f.position=dt(f.position)),a.push(f)})}if(e==="right"){const u=Math.max(...c.map(f=>f.position.x+f.width));c.forEach(f=>{const d={x:u-f.position.x-f.width,y:0},p=this.setChildrenPosition(d,f);a.push(...p),f.position.x=u-f.width,n===Q&&(f.position=dt(f.position)),a.push(f)})}a.forEach(u=>{u.component.fire("position-change",{position:u.position})}),Nt.saveAction({updateType:"position-change",data:a,oldData:l}),this.lines.clear(),this.update(a),this._updateVnodeGroups(a)}},F.prototype.arrange=function(e,t=1,n){const{mode:i}=$.getState(),s=[],a=this._getCheckedSubstanceShape(n),c=a.map(u=>We.cloneDeep(u));if(a.length<2)return;const{length:l}=a;if(i===Q&&a.forEach(u=>{u.position=_t(u.position)}),e==="row"){const u=a.sort((p,y)=>p.position.x-y.position.x);let f=u[0].position.x;const d=u[0].position.y;u.forEach((p,y)=>{y>0&&(p.position.x=f+u[y-1].width+Fe*t,p.position.y=d,f=p.position.x,i===Q&&(p.position=dt(p.position)),s.push(p))})}if(e==="col"){const u=a.sort((p,y)=>p.position.x-y.position.x),f=u[0].position.x;let d=u[0].position.y;u.forEach((p,y)=>{y>0&&(p.position.x=f,p.position.y=d+u[y-1].height+Fe*t,d=p.position.y,i===Q&&(p.position=dt(p.position)),s.push(p))})}if(e==="square"){const u=Math.ceil(l**.5),f=a.sort((y,m)=>y.position.x+y.position.y-(m.position.x+m.position.y)),d=f[0].position.x,p=f[0].position.y;f.forEach((y,m)=>{const _=~~(m/u),x=m%u;y.position.x=d+(Fe*t+Fe)*x,y.position.y=p+(Fe*t+Fe)*_,i===Q&&(y.position=dt(y.position)),s.push(y)})}s.forEach(u=>{u.component.fire("position-change",{position:u.position})}),this.lines.clear(),this.update(s),Nt.saveAction({updateType:"position-change",data:s,oldData:c}),this._updateVnodeGroups(s)},F.prototype.dispersion=function(e="row"){const{mode:t}=$.getState(),n=[],i={row:"width",col:"height"},s=this._getCheckedSubstanceShape(),{length:a}=s;if(a<3)return;const{shapes:c}=this.data,l=Object.keys(c).map(x=>c[x]).map(x=>We.cloneDeep(x));t===Q&&s.forEach(x=>{x.position=_t(x.position)});const u={row:"x",col:"y"}[e],f=i[e],d=s.sort((x,b)=>x.position[u]-b.position[u]);let p=0;const y=d.slice(1,a-1).reduce((x,b)=>x+b.width,0);p=d[a-1].position[u]-d[0].position[u]-d[0][f]-y;const m=p/(a-1);let _=d[0].position[u];d.forEach((x,b)=>{if(b!==0&&b!==a-1){const w=x.position[u];x.position[u]=_+d[b-1][i[e]]+m;const C=x.position[u]-w,A={x:0,y:0};A[u]=C,this.setChildrenPosition(A,x),_=x.position[u],t===Q&&(x.position=dt(x.position)),n.push(x)}}),n.forEach(x=>{x.component.fire("position-change",{position:x.position})}),this.lines.clear(),this.update(n),Nt.saveAction({updateType:"position-change",data:n,oldData:l}),this._updateVnodeGroups(n)},F.prototype._getTypesShapes=function(e){const{shapes:t}=this.data,n=lt.isArray(e)?e:[e],i=[];for(const s in t){const a=t[s];n.includes(a.type)&&i.push(a)}return i},F.prototype._getCheckedShapes=function(e={}){const{shapes:t}=this.data,{excludeKeys:n=[],excludeType:i="",checkedable:s}=e,a=[];for(const c in t){const l=t[c];if(l){const u=l.isChecked&&(l.checkedable||l.checkedable===void 0||s);let f=!0,d=!0;n.length&&(f=!n.includes(l.key)),i&&(d=l.type!==i),u&&f&&d&&a.push(t[c])}}return a},F.prototype._getShapesByKeys=function(e){const{shapes:t}=this.data,n=[];for(let i=0;i<e.length;i++){const s=t[e[i]];s&&n.push(s)}return n},F.prototype._getMovingLineShape=function(){const{shapes:e}=this.data;for(const t in e)if(Hr(e[t]))return e[t];return null},F.prototype._getDraggingLineShape=function(){const{shapes:e}=this.data;for(const t in e){const n=e[t],{type:i,data:s}=n;if(i===pe&&[s.start.type,s.end.type].includes("dragging"))return n}return null},F.prototype._getMouseLineShape=function(){const{shapes:e}=this.data;for(const t in e){const n=e[t],{type:i,data:s}=n;if(i===pe&&s.end.type==="mouse")return n}return null},F.prototype._getNetworkShapes=function(){return this._getTypesShapes(nd)},F.prototype._deDuplicateShapes=function(e){if(!Array.isArray(e)||!e.length)return;const t={};for(const n of e)t[n.key]=n;return Object.values(t)},F.prototype._getRelationShapes=function(e){const t=Array.isArray(e)?e:[e],n={},{shapes:i}=this.data;for(const c of t){if(Qe.includes(c.type))for(const l in c.relations){const u=i[l];u&&(n[l]=u,this._getRelationShapes(u).forEach(f=>{n[f.key]=f}))}if(c.sticky){const l=i[c.sticky];l&&(n[l.key]=l)}}const s=Object.keys(n),a=Object.keys(i).filter(c=>{var l,u,f,d,p,y,m,_,x;return((f=(u=(l=i[c])==null?void 0:l.styles)==null?void 0:u.lineType)==null?void 0:f.value)==="polyline"&&(s==null?void 0:s.includes((y=(p=(d=i[c])==null?void 0:d.data)==null?void 0:p.start)==null?void 0:y.vkey))&&(s==null?void 0:s.includes((x=(_=(m=i[c])==null?void 0:m.data)==null?void 0:_.end)==null?void 0:x.vkey))});return a!=null&&a.length&&a.forEach(c=>{n[c]=i[c]}),Object.values(n)},F.prototype._getRelationShapesWithoutTke=function(e){const t=Array.isArray(e)?e:[e],n={},{shapes:i}=this.data;for(const c of t){if(Qe.includes(c.type)&&c.type!==Pn)for(const l in c.relations){const u=i[l];u&&(n[l]=u,this._getRelationShapesWithoutTke(u).forEach(f=>{n[f.key]=f}))}if(c.sticky){const l=i[c.sticky];l&&(n[l.key]=l)}}const s=Object.keys(n),a=Object.keys(i).filter(c=>{var l,u,f,d,p,y,m,_,x;return((f=(u=(l=i[c])==null?void 0:l.styles)==null?void 0:u.lineType)==null?void 0:f.value)==="polyline"&&(s==null?void 0:s.includes((y=(p=(d=i[c])==null?void 0:d.data)==null?void 0:p.start)==null?void 0:y.vkey))&&(s==null?void 0:s.includes((x=(_=(m=i[c])==null?void 0:m.data)==null?void 0:_.end)==null?void 0:x.vkey))});return a!=null&&a.length&&a.forEach(c=>{n[c]=i[c]}),Object.values(n)},F.prototype._getCheckedSubstanceShape=function(e){let t;const{shapes:n}=this.data;return t=e&&Array.isArray(e)?e.reduce((i,s)=>(s&&n[s]&&i.push(n[s]),i),[]):this._getCheckedShapes(),t.map(i=>Object.keys((i==null?void 0:i.groups)??[])[0]).every((i,s,a)=>i&&(s===0||i===a[0]))?t:t.filter(i=>!d_.includes(i.type)&&!i.attach&&Object.keys(i.groups).length===0)},F.prototype._keyPressUp=function(){const{mode:e}=$.getState(),t=e===Q?[vn,-9.25]:[0,-22.5];this.move(...t)},F.prototype._keyPressDown=function(){const{mode:e}=$.getState(),t=e===Q?[-16,Ye]:[0,At];this.move(...t)},F.prototype._keyPressLeft=function(){const{mode:e}=$.getState(),t=e===Q?[-16,-9.25]:[-22.5,0];this.move(...t)},F.prototype._keyPressRight=function(){const{mode:e}=$.getState(),t=e===Q?[vn,Ye]:[At,0];this.move(...t)},F.prototype._keyPressCtrlAndC=function(){this.copy()},F.prototype._keyPressCtrlAndV=function(){this.paste()},F.prototype._keyPressCtrlAndD=function(){this.clone()},F.prototype._keyPressCtrlAndZ=function(){this.undo()},F.prototype._keyPressCtrlAndY=function(){this.redo()},F.prototype._keyPressCtrlAndX=function(){this.cut()},F.prototype._keyPressCtrlAndA=function(){this.allShapeChecked()},F.prototype._keyPressDelete=function(){this.delete()},F.prototype._keyPressCtrlAndS=function(){const{callbacks:e={}}=this.options;e.customizeSave&&e.customizeSave()},F.prototype._keyPressCtrlAndL=function(){this.toggleLock()},F.prototype._keyPressCtrlAndG=function(){},F.prototype._keyPressCtrlAndLeft=function(){this.align("left")},F.prototype._keyPressCtrlAndRight=function(){this.align("right")},F.prototype._keyPressCtrlAndUp=function(){this.align("top")},F.prototype._keyPressCtrlAndDown=function(){this.align("bottom")},F.prototype.redo=function(){Nt.redo(this)},F.prototype.undo=function(){Nt.undo(this)},F.prototype._linkUndoNodesInData=function(e){const{mode:t}=$.getState(),n=[];return(t===Q?e:this.transform(e)).forEach(i=>{const s=this.data.shapes[i.key];s&&(zr.forEach(a=>{delete i[a]}),Object.assign(s,i),n.push(s))}),n},F.prototype._updateNodes=function(e,t){if(!e||e.length===0)return;const{mode:n}=$.getState(),i=Array.isArray(t)?t:[t],s=n===Q?e:this.transform(e),a=[];s.forEach(c=>{const l=this.data.shapes[c.key];l&&(zr.forEach(u=>{delete c[u]}),Object.assign(l,c),i.forEach(u=>l.component.fire(u,{pure:!0})),a.push(l))}),a.length===1&&this._setShapeChecked(a,!0)},F.prototype._autoLocalSave=function(){const{mode:e}=$.getState();Kl&&clearTimeout(Kl);const t=this;Kl=setTimeout(()=>{let n={};const{shapes:i}=t.data;for(const s in i){const a=i[s];if(Hr(a))continue;const c=Ft(a);n[s]=c}e===ee&&(n=t.reduction(n));try{localStorage.setItem("sigma_graph_data",(s=>{let a=[];const c=JSON.stringify(s,(l,u)=>{if(typeof u=="object"&&u!==null){if(a.indexOf(u)!==-1)return;a.push(u)}return u});return a=null,c})(n))}catch{console.warn("Local Storage is full, Please empty data")}},1e3)},F.prototype.__onCheckedChange=function(){const e=this;setTimeout(()=>{const t=e._getCheckedShapes(),{callbacks:n={}}=e.options;n.onCheckedChange&&n.onCheckedChange(Ft(t))},0)},F.prototype.__onGraphChange=function(){const{callbacks:e={}}=this.options,{shapes:t}=this.data;e.onGraphChange&&e.onGraphChange(t)},F.prototype.__onmousedown=function(e,t){e.button!==0?e.button===2&&this.__onmouserightdown(e,t):this.__onmouseleftdown(e,t)},F.prototype.__onmouseleftdown=function(e,t){var Qt;const{mode:n,uneditable:i,dropzone:s}=$.getState(),a=this._getMovingLineShape();if(a&&t.type===pe)return;e.stopPropagation();const c=this;let l=!0;const{component:u,key:f,connectable:d,lock:p}=t,y=t.isChecked;if(y||(e.ctrlKey||e.metaKey?c._toggleShapeChecked(t):c.onlyShapeChecked(t)),c.__onclick(e,t),a&&t.key!==a.key){let et;return d&&(et=f),!d&&t.attach&&(et=t.attach),void(et&&((Qt=a.component)==null?void 0:Qt.fire("line-complete",{key:et})))}const m=u.root(),_=this._getCheckedShapes(),x=_.length===1&&Sl(_[0],this)?_:this._getAllUpdateNodes(_),b=We.cloneDeep(x),{callbacks:w={}}=this.options;this.lines.sort(x);const C=this._getRelationLines(x),A=Ft(C),L=n===Q,P=L?ve:Te,R=m.point(e.clientX,e.clientY);let O={x:0,y:0},W=!1,B=!1,H=!1,Y=null;const V=s.node.width.baseVal.value,at=s.node.height.baseVal.value,rt=s.node.x.baseVal.value,ft=s.node.y.baseVal.value,$t=et=>{var Tt,Wt;if(B||(l&&re(),i))return;B=!0;const{doc:nt}=$.getState(),ot=nt.point(et.clientX,et.clientY),xt=this._getMouseMoveDiff(R,ot);if(xt.x===0&&xt.y===0)return void(B=!1);C.forEach(Xt=>{Xt.component.fire("position-change",{diff:{x:xt.x-O.x,y:xt.y-O.y}})}),O=xt;const Vt=(C==null?void 0:C.map(Xt=>Xt==null?void 0:Xt.key))??[];for(let Xt=0;Xt<x.length;Xt++){W||((Tt=x[Xt].component)==null||Tt.fire("start-move"));const{position:oe}=b[Xt],se={x:oe.x+xt.x,y:oe.y+xt.y};if(t!=null&&t.isTkeSubVode){let ce=L?_t(P(se)):P(se);L&&(ce.x=ce.x+112.5,ce.y=ce.y+22.5,ce=Te(ce));const wn=t==null?void 0:t.groups;if(wn){const hn=Object.keys(wn);if(hn!=null&&hn.length){const Me=this.data.shapes[hn[0]],{width:li,height:Yr,position:kn}=Me,{width:tn,height:vi}=x[Xt];if(!ad({...L?_t(kn):kn,w:li,h:Yr},{...ce,w:tn,h:vi}))break}}}const Le={x:rt,y:ft,w:V,h:at},Ee=P(se),{width:ln,height:un}=x[Xt];if(!ad(Le,{x:Ee.x,y:Ee.y,w:ln,h:un}))break;x[Xt].position=Ee,(Wt=x[Xt].component)==null||Wt.fire("position-change",{position:x[Xt].position,type:"mousemove",mousePosition:ot,movingLineKeys:Vt})}W=!0,c.update(x),B=!1,this.__handleRelationsWhenMove(_),w.onShapeMove&&w.onShapeMove(x),l=!1},Mt=()=>{if(e.ctrlKey||e.metaKey?y?c._setShapeChecked(t,!1):c._setShapeChecked(t,!0):e.shiftKey&&c.allShapeTypeChecked(t),d){const et=this._getCheckedShapes();et.length===1?hd(this,et[0]):re()}else re();m.off("mousemove.component",Gt),m.off("mouseup.component",Mt),Y!==null&&(cancelAnimationFrame(Y),Y=null),W&&(c.update(x),Nt.saveAction({updateType:"position-change",data:x.concat(C),oldData:b.concat(A)}),x.forEach(({component:et})=>{et==null||et.fire("end-move")}),this.__handleRelationsWhenMove(_,!0),w.onShapeMoveEnd&&w.onShapeMoveEnd(x)),this.lines.clear()},Gt=et=>{H||(H=!0,Y=requestAnimationFrame(()=>{$t(et),H=!1}))};p||(w.onShapeMoveStart&&w.onShapeMoveStart(x),m.on("mousemove.component",Gt)),m.on("mouseup.component",Mt)},F.prototype._getAllUpdateNodes=function(e){return this._deDuplicateShapes(e.concat(this._getRelationShapes(e))).filter(({type:t,attach:n})=>!n&&t!==pe)},F.prototype._getMouseMoveDiff=function(e,t,n){const i=n?ve:Te,s=n?32:At,a=n?gl:At,c={x:t.x-e.x,y:t.y-e.y};if(Math.abs(c.x)<s&&Math.abs(c.y)<a){const l=4*(c.x>=0?s:-s),u=4*(c.y>=0?a:-a),f=i({x:l+c.x,y:u+c.y});return{x:f.x-l,y:f.y-u}}return i(c)},F.prototype.__handleRelationsWhenMove=function(e,t=!1){const n=Sp(We.isArray(e)?e:[e]),{shapes:i}=this.data,s=function(u,f){const d=u._getTypesShapes(Qe);if(!d.length)return;const p=function(x){return x.sort((b,w)=>w.component.position()-b.component.position())}(d),{shapes:y}=u.data;let m,_=-1;for(const x of f){const b=dv(p,x,fa(y,x));if(!b)continue;const w=b.component.position();w>_&&(m=b,_=w)}return m}(this,n);if((s==null?void 0:s.type)===Pn)return;const a={},c={},l={};for(const u of n){const{prevSelectdGroup:f}=$.getState();if(t){const d={};s&&(d[s.key]={group:s,status:2});for(const p in u.groups)if(d[p])d[p].status=1;else{const y=i[p];if(!y)continue;d[p]={group:y,status:0}}for(const p in d){const{group:y,status:m}=d[p];c[y.key]||(c[y.key]=Ft(y)),[2,0].includes(m)&&!l[y.key]&&(l[u.key]=Ft(u)),m===2?y.key!==u.key&&(y.relations[u.key]=u.type,u.groups[y.key]=y.type,y.component.fire("rank-change")):m===0&&(delete y.relations[u.key],delete u.groups[y.key]),a[y.key]=y,y.component.fire("pre-select",{key:null})}f==null||f.component.fire("pre-select",{key:null}),$.dispatch({type:dl,value:null})}else(f==null?void 0:f.key)!==(s==null?void 0:s.key)&&(f&&f.component.fire("pre-select",{key:s==null?void 0:s.key}),s&&s.component.fire("pre-select",{key:s.key}),$.dispatch({type:dl,value:s}))}if(t){const u=Object.keys(l);u.length>0&&Nt.saveAction({data:u.map(f=>i[f]),oldData:u.map(f=>l[f]),isSub:!0}),Object.values(a).forEach(f=>{f.component.fire("relations-change",{vnodesPrevSnapshots:[c[f.key]]})})}},F.prototype._getRelationLines=function(e){const t=this._getTypesShapes(pe),n=[];for(const i of t){if(Hr(i))continue;const{start:s,end:a}=i.data;let c=!1,l=!1;for(const{key:u}of e)if(c||(c=u===s.vkey),l||(l=u===a.vkey),c&&l){n.push(i);break}}return n},F.prototype.__onmouserightdown=function(e,t){if(e.stopPropagation(),this._getMovingLineShape())return;const{options:{callbacks:n={}}}=this;t.isChecked||this.onlyShapeChecked(t),re(),n.onShapeMouseRightDown&&n.onShapeMouseRightDown(e,t)},F.prototype.__onmouseup=function(e,t){const{callbacks:n={}}=this.options;n.onShapeMouseUp&&n.onShapeMouseUp(e,t)},F.prototype.__onmouseover=function(e,t){var s;const n=(s=t.component)==null?void 0:s.findOne(".shape-body");n==null||n.attr({opacity:.6});const{callbacks:i={}}=this.options;i.onShapeMouseOver&&i.onShapeMouseOver(e,t),Dd(this,t,!0)},F.prototype.__onmouseout=function(e,t){var s;const n=(s=t.component)==null?void 0:s.findOne(".shape-body");n==null||n.attr({opacity:1});const{callbacks:i={}}=this.options;i.onShapeMouseOut&&i.onShapeMouseOut(e,t)},F.prototype.__onclick=function(e,t){const{callbacks:n={}}=this.options;n.onShapeClick&&n.onShapeClick(e,t)},F.prototype.__ondoubleclick=function(e,t){const{callbacks:n={}}=this.options;n.onShapeDBLClick&&n.onShapeDBLClick(e,t)},F.prototype.__onrightclick=function(e,t){const{callbacks:n={}}=this.options;n.onShapeMouseRightClick&&n.onShapeMouseRightClick(e,t)},F.prototype._vnodeVerify=function(e){const t=this._getTypesShapes(pe);let n=e;for(let i=0;i<e.length;i++){const s=e[i];if(s.type===pe){const{start:a,end:c}=s.data;for(let l=0;l<t.length;l++){const{start:u,end:f}=t[l].data;a&&c&&u.vkey===a.vkey&&f.vkey===c.vkey&&(n=e.filter(d=>d.key!==s.key))}}}return n},F.prototype.__whenShapeUpdate=function(e){const{shapes:t}=this.data,n=[];for(const i in t){const s=t[i];s.type===pe&&(s.data.start.type==="vnode"&&e.filter(a=>a.key===s.data.start.vkey).length>0&&n.push(s),s.data.end.type==="vnode"&&e.filter(a=>a.key===s.data.end.vkey).length>0&&n.push(s)),Qe.includes(s.type)&&e.filter(a=>{var c;return(c=s.relations)==null?void 0:c[a.key]}).length>0&&n.push(s)}n.length&&this.update(n)},F.prototype.__whenShapeDelete=function(e,t={}){var u;const{_historical:n=!0}=t;let i=[];const s=[],a=[],c=[],{shapes:l}=this.data;for(const f in l){const d=l[f],p=!e.filter(y=>y.key===f).length;d.type===pe&&p&&c.push(d)}for(let f=0;f<e.length;f++){const d=e[f];if(d.type===pe){const p=d.data.start.vkey,y=d.data.end.vkey;y||window.clearMouseLineEvents();const m=c.filter(x=>x.data.start.vkey===p||x.data.end.vkey===p),_=c.filter(x=>x.data.start.vkey===y||x.data.end.vkey===y);!m.length&&l[p]&&l[p].type===$r&&i.push(l[p]),!_.length&&l[y]&&l[y].type===$r&&i.push(l[y]),(u=d.component)==null||u.fire("pre-delete")}else{const p=c.filter(y=>y.data.start.vkey===d.key||y.data.end.vkey===d.key);if(p.length===2&&d.type===$r){i.push(p[0]);const y=p[1],m=p[0].data.start.vkey,_=p[1].data.start.vkey,x=p[0].data.end.vkey,b=p[1].data.end.vkey;a.push(Ft(y)),y.data.start.vkey=m===d.key?_===d.key?b:_:m,y.data.end.vkey=x===d.key?b===d.key?_:b:x,s.push(y)}else i=i.concat(p)}}i.length&&this.remove(i,{_historical:n,isSub:!0}),s.length&&(this.update(s),Nt.saveAction({data:s,oldData:a}))},F.prototype.__checkShapeLocation=function(e,t){const{shapes:n}=this.data,{key:i,component:s,zIndex:a}=e;if(!(s!=null&&s.parent()))return;const c=s.siblings();if(c.length<=1)return;let l=null;const u=[];for(let f=0;f<c.length;f++){const d=c[f].attr("key"),p=n[d];if(d===i||!p)continue;const y=p.zIndex;u.push(y),!l&&y>a&&(l=c[f])}t===Wi&&l&&s.insertBefore(l),u.filter(f=>f>=a).length===0?s.front():u.filter(f=>f<=a).length===0&&s.back()},F.prototype.__setShapeLocation=function(e){if(e.__index){const t=document.getElementById(`${e.__index}`);t&&e.component.insertBefore(t)}},F.prototype.__getShapesMaxAndMinZIndex=function(){const{shapes:e}=this.data;let t=0,n=0;for(const i in e){const{zIndex:s}=e[i];n=n<s?s:n,t=t>s?s:t}return{min:t,max:n}},F.prototype.__rangSelectionNodes=function(e){Zl&&clearTimeout(Zl),Zl=setTimeout(()=>{const{shapes:t}=this.data,n=Gr(e),i=[];for(const s in t){const a=t[s];a.type!==pe&&Gr(Ne.getflat(a)).some(c=>Uc(c,n))&&i.push(a)}i.push(...this._getRelationLines(i)),this.onlyShapeChecked(i)},0)},F.prototype.__getStandardPosition=function(e){const{mode:t}=$.getState();if(lt.isObject(e)&&lt.isNumber(e.x)&&lt.isNumber(e.y)){const n=t===ee?Te(e):ve(e);return n.x===e.x&&n.y===e.y||console.warn("所设坐标不属于规范坐标，已自动修正。"),n}return null},F.prototype._getCustomizeShape=function(e){const{shapes:t}=this.data,n=this._getCheckedShapes();return e&&t[e]?t[e]:n.length===1?n[0]:null},F.prototype.setCustomize=function(e={},t){if(!lt.isObject(e))return console.error("自定义属性值必须是一个 Object"),!1;const n=this._getCustomizeShape(t);return!!n&&(n.customize=We.cloneDeep(e),!0)},F.prototype.resetCustomize=function(e){const t=this._getCustomizeShape(e);return!!t&&(t.customize=void 0,!0)},F.prototype.getCustomize=function(e){const t=this._getCustomizeShape(e);return t?t.customize:{}},F.prototype.getGraphData=function(){const{mode:e}=$.getState(),t=Ft(this.data.shapes,!0),n=e===Q?t:this.reduction(t);Object.keys(n).forEach(i=>{n[i].dragPoints&&delete n[i].dragPoints});try{return JSON.stringify(n)}catch{console.error("getGraphData execution error！The shapes is:",n)}},F.prototype.clearHistory=function(){this.cmdStack.clearCmd()},F.prototype.__handleEditable=function(e){$.dispatch({type:qe,value:{uneditable:e}});const{shapes:t}=this.data;for(const n in t){const{component:i}=t[n];i.fire("editable-change",{uneditable:e})}return e},F.prototype._createNodeLabel=function(e,t,n={}){var i;if(!e.sticky){const s=[Ft(e)],a=new Cl,c=e.type===pe;if(c){const{position:u,positionPercent:f}=n;a.position=u||{...e.data.points[0]},a.positionPercent=f,Nt.saveAction({maxGroupBegin:!0})}else Nt.saveAction({groupBegin:!0});const l=a.createLinkNode({key:e.key,name:t??e.name});l.connectable=!1,e.sticky=l.key,this.add(l,{_historical:!0}),c&&this.allShapeNotChecked(),this._setShapeChecked(l,!0,!1),this.update([e],{cachemanager:!0}),Nt.saveAction({data:[e],oldData:s}),c?((i=l.component)==null||i.fire("start-edit"),l.component.on("end-edit",()=>{Nt.saveAction({maxGroupEnd:!0})}),this.setStyles({fontSize:{name:"字号",type:"number",default:24,value:14}},[l.key],{_historical:!1})):Nt.saveAction({groupEnd:!0}),this.lines.clear()}},F.prototype.setIsDoubleTouch=function(e){this.isDoubleTouch=e};const Cp="sticky-units";class mv{constructor({vnode:t,offset:n={x:0,y:0},child:i,element:s="foreignObject",width:a,height:c,...l}){M(this,"_wrapper",null);M(this,"_element",null);M(this,"_main",null);this.id=Eo(),this.vnode=t,this.offset=n,this.child=i,this.element=s,this.width=a||t.width,this.height=c||t.height,this.attr=l,this._init()}_init(){const{id:t,vnode:n,offset:i,child:s,element:a,width:c,height:l,attr:u}=this,{position:{x:f,y:d}}=n,{x:p,y}=i;this._initWrapper(),this._element=new ht().attr({class:"unit",id:t,x:f+p,y:d+y}),this._wrapper.add(this._element),this._main=this._element.element(a).attr({width:c,height:l,overflow:"visible",...u}),this._main.add(ht(s)),this._bindEvent(),n.stickyUnits||(n.stickyUnits={}),n.stickyUnits[this.id]=null,n.stickyUnits[this.id]=this}_bindEvent(){const{vnode:t}=this,n=["position-change.unit","style-changed.unit"];t.component.off(n),t.component.on(n,()=>{this._positionChange()}),t.component.off("deleted.unit"),t.component.on("deleted.unit",()=>{this.remove()})}_positionChange(){const{vnode:t,offset:n}=this,{position:{x:i,y:s}}=t,{x:a,y:c}=n;this._element.attr({x:i+a,y:s+c})}updateChild(t){this.child=t,this._main.clear(),this._main.add(ht(t))}updateOffset(t){this.offset=t,this._positionChange()}remove(){this._element.remove(),delete this.vnode.stickyUnits[this.id]}_initWrapper(){const{container:{gCache:t}}=$.getState();this._wrapper=t.findOne(`.${Cp}`),this._wrapper||(this._wrapper=t.group().attr({class:Cp})),this._wrapper.front()}}function J(e,t={}){this.VERSION="0.0.7",console.log("sigma-editor 加载的版本 6.6.6"),this.IS_IE=navigator.userAgent!=null&&navigator.userAgent.indexOf("MSIE")>=0,this.components=[],this.core=null,this.container=e,t!=null&&t.gain||(t.gain=15),this.options=t,this.isDoubleTouch=!1,this.config={backgroundColor:{default:"#ffffff",value:localStorage.getItem("sigma.backgroundColor")||null},entireColor:{default:"#DCDCDC",value:localStorage.getItem("sigma.entireColor")||null},quarterColor:{default:"#eeeeee",value:localStorage.getItem("sigma.quarterColor")||null}},this.graph={},On(window,ri),this._init()}J.prototype._init=function(){const{container:e,options:t}=this;if(!e)return void console.error("容器初始化错误, 请检查");e.tabIndex=-1,e.innerHTML="";const{scale:n}=$.getState(),i=e.offsetWidth,s=e.offsetHeight,a=lt.round(i*n),c=lt.round(s*n),l=new ht().addTo(e).attr({viewBox:`0 0 ${a} ${c}`,width:i,height:s,overflow:"visible"}),u=l.defs(),f=l.group(),d=f.rect().attr({"g-name":"dropzone"}).css("cursor",Wr),p=f.group().attr({"pointer-events":"none","g-name":"sence"}),y=f.group().attr({"pointer-events":"none","g-name":"grid"}),m=f.group().attr({"pointer-events":"auto","g-name":"gNetwork"}),_=f.group().attr({"pointer-events":"auto","g-name":"gRect"}),x=f.group().attr({"pointer-events":"auto","g-name":"gLine"}),b=f.group().attr({"pointer-events":"auto","g-name":"gCircle"}),w=f.group().attr({"pointer-events":"auto","g-name":"gProduct"}),C=f.group().attr({"pointer-events":"auto","g-name":"gImage"}),A=f.group().attr({"pointer-events":"auto","g-name":"gIcon"}),L=f.group().attr({"pointer-events":"auto","g-name":"gText"}),P=f.group().attr({"pointer-events":"auto","g-name":"gCache"}),R={root:e,doc:l,defs:u,g:f,dropzone:d,sence:p,grid:y,mode:t.mode==="2d"?ee:Q,w:i,h:s,viewBox:{x:0,y:0,w:a,h:c},container:{gText:L,gImage:C,gIcon:A,gProduct:w,gRect:_,gNetwork:m,gLine:x,gCircle:b,gCache:P,0:d,1:p,3:y,4:m,5:_,6:x,7:b,8:w,9:C,10:A,11:L,20:P}};$.dispatch({type:qe,value:R});const{callbacks:O={}}=t;O.onDocClick&&l.on("mousedown",B=>{this.core.lines.clear(),O.onDocClick(B)});const W=new F(t);this.core=W,$.dispatch({type:qe,value:{core:W}}),this._initSence(),this._initFun(),this._initComponents(),this.zoomGraphFit(),this.cmdStack=this.core.cmdStack},J.prototype._initSence=function(){const e=this.options.gain,{mode:t,defs:n,w:i,h:s,sence:a,dropzone:c,grid:l}=$.getState(),{backgroundColor:u,quarterColor:f,entireColor:d}=this.config;c.attr({width:e*i,height:e*s,x:-e/2*i,y:-e/2*s,fill:u.value||u.default}),a.clear(),l.clear(),n.clear();const p=a.group().attr({class:"gSence-bg"}),y=l.group().attr({class:"gGrid-bg"}),m=n.pattern().attr({id:"entire-grid",patternUnits:"userSpaceOnUse"}),_=n.pattern().attr({id:"quarter-grid",patternUnits:"userSpaceOnUse"}),x=new ht(`<filter id="bright75">
  <feComponentTransfer>
    <feFuncR type="linear" slope="0.6" />
    <feFuncG type="linear" slope="0.6" />
    <feFuncB type="linear" slope="0.6" />
  </feComponentTransfer>
</filter>`);n.add(x);const b=`${-e*i*100} ${-e*s*100}, ${e*i*100} ${-e*s*100}, ${e*i*100} ${e*s*100}, ${-e*i*100} ${e*s*100}`;y.polygon().attr({points:b,fill:"url(#entire-grid)",class:"entire-grid"}),p.polygon().attr({points:b,fill:"url(#quarter-grid)",class:"quarter-grid"}),t===ee&&(m.attr({x:0,y:0,width:90,height:90}),m.path().attr({d:"M 0 0 L 90 0 90 90 0 90 z",stroke:d.value||d.default,"stroke-width":1,fill:"none",class:"entirePattern"}),_.attr({x:0,y:0,width:90,height:90}),_.path().attr({d:"M -45 45 L 135 45",stroke:f.value||f.default,"stroke-width":1,class:"quarterPattern"}),_.path().attr({d:"M 45 -45 L 45 135",stroke:f.value||f.default,"stroke-width":1,class:"quarterPattern"})),t===Q&&(m.attr({x:0,y:37,width:128,height:74}),m.path().attr({d:"M 64 0 L 128 37 64 74 0 37 z",stroke:d.value||d.default,"stroke-width":1,fill:"none",class:"entirePattern"}),_.attr({x:0,y:37,width:128,height:74}),_.path().attr({d:"M 0 0 L 128 74",stroke:f.value||f.default,"stroke-width":1,class:"quarterPattern"}),_.path().attr({d:"M 128 0 L 0 74",stroke:f.value||f.default,"stroke-width":1,class:"quarterPattern"}))},J.prototype._initFun=function(){this.__zoom(),this.__drag(),this.__initWindowFun()},J.prototype._initComponents=function(){const e=new ha,t=new cd,n=new Cl,i=new ud,s=[e,t,n,new ld,i,new Xn,new Ul,new Vl,new jl,new ql,new Xl,new Yl,new uv,new xp,new _p];this.components=s},J.prototype.__zoom=function(){const{doc:e,root:t}=$.getState(),{callbacks:n={},gain:i}=this.options,{onScaleChange:s}=n;let a,c,l=!1;const u=this,f=function(m,_){let x=0;return function(...b){const w=Date.now();w-x>=_&&(m.apply(u,b),x=w)}}(this.scaleStep,10),d=m=>Math.sqrt((m[0].clientX-m[1].clientX)**2+(m[0].clientY-m[1].clientY)**2),p=m=>{if(a)return;m.preventDefault();let _=m;if(m.type==="touchmove")if(m.touches.length===2)_=m.touches;else{if(m.targetTouches.length!==2)return;_=m.targetTouches}a=!0,requestAnimationFrame(()=>{(function(x){let b,w=0,C=0;const{viewBox:A,maxScale:L,minScale:P,w:R,h:O,step:W,scale:B,doc:H,dropzone:Y,sence:V,grid:at}=$.getState();if(x.length===2){const Le=d(x)-c>0?-1:1;if(b=lt.round(B+Le*W,2),b=Math.max(P,Math.min(L,b)),w=(x[0].pageX+x[1].pageX)/2,C=(x[0].pageY+x[1].pageY)/2,l)return void f(Le/10)}else{if(!x.ctrlKey&&!x.metaKey)return;const Le=(x.wheelDelta||x.detail)>0?-1:1;b=lt.round(B+Le*W,2),b=Math.max(P,Math.min(L,b)),w=x.pageX,C=x.pageY}const rt=lt.round(b*R,2),ft=lt.round(b*O,2),{x:$t,y:Mt,w:Gt,h:Qt}=A,{x:et,y:nt}=H.point(w,C),ot=et-(et-$t)/Gt*rt,xt=nt-(nt-Mt)/Qt*ft,Vt=`${ot} ${xt} ${rt} ${ft}`;$.dispatch({type:qe,value:{viewBox:{x:ot,y:xt,w:rt,h:ft},scale:b}}),H.attr({viewBox:Vt});let Tt=0,Wt=0;Tt=ot<=0?-(Math.abs(rt)*(i/2)+Math.abs(ot)):ot<=Math.abs(rt)*(i/2)?-(Math.abs(rt)*(i/2)-ot):ot-Math.abs(rt)*(i/2),Wt=xt<=0?-(Math.abs(ft)*(i/2)+Math.abs(xt)):xt<=Math.abs(ft)*(i/2)?-(Math.abs(ft)*(i/2)-xt):xt-Math.abs(ft)*(i/2),Y.attr({x:Tt,y:Wt,width:Math.abs(rt)*i,height:Math.abs(ft)*i});const Xt=V.findOne(".gSence-bg"),oe=at.findOne(".gGrid-bg"),se=`${-Math.abs(rt)*i*100} ${-Math.abs(ft)*i*100}, ${Math.abs(rt)*i*100} ${-Math.abs(ft)*i*100}, ${Math.abs(rt)*i*100} ${Math.abs(ft)*i*100}, ${-Math.abs(rt)*i*100} ${Math.abs(ft)*i*100}`;Xt.findOne(".quarter-grid").attr({points:se,fill:"url(#quarter-grid)",class:"quarter-grid",x:Tt,y:Wt}),oe.findOne(".entire-grid").attr({points:se,fill:"url(#entire-grid)",class:"entire-grid",x:Tt,y:Wt}),s&&s(b),re(),n.onDocMove&&n.onDocMove()})(_),a=!1})},y=(m,_="add")=>{const x=`[key="${m}"]`,b=e.find(x);b==null||b.forEach(w=>{w==null||w.style("pointer-events",_==="add"?"none":"auto")})};e.on("wheel",p),e.on("touchstart",m=>{let _=[];if(m.type==="touchstart"&&(m.touches.length===2||m.targetTouches.length===2)){this.isDoubleTouch=!0,this.core.isDoubleTouch=!0,m.touches.length===2&&(_=m.touches),m.targetTouches.length===2&&(_=m.targetTouches);const x=this.core.data.shapes;Object.keys(x).forEach(b=>{y(b)}),c=d(_),e.on("touchmove",p),l=t.classList.contains("isRotated")}}),e.on("touchend",m=>{if(m.type==="touchend"){this.isDoubleTouch=!1,this.core.isDoubleTouch=!1;const _=this.core.data.shapes;Object.keys(_).forEach(x=>{y(x,"remove")}),c=0,e.off("touchmove",p)}})},J.prototype.__drag=function(){const{doc:e,dropzone:t,root:n}=$.getState(),{callbacks:i={}}=this.options,s={};let a,c,l=!1,u=!1,f=!1;const d=this;function p(_){let x=_;if(_.type==="touchmove"&&(_.targetTouches.length!==1||(x=_.targetTouches[0],_.preventDefault(),d.isDoubleTouch)))return;const{viewBox:b,scale:w,dropzone:C}=$.getState(),{x:A,y:L,w:P,h:R}=b;let O,W;f?(W=x.clientX-s.x,O=s.y-x.clientY):(O=s.x-x.clientX,W=s.y-x.clientY);const B=C.node.width.baseVal.value,H=C.node.height.baseVal.value,Y=C.node.x.baseVal.value,V=C.node.y.baseVal.value;if(a=lt.round(A+O*w,0),c=lt.round(L+W*w,0),u=!1,V>=0&&c>=0){if(c<=V)return void(c=V);if(c-V+R>=H)return void(c=H-R+V)}if(V<0&&c>=0&&Math.abs(V)+c+R>=H)return void(c=H-Math.abs(V)-R);if(V<0&&c<0){if(c<=V)return void(c=V);if(Math.abs(V)-Math.abs(c)+R>=H)return void(c=-(H-Math.abs(V)-R))}if(Y>=0&&a>=0){if(a<=Y)return void(a=Y);if(a-Y+P>=B)return void(a=B-P+Y)}if(Y<0&&a>=0&&Math.abs(Y)+a+P>=B)return void(a=B-Math.abs(Y)-P);if(Y<0&&a<0){if(a<=Y)return void(a=Y);if(Math.abs(Y)-Math.abs(a)+P>=B)return void(a=-(B-Math.abs(Y)-P))}u=!0;const at=`${a} ${c} ${b.w} ${b.h}`;e.attr({viewBox:at}),i.onDocMove&&i.onDocMove()}function y(){const{viewBox:_}=$.getState();a!==void 0&&c!==void 0&&u&&($.dispatch({type:qe,value:{viewBox:{x:a,y:c,w:_.w,h:_.h}}}),a=void 0,c=void 0),t.off("mousemove",p),t.off("touchmove",p),l||(t.off("mouseup",y),t.off("touchend",y),t.off("mousedown",m),t.off("touchstart",m))}function m(_){f=n.classList.contains("isRotated"),re();let x=_;if(_.type==="touchstart"){if(_.targetTouches.length!==1||(x=_.targetTouches[0],d.isDoubleTouch))return}else if(_.ctrlKey||_.metaKey)return;s.x=x.clientX,s.y=x.clientY,t.on("mousemove",p),t.on("touchmove",p),t.on("mouseup",y),t.on("touchup",y)}Ie(window,`mouseup${ri}`,y),Ie(window,`touchend${ri}`,y),t.on("mouseup",y),t.on("touchend",y),t.on("touchstart",_=>{setTimeout(()=>{m(_)},0)}),Ie(window,`keydown${ri}`,_=>{l||_.code==="Space"&&(l=!0,t.css("cursor","grab"),a=void 0,c=void 0,t.on("mousedown",m))}),Ie(window,`keyup${ri}`,_=>{sessionStorage.getItem("sigmaIsGrab")!=="true"&&_.code==="Space"&&l&&(l=!1,t.css("cursor",Wr),y())})},J.prototype.__setExportArea=function(e,t){const n=e,i=t,{viewBox:{x:s,y:a,w:c,h:l},doc:u}=$.getState(),f=u.findOne(".entire-grid"),d=u.findOne(".quarter-grid");if(n&&i){const p=(c-n)/2,y=(l-i)/2,m=s+(p>200?200:p),_=a+(y>200?200:y),x=`${m} ${_}, ${m+n} ${_}, ${m+n} ${_+i}, ${m} ${_+i}`;f.attr({points:x}),d.attr({points:x,style:"outline: 2px solid #dddddd"})}else{const p=`${-10*c} ${-10*l}, ${10*c} ${-10*l}, ${10*c} ${10*l}, ${-10*c} ${10*l}`;f.attr({points:p}),d.attr({points:p,style:null})}},J.prototype.__initWindowFun=function(){const e=this;if(this.container.classList.add("__sigma__container"),this.container.id="sigma-container",!document.querySelector('style[data-sigma="built-in"]')){const t=document.createElement("style");t.setAttribute("data-sigma","built-in"),t.innerText="#sigma-container svg {overflow: auto;}",document.body.appendChild(t)}Ie(window,`keydown${ri}`,t=>{const n=t.key,i=t.ctrlKey||t.metaKey;switch(!0){case(i&&(n==="+"||n==="=")):t.preventDefault(),e.scaleStep(-.05);break;case(i&&n==="-"):t.preventDefault(),e.scaleStep(.05);break;case(i&&n==="0"):t.preventDefault(),e.setScale(1)}}),Ie(window,`resize${ri}`,()=>{this.refreshViewport()})},J.prototype.refreshViewport=function(){const{callbacks:e={}}=this.options,{doc:t,scale:n,viewBox:i}=$.getState(),{container:s}=this,a=s.offsetWidth,c=s.offsetHeight,l=lt.round(a*n),u=lt.round(c*n);t.attr({viewBox:`${i.x} ${i.y} ${l} ${u}`,width:a,height:c}),$.dispatch({type:qe,value:{w:a,h:c,viewBox:{x:i.x,y:i.y,w:l,h:u}}}),e.onDocMove&&e.onDocMove()},J.prototype.getComponents=function(){return this.components},J.prototype.getLayers=function(){const{sence:e,grid:t,container:{gText:n,gImage:i,gIcon:s,gNetwork:a,gProduct:c,gRect:l,gLine:u}}=$.getState();return[{key:"sigma-layer-Sence",name:"Sence",cName:"背景网格",layer:e},{key:"sigma-layer-Grid",name:"Grid",cName:"基础网格",layer:t},{key:"sigma-layer-Network",name:"Network",cName:"组",layer:a},{key:"sigma-layer-Text",name:"Text",cName:"文本",layer:n},{key:"sigma-layer-Image",name:"Image",cName:"图片",layer:i},{key:"sigma-layer-Icon",name:"Icon",cName:"图标",layer:s},{key:"sigma-layer-Product",name:"Product",cName:"产品图标",layer:c},{key:"sigma-layer-Rect",name:"Rect",cName:"区域",layer:l},{key:"sigma-layer-Line",name:"Line",cName:"连线",layer:u}]},J.prototype.registerComponent=function(e,t){if(typeof e!="function")return console.error("注册的自定义元素必须是一个构造函数, 返回之中"),!1;if(typeof t.render!="function"||typeof t.rerender!="function"||typeof t.exports!="function"||typeof t.transform!="function"||typeof t.reduction!="function"||typeof t.getflat!="function")return console.error("渲染器必须包括 render、rerender、exports、transform、reduction、getflat 属性方法"),!1;const n=new e,i=n.create();this.components.push(n),Ne.register(i.type,t)},J.prototype.toggleMode=function(){sessionStorage.setItem("init",!0);const{mode:e}=$.getState(),t=e===ee?Q:ee;$.dispatch({type:qe,value:{mode:t}}),this._initSence(),this.core._toggleMode(),this.zoomGraphFit(),localStorage.setItem("sigma_mode",t),setTimeout(()=>{sessionStorage.setItem("init",!1)},0)},J.prototype.setScale=function(e){const t=e-$.getState().scale;this.scaleStep(t)},J.prototype.scaleStep=function(e){const{callbacks:t={},gain:n}=this.options,{onScaleChange:i,onDocMove:s}=t,{doc:a,w:c,h:l,viewBox:{x:u,y:f},scale:d,maxScale:p,minScale:y,dropzone:m,sence:_,grid:x}=$.getState();let b=lt.round(d+e,2);if(b=Math.min(p,Math.max(y,b)),b===d)return;const w=b*c,C=b*l,A=u-c*e/2,L=f-l*e/2,P=`${A} ${L} ${w} ${C}`;a.attr({viewBox:P}),$.dispatch({type:qe,value:{viewBox:{x:A,y:L,w,h:C},scale:b}});let R=0,O=0;R=A<=0?-(Math.abs(w)*(n/2)+Math.abs(A)):A<=Math.abs(w)*(n/2)?-(Math.abs(w)*(n/2)-A):A-Math.abs(w)*(n/2),O=L<=0?-(Math.abs(C)*(n/2)+Math.abs(L)):L<=Math.abs(C)*(n/2)?-(Math.abs(C)*(n/2)-L):L-Math.abs(C)*(n/2),m.attr({x:R,y:O,width:Math.abs(w)*n,height:Math.abs(C)*n});const W=_.findOne(".gSence-bg"),B=x.findOne(".gGrid-bg"),H=`${-Math.abs(w)*n*100} ${-Math.abs(C)*n*100}, ${Math.abs(w)*n*100} ${-Math.abs(C)*n*100}, ${Math.abs(w)*n*100} ${Math.abs(C)*n*100}, ${-Math.abs(w)*n*100} ${Math.abs(C)*n*100}`;W.findOne(".quarter-grid").attr({points:H,fill:"url(#quarter-grid)",class:"quarter-grid",x:R,y:O}),B.findOne(".entire-grid").attr({points:H,fill:"url(#entire-grid)",class:"entire-grid",x:R,y:O}),s&&s(),i&&i(b),re()},J.prototype.loadShapes=function(e){const t=[];for(let n=0;n<e.length;n++){const i=e[n];i.type===rr&&t.push(new mp(i)),i.type===Yo&&t.push(new vp(i))}this.components=[...this.components,...t]},J.prototype.setStyleNode=function(e,t,n){this.core.setStyles(e,t,n)},J.prototype.createNode=function(e,t={}){const{customize:n,label:i,position:s,key:a}=t,c=e.create(a),l=[c];if(t){const u=this.core.__getStandardPosition(s);c.position={...c.position,...u}}if(n){if(!lt.isObject(n))return void console.error("自定义数据必须是一个对象");c.customize=lt.cloneDeep(n)}return(e==null?void 0:e.type)===rr&&this.cmdStack.saveAction({groupBegin:!0}),this.core.add(l),i&&lt.isString(i)&&i.length>0&&this.createNodeLabel(c,i),this.core.lines.clear(),c.key},J.prototype.replaceNode=function(e,t={}){const{customize:n}=t,i=e.create();n&&(lt.isObject(n)?i.customize=lt.cloneDeep(n):console.error("自定义数据必须是一个对象")),this.core.replace(i)},J.prototype.cloneNode=function(){this.core.clone()},J.prototype.cutNode=function(){this.core.cut()},J.prototype.copyNode=function(){this.core.copy()},J.prototype.deleteNode=function(){this.core.delete()},J.prototype.lockNode=function(){this.core.lock()},J.prototype.unlockNode=function(){this.core.unlock()},J.prototype.toggleLockNode=function(){this.core.toggleLock()},J.prototype.descriptNode=function(e){this.core.descript(e)},J.prototype.redo=function(){this.core.redo()},J.prototype.undo=function(){this.core.undo()},J.prototype.raiseShape=function(){this.core.raise()},J.prototype.lowerShape=function(){this.core.lower()},J.prototype.setNodePosition=function(e={},t){this.core.setPosition(e,t)},J.prototype.setVnodePosition=function(e,t){if(!e||!e.component)return void console.error("vnode component 未渲染！");const n=this.core.__getStandardPosition(t);e.position=n,e.component.fire("position-change")},J.prototype.toggleLayerVisable=function(e){e.visible()?e.hide():e.show()},J.prototype.setBackgroundGrid=function(e,t){lt.isNumber(e)&&lt.isNumber(t)?(this.config.backgroundGrid={width:e,height:t},this.__setExportArea(1.28*e,1.28*t)):(this.config.backgroundGrid=null,this.__setExportArea())},J.prototype.getBackgroundGrid=function(){const{g:e}=$.getState(),t=e.clone();t.insertAfter(e);const n=t.findOne('[g-name="grid"]'),i=t.findOne('[g-name="sence"]'),s=t.findOne('[g-name="dropzone"]');n.remove(),i.remove(),s.remove();const{backgroundGrid:a}=this.config;let c=1/0,l=1/0,u=-1,f=-1;const{w:d,h:p,x:y,y:m}=t.bbox();if(a){const{width:_,height:x}=a;u=_,f=x}else u=d,f=p;return c=y,l=m,t.remove(),{w:u,h:f,x:c,y:l}},J.prototype.getRenderedXML=async function(e={}){const{width:t,height:n,includeGrid:i=!0,onlySelection:s,exportMode:a}=e,{gain:c}=this.options;function l(Qt=0,et=200){return Math.max(Qt||0,et)}const u=l(t),f=l(n),{quarterColor:d,entireColor:p}=this.config,y=a||$.getState().mode;let m=document.createElement("div");const{x:_,y:x,w:b,h:w}=this.getBackgroundGrid(),C=l(b,u),A=l(w,f),L=new ht().addTo(m).attr({viewBox:`${_} ${x} ${C} ${A}`,width:u,height:f}),P=L.defs(),R=L.group(),O=R.group().attr({"pointer-events":"none","g-name":"sence"}),W=R.group().attr({"pointer-events":"none","g-name":"grid"}),B=R.group().attr({"pointer-events":"auto","g-name":"gNetwork"}),H=R.group().attr({"pointer-events":"auto","g-name":"gRect"}),Y=R.group().attr({"pointer-events":"auto","g-name":"gLine"}),V=R.group().attr({"pointer-events":"auto","g-name":"gCircle"}),at=R.group().attr({"pointer-events":"auto","g-name":"gProduct"}),rt=R.group().attr({"pointer-events":"auto","g-name":"gImage"}),ft=R.group().attr({"pointer-events":"auto","g-name":"gIcon"}),$t=R.group().attr({"pointer-events":"auto","g-name":"gText"}),Mt=R.group().attr({"pointer-events":"auto","g-name":"gCache"});if(i){const Qt=O.group(),et=W.group(),nt=P.pattern().attr({id:"entire-grid",patternUnits:"userSpaceOnUse"}),ot=P.pattern().attr({id:"quarter-grid",patternUnits:"userSpaceOnUse"}),xt=`${100*-c*u} ${100*-c*f}, ${100*c*u} ${100*-c*f}, ${100*c*u} ${100*c*f}, ${100*-c*u} ${100*c*f}`;et.polygon().attr({points:xt,fill:"url(#entire-grid)",class:"entire-grid"}),Qt.polygon().attr({points:xt,fill:"url(#quarter-grid)",class:"quarter-grid"}),y===Q?(nt.attr({x:0,y:37,width:128,height:74}),nt.path().attr({d:"M 64 0 L 128 37 64 74 0 37 z",stroke:"#DCDCDC","stroke-width":1,fill:"none"}),ot.attr({x:0,y:37,width:128,height:74}),ot.path().attr({d:"M 0 0 L 128 74",stroke:"#eee","stroke-width":1}),ot.path().attr({d:"M 128 0 L 0 74",stroke:"#eee","stroke-width":1})):(nt.attr({x:0,y:0,width:90,height:90}),nt.path().attr({d:"M 0 0 L 90 0 90 90 0 90 z",stroke:p.value||p.default,"stroke-width":1,fill:"none",class:"entirePattern"}),ot.attr({x:0,y:0,width:90,height:90}),ot.path().attr({d:"M -45 45 L 135 45",stroke:d.value||d.default,"stroke-width":1,class:"quarterPattern"}),ot.path().attr({d:"M 45 -45 L 45 135",stroke:d.value||d.default,"stroke-width":1,class:"quarterPattern"}))}else O.remove(),W.remove();await this.core._pureRender({1:O,3:W,4:B,5:H,6:Y,7:V,8:at,9:rt,10:ft,11:$t,20:Mt},{onlySelection:s,mode:y});const Gt=m.innerHTML;return m=null,Gt},J.prototype.clearPaint=function(){var e,t;(e=this.core)==null||e.allShapeChecked(),(t=this.core)==null||t.delete()},J.prototype.resetViewbox=function(){const{callbacks:e={}}=this.options,{doc:t,viewBox:{w:n,h:i}}=$.getState(),s={x:0,y:0,w:n,h:i};t.attr({viewBox:`0 0 ${n}, ${i}`}),$.dispatch({type:qe,value:{viewBox:s}}),e.onDocMove&&e.onDocMove()},J.prototype.initWithGraphData=function(e){sessionStorage.setItem("init",!0),this.graph=e;const t=unescape(e.content);t&&t!=="null"&&(this.resetViewbox(),this.core.data.id=e.key,this.core._initRenderWithStringData(t),this.zoomGraphFit()),sessionStorage.setItem("init",!1)},J.prototype.initWithTPLGraphData=function(e){sessionStorage.setItem("init",!0);const t=unescape(e);t&&t!=="null"&&(this.core._initRenderWithStringData(t),this.zoomGraphFit()),sessionStorage.setItem("init",!1)},J.prototype.getLocalGraphData=function(){return this.core.getGraphData()},J.prototype.getNetworkShapes=function(){return this.core._getNetworkShapes()},J.prototype.setBaseGroup=function(e){return this.core.setBaseGroup(e)},J.prototype.setAreaGroup=function(e){return this.core.setAreaGroup(e)},J.prototype.setAvailabilityZoneGroup=function(e){return this.core.setAvailabilityZoneGroup(e)},J.prototype.setSecurityGroup=function(e){return this.core.setSecurityGroup(e)},J.prototype.setVPCGroup=function(e){return this.core.setVPCGroup(e)},J.prototype.setSubnetGroup=function(e){return this.core.setSubnetGroup(e)},J.prototype.setCcnGroup=function(e){return this.core.setCcnGroup(e)},J.prototype.setTkeGroup=function(e){return this.core.setTkeGroup(e)},J.prototype.zoomGraphFit=function(){const{callbacks:e={},gain:t}=this.options,{onScaleChange:n,onDocMove:i}=e,{mode:s,doc:a,w:c,h:l,dropzone:u,sence:f,grid:d}=$.getState(),{w:p,h:y,x:m,y:_}=this.getBackgroundGrid();let x,b,w,C,A=1;const L=s===Q,P=L?256:180,R=L?148:180;if(p!==0&&y!==0)if(p+P>c||y+R>l){const at=lt.ceil((p+P)/c,1),rt=lt.ceil((y+R)/l,1);A=at>rt?at:rt,w=lt.round(A*c,2),C=lt.round(A*l,2),x=m-(w-p)/2,b=_-(C-y)/2}else w=c,C=l,x=m-(c-p)/2,b=_-(l-y)/2;else w=c,C=l,x=0,b=0;const O=`${x} ${b} ${w} ${C}`;a.attr({viewBox:O});let W=0,B=0;W=x<=0?-(Math.abs(w)*(t/2)+Math.abs(x)):x<=Math.abs(w)*(t/2)?-(Math.abs(w)*(t/2)-x):x-Math.abs(w)*(t/2),B=b<=0?-(Math.abs(C)*(t/2)+Math.abs(b)):b<=Math.abs(C)*(t/2)?-(Math.abs(C)*(t/2)-b):b-Math.abs(C)*(t/2),u.attr({x:W,y:B,width:Math.abs(w)*t,height:Math.abs(C)*t});const H=f.findOne(".gSence-bg"),Y=d.findOne(".gGrid-bg"),V=`${-Math.abs(w)*t*100} ${-Math.abs(C)*t*100}, ${Math.abs(w)*t*100} ${-Math.abs(C)*t*100}, ${Math.abs(w)*t*100} ${Math.abs(C)*t*100}, ${-Math.abs(w)*t*100} ${Math.abs(C)*t*100}`;H.findOne(".quarter-grid").attr({points:V,fill:"url(#quarter-grid)",class:"quarter-grid"}),Y.findOne(".entire-grid").attr({points:V,fill:"url(#entire-grid)",class:"entire-grid"}),$.dispatch({type:qe,value:{viewBox:{x,y:b,w,h:C},scale:A}}),i&&i(),n&&n(A),re()},J.prototype.setEditor=function(e){const{backgroundColor:t,quarterColor:n,entireColor:i,remember:s}=e,{doc:a,dropzone:c}=$.getState();t&&(c.attr("fill",t),s&&localStorage.setItem("sigma.backgroundColor",t)),i&&(a.find(".entirePattern").attr("stroke",i),s&&localStorage.setItem("sigma.entireColor",i)),n&&(a.find(".quarterPattern").attr("stroke",n),s&&localStorage.setItem("sigma.quarterColor",n))},J.prototype.resetEditor=function(){const{doc:e,dropzone:t}=$.getState();localStorage.removeItem("sigma.backgroundColor"),localStorage.removeItem("sigma.quarterColor"),localStorage.removeItem("sigma.entireColor"),this.config.backgroundColor.value=null,this.config.entireColor.value=null,this.config.quarterColor.value=null;const n=e.find(".entirePattern"),i=e.find(".quarterPattern");t.attr("fill",this.config.backgroundColor.default),n.attr("stroke",this.config.entireColor.default),i.attr("stroke",this.config.quarterColor.default)},J.prototype.getReferOriginPosition=function(){const{viewBox:{x:e,y:t}}=$.getState();return{x:e,y:t}},J.prototype.setCustomize=function(e,t){this.core.setCustomize(e,t)},J.prototype.resetCustomize=function(e){this.core.resetCustomize(e)},J.prototype.getCustomize=function(e){this.core.getCustomize(e)},J.prototype.alignLeft=function(e){this.core.align("left",e)},J.prototype.alignRight=function(e){this.core.align("right",e)},J.prototype.alignTop=function(e){this.core.align("top",e)},J.prototype.alignBottom=function(e){this.core.align("bottom",e)},J.prototype.arrangeRow=function(e,t){this.core.arrange("row",e,t)},J.prototype.arrangeCol=function(e,t){this.core.arrange("col",e,t)},J.prototype.arrangeSquare=function(e,t){this.core.arrange("square",e,t)},J.prototype.appendChild=function(e,t,n={}){const{container:i=".position",element:s="foreignObject",isRemoveExisted:a=!0,...c}=n,[l]=this.core._getShapesByKeys([e]);if(!l)return void console.error("appendChild: 该元素不存在！");const{component:u}=l;if(!u)return void console.error("appendChild: component 为空，元素未初始化！");const f=u.findOne(i);if(!f)return void console.error("appendChild: 当前container不存在！");const d="slot-child";if(a){const x=f.findOne(`.${d}`);x==null||x.remove()}const{width:p,height:y}=f.bbox(),m={width:p,height:y,...c},_=f.element(s).attr(m);_.addClass(d),_.add(ht(t))},J.prototype.bindStickyUnit=function(e,t,n={}){const[i]=this.core._getShapesByKeys([e]);return i?new mv({vnode:i,child:t,...n}):void console.error("appendChild: 该元素不存在！")},J.prototype.createLineNode=function(e){const t=new Al().createByVnode(e);return this.core.add([t]),t},J.prototype.setUneditable=function(){return this.core.__handleEditable(!0)},J.prototype.setEditable=function(){return this.core.__handleEditable(!1)},J.prototype.toggleEditable=function(){const{uneditable:e}=$.getState();return this.core.__handleEditable(!e),!e},J.prototype.setGlobalValue=function(e){$.dispatch({type:qe,value:e})},J.prototype.getGlobalValue=function(){const{root:e,mode:t,scale:n,w:i,h:s,doc:a,g:c,defs:l,viewBox:u,container:f,uneditable:d,core:p}=$.getState(),{shapes:y}=p.data;return{root:e,mode:t,scale:n,w:i,h:s,doc:a,g:c,defs:l,viewBox:u,container:f,uneditable:d,shapes:y}},J.prototype.transformPoint=function(e,t,n=!0,i=0,s=0){return(e===Q?dt:_t)(t,n,i,s)},J.prototype.toggleShapeChecked=function(e){this.core._toggleShapeChecked(e)},J.prototype.setShapeChecked=function(e,t){this.core._setShapeChecked(e,t)},J.prototype.getCheckedShapes=function(e={}){return this.core._getCheckedShapes(e)},J.prototype.clearHistory=function(){this.core.clearHistory(),$.dispatch({type:qe,value:{history:{historical:[],pointer:-1}}})},J.prototype.createNodeLabel=function(e,t){this.core._createNodeLabel(e,t)},J.prototype.setConnectionNumber=function(e){const t=[16,8,4].includes(e)?e:16;$.dispatch({type:qe,value:{connectionNumber:t}})},J.prototype.getMode=function(){return $.getState().mode},J.prototype.setDispersion=function(e){this.core.dispersion(e)},J.prototype.transform3DTo2D=_t,J.prototype.transform2DTo3D=dt,J.prototype.getStandardPosition=function(e){return this.core.__getStandardPosition(e)};const Ap=({detail:e})=>{const t=Ue.useRef(void 0),n=Ue.useRef(void 0);return console.log(22,e),Ue.useEffect(()=>{t.current&&(n.current=new J(t.current,{callbacks:{onShapeClick:(i,s)=>{console.log(i),console.log(11,s)},onCheckedChange:i=>{i.length||console.log(11,i)},onDocClick:()=>{}}}))},[]),oc.jsxs("div",{style:{width:900,height:900,background:"red"},children:[oc.jsx("div",{children:"222test"}),oc.jsx("div",{ref:t,style:{width:436,height:233}})]})};return window.Export=Ap,window.Sigma=J,Ap});
