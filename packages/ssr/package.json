{"name": "server", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tencent/sigma-editor": "workspace:*", "@tencent/sigma-editor-for-server": "workspace:*", "@tencent/tea-sdk-capacity-monitoring": "workspace:*", "vite-template": "workspace:*", "next": "^15.2.3", "puppeteer": "^20.9.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "eslint": "^8", "eslint-config-next": "15.0.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}