import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
const exportName = 'report';
export async function GET() {
  try {
    const htmlTemplatePath = path.join(
      __dirname,
      '../../../../../../../vite-template',
      '/build',
      `/${exportName}.html`
    );
    const html = fs.readFileSync(htmlTemplatePath, 'utf-8');
    return new NextResponse(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error) {
    console.error('Error rendering component:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}