import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import puppeteer from 'puppeteer';
import { data } from '../../../mock';
const exportName = 'report';
export async function GET() {
  let browser;
  try {
    const htmlTemplatePath = path.join(
      __dirname,
      '../../../../../../../vite-template',
      '/build',
      `/${exportName}.html`
    );

    const html = fs.readFileSync(htmlTemplatePath, 'utf-8').replace('<style>@page {margin: 0mm 0mm 0mm 0mm;size: 148.1mm 211mm}</style>', '');

    browser = await puppeteer.launch();
    const page = await browser.newPage();

    page.on('console', (msg) => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[Browser ${type}]: ${text}`);
    });

    page.on('pageerror', (err) => {
      console.error('Page error:', err.message);
    });

    page.on('error', (err) => {
      console.error('Error:', err);
    });

    page.on('requestfailed', (request) => {
      console.error(`Request failed: ${request.url()}`, request.failure());
    });

    await page.evaluate((injectedData) => {
      (window as any).dataDev = injectedData;
    }, data);

    await page.setContent(html);

    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.waitForSelector('#root > *', {
      timeout: 10000
    }).catch(err => {
      throw new Error(`等待选择器超时: ${err.message}`);
    });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      scale: 1.355, // 调整缩放比例以适应 A4 纸
    });

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename=generated.pdf',
      },
    });
  } catch (error) {
    console.error('PDF生成错误:', error);
    return NextResponse.json(
      { error: (error as Error)?.message || '内部服务器错误' },
      { status: 500 }
    );
  } finally {
    if (browser) {
      await browser.close().catch(console.error);
    }
  }
}
