{
  "parser": "@typescript-eslint/parser",
  "extends": [
    "@tencent/eslint-config-tencent",
    "@tencent/eslint-config-tencent/ts",
    "airbnb",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "plugins": [
    "import",
    "@typescript-eslint"
  ],
  "env": {
    "browser": true,
    "es6": true
  },
  "settings": {
    "react": {
      "version": "detect"
    },
    "import/extensions": [".js", ".jsx", ".ts", ".tsx"],
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"]
    },
    "import/resolver": {
      "alwaysTryTypes": true,
      "project": "./tsconfig.json"
      // "project": [
      //   "./tsconfig.json",
      //   "tea-app-cloud-arch/tsconfig.json",
      //   "isa-cloud-arch/tsconfig.json"
      // ]
    }
  },
  "parserOptions": {
    "ecmaVersion": 2018,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "rules": {
    "no-unused-vars": "error",
    "linebreak-style": "off",
    "react/self-closing-comp": "error",
    "react/jsx-indent": ["warn", 2],
    "react/jsx-indent-props": ["warn", 2],
    "react/jsx-max-props-per-line": [
      "error",
      { "maximum": 1, "when": "multiline" }
    ],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/naming-convention": "warn",
    "@typescript-eslint/quotes": "error",
    "no-use-before-define": "off",
    "react/jsx-filename-extension": "off",
    "react/jsx-no-useless-fragment": "off",
    "react/jsx-no-constructed-context-values": "off",
    "react/jsx-props-no-spreading": "off",
    "react/function-component-definition": "off",
    "react/jsx-wrap-multilines": "off",
    "react/require-default-props": "off",
    "react/no-unused-prop-types": "off",
    "react/jsx-closing-tag-location": "warn",
    "react/no-deprecated": ["off"],
    "jsx-a11y/no-static-element-interactions": "off",
    "jsx-a11y/label-has-associated-control": "warn",
    "jsx-a11y/click-events-have-key-events": "off",
    "no-restricted-syntax": ["warn"],
    "no-shadow": "off",
    "no-restricted-globals": "off",
    "import/extensions": "off",
    "import/export": "warn",
    "import/prefer-default-export": "warn",
    "indent": ["off"],
    "max-len": ["warn", { "code": 120 }]
  }
}
